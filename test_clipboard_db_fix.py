import sys
import os
sys.path.append('.')

try:
    from smartvault.utils.config import load_config
    from smartvault.data.database import Database
    
    print("测试剪贴板监控服务数据库连接修复...")
    
    # 模拟剪贴板监控服务的数据库连接逻辑
    config = load_config()
    library_path = config.get("library_path", "")
    
    print(f"配置中的文件库路径: {library_path}")
    
    if not library_path:
        print("⚠️ 文件库路径未配置，剪贴板查重功能将仅支持文件名清洗")
    else:
        # 检查路径是否存在
        if not os.path.exists(library_path):
            print(f"⚠️ 文件库路径不存在: {library_path}")
        else:
            # 构建正确的数据库文件路径（修复后的逻辑）
            db_path = os.path.join(library_path, "data", "smartvault.db")
            
            print(f"数据库文件路径: {db_path}")
            
            # 检查数据库文件是否存在
            if not os.path.exists(db_path):
                print(f"⚠️ 数据库文件不存在: {db_path}")
            else:
                print("✅ 数据库文件存在")
                
                # 测试数据库连接
                try:
                    db = Database(db_path)
                    print("✅ 剪贴板服务数据库连接已建立")
                    
                    # 测试查询功能
                    cursor = db.conn.cursor()
                    
                    # 测试按哈希查找文件
                    cursor.execute(
                        "SELECT id, name, original_path, library_path, entry_type FROM files WHERE file_hash = ? LIMIT 1",
                        ("00000000",)  # 测试哈希
                    )
                    hash_results = cursor.fetchall()
                    print(f"按哈希查找测试结果: {len(hash_results)} 个文件")
                    
                    # 测试按文件名查找文件
                    cursor.execute(
                        "SELECT id, name, original_path, library_path, entry_type FROM files WHERE name LIKE ? LIMIT 5",
                        ("%测试%",)  # 测试文件名
                    )
                    name_results = cursor.fetchall()
                    print(f"按文件名查找测试结果: {len(name_results)} 个文件")
                    
                    if name_results:
                        print("找到的文件示例:")
                        for row in name_results[:3]:
                            print(f"  - {row[1]}")
                    
                    db.close()
                    print("✅ 数据库连接测试完成")
                    
                except Exception as e:
                    print(f"⚠️ 建立数据库连接失败: {e}")
                    import traceback
                    traceback.print_exc()
    
    print("\n修复总结:")
    print("- 修复了剪贴板监控服务中数据库路径构建错误")
    print("- 原来传递的是library_path，现在正确传递db_path")
    print("- 添加了数据库文件存在性检查")
    
except Exception as e:
    print(f"测试失败: {e}")
    import traceback
    traceback.print_exc()
