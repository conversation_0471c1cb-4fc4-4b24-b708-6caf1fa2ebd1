"""
备份管理器 - 从core.py拆分出的备份相关功能
负责处理自动备份服务的启动、停止、状态显示和手动备份等功能
"""

from PySide6.QtWidgets import QLabel, QMenu
from PySide6.QtCore import QTimer, Qt


class BackupManager:
    """备份管理器类 - 处理所有备份相关的UI交互"""

    def __init__(self, main_window):
        """初始化备份管理器

        Args:
            main_window: 主窗口实例
        """
        self.main_window = main_window
        self.backup_status_label = None
        self.backup_status_timer = None

    def start_backup_service(self):
        """启动自动备份服务"""
        try:
            print("启动自动备份服务...")
            self.main_window.backup_service.start_auto_backup()

            # 获取备份状态
            status = self.main_window.backup_service.get_backup_status()
            if status['is_running']:
                print(f"自动备份服务已启动 (每{status['interval_hours']}小时备份一次)")
                self.main_window.show_status_message("自动备份服务已启动", True)
            else:
                print("自动备份服务已禁用")

        except Exception as e:
            print(f"启动备份服务失败: {e}")
            self.main_window.show_status_message(f"启动备份服务失败: {e}", False)

    def stop_backup_service(self):
        """停止自动备份服务"""
        try:
            print("停止自动备份服务...")
            self.main_window.backup_service.stop_auto_backup()
            print("自动备份服务已停止")

        except Exception as e:
            print(f"停止备份服务失败: {e}")

    def get_backup_status(self):
        """获取备份状态信息"""
        try:
            return self.main_window.backup_service.get_backup_status()
        except Exception as e:
            print(f"获取备份状态失败: {e}")
            return {
                'enabled': False,
                'is_running': False,
                'total_backups': 0,
                'latest_backup': None
            }

    def setup_backup_status_display(self):
        """设置状态栏备份状态显示"""
        try:
            # 创建备份状态标签
            self.backup_status_label = QLabel()
            self.backup_status_label.setToolTip("点击查看备份详情")
            self.backup_status_label.mousePressEvent = self.on_backup_status_clicked

            # 添加到状态栏
            self.main_window.statusBar().addPermanentWidget(self.backup_status_label)

            # 创建定时器，每30秒更新一次状态
            self.backup_status_timer = QTimer()
            self.backup_status_timer.timeout.connect(self.update_backup_status_display)
            self.backup_status_timer.start(30000)  # 30秒

            # 立即更新一次状态
            self.update_backup_status_display()

        except Exception as e:
            print(f"设置备份状态显示失败: {e}")

    def update_backup_status_display(self):
        """更新状态栏的备份状态显示"""
        try:
            status = self.get_backup_status()

            if status['enabled'] and status['is_running']:
                # 备份服务运行中
                icon = "🔄"
                if status['latest_backup']:
                    latest = status['latest_backup']
                    status_text = f"{icon} 备份: {latest['date']} ({latest['size_mb']:.1f}MB)"
                else:
                    status_text = f"{icon} 备份: 运行中"
                self.backup_status_label.setStyleSheet("color: green;")
            elif status['enabled']:
                # 备份已启用但未运行
                icon = "⏸️"
                status_text = f"{icon} 备份: 已启用"
                self.backup_status_label.setStyleSheet("color: orange;")
            else:
                # 备份已禁用
                icon = "⏹️"
                status_text = f"{icon} 备份: 已禁用"
                self.backup_status_label.setStyleSheet("color: gray;")

            self.backup_status_label.setText(status_text)

            # 更新工具提示
            if status['latest_backup']:
                latest = status['latest_backup']
                tooltip = f"备份状态: {'运行中' if status['is_running'] else '已停止'}\n"
                tooltip += f"最新备份: {latest['date']}\n"
                tooltip += f"备份大小: {latest['size_mb']:.2f}MB\n"
                tooltip += f"总备份数: {status['total_backups']}\n"
                tooltip += "左键: 打开设置 | 右键: 快捷菜单"
            else:
                tooltip = f"备份状态: {'运行中' if status['is_running'] else '已停止'}\n"
                tooltip += "暂无备份\n"
                tooltip += "左键: 打开设置 | 右键: 快捷菜单"

            self.backup_status_label.setToolTip(tooltip)

        except Exception as e:
            print(f"更新备份状态显示失败: {e}")
            if self.backup_status_label:
                self.backup_status_label.setText("❌ 备份: 错误")
                self.backup_status_label.setStyleSheet("color: red;")
                self.backup_status_label.setToolTip(f"备份状态获取失败: {e}")

    def on_backup_status_clicked(self, event):
        """处理备份状态标签点击事件"""
        try:
            if event.button() == Qt.LeftButton:
                # 左键点击：打开设置对话框的备份页面
                self.open_backup_settings()
            elif event.button() == Qt.RightButton:
                # 右键点击：显示快捷菜单
                self.show_backup_context_menu(event.globalPos())

        except Exception as e:
            print(f"处理备份状态点击失败: {e}")

    def open_backup_settings(self):
        """打开备份设置页面"""
        try:
            from smartvault.ui.dialogs.settings_dialog import SettingsDialog

            dialog = SettingsDialog(self.main_window)
            # 直接切换到备份页面
            if hasattr(dialog, 'switch_to_page'):
                dialog.switch_to_page('backup')
            dialog.exec()

        except Exception as e:
            print(f"打开备份设置失败: {e}")

    def on_backup_completed(self, backup_info):
        """处理备份完成事件"""
        try:
            print(f"备份完成: {backup_info}")
            self.main_window.show_status_message(f"备份完成: {backup_info.get('message', '成功')}", True)
            # 更新状态显示
            self.update_backup_status_display()
        except Exception as e:
            print(f"处理备份完成事件失败: {e}")

    def on_backup_failed(self, error_info):
        """处理备份失败事件"""
        try:
            print(f"备份失败: {error_info}")
            self.main_window.show_status_message(f"备份失败: {error_info.get('message', '未知错误')}", False)
            # 更新状态显示
            self.update_backup_status_display()
        except Exception as e:
            print(f"处理备份失败事件失败: {e}")

    def show_backup_context_menu(self, position):
        """显示备份状态右键菜单"""
        try:
            menu = QMenu(self.main_window)

            # 立即备份
            backup_action = menu.addAction("🔄 立即备份")
            backup_action.triggered.connect(self.create_manual_backup)

            # 查看备份历史
            history_action = menu.addAction("📋 备份历史")
            history_action.triggered.connect(self.open_backup_settings)

            menu.addSeparator()

            # 备份设置
            settings_action = menu.addAction("⚙️ 备份设置")
            settings_action.triggered.connect(self.open_backup_settings)

            menu.exec(position)

        except Exception as e:
            print(f"显示备份右键菜单失败: {e}")

    def create_manual_backup(self):
        """创建手动备份"""
        try:
            # 显示进度提示
            self.main_window.show_status_message("正在创建备份...", True)

            # 创建备份
            success, backup_path, message = self.main_window.backup_service.create_backup("manual")

            if success:
                self.main_window.show_status_message(f"备份创建成功: {message}", True)
                # 立即更新状态显示
                self.update_backup_status_display()
            else:
                self.main_window.show_status_message(f"备份创建失败: {message}", False)

        except Exception as e:
            print(f"创建手动备份失败: {e}")
            self.main_window.show_status_message(f"创建备份失败: {e}", False)

    def cleanup(self):
        """清理资源"""
        try:
            if self.backup_status_timer:
                self.backup_status_timer.stop()
                self.backup_status_timer = None

            if self.backup_status_label:
                # 从状态栏移除标签
                self.main_window.statusBar().removeWidget(self.backup_status_label)
                self.backup_status_label = None

        except Exception as e:
            print(f"清理备份管理器资源失败: {e}")
