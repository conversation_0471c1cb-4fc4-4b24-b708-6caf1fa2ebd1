#!/usr/bin/env python3
"""
AI设置界面测试

测试AI设置界面的模块化架构和功能完整性
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from smartvault.ui.dialogs.settings.pages.ai.ai_main_page import AISettingsPage
from smartvault.services.ai.ai_manager import AIManager
from smartvault.utils.config import load_config


class TestWindow(QMainWindow):
    """测试窗口"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("AI设置界面测试")
        self.setGeometry(100, 100, 1000, 800)

        # 创建中央窗口
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)

        # 创建AI设置页面
        self.ai_page = AISettingsPage()
        layout.addWidget(self.ai_page)

        # 初始化AI管理器（模拟）
        self.setup_ai_manager()

        # 加载测试配置
        self.load_test_config()

    def setup_ai_manager(self):
        """设置AI管理器"""
        try:
            # 创建AI管理器
            ai_manager = AIManager()

            # 加载配置
            config = load_config()

            # 初始化AI管理器
            ai_manager.initialize(config)

            # 设置到AI页面
            self.ai_page.set_ai_manager(ai_manager)

            print("✅ AI管理器初始化成功")

        except Exception as e:
            print(f"❌ AI管理器初始化失败: {e}")

    def load_test_config(self):
        """加载测试配置"""
        try:
            # 创建测试配置
            test_config = {
                'ai': {
                    'enabled': True,
                    'stage': 'rule_based',
                    'features': {
                        'project_detection': {
                            'enabled': True,
                            'min_files': 3,
                            'confidence_threshold': 0.7
                        },
                        'series_detection': {
                            'enabled': True,
                            'min_files': 2,
                            'confidence_threshold': 0.6
                        },
                        'behavior_learning': {
                            'enabled': True,
                            'auto_learn': True,
                            'learn_from_tags': True,
                            'learn_from_moves': True,
                            'learning_rate': 0.1,
                            'max_patterns': 1000
                        },
                        'smart_suggestions': {
                            'enabled': True,
                            'max_suggestions': 8,
                            'merge_with_rules': True
                        }
                    }
                }
            }

            # 加载到AI页面
            self.ai_page.load_settings(test_config)

            print("✅ 测试配置加载成功")

        except Exception as e:
            print(f"❌ 测试配置加载失败: {e}")


def test_ai_components():
    """测试AI组件功能"""
    print("🧪 测试AI设置组件")
    print("=" * 50)

    try:
        # 确保QApplication存在
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        # 测试配置管理器
        from smartvault.ui.dialogs.settings.pages.ai.utils.ai_config_helper import AIConfigManager

        config_manager = AIConfigManager()
        default_config = config_manager.load_ai_config()
        print(f"✅ 配置管理器测试通过，默认配置: {len(default_config)} 项")

        # 测试AI状态组件
        from smartvault.ui.dialogs.settings.pages.ai.components.ai_status_widget import AIStatusWidget

        status_widget = AIStatusWidget()
        status_widget.set_config_manager(config_manager)
        print("✅ AI状态组件测试通过")

        # 测试AI功能组件
        from smartvault.ui.dialogs.settings.pages.ai.components.ai_features_widget import AIFeaturesWidget

        features_widget = AIFeaturesWidget()
        features_widget.set_config_manager(config_manager)
        print("✅ AI功能组件测试通过")

        # 测试行为学习组件
        from smartvault.ui.dialogs.settings.pages.ai.components.behavior_learning_widget import BehaviorLearningWidget

        learning_widget = BehaviorLearningWidget()
        learning_widget.set_config_manager(config_manager)
        print("✅ 行为学习组件测试通过")

        # 测试自适应规则组件
        from smartvault.ui.dialogs.settings.pages.ai.components.adaptive_rules_widget import AdaptiveRulesWidget

        rules_widget = AdaptiveRulesWidget()
        rules_widget.set_config_manager(config_manager)
        print("✅ 自适应规则组件测试通过")

        # 测试统计信息组件
        from smartvault.ui.dialogs.settings.pages.ai.components.ai_statistics_widget import AIStatisticsWidget

        stats_widget = AIStatisticsWidget()
        stats_widget.set_config_manager(config_manager)
        print("✅ 统计信息组件测试通过")

        return True

    except Exception as e:
        print(f"❌ AI组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_code_length():
    """测试代码长度控制"""
    print("\n📏 测试代码长度控制")
    print("=" * 50)

    files_to_check = [
        ('smartvault/ui/dialogs/settings/pages/ai/ai_main_page.py', 250),
        ('smartvault/ui/dialogs/settings/pages/ai/utils/ai_config_helper.py', 250),
        ('smartvault/ui/dialogs/settings/pages/ai/components/ai_status_widget.py', 200),
        ('smartvault/ui/dialogs/settings/pages/ai/components/ai_features_widget.py', 250),
        ('smartvault/ui/dialogs/settings/pages/ai/components/behavior_learning_widget.py', 250),
        ('smartvault/ui/dialogs/settings/pages/ai/components/adaptive_rules_widget.py', 250),
        ('smartvault/ui/dialogs/settings/pages/ai/components/ai_statistics_widget.py', 220),
        ('smartvault/ui/dialogs/settings/pages/ai/dialogs/rule_performance_dialog.py', 300),
    ]

    all_passed = True
    total_lines = 0

    for file_path, max_lines in files_to_check:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = len(f.readlines())
                    total_lines += lines

                    if lines <= max_lines:
                        status = "✅"
                    else:
                        status = "❌"
                        all_passed = False

                    filename = os.path.basename(file_path)
                    print(f"{status} {filename}: {lines}/{max_lines} lines")

            except Exception as e:
                print(f"❌ {file_path}: 读取失败 - {e}")
                all_passed = False
        else:
            print(f"❌ {file_path}: 文件不存在")
            all_passed = False

    print(f"\n📊 总计: {total_lines} lines")
    print(f"🎯 代码长度控制: {'✅ 通过' if all_passed else '❌ 失败'}")

    return all_passed


def main():
    """主测试函数"""
    print("🚀 AI设置界面模块化架构测试")
    print("=" * 60)

    # 测试组件功能
    components_ok = test_ai_components()

    # 测试代码长度
    length_ok = test_code_length()

    # 测试UI界面（如果有显示环境）
    ui_ok = True
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        # 创建测试窗口
        window = TestWindow()

        print("\n🖥️ UI界面测试")
        print("=" * 50)
        print("✅ AI设置页面创建成功")
        print("✅ 组件布局正常")
        print("✅ 配置加载正常")

        # 显示窗口（可选）
        if len(sys.argv) > 1 and sys.argv[1] == '--show':
            print("🖼️ 显示测试窗口...")
            window.show()
            app.exec()

    except Exception as e:
        print(f"❌ UI界面测试失败: {e}")
        ui_ok = False

    # 输出测试结果
    print("\n📊 测试结果汇总")
    print("=" * 60)
    print(f"组件功能测试: {'✅ 通过' if components_ok else '❌ 失败'}")
    print(f"代码长度控制: {'✅ 通过' if length_ok else '❌ 失败'}")
    print(f"UI界面测试: {'✅ 通过' if ui_ok else '❌ 失败'}")

    all_passed = components_ok and length_ok and ui_ok

    if all_passed:
        print("\n🎉 恭喜！AI设置界面模块化架构测试全部通过！")
        print("📋 架构优势:")
        print("   ✅ 代码长度严格控制，每个文件都在预期范围内")
        print("   ✅ 组件化设计，职责单一，易于维护")
        print("   ✅ 模块化架构，便于扩展和团队协作")
        print("   ✅ 统一的配置管理，数据流清晰")
        print("   ✅ 完善的错误处理和用户反馈")
        print("\n🚀 可以开始集成到主设置对话框！")
    else:
        print("\n❌ 测试存在失败项，请检查并修复问题")

    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
