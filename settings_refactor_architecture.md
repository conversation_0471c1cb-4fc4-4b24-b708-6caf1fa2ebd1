
# 设置对话框模块化架构设计

## 目录结构
```
smartvault/ui/dialogs/settings/
├── __init__.py                 # 主入口，保持向后兼容
├── base/
│   ├── __init__.py
│   ├── base_dialog.py         # 基础对话框类
│   └── base_page.py           # 基础设置页面类
├── pages/
│   ├── __init__.py
│   ├── library_page.py        # 智能文件库设置页面
│   ├── monitor_page.py        # 文件监控设置页面
│   ├── ui_page.py             # 界面设置页面
│   ├── search_page.py         # 搜索设置页面
│   ├── auto_tag_page.py       # 自动标签设置页面
│   └── advanced_page.py       # 高级设置页面
├── dialogs/
│   ├── __init__.py
│   └── monitor_config_dialog.py  # 监控配置对话框
└── main_dialog.py             # 主设置对话框
```

## 架构原则

### 1. 单一职责原则
- 每个页面类只负责一个设置类别
- 基础类提供通用功能
- 主对话框只负责页面组织和协调

### 2. 开放封闭原则
- 新增设置页面无需修改现有代码
- 通过继承和组合扩展功能
- 配置接口保持稳定

### 3. 依赖倒置原则
- 页面依赖抽象的配置接口
- 主对话框依赖抽象的页面接口
- 具体实现可以独立变化

## 接口设计

### BaseSettingsPage 接口
```python
class BaseSettingsPage(QWidget):
    def load_settings(self, config: dict) -> None:
        """从配置加载设置到UI控件"""
        pass
    
    def save_settings(self) -> dict:
        """从UI控件保存设置到配置"""
        pass
    
    def validate_settings(self) -> tuple[bool, str]:
        """验证设置有效性"""
        return True, ""
    
    def reset_to_defaults(self) -> None:
        """重置为默认设置"""
        pass
```

### SettingsDialog 接口
```python
class SettingsDialog(QDialog):
    def __init__(self, parent=None):
        """保持原有接口"""
        pass
    
    def get_config(self) -> dict:
        """获取当前配置（新增）"""
        pass
    
    def set_config(self, config: dict) -> None:
        """设置配置（新增）"""
        pass
```

## 迁移策略

### 阶段1：创建基础框架
1. 创建目录结构
2. 实现 BaseSettingsPage
3. 实现 BaseSettingsDialog
4. 创建测试框架

### 阶段2：迁移简单页面
1. UI设置页面（最简单）
2. 搜索设置页面
3. 高级设置页面

### 阶段3：迁移复杂页面
1. 智能文件库设置页面
2. 自动标签设置页面
3. 文件监控设置页面（最复杂）

### 阶段4：整合和优化
1. 创建新的主对话框
2. 更新 __init__.py 保持兼容性
3. 删除旧代码
4. 性能优化

## 风险控制

### 向后兼容性
```python
# smartvault/ui/dialogs/settings/__init__.py
from .main_dialog import SettingsDialog

# 保持原有导入路径有效
# from smartvault.ui.dialogs.settings_dialog import SettingsDialog
```

### 渐进式替换
- 保留原文件直到完全迁移完成
- 每个页面独立测试
- 可以随时回滚到原版本

### 测试覆盖
- 每个页面都有独立测试
- 集成测试确保页面间协作
- 性能测试确保无回归
