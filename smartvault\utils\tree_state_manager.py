"""
树控件展开状态管理器
用于统一管理所有树控件的展开状态持久化
"""

from PySide6.QtCore import QSettings
from PySide6.QtWidgets import QTreeWidget, QTreeView
from PySide6.QtCore import QModelIndex


class TreeStateManager:
    """树控件展开状态管理器"""
    
    def __init__(self):
        """初始化管理器"""
        self.settings = QSettings("SmartVault", "TreeStates")
    
    def save_tree_state(self, tree_widget, tree_id):
        """保存树控件的展开状态
        
        Args:
            tree_widget: 树控件 (QTreeWidget 或 QTreeView)
            tree_id: 树控件的唯一标识符
        """
        try:
            if isinstance(tree_widget, QTreeWidget):
                self._save_tree_widget_state(tree_widget, tree_id)
            elif isinstance(tree_widget, QTreeView):
                self._save_tree_view_state(tree_widget, tree_id)
            else:
                print(f"⚠️  不支持的树控件类型: {type(tree_widget)}")
        except Exception as e:
            print(f"❌ 保存树状态失败 [{tree_id}]: {e}")
    
    def restore_tree_state(self, tree_widget, tree_id):
        """恢复树控件的展开状态
        
        Args:
            tree_widget: 树控件 (QTreeWidget 或 QTreeView)
            tree_id: 树控件的唯一标识符
        """
        try:
            if isinstance(tree_widget, QTreeWidget):
                self._restore_tree_widget_state(tree_widget, tree_id)
            elif isinstance(tree_widget, QTreeView):
                self._restore_tree_view_state(tree_widget, tree_id)
            else:
                print(f"⚠️  不支持的树控件类型: {type(tree_widget)}")
        except Exception as e:
            print(f"❌ 恢复树状态失败 [{tree_id}]: {e}")
    
    def _save_tree_widget_state(self, tree_widget, tree_id):
        """保存QTreeWidget的展开状态"""
        expanded_items = []
        
        def collect_expanded_items(item, path=""):
            """递归收集展开的项目"""
            if item is None:
                return
            
            # 构建当前项目的路径
            current_path = f"{path}/{item.text(0)}" if path else item.text(0)
            
            # 如果项目是展开的，记录路径
            if item.isExpanded():
                expanded_items.append(current_path)
            
            # 递归处理子项目
            for i in range(item.childCount()):
                child = item.child(i)
                collect_expanded_items(child, current_path)
        
        # 从根项目开始收集
        root = tree_widget.invisibleRootItem()
        for i in range(root.childCount()):
            top_level_item = root.child(i)
            collect_expanded_items(top_level_item)
        
        # 保存到设置
        self.settings.setValue(f"{tree_id}/expanded_items", expanded_items)
        print(f"🔧 已保存树状态 [{tree_id}]: {len(expanded_items)} 个展开项")
    
    def _restore_tree_widget_state(self, tree_widget, tree_id):
        """恢复QTreeWidget的展开状态"""
        expanded_items = self.settings.value(f"{tree_id}/expanded_items", [])
        
        if not expanded_items:
            print(f"🔧 无保存的树状态 [{tree_id}]，使用默认展开")
            # 如果没有保存的状态，展开第一级
            self._expand_first_level(tree_widget)
            return
        
        print(f"🔧 恢复树状态 [{tree_id}]: {len(expanded_items)} 个展开项")
        
        def find_and_expand_item(item, target_path, current_path=""):
            """递归查找并展开指定路径的项目"""
            if item is None:
                return False
            
            # 构建当前项目的路径
            item_path = f"{current_path}/{item.text(0)}" if current_path else item.text(0)
            
            # 如果找到目标路径，展开该项目
            if item_path == target_path:
                item.setExpanded(True)
                return True
            
            # 如果当前路径是目标路径的前缀，继续搜索子项目
            if target_path.startswith(item_path + "/"):
                for i in range(item.childCount()):
                    child = item.child(i)
                    if find_and_expand_item(child, target_path, item_path):
                        return True
            
            return False
        
        # 恢复展开状态
        for expanded_path in expanded_items:
            root = tree_widget.invisibleRootItem()
            for i in range(root.childCount()):
                top_level_item = root.child(i)
                find_and_expand_item(top_level_item, expanded_path)
    
    def _save_tree_view_state(self, tree_view, tree_id):
        """保存QTreeView的展开状态"""
        expanded_indexes = []
        model = tree_view.model()
        
        if not model:
            return
        
        def collect_expanded_indexes(parent_index=QModelIndex(), path=""):
            """递归收集展开的索引"""
            row_count = model.rowCount(parent_index)
            
            for row in range(row_count):
                index = model.index(row, 0, parent_index)
                if not index.isValid():
                    continue
                
                # 构建路径（使用显示文本）
                item_text = model.data(index, 0) or ""  # DisplayRole
                current_path = f"{path}/{item_text}" if path else item_text
                
                # 如果索引是展开的，记录路径
                if tree_view.isExpanded(index):
                    expanded_indexes.append(current_path)
                
                # 递归处理子项
                collect_expanded_indexes(index, current_path)
        
        collect_expanded_indexes()
        
        # 保存到设置
        self.settings.setValue(f"{tree_id}/expanded_indexes", expanded_indexes)
        print(f"🔧 已保存树视图状态 [{tree_id}]: {len(expanded_indexes)} 个展开项")
    
    def _restore_tree_view_state(self, tree_view, tree_id):
        """恢复QTreeView的展开状态"""
        expanded_indexes = self.settings.value(f"{tree_id}/expanded_indexes", [])
        model = tree_view.model()
        
        if not model:
            return
        
        if not expanded_indexes:
            print(f"🔧 无保存的树视图状态 [{tree_id}]，使用默认展开")
            # 如果没有保存的状态，展开第一级
            self._expand_first_level_view(tree_view)
            return
        
        print(f"🔧 恢复树视图状态 [{tree_id}]: {len(expanded_indexes)} 个展开项")
        
        def find_and_expand_index(parent_index=QModelIndex(), target_path="", current_path=""):
            """递归查找并展开指定路径的索引"""
            row_count = model.rowCount(parent_index)
            
            for row in range(row_count):
                index = model.index(row, 0, parent_index)
                if not index.isValid():
                    continue
                
                # 构建当前路径
                item_text = model.data(index, 0) or ""
                index_path = f"{current_path}/{item_text}" if current_path else item_text
                
                # 如果找到目标路径，展开该索引
                if index_path == target_path:
                    tree_view.setExpanded(index, True)
                    return True
                
                # 如果当前路径是目标路径的前缀，继续搜索
                if target_path.startswith(index_path + "/"):
                    if find_and_expand_index(index, target_path, index_path):
                        return True
            
            return False
        
        # 恢复展开状态
        for expanded_path in expanded_indexes:
            find_and_expand_index(QModelIndex(), expanded_path)
    
    def _expand_first_level(self, tree_widget):
        """展开QTreeWidget的第一级"""
        root = tree_widget.invisibleRootItem()
        for i in range(root.childCount()):
            item = root.child(i)
            item.setExpanded(True)
    
    def _expand_first_level_view(self, tree_view):
        """展开QTreeView的第一级"""
        model = tree_view.model()
        if not model:
            return
        
        row_count = model.rowCount()
        for row in range(row_count):
            index = model.index(row, 0)
            if index.isValid():
                tree_view.setExpanded(index, True)
    
    def clear_tree_state(self, tree_id):
        """清除指定树控件的保存状态
        
        Args:
            tree_id: 树控件的唯一标识符
        """
        try:
            self.settings.remove(f"{tree_id}/expanded_items")
            self.settings.remove(f"{tree_id}/expanded_indexes")
            print(f"🧹 已清除树状态 [{tree_id}]")
        except Exception as e:
            print(f"❌ 清除树状态失败 [{tree_id}]: {e}")
    
    def clear_all_states(self):
        """清除所有保存的树状态"""
        try:
            self.settings.clear()
            print("🧹 已清除所有树状态")
        except Exception as e:
            print(f"❌ 清除所有树状态失败: {e}")


# 全局实例
tree_state_manager = TreeStateManager()
