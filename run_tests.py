"""
运行所有测试
"""

import os
import sys
import unittest
import argparse


def _run_single_test_type(test_dir, verbose=False):
    """运行单个类型的测试

    Args:
        test_dir: 测试目录名
        verbose: 是否显示详细信息

    Returns:
        bool: 测试是否成功
    """
    start_dir = os.path.join("tests", test_dir)

    # 检查目录是否存在
    if not os.path.exists(start_dir):
        print(f"警告: 测试目录 {start_dir} 不存在")
        return True

    # 设置测试加载器
    loader = unittest.TestLoader()

    # 发现测试
    suite = loader.discover(start_dir, pattern="test_*.py")

    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2 if verbose else 1)
    result = runner.run(suite)

    return result.wasSuccessful()


def run_tests(test_type=None, verbose=False):
    """运行测试

    Args:
        test_type: 测试类型（unit/integration/ui/all）
        verbose: 是否显示详细信息
    """
    if test_type and test_type != "all":
        # 运行特定类型的测试
        return _run_single_test_type(test_type, verbose)
    else:
        # 运行所有类型的测试
        success = True
        test_types = ["unit", "integration", "ui"]

        for test_dir in test_types:
            print(f"\n运行{test_dir}测试:")
            result = _run_single_test_type(test_dir, verbose)
            success = success and result

        return success


if __name__ == "__main__":
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="运行SmartVault测试")
    parser.add_argument(
        "--type",
        choices=["unit", "integration", "ui", "all"],
        default="all",
        help="测试类型"
    )
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="显示详细信息"
    )
    args = parser.parse_args()

    # 运行测试
    success = run_tests(args.type, args.verbose)

    # 设置退出码
    sys.exit(0 if success else 1)
