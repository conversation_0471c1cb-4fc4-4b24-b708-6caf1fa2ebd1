"""
主题测试脚本
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                               QWidget, QPushButton, QLabel, QLineEdit, QTableWidget, 
                               QTableWidgetItem, QComboBox, QMenuBar, QMenu, QStatusBar,
                               QHeaderView)
from PySide6.QtCore import Qt

class ThemeTestWindow(QMainWindow):
    """主题测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("SmartVault 主题测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建UI
        self.init_ui()
        
        # 创建状态栏
        self.create_status_bar()
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 主题菜单
        theme_menu = menubar.addMenu("主题")
        
        # 添加主题选项
        themes = [
            ("浅色主题", "light"),
            ("深色主题", "dark"),
            ("蓝色主题", "blue"),
            ("绿色主题", "green")
        ]
        
        for theme_name, theme_key in themes:
            action = theme_menu.addAction(theme_name)
            action.triggered.connect(lambda checked, key=theme_key: self.change_theme(key))
        
        # 测试菜单
        test_menu = menubar.addMenu("测试")
        test_menu.addAction("刷新界面").triggered.connect(self.refresh_ui)
        test_menu.addAction("重置主题").triggered.connect(lambda: self.change_theme("light"))
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("准备就绪 - 请测试不同主题")
    
    def init_ui(self):
        """初始化UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("SmartVault 主题测试")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)
        
        # 主题切换按钮区域
        theme_layout = QHBoxLayout()
        theme_buttons = [
            ("浅色主题", "light"),
            ("深色主题", "dark"),
            ("蓝色主题", "blue"),
            ("绿色主题", "green")
        ]
        
        for theme_name, theme_key in theme_buttons:
            button = QPushButton(theme_name)
            button.clicked.connect(lambda checked, key=theme_key: self.change_theme(key))
            theme_layout.addWidget(button)
        
        layout.addLayout(theme_layout)
        
        # 输入框测试区域
        input_layout = QHBoxLayout()
        input_layout.addWidget(QLabel("输入框测试:"))
        
        line_edit = QLineEdit()
        line_edit.setPlaceholderText("请输入测试文本...")
        input_layout.addWidget(line_edit)
        
        combo_box = QComboBox()
        combo_box.addItems(["选项1", "选项2", "选项3"])
        input_layout.addWidget(combo_box)
        
        layout.addLayout(input_layout)
        
        # 表格测试区域
        table_label = QLabel("表格测试:")
        layout.addWidget(table_label)
        
        table = QTableWidget(5, 4)
        table.setHorizontalHeaderLabels(["文件名", "大小", "日期", "标签"])
        
        # 填充测试数据
        test_data = [
            ["测试文件1.txt", "1.2 KB", "2025-01-25", "文档"],
            ["测试文件2.pdf", "2.5 MB", "2025-01-24", "重要"],
            ["测试文件3.jpg", "800 KB", "2025-01-23", "图片"],
            ["测试文件4.docx", "1.8 MB", "2025-01-22", "工作"],
            ["测试文件5.xlsx", "950 KB", "2025-01-21", "数据"]
        ]
        
        for row, row_data in enumerate(test_data):
            for col, cell_data in enumerate(row_data):
                item = QTableWidgetItem(cell_data)
                table.setItem(row, col, item)
        
        # 设置表格属性
        table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        layout.addWidget(table)
        
        # 按钮测试区域
        button_layout = QHBoxLayout()
        
        test_buttons = [
            ("普通按钮", lambda: self.show_message("点击了普通按钮")),
            ("悬停测试", lambda: self.show_message("点击了悬停测试按钮")),
            ("禁用按钮", None)
        ]
        
        for button_text, callback in test_buttons:
            button = QPushButton(button_text)
            if callback:
                button.clicked.connect(callback)
            else:
                button.setEnabled(False)
            button_layout.addWidget(button)
        
        layout.addLayout(button_layout)
        
        # 当前主题显示
        self.current_theme_label = QLabel()
        self.update_theme_label()
        layout.addWidget(self.current_theme_label)
    
    def change_theme(self, theme_key):
        """切换主题"""
        try:
            from smartvault.ui.themes import theme_manager
            theme_manager.set_theme(theme_key)
            self.update_theme_label()
            self.show_message(f"已切换到{theme_manager.THEMES[theme_key]}")
        except Exception as e:
            self.show_message(f"切换主题失败: {e}")
    
    def update_theme_label(self):
        """更新主题标签"""
        try:
            from smartvault.ui.themes import theme_manager
            current_theme = theme_manager.get_current_theme()
            theme_name = theme_manager.THEMES.get(current_theme, current_theme)
            self.current_theme_label.setText(f"当前主题: {theme_name}")
        except Exception as e:
            self.current_theme_label.setText(f"获取主题失败: {e}")
    
    def refresh_ui(self):
        """刷新界面"""
        self.update()
        self.show_message("界面已刷新")
    
    def show_message(self, message):
        """显示状态消息"""
        self.status_bar.showMessage(message, 3000)
        print(f"状态: {message}")


def main():
    """主函数"""
    print("启动SmartVault主题测试...")
    
    # 创建应用
    app = QApplication(sys.argv)
    
    # 应用中文化
    try:
        from smartvault.ui.localization import apply_chinese_style
        apply_chinese_style(app)
        print("已应用中文化样式")
    except Exception as e:
        print(f"中文化失败: {e}")
    
    # 应用当前主题
    try:
        from smartvault.ui.themes import theme_manager
        theme_manager.apply_theme(theme_manager.get_current_theme())
        print(f"已应用主题: {theme_manager.get_current_theme()}")
    except Exception as e:
        print(f"应用主题失败: {e}")
    
    # 创建并显示测试窗口
    window = ThemeTestWindow()
    window.show()
    
    print("主题测试窗口已打开")
    print("请测试以下功能:")
    print("1. 点击主题按钮切换不同主题")
    print("2. 使用菜单栏切换主题")
    print("3. 观察表格、按钮、输入框的主题效果")
    print("4. 测试悬停和选中效果")
    print("5. 关闭窗口退出测试")
    
    # 运行应用
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
