import sys
import os
sys.path.append('.')

try:
    from smartvault.services.clipboard_monitor_service import ClipboardMonitorService
    from smartvault.utils.config import load_config
    
    print("测试改进后的剪贴板监控服务...")
    
    # 创建剪贴板监控服务实例（不需要Qt应用）
    service = ClipboardMonitorService()
    
    # 测试用例
    test_cases = [
        {
            "name": "有扩展名的文件名",
            "text": "测试1文件_- Ab古아み.txt",
            "expected_strategy": "精确匹配（含扩展名）"
        },
        {
            "name": "没有扩展名的文件名",
            "text": "测试1文件_- Ab古아み",
            "expected_strategy": "匹配文件名主体（不含扩展名）"
        },
        {
            "name": "英文文件名有扩展名",
            "text": "aint a party.mp3",
            "expected_strategy": "精确匹配（含扩展名）"
        },
        {
            "name": "英文文件名无扩展名",
            "text": "aint a party",
            "expected_strategy": "匹配文件名主体（不含扩展名）"
        }
    ]
    
    print("\n=== 测试文件名清洗逻辑 ===")
    for test_case in test_cases:
        text = test_case["text"]
        cleaned = service._clean_filename(text)
        print(f"输入: '{text}'")
        print(f"清洗后: '{cleaned}'")
        print(f"结果: {'✅ 通过清洗' if cleaned else '❌ 被跳过'}")
        print("-" * 50)
    
    print("\n=== 测试数据库查询策略 ===")
    # 测试数据库查询逻辑（需要数据库连接）
    if service._ensure_database_connection():
        print("✅ 数据库连接成功，测试查询策略...")
        
        for test_case in test_cases:
            text = test_case["text"]
            cleaned = service._clean_filename(text)
            
            if cleaned:
                print(f"\n测试: '{text}' -> '{cleaned}'")
                print(f"预期策略: {test_case['expected_strategy']}")
                
                # 检查扩展名判断逻辑
                has_extension = '.' in cleaned and not cleaned.endswith('.')
                actual_strategy = "精确匹配（含扩展名）" if has_extension else "匹配文件名主体（不含扩展名）"
                print(f"实际策略: {actual_strategy}")
                
                # 执行查询
                results = service._find_files_by_name(cleaned)
                print(f"查询结果: 找到 {len(results)} 个文件")
                
                if results:
                    print("匹配的文件:")
                    for i, file_info in enumerate(results[:3]):  # 只显示前3个
                        print(f"  {i+1}. {file_info['name']}")
                    if len(results) > 3:
                        print(f"  ... 还有 {len(results) - 3} 个文件")
                
                print("-" * 50)
    else:
        print("❌ 数据库连接失败，跳过查询测试")
    
    print("\n=== 改进总结 ===")
    print("✅ 1. 简化了文件名判断逻辑")
    print("   - 移除了扩展名检查，只使用清洗环节")
    print("   - 支持无扩展名的文本进行查重")
    print()
    print("✅ 2. 实现了智能查询策略")
    print("   - 有扩展名：精确匹配完整文件名")
    print("   - 无扩展名：匹配文件名主体部分")
    print()
    print("✅ 3. 优化了显示逻辑")
    print("   - 浮动窗口只显示库中文件名，避免内容过长")
    
except Exception as e:
    print(f"测试失败: {e}")
    import traceback
    traceback.print_exc()
