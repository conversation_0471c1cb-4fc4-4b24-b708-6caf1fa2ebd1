@echo off
chcp 65001 >nul
title SmartVault 智能文件管理系统

echo.
echo 🎯 SmartVault 智能文件管理系统
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python
    echo 请先安装Python 3.8或更高版本
    echo 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 显示Python版本
echo 📋 检查Python环境...
python --version

REM 运行SmartVault启动脚本
echo.
echo 🚀 正在启动SmartVault...
python run_smartvault.py

REM 如果程序异常退出，暂停以便查看错误信息
if errorlevel 1 (
    echo.
    echo ❌ SmartVault启动失败
    pause
)

echo.
echo 👋 SmartVault已退出
pause
