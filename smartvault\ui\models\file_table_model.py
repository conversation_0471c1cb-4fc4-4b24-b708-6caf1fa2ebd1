"""
文件表格数据模型
"""

import os
from datetime import datetime
from PySide6.QtCore import Qt, QAbstractTableModel, QModelIndex
from PySide6.QtGui import QFont, QBrush, QColor
from smartvault.ui.resources import get_icon


class FileTableModel(QAbstractTableModel):
    """文件表格数据模型"""

    # 列定义
    COLUMNS = ["名称", "位置", "日期", "大小", "类型", "标签"]

    # 列索引
    NAME_COLUMN = 0
    LOCATION_COLUMN = 1
    DATE_COLUMN = 2
    SIZE_COLUMN = 3
    TYPE_COLUMN = 4
    TAGS_COLUMN = 5

    # 自定义角色
    FileIdRole = Qt.UserRole + 1
    FilePathRole = Qt.UserRole + 2
    FileSizeRole = Qt.UserRole + 3
    FileTypeRole = Qt.UserRole + 4
    EntryTypeRole = Qt.UserRole + 5
    CreatedAtRole = Qt.UserRole + 6
    ModifiedAtRole = Qt.UserRole + 7
    AddedAtRole = Qt.UserRole + 8
    IsDirectoryRole = Qt.UserRole + 9

    def __init__(self, parent=None):
        """初始化模型

        Args:
            parent: 父对象
        """
        super().__init__(parent)
        self.files = []
        self.filtered_files = []  # 过滤后的文件列表
        self.visible_files = []   # 当前可见的文件列表

        # 分页相关
        # 【页面显示限制】- 这个控制UI每页显示多少条，保证界面性能
        self.page_size = 100      # 每页显示的文件数量，默认100条保证性能
        self.current_page = 0     # 当前页码
        self.total_pages = 0      # 总页数
        self.total_files = 0      # 数据库中的总文件数

        # 过滤相关
        self.filter_text = ""     # 过滤文本
        self.filter_column = 0    # 过滤列

        # 缓存
        self.icon_cache = {}      # 图标缓存
        self.path_exists_cache = {}  # 路径存在缓存
        self.config_cache = None  # 配置缓存
        self.library_path_cache = None  # 库路径缓存

        # 性能优化
        self.use_virtual_mode = False  # 是否使用虚拟模式，设为False启用分页功能

        # 数据加载回调
        self.data_loader_callback = None  # 用于从数据库加载数据的回调函数
        self.details_view_update_callback = None  # 用于更新详情视图的回调函数

        # 标签相关
        self.file_tags_cache = {}  # 文件标签缓存 {file_id: [tag_dict, ...]}
        self.tag_service = None    # 标签服务实例
        self.tags_cache_size_limit = 1000  # 标签缓存大小限制

    def clear_all_state(self):
        """清除所有状态（用于文件库切换）"""
        print("清除FileTableModel所有状态")
        self.beginResetModel()

        # 清除数据
        self.files = []
        self.filtered_files = []
        self.visible_files = []

        # 重置分页状态
        self.current_page = 0
        self.total_files = 0
        self.total_pages = 1

        # 重置过滤状态
        self.filter_text = ""
        self.filter_column = 0

        # 清除缓存
        self.icon_cache = {}
        self.path_exists_cache = {}
        self.config_cache = None
        self.library_path_cache = None
        self.file_tags_cache = {}

        self.endResetModel()
        print("FileTableModel状态清除完成")

    def set_data_loader_callback(self, callback):
        """设置数据加载回调函数

        Args:
            callback: 回调函数，接受(limit, offset)参数，返回文件列表
        """
        self.data_loader_callback = callback

    def set_details_view_update_callback(self, callback):
        """设置详情视图更新回调函数

        Args:
            callback: 回调函数，接受(files)参数，用于更新详情视图
        """
        self.details_view_update_callback = callback

    def set_search_total_count_callback(self, callback):
        """设置搜索总数获取回调函数

        Args:
            callback: 回调函数，接受(search_keyword, search_column)参数，返回总数
        """
        self._get_search_total_count = callback

    def set_tag_service(self, tag_service):
        """设置标签服务

        Args:
            tag_service: TagService实例
        """
        self.tag_service = tag_service

    def rowCount(self, parent=QModelIndex()):
        """返回行数

        Args:
            parent: 父索引

        Returns:
            int: 行数
        """
        if parent.isValid():
            return 0

        if self.use_virtual_mode:
            # 虚拟模式下，返回过滤后的文件数量
            return len(self.filtered_files)
        else:
            # 分页模式下，返回当前页的文件数量
            return len(self.visible_files)

    def columnCount(self, parent=QModelIndex()):
        """返回列数

        Args:
            parent: 父索引

        Returns:
            int: 列数
        """
        return len(self.COLUMNS)

    def headerData(self, section, orientation, role=Qt.DisplayRole):
        """返回表头数据

        Args:
            section: 节索引
            orientation: 方向
            role: 角色

        Returns:
            object: 数据
        """
        if role == Qt.DisplayRole and orientation == Qt.Horizontal:
            return self.COLUMNS[section]
        return None

    def data(self, index, role=Qt.DisplayRole):
        """返回数据

        Args:
            index: 索引
            role: 角色

        Returns:
            object: 数据
        """
        if not index.isValid():
            return None

        # 获取文件信息
        if self.use_virtual_mode:
            if index.row() >= len(self.filtered_files):
                return None
            file = self.filtered_files[index.row()]
        else:
            if index.row() >= len(self.visible_files):
                return None
            file = self.visible_files[index.row()]
        column = index.column()

        # 显示角色
        if role == Qt.DisplayRole:
            if column == self.NAME_COLUMN:
                return file["name"]
            elif column == self.LOCATION_COLUMN:
                # 优先检查中转状态
                staging_status = file.get("staging_status", "normal")
                if staging_status == "staging":
                    return "中转"

                # 根据文件路径判断文件位置
                file_path = None
                if file["entry_type"] in ["copy", "move"]:
                    file_path = file["library_path"]
                else:
                    file_path = file["original_path"]

                # 检查文件是否存在（使用缓存，延迟检查）
                if file_path in self.path_exists_cache:
                    file_exists = self.path_exists_cache[file_path]
                else:
                    # 延迟检查：先假设文件存在，避免启动时大量IO
                    file_exists = True
                    self.path_exists_cache[file_path] = file_exists
                    # TODO: 可以在后台线程中异步检查文件存在性

                # 获取库路径（使用缓存）
                if self.library_path_cache is None:
                    from smartvault.utils.config import load_config
                    config = load_config()
                    self.library_path_cache = config.get("library_path", "")
                library_path = self.library_path_cache

                # 检查文件是否在库内（使用缓存）
                cache_key = f"{file_path}:{library_path}"
                if cache_key in self.path_exists_cache:
                    is_in_library = self.path_exists_cache[cache_key]
                else:
                    is_in_library = False
                    if file_path and library_path:
                        file_path = os.path.normpath(file_path)
                        library_path = os.path.normpath(library_path)
                        is_in_library = file_path.startswith(library_path)
                    self.path_exists_cache[cache_key] = is_in_library

                # 返回位置信息
                if is_in_library:
                    if file_exists:
                        return "库内"
                    else:
                        return "库内(文件已移除)"
                else:
                    if file_exists:
                        return "库外"
                    else:
                        return "库外(文件已移除)"
            elif column == self.DATE_COLUMN:
                try:
                    added_at = datetime.fromisoformat(file["added_at"])
                    return added_at.strftime("%Y-%m-%d %H:%M")
                except (ValueError, TypeError):
                    return str(file["added_at"])
            elif column == self.SIZE_COLUMN:
                return self._format_size(file["size"])
            elif column == self.TYPE_COLUMN:
                if os.path.isdir(file["original_path"]):
                    return "文件夹"
                else:
                    ext = os.path.splitext(file["name"])[1].lower()
                    return ext[1:] if ext else "文件"
            elif column == self.TAGS_COLUMN:
                return self._get_file_tags_and_note_display(file["id"])

        # 装饰角色（图标）- 延迟加载优化
        elif role == Qt.DecorationRole and column == self.NAME_COLUMN:
            if os.path.isdir(file["original_path"]):
                return get_icon("folder")
            else:
                # 使用缓存的图标，如果没有缓存则返回默认图标
                filename = file["name"]
                if filename in self.icon_cache:
                    return self.icon_cache[filename]
                else:
                    # 返回默认文件图标，避免启动时大量图标计算
                    default_icon = get_icon("file")
                    self.icon_cache[filename] = default_icon
                    return default_icon

        # 工具提示角色
        elif role == Qt.ToolTipRole:
            if column == self.TAGS_COLUMN:
                return self._get_file_tags_tooltip(file["id"])
            else:
                return self._format_tooltip(file)

        # 文本对齐角色
        elif role == Qt.TextAlignmentRole:
            if column in [self.SIZE_COLUMN, self.DATE_COLUMN]:
                return Qt.AlignRight | Qt.AlignVCenter
            return Qt.AlignLeft | Qt.AlignVCenter

        # 字体角色
        elif role == Qt.FontRole:
            font = QFont()
            if column == self.NAME_COLUMN:
                font.setBold(True)
            return font

        # 前景色角色
        elif role == Qt.ForegroundRole:
            if column == self.LOCATION_COLUMN:
                # 优先检查中转状态
                staging_status = file.get("staging_status", "normal")
                if staging_status == "staging":
                    return QBrush(QColor("#ff9800"))  # 橙色，表示中转状态

                # 获取文件路径
                file_path = None
                if file["entry_type"] in ["copy", "move"]:
                    file_path = file["library_path"]
                else:
                    file_path = file["original_path"]

                # 获取库路径（使用缓存）
                if self.library_path_cache is None:
                    from smartvault.utils.config import load_config
                    config = load_config()
                    self.library_path_cache = config.get("library_path", "")
                library_path = self.library_path_cache

                # 检查文件是否在库内
                is_in_library = False
                if file_path and library_path:
                    file_path = os.path.normpath(file_path)
                    library_path = os.path.normpath(library_path)
                    is_in_library = file_path.startswith(library_path)

                if is_in_library:
                    return None  # 使用默认黑色
                else:
                    return QBrush(QColor("#0277bd"))  # 蓝色
            elif column == self.TAGS_COLUMN:
                # 标签列使用基于标签颜色的显示 - 参照标签导航的方法
                return self._get_tags_foreground_color(file["id"])

        # 自定义角色
        elif role == self.FileIdRole:
            return file["id"]
        elif role == self.FilePathRole:
            return file["original_path"]
        elif role == self.FileSizeRole:
            return file["size"]
        elif role == self.FileTypeRole:
            return os.path.splitext(file["name"])[1].lower()
        elif role == self.EntryTypeRole:
            return file["entry_type"]
        elif role == self.CreatedAtRole:
            return file["created_at"]
        elif role == self.ModifiedAtRole:
            return file["modified_at"]
        elif role == self.AddedAtRole:
            return file["added_at"]
        elif role == self.IsDirectoryRole:
            return os.path.isdir(file["original_path"])

        return None

    def _format_tooltip(self, file):
        """格式化工具提示

        Args:
            file: 文件信息字典

        Returns:
            str: 工具提示
        """
        size_str = self._format_size(file["size"])

        # 解析日期时间字符串
        try:
            created_at = datetime.fromisoformat(file["created_at"])
            modified_at = datetime.fromisoformat(file["modified_at"])
            added_at = datetime.fromisoformat(file["added_at"])

            created_str = created_at.strftime("%Y-%m-%d %H:%M:%S")
            modified_str = modified_at.strftime("%Y-%m-%d %H:%M:%S")
            added_str = added_at.strftime("%Y-%m-%d %H:%M:%S")
        except (ValueError, TypeError):
            created_str = str(file["created_at"])
            modified_str = str(file["modified_at"])
            added_str = str(file["added_at"])

        # 入库方式
        entry_type_map = {
            "link": "链接",
            "copy": "复制",
            "move": "移动"
        }
        entry_type = entry_type_map.get(file["entry_type"], file["entry_type"])

        return (
            f"名称: {file['name']}\n"
            f"路径: {file['original_path']}\n"
            f"大小: {size_str}\n"
            f"创建时间: {created_str}\n"
            f"修改时间: {modified_str}\n"
            f"添加时间: {added_str}\n"
            f"入库方式: {entry_type}"
        )

    def _format_size(self, size):
        """格式化文件大小

        Args:
            size: 文件大小（字节）

        Returns:
            str: 格式化后的文件大小
        """
        if size < 1024:
            return f"{size} B"
        elif size < 1024 * 1024:
            return f"{size / 1024:.2f} KB"
        elif size < 1024 * 1024 * 1024:
            return f"{size / (1024 * 1024):.2f} MB"
        else:
            return f"{size / (1024 * 1024 * 1024):.2f} GB"

    def _get_file_icon(self, filename):
        """获取文件图标

        Args:
            filename: 文件名

        Returns:
            QIcon: 文件图标
        """
        # 检查缓存
        if filename in self.icon_cache:
            return self.icon_cache[filename]

        # 获取文件扩展名
        ext = os.path.splitext(filename)[1].lower()

        # 根据扩展名返回图标
        icon = None
        if ext in ['.txt', '.md', '.log']:
            icon = get_icon("text")
        elif ext in ['.pdf']:
            icon = get_icon("pdf")
        elif ext in ['.doc', '.docx', '.odt']:
            icon = get_icon("word")
        elif ext in ['.xls', '.xlsx', '.ods']:
            icon = get_icon("excel")
        elif ext in ['.ppt', '.pptx', '.odp']:
            icon = get_icon("powerpoint")
        elif ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
            icon = get_icon("image")
        elif ext in ['.mp3', '.wav', '.ogg', '.flac']:
            icon = get_icon("audio")
        elif ext in ['.mp4', '.avi', '.mkv', '.mov']:
            icon = get_icon("video")
        elif ext in ['.zip', '.rar', '.7z', '.tar', '.gz']:
            icon = get_icon("archive")
        else:
            icon = get_icon("file")

        # 添加到缓存
        self.icon_cache[filename] = icon
        return icon

    def _get_file_tags_display(self, file_id):
        """获取文件标签的显示文本

        Args:
            file_id: 文件ID

        Returns:
            str: 标签显示文本
        """
        if not self.tag_service:
            return ""

        # 检查缓存
        if file_id in self.file_tags_cache:
            tags = self.file_tags_cache[file_id]
        else:
            # 从数据库获取标签
            try:
                tags = self.tag_service.get_file_tags(file_id)
                self.file_tags_cache[file_id] = tags

                # 检查缓存大小，如果超过限制则清理
                self._cleanup_tags_cache_if_needed()

            except Exception as e:
                print(f"获取文件标签失败: {e}")
                return ""

        if not tags:
            return ""

        # 格式化标签显示（最多显示3个标签，超过则显示省略号）
        tag_names = [tag['name'] for tag in tags]
        if len(tag_names) <= 3:
            return " • ".join(tag_names)  # 使用圆点分隔符，更美观
        else:
            return " • ".join(tag_names[:3]) + " • ..."

    def _get_file_tags_and_note_display(self, file_id):
        """获取文件标签和备注的显示文本

        Args:
            file_id: 文件ID

        Returns:
            str: 标签和备注的显示文本
        """
        # 获取标签显示
        tags_display = self._get_file_tags_display(file_id)

        # 获取文件备注
        try:
            # 从可见文件列表中查找文件信息
            file_note = ""
            for file in self.visible_files:
                if file["id"] == file_id:
                    file_note = file.get("note", "") or ""
                    break

            # 如果没找到，从数据库查询
            if not file_note:
                from smartvault.services.file import FileService
                file_service = FileService()
                file_note = file_service.get_file_note(file_id)

            # 组合显示
            display_parts = []
            if tags_display:
                display_parts.append(tags_display)
            if file_note:
                display_parts.append(f"📝{file_note}")

            return " • ".join(display_parts) if display_parts else ""

        except Exception as e:
            print(f"获取文件备注显示失败: {e}")
            return tags_display  # 出错时至少显示标签

    def _get_file_tags_tooltip(self, file_id):
        """获取文件标签的工具提示

        Args:
            file_id: 文件ID

        Returns:
            str: 标签工具提示文本
        """
        if not self.tag_service:
            return "无标签"

        # 检查缓存
        if file_id in self.file_tags_cache:
            tags = self.file_tags_cache[file_id]
        else:
            # 从数据库获取标签
            try:
                tags = self.tag_service.get_file_tags(file_id)
                self.file_tags_cache[file_id] = tags

                # 检查缓存大小，如果超过限制则清理
                self._cleanup_tags_cache_if_needed()

            except Exception as e:
                return "获取标签失败"

        if not tags:
            return "无标签"

        # 构建详细的标签和备注信息
        tooltip_lines = []

        # 添加文件备注（如果有）
        try:
            file_note = ""
            for file in self.visible_files:
                if file["id"] == file_id:
                    file_note = file.get("note", "") or ""
                    break

            # 如果没找到，从数据库查询
            if not file_note:
                from smartvault.services.file import FileService
                file_service = FileService()
                file_note = file_service.get_file_note(file_id)

            if file_note:
                tooltip_lines.append("文件备注:")
                tooltip_lines.append(f"📝 {file_note}")
                tooltip_lines.append("")  # 空行分隔
        except:
            pass

        # 添加标签信息
        if tags:
            tooltip_lines.append("文件标签:")
            for tag in tags:
                tag_line = f"• {tag['name']}"
                if tag.get('color'):
                    tag_line += f" (颜色: {tag['color']})"
                tooltip_lines.append(tag_line)

            if len(tags) > 3:
                tooltip_lines.append(f"共 {len(tags)} 个标签")
        else:
            if not any("文件备注:" in line for line in tooltip_lines):
                tooltip_lines.append("无标签和备注")

        return "\n".join(tooltip_lines)

    def _get_tags_foreground_color(self, file_id):
        """获取文件标签的前景色（参照标签导航的方法）

        Args:
            file_id: 文件ID

        Returns:
            QBrush: 前景色画刷，如果没有标签则返回None
        """
        if not self.tag_service:
            return None

        # 检查缓存
        if file_id in self.file_tags_cache:
            tags = self.file_tags_cache[file_id]
        else:
            # 从数据库获取标签
            try:
                tags = self.tag_service.get_file_tags(file_id)
                self.file_tags_cache[file_id] = tags
                self._cleanup_tags_cache_if_needed()
            except Exception as e:
                print(f"获取文件标签失败: {e}")
                return None

        if not tags:
            return None

        # 获取第一个有颜色的标签作为显示颜色
        for tag in tags:
            if tag.get('color'):
                from smartvault.ui.utils.color_utils import get_inherited_color
                # 使用标签的原始颜色（深度为0）
                display_color = get_inherited_color(tag['color'], 0)
                return QBrush(display_color)

        # 如果没有标签有颜色，尝试获取继承颜色
        for tag in tags:
            try:
                inherited_attrs = self.tag_service.get_inherited_attributes(tag['id'])
                if inherited_attrs.get('color'):
                    from smartvault.ui.utils.color_utils import get_inherited_color
                    display_color = get_inherited_color(inherited_attrs['color'], 0)
                    return QBrush(display_color)
            except:
                continue

        # 如果都没有颜色，返回默认的深灰色
        return QBrush(QColor("#666666"))

    def _cleanup_tags_cache_if_needed(self):
        """清理标签缓存（如果超过大小限制）"""
        if len(self.file_tags_cache) > self.tags_cache_size_limit:
            # 保留最近使用的一半缓存
            keep_size = self.tags_cache_size_limit // 2

            # 获取当前可见文件的ID，优先保留这些（但限制数量）
            visible_file_ids = set()
            if self.visible_files:
                visible_file_ids = {file['id'] for file in self.visible_files[:keep_size]}

            # 构建新的缓存
            new_cache = {}

            # 首先保留可见文件的标签缓存（限制数量）
            for file_id in list(visible_file_ids)[:keep_size]:
                if file_id in self.file_tags_cache:
                    new_cache[file_id] = self.file_tags_cache[file_id]

            # 如果还有空间，保留其他缓存项
            remaining_space = keep_size - len(new_cache)
            if remaining_space > 0:
                other_items = [(k, v) for k, v in self.file_tags_cache.items()
                              if k not in visible_file_ids]
                for k, v in other_items[:remaining_space]:
                    new_cache[k] = v

            self.file_tags_cache = new_cache
            print(f"标签缓存已清理，保留 {len(new_cache)} 项")

    def _load_file_tags_batch(self, file_ids):
        """批量加载文件标签（性能优化）

        Args:
            file_ids: 文件ID列表
        """
        if not self.tag_service or not file_ids:
            return

        # 过滤出未缓存的文件ID
        uncached_file_ids = [fid for fid in file_ids if fid not in self.file_tags_cache]

        if uncached_file_ids:
            try:
                # 批量获取标签
                batch_tags = self.tag_service.get_files_tags_batch(uncached_file_ids)

                # 更新缓存
                for file_id, tags in batch_tags.items():
                    self.file_tags_cache[file_id] = tags

                # 检查缓存大小，如果超过限制则清理
                self._cleanup_tags_cache_if_needed()

            except Exception as e:
                print(f"批量加载文件标签失败: {e}")

    def refresh_file_tags(self, file_ids=None):
        """刷新文件标签缓存

        Args:
            file_ids: 要刷新的文件ID列表，如果为None则刷新所有
        """
        if file_ids is None:
            # 清空所有缓存
            self.file_tags_cache = {}
        else:
            # 清空指定文件的缓存
            for file_id in file_ids:
                self.file_tags_cache.pop(file_id, None)

        # 通知视图刷新标签列
        if self.TAGS_COLUMN < len(self.COLUMNS):
            top_left = self.index(0, self.TAGS_COLUMN)
            bottom_right = self.index(self.rowCount() - 1, self.TAGS_COLUMN)
            self.dataChanged.emit(top_left, bottom_right, [Qt.DisplayRole])

    def refresh_file_notes(self, file_ids=None):
        """刷新文件备注信息

        Args:
            file_ids: 要刷新的文件ID列表，如果为None则刷新所有
        """
        try:
            from smartvault.services.file import FileService
            file_service = FileService()

            if file_ids is None:
                # 刷新所有文件的备注
                for file in self.visible_files:
                    file_id = file.get('id')
                    if file_id:
                        updated_note = file_service.get_file_note(file_id)
                        file['note'] = updated_note
            else:
                # 刷新指定文件的备注
                for file_id in file_ids:
                    # 在visible_files中查找并更新
                    for file in self.visible_files:
                        if file.get('id') == file_id:
                            updated_note = file_service.get_file_note(file_id)
                            file['note'] = updated_note
                            break

                    # 在filtered_files中查找并更新
                    for file in self.filtered_files:
                        if file.get('id') == file_id:
                            updated_note = file_service.get_file_note(file_id)
                            file['note'] = updated_note
                            break

                    # 在files中查找并更新
                    for file in self.files:
                        if file.get('id') == file_id:
                            updated_note = file_service.get_file_note(file_id)
                            file['note'] = updated_note
                            break

            # 通知视图刷新标签列（包含备注）
            if self.TAGS_COLUMN < len(self.COLUMNS):
                top_left = self.index(0, self.TAGS_COLUMN)
                bottom_right = self.index(self.rowCount() - 1, self.TAGS_COLUMN)
                self.dataChanged.emit(top_left, bottom_right, [Qt.DisplayRole, Qt.ToolTipRole])

        except Exception as e:
            print(f"刷新文件备注失败: {e}")

    def refresh_file_data(self, file_ids=None):
        """刷新文件数据（包括标签和备注）

        Args:
            file_ids: 要刷新的文件ID列表，如果为None则刷新所有
        """
        # 刷新标签缓存
        self.refresh_file_tags(file_ids)

        # 刷新备注信息
        self.refresh_file_notes(file_ids)

    def setFiles(self, files):
        """设置文件列表

        Args:
            files: 文件信息字典列表
        """
        # 清除缓存
        self.icon_cache = {}
        self.path_exists_cache = {}
        self.config_cache = None
        self.library_path_cache = None
        self.file_tags_cache = {}

        # 保存文件列表
        self.beginResetModel()
        self.files = files

        # 应用过滤器
        self.applyFilter(self.filter_text, self.filter_column)

        # 计算总页数（优先使用数据库中的总文件数）
        if hasattr(self, 'total_files') and self.total_files > 0:
            if self.page_size >= 999999:  # "全部"选项
                self.total_pages = 1
            else:
                self.total_pages = (self.total_files + self.page_size - 1) // self.page_size
        else:
            self.total_pages = (len(self.filtered_files) + self.page_size - 1) // self.page_size if self.page_size > 0 else 1

        # 确保当前页码有效
        if self.current_page >= self.total_pages:
            self.current_page = max(0, self.total_pages - 1)

        # 更新可见文件列表
        self.updateVisibleFiles()

        # 批量加载文件标签（性能优化）
        if files:
            file_ids = [file["id"] for file in files]
            self._load_file_tags_batch(file_ids)

        self.endResetModel()

    def setFilesForTagFilter(self, files):
        """为标签筛选设置文件列表（不触发数据库重新加载）

        Args:
            files: 文件信息字典列表
        """
        # 清除缓存
        self.icon_cache = {}
        self.path_exists_cache = {}
        self.config_cache = None
        self.library_path_cache = None
        self.file_tags_cache = {}

        # 保存文件列表
        self.beginResetModel()
        self.files = files
        self.filtered_files = files.copy()
        self.visible_files = files.copy()

        # 清除搜索过滤器，因为这是标签筛选的结果
        self.filter_text = ""
        self.filter_column = 0

        # 🔧 修复：不要重置total_files，保持主窗口设置的值
        # 这样分页信息就能正确显示标签筛选的总文件数
        # self.total_files = len(files)  # 注释掉这行

        # 重新计算总页数（基于已设置的total_files）
        if hasattr(self, 'total_files') and self.total_files > 0:
            if self.page_size >= 999999:  # "全部"选项
                self.total_pages = 1
            else:
                self.total_pages = (self.total_files + self.page_size - 1) // self.page_size
        else:
            self.total_pages = 1

        self.current_page = 0

        # 批量加载文件标签（性能优化）
        if files:
            file_ids = [file["id"] for file in files]
            self._load_file_tags_batch(file_ids)

        self.endResetModel()

    def applyFilter(self, filter_text, filter_column):
        """应用过滤器 - 使用数据库搜索

        Args:
            filter_text: 过滤文本
            filter_column: 过滤列
        """
        print(f"应用过滤器: '{filter_text}', 列={filter_column}")

        self.filter_text = filter_text
        self.filter_column = filter_column

        # 重置到第一页
        self.current_page = 0

        # 如果有数据加载回调，使用数据库搜索
        if self.data_loader_callback:
            try:
                # 首先获取搜索结果的总数
                if hasattr(self, '_get_search_total_count'):
                    search_total = self._get_search_total_count(filter_text, filter_column)
                    self.set_total_files(search_total)

                # 重置到第一页并重新加载数据
                self.current_page = 0
                self._reload_current_page_data()

                print(f"搜索完成: 当前页有 {len(self.visible_files)} 个结果")
                return

            except Exception as e:
                print(f"数据库搜索失败: {e}")
                # 回退到内存过滤

        # 使用内存过滤（备用方案）
        self._apply_memory_filter(filter_text, filter_column)

    def _apply_memory_filter(self, filter_text, filter_column):
        """内存过滤（备用方案）"""
        print("使用内存过滤")

        # 开始重置模型
        self.beginResetModel()

        # 如果没有过滤文本，则显示所有文件
        if not filter_text:
            self.filtered_files = self.files.copy()
        else:
            # 根据过滤文本过滤文件
            self.filtered_files = []
            for file in self.files:
                # 根据不同列进行过滤
                if filter_column == self.NAME_COLUMN:
                    if filter_text.lower() in file["name"].lower():
                        self.filtered_files.append(file)
                elif filter_column == self.TYPE_COLUMN:
                    ext = os.path.splitext(file["name"])[1].lower()
                    if filter_text.lower() in ext.lower():
                        self.filtered_files.append(file)
                elif filter_column == self.LOCATION_COLUMN:
                    location = "库内" if file["entry_type"] in ["copy", "move"] else "库外"
                    if filter_text.lower() in location.lower():
                        self.filtered_files.append(file)
                else:
                    # 默认在所有列中搜索
                    if (filter_text.lower() in file["name"].lower() or
                        filter_text.lower() in os.path.splitext(file["name"])[1].lower()):
                        self.filtered_files.append(file)

        # 在内存过滤模式下，直接显示过滤结果
        self.visible_files = self.filtered_files.copy()

        # 结束重置模型
        self.endResetModel()

    def updateVisibleFiles(self):
        """更新可见文件列表 - 简化版本，仅用于兼容性"""
        # 在数据库分页模式下，visible_files就是当前加载的文件
        self.visible_files = self.files.copy()

        # 通知视图数据已更改
        self.beginResetModel()
        self.endResetModel()

    def load_more_files(self):
        """加载更多文件"""
        if not self.is_loading or self.loaded_count >= len(self.files):
            self.is_loading = False
            return

        # 计算本次加载的范围
        start = self.loaded_count
        end = min(start + self.batch_size, len(self.files))

        if start < end:
            # 通知视图开始插入行
            self.beginInsertRows(QModelIndex(), start, end - 1)
            self.loaded_count = end
            self.endInsertRows()

            # 如果还有更多文件需要加载，继续启动定时器
            if self.loaded_count < len(self.files):
                self.load_timer.start(100)  # 100毫秒后加载下一批
            else:
                self.is_loading = False

    def addFile(self, file):
        """添加文件

        Args:
            file: 文件信息字典
        """
        self.beginInsertRows(QModelIndex(), len(self.files), len(self.files))
        self.files.append(file)
        self.endInsertRows()

    def appendFiles(self, files):
        """批量添加文件

        Args:
            files: 文件信息字典列表
        """
        if not files:
            return

        # 添加到原始文件列表
        start_row = len(self.files)
        end_row = start_row + len(files) - 1

        self.beginInsertRows(QModelIndex(), start_row, end_row)
        self.files.extend(files)
        self.endInsertRows()

        # 重新应用过滤器和分页
        self.applyFilter(self.filter_text, self.filter_column)

    def removeFile(self, file_id):
        """移除文件

        Args:
            file_id: 文件ID

        Returns:
            bool: 是否成功移除
        """
        # 从所有文件列表中移除
        removed = False

        # 从原始文件列表中移除
        for i, file in enumerate(self.files):
            if file["id"] == file_id:
                self.files.pop(i)
                removed = True
                break

        # 从过滤后的文件列表中移除
        for i, file in enumerate(self.filtered_files):
            if file["id"] == file_id:
                self.filtered_files.pop(i)
                break

        # 从可见文件列表中移除并通知视图
        for i, file in enumerate(self.visible_files):
            if file["id"] == file_id:
                self.beginRemoveRows(QModelIndex(), i, i)
                self.visible_files.pop(i)
                self.endRemoveRows()
                break

        # 清除相关缓存
        if file_id in self.file_tags_cache:
            del self.file_tags_cache[file_id]

        return removed

    def getFile(self, index):
        """获取文件信息

        Args:
            index: 索引

        Returns:
            dict: 文件信息字典，如果索引无效则返回None
        """
        if not index.isValid():
            return None

        # 获取文件信息
        if self.use_virtual_mode:
            if index.row() >= len(self.filtered_files):
                return None
            return self.filtered_files[index.row()]
        else:
            if index.row() >= len(self.visible_files):
                return None
            return self.visible_files[index.row()]

    def goToPage(self, page):
        """转到指定页

        Args:
            page: 页码（从0开始）

        Returns:
            bool: 是否成功
        """
        if 0 <= page < self.total_pages and page != self.current_page:
            self.current_page = page
            # 使用数据库分页，重新加载数据
            if self.data_loader_callback:
                self._reload_current_page_data()
            return True
        return False

    def nextPage(self):
        """转到下一页"""
        if self.current_page < self.total_pages - 1:
            self.current_page += 1
            # 使用数据库分页，重新加载数据
            if self.data_loader_callback:
                self._reload_current_page_data()
            return True
        return False

    def previousPage(self):
        """转到上一页"""
        if self.current_page > 0:
            self.current_page -= 1
            # 使用数据库分页，重新加载数据
            if self.data_loader_callback:
                self._reload_current_page_data()
            return True
        return False

    def _reload_current_page_data(self):
        """重新加载当前页的数据"""
        if self.data_loader_callback:
            try:
                offset = self.current_page * self.page_size
                print(f"重新加载第 {self.current_page + 1} 页数据，offset={offset}, limit={self.page_size}")

                # 获取搜索条件
                search_keyword = self.filter_text if self.filter_text else None
                search_column = self.filter_column if search_keyword else None

                # 从数据库加载当前页的数据（支持搜索条件）
                new_files = self.data_loader_callback(
                    self.page_size,
                    offset,
                    search_keyword=search_keyword,
                    search_column=search_column
                )

                # 直接设置为当前页数据，不使用内存分页逻辑
                self.beginResetModel()
                self.files = new_files
                self.filtered_files = new_files.copy()
                self.visible_files = new_files.copy()  # 直接设置可见文件
                self.endResetModel()

                print(f"成功加载 {len(new_files)} 个文件")

                # 通知详情视图更新文件列表
                if hasattr(self, 'details_view_update_callback') and self.details_view_update_callback:
                    self.details_view_update_callback(new_files)

            except Exception as e:
                print(f"重新加载数据失败: {e}")

    def getCurrentPage(self):
        """获取当前页码

        Returns:
            int: 当前页码
        """
        return self.current_page

    def getTotalPages(self):
        """获取总页数

        Returns:
            int: 总页数
        """
        return self.total_pages

    def getPageSize(self):
        """获取每页大小

        Returns:
            int: 每页大小
        """
        return self.page_size

    def setPageSize(self, size):
        """设置每页大小

        Args:
            size: 每页大小

        Returns:
            bool: 是否成功
        """
        if size > 0:
            old_page_size = self.page_size
            self.page_size = size

            # 重新计算总页数（基于数据库中的总文件数）
            if self.total_files > 0:
                if size >= 999999:  # "全部"选项
                    self.total_pages = 1
                else:
                    self.total_pages = (self.total_files + size - 1) // size
            else:
                self.total_pages = 1

            # 确保当前页码有效
            if self.current_page >= self.total_pages:
                self.current_page = max(0, self.total_pages - 1)

            # 如果页面大小改变了，需要重新加载数据
            if old_page_size != size and self.data_loader_callback:
                self._reload_current_page_data()

            return True
        return False

    def set_total_files(self, total_files):
        """设置数据库中的总文件数

        Args:
            total_files: 总文件数
        """
        self.total_files = total_files
        # 重新计算总页数
        if self.page_size >= 999999:  # "全部"选项
            self.total_pages = 1
        else:
            self.total_pages = (total_files + self.page_size - 1) // self.page_size if total_files > 0 else 1

    def sort(self, column, order):
        """排序数据

        Args:
            column: 排序列
            order: 排序顺序
        """
        self.layoutAboutToBeChanged.emit()

        # 根据不同列进行排序
        if column == self.NAME_COLUMN:
            # 按名称排序
            self.files.sort(key=lambda x: x["name"].lower(), reverse=(order == Qt.DescendingOrder))
        elif column == self.LOCATION_COLUMN:
            # 按位置排序（中转 > 库内 > 库外）
            def get_location_key(file):
                staging_status = file.get("staging_status", "normal")
                if staging_status == "staging":
                    return "0"  # 中转状态排在最前
                elif file["entry_type"] in ["copy", "move"]:
                    return "1"  # 库内文件
                else:
                    return "2"  # 库外文件

            self.files.sort(key=get_location_key, reverse=(order == Qt.DescendingOrder))
        elif column == self.DATE_COLUMN:
            # 按添加日期排序
            self.files.sort(key=lambda x: x["added_at"], reverse=(order == Qt.DescendingOrder))
        elif column == self.SIZE_COLUMN:
            # 按大小排序
            self.files.sort(key=lambda x: x["size"], reverse=(order == Qt.DescendingOrder))
        elif column == self.TYPE_COLUMN:
            # 按类型排序（先文件夹，再按扩展名）
            def get_type_key(file):
                if os.path.isdir(file["original_path"]):
                    return "0"  # 文件夹排在前面
                else:
                    ext = os.path.splitext(file["name"])[1].lower()
                    return ext if ext else "1"

            self.files.sort(key=get_type_key, reverse=(order == Qt.DescendingOrder))

        # 重新应用过滤器和分页
        self.applyFilter(self.filter_text, self.filter_column)
        self.updateVisibleFiles()

        self.layoutChanged.emit()
