"""
文件库设置页面
从原 settings_dialog.py 中的 create_library_tab 方法迁移而来
"""

from PySide6.QtWidgets import (
    QVBoxLayout, QHBoxLayout, QGroupBox, QLabel, QPushButton, 
    QFrame, QProgressBar
)
from PySide6.QtCore import Qt, Signal
from typing import Tuple
from ..base.base_page import BaseSettingsPage


class LibrarySettingsPage(BaseSettingsPage):
    """文件库设置页面"""

    # 自定义信号
    library_changed = Signal(str)  # 文件库路径变更信号

    def __init__(self, parent=None):
        """初始化页面"""
        super().__init__(parent)
        self.file_system = None
        self.move_thread = None

    def setup_ui(self):
        """设置UI界面"""
        layout = QVBoxLayout(self)

        # 当前文件库信息
        info_group = QGroupBox("当前智能文件库信息")
        info_layout = QVBoxLayout(info_group)

        # 文件库路径
        path_layout = QHBoxLayout()
        path_label = QLabel("路径:")
        path_layout.addWidget(path_label)

        self.path_label = QLabel("未设置")
        self.path_label.setStyleSheet("font-weight: bold;")
        path_layout.addWidget(self.path_label)
        path_layout.addStretch()

        info_layout.addLayout(path_layout)

        # 文件库统计信息容器
        self.stats_layout = QVBoxLayout()
        info_layout.addLayout(self.stats_layout)

        layout.addWidget(info_group)

        # 文件库管理按钮
        buttons_group = QGroupBox("智能文件库管理")
        buttons_layout = QVBoxLayout(buttons_group)

        # 创建文件库按钮
        create_button = QPushButton("创建智能文件库")
        create_button.setToolTip("创建新的智能文件库，并将其设置为当前文件库")
        create_button.clicked.connect(self.on_create_library)
        buttons_layout.addWidget(create_button)

        # 打开旧的智能文件库按钮
        open_button = QPushButton("打开旧的智能文件库")
        open_button.setToolTip("选择并加载已存在的智能文件库")
        open_button.clicked.connect(self.on_open_existing_library)
        buttons_layout.addWidget(open_button)

        # 移动文件库按钮
        move_button = QPushButton("移动智能文件库")
        move_button.setToolTip("将当前智能文件库移动到新位置")
        move_button.clicked.connect(self.on_move_library)
        buttons_layout.addWidget(move_button)

        # 删除文件库按钮
        delete_button = QPushButton("删除智能文件库")
        delete_button.setToolTip("删除当前智能文件库（此操作不可撤销）")
        delete_button.setStyleSheet("color: red;")
        delete_button.clicked.connect(self.on_delete_library)
        buttons_layout.addWidget(delete_button)

        layout.addWidget(buttons_group)

        # 添加说明
        info_frame = QFrame()
        info_frame.setFrameShape(QFrame.StyledPanel)
        info_frame.setFrameShadow(QFrame.Sunken)
        info_layout = QVBoxLayout(info_frame)

        info_title = QLabel("重要提示")
        info_title.setStyleSheet("font-weight: bold;")
        info_layout.addWidget(info_title)

        info_text = QLabel(
            "• 智能文件库存储了所有文件索引和元数据信息\n"
            "• 请使用上方按钮安全地管理文件库\n"
            "• 不要直接删除文件库文件夹，这可能导致数据丢失\n"
            "• 建议定期备份文件库"
        )
        info_layout.addWidget(info_text)

        layout.addWidget(info_frame)
        layout.addStretch()

    def set_file_system(self, file_system):
        """设置文件系统实例

        Args:
            file_system: FileSystem实例
        """
        self.file_system = file_system

    def load_settings(self, config: dict):
        """从配置加载设置到UI控件

        Args:
            config: 配置字典
        """
        self.config = config

        # 更新文件库路径显示
        library_path = config.get("library_path", "")
        if library_path:
            from smartvault.utils.config import normalize_path
            normalized_path = normalize_path(library_path)
            self.path_label.setText(normalized_path)
        else:
            self.path_label.setText("未设置")

        # 更新统计信息
        self.update_library_stats()

    def save_settings(self) -> dict:
        """从UI控件保存设置到配置

        Returns:
            dict: 文件库设置字典（通常为空，因为文件库设置通过操作按钮直接保存）
        """
        return {}

    def validate_settings(self) -> Tuple[bool, str]:
        """验证设置有效性

        Returns:
            tuple: (是否有效, 错误信息)
        """
        return True, ""

    def reset_to_defaults(self):
        """重置为默认设置"""
        # 文件库设置不支持重置，因为这会影响数据安全
        pass

    def get_page_title(self) -> str:
        """获取页面标题

        Returns:
            str: 页面标题
        """
        return "智能文件库设置"

    def update_library_stats(self):
        """更新文件库统计信息"""
        # 清除现有统计信息
        for i in reversed(range(self.stats_layout.count())):
            child = self.stats_layout.takeAt(i)
            if child.widget():
                child.widget().deleteLater()

        if not self.file_system:
            return

        try:
            stats = self.file_system.get_library_stats()
            stats_layout = QHBoxLayout()

            # 文件总数
            total_files_label = QLabel(f"文件总数: {stats['total_files']}")
            stats_layout.addWidget(total_files_label)

            # 总大小
            total_size = self._format_size(stats['total_size'])
            total_size_label = QLabel(f"总大小: {total_size}")
            stats_layout.addWidget(total_size_label)

            stats_layout.addStretch()
            self.stats_layout.addLayout(stats_layout)
        except Exception as e:
            error_label = QLabel(f"无法获取统计信息: {e}")
            error_label.setStyleSheet("color: red;")
            self.stats_layout.addWidget(error_label)

    def _format_size(self, size_bytes):
        """格式化文件大小

        Args:
            size_bytes: 字节数

        Returns:
            str: 格式化后的大小字符串
        """
        if size_bytes == 0:
            return "0 B"
        
        units = ['B', 'KB', 'MB', 'GB', 'TB']
        unit_index = 0
        size = float(size_bytes)
        
        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1
        
        return f"{size:.1f} {units[unit_index]}"

    def on_create_library(self):
        """创建智能文件库"""
        from PySide6.QtWidgets import QFileDialog, QMessageBox
        from smartvault.utils.config import save_config, normalize_path
        
        parent_folder = QFileDialog.getExistingDirectory(
            self, "选择智能文件库的父文件夹"
        )
        
        if not parent_folder:
            return

        try:
            if not self.file_system:
                QMessageBox.warning(self, "错误", "文件系统未初始化")
                return

            # 创建新的文件库
            success, library_path, details = self.file_system.create_library(parent_folder)

            if success:
                # 更新配置
                self.config["library_path"] = library_path
                save_config(self.config)

                # 更新UI
                normalized_path = normalize_path(library_path)
                self.path_label.setText(normalized_path)

                # 显示成功信息
                QMessageBox.information(self, "成功", details)

                # 发送文件库变更信号
                self.library_changed.emit(library_path)

                # 更新统计信息
                self.update_library_stats()
            else:
                QMessageBox.warning(self, "警告", f"无法创建智能文件库：\n{details}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"创建智能文件库失败: {e}")

    def on_open_existing_library(self):
        """打开已存在的智能文件库"""
        from PySide6.QtWidgets import QFileDialog, QMessageBox
        from smartvault.utils.config import save_config, normalize_path
        
        library_folder = QFileDialog.getExistingDirectory(
            self, "选择智能文件库文件夹"
        )
        
        if not library_folder:
            return

        try:
            if not self.file_system:
                QMessageBox.warning(self, "错误", "文件系统未初始化")
                return

            # 验证文件库
            if self.file_system.validate_library(library_folder):
                # 更新配置
                self.config["library_path"] = library_folder
                save_config(self.config)

                # 更新UI
                normalized_path = normalize_path(library_folder)
                self.path_label.setText(normalized_path)

                # 显示成功信息
                QMessageBox.information(self, "成功", f"已切换到智能文件库：\n{library_folder}")

                # 发送文件库变更信号
                self.library_changed.emit(library_folder)

                # 更新统计信息
                self.update_library_stats()
            else:
                QMessageBox.warning(self, "警告", "选择的文件夹不是有效的智能文件库")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开智能文件库失败: {e}")

    def on_move_library(self):
        """移动智能文件库"""
        from PySide6.QtWidgets import QMessageBox
        
        # TODO: 实现移动文件库的逻辑
        QMessageBox.information(self, "提示", "移动文件库功能正在开发中")

    def on_delete_library(self):
        """删除智能文件库"""
        from PySide6.QtWidgets import QMessageBox
        
        reply = QMessageBox.warning(
            self, "危险操作", 
            "确定要删除当前智能文件库吗？\n\n"
            "⚠️ 此操作将永久删除所有文件索引和元数据！\n"
            "⚠️ 此操作不可撤销！\n\n"
            "请确保您已经备份了重要数据。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # TODO: 实现删除文件库的逻辑
            QMessageBox.information(self, "提示", "删除文件库功能正在开发中")
