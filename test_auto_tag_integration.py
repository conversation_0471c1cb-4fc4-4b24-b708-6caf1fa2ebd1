#!/usr/bin/env python3
"""
自动标签功能集成测试
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import unittest
from smartvault.services.auto_tag_service import AutoTagService, AutoTagRule, ConditionType


class TestAutoTagIntegration(unittest.TestCase):
    """自动标签功能集成测试"""
    
    def setUp(self):
        """设置测试"""
        self.service = AutoTagService()
        
        # 创建测试规则
        self.rules = [
            AutoTagRule(
                id="rule1",
                name="PDF文档规则",
                condition_type=ConditionType.FILE_EXTENSION,
                condition_value="pdf",
                tag_names=["文档", "PDF"]
            ),
            AutoTagRule(
                id="rule2", 
                name="报告文件规则",
                condition_type=ConditionType.FILE_NAME_PATTERN,
                condition_value="report",
                tag_names=["报告", "重要"]
            ),
            AutoTagRule(
                id="rule3",
                name="大文件规则",
                condition_type=ConditionType.FILE_SIZE_RANGE,
                condition_value="10MB-100MB",
                tag_names=["大文件"]
            ),
            AutoTagRule(
                id="rule4",
                name="图片文件规则",
                condition_type=ConditionType.FILE_TYPE,
                condition_value="图片",
                tag_names=["图片", "媒体"]
            )
        ]
        
        # 添加规则到服务
        for rule in self.rules:
            self.service.add_rule(rule)
    
    def test_pdf_file_auto_tagging(self):
        """测试PDF文件自动标签"""
        file_info = {
            "name": "document.pdf",
            "size": 1024 * 1024,  # 1MB
            "original_path": "/path/to/document.pdf"
        }
        
        auto_tags = self.service.get_auto_tags_for_file(file_info)
        
        self.assertIn("文档", auto_tags)
        self.assertIn("PDF", auto_tags)
    
    def test_report_file_auto_tagging(self):
        """测试报告文件自动标签"""
        file_info = {
            "name": "monthly_report.pdf",
            "size": 2 * 1024 * 1024,  # 2MB
            "original_path": "/path/to/monthly_report.pdf"
        }
        
        auto_tags = self.service.get_auto_tags_for_file(file_info)
        
        # 应该匹配两个规则：PDF文档规则和报告文件规则
        self.assertIn("文档", auto_tags)
        self.assertIn("PDF", auto_tags)
        self.assertIn("报告", auto_tags)
        self.assertIn("重要", auto_tags)
    
    def test_large_file_auto_tagging(self):
        """测试大文件自动标签"""
        file_info = {
            "name": "video.mp4",
            "size": 50 * 1024 * 1024,  # 50MB
            "original_path": "/path/to/video.mp4"
        }
        
        auto_tags = self.service.get_auto_tags_for_file(file_info)
        
        self.assertIn("大文件", auto_tags)
    
    def test_image_file_auto_tagging(self):
        """测试图片文件自动标签"""
        file_info = {
            "name": "photo.jpg",
            "size": 5 * 1024 * 1024,  # 5MB
            "original_path": "/path/to/photo.jpg"
        }
        
        auto_tags = self.service.get_auto_tags_for_file(file_info)
        
        self.assertIn("图片", auto_tags)
        self.assertIn("媒体", auto_tags)
    
    def test_no_matching_rules(self):
        """测试没有匹配规则的文件"""
        file_info = {
            "name": "readme.txt",
            "size": 1024,  # 1KB
            "original_path": "/path/to/readme.txt"
        }
        
        auto_tags = self.service.get_auto_tags_for_file(file_info)
        
        self.assertEqual(len(auto_tags), 0)
    
    def test_disabled_service(self):
        """测试禁用的自动标签服务"""
        self.service.enabled = False
        
        file_info = {
            "name": "document.pdf",
            "size": 1024 * 1024,
            "original_path": "/path/to/document.pdf"
        }
        
        auto_tags = self.service.get_auto_tags_for_file(file_info)
        
        self.assertEqual(len(auto_tags), 0)
    
    def test_rule_priority(self):
        """测试规则优先级"""
        # 添加一个高优先级规则
        high_priority_rule = AutoTagRule(
            id="high_priority",
            name="高优先级规则",
            condition_type=ConditionType.FILE_EXTENSION,
            condition_value="pdf",
            tag_names=["高优先级"],
            priority=10
        )
        
        self.service.add_rule(high_priority_rule)
        
        file_info = {
            "name": "document.pdf",
            "size": 1024 * 1024,
            "original_path": "/path/to/document.pdf"
        }
        
        auto_tags = self.service.get_auto_tags_for_file(file_info)
        
        # 高优先级标签应该在前面
        self.assertEqual(auto_tags[0], "高优先级")
    
    def test_config_loading(self):
        """测试从配置加载规则"""
        config = {
            "auto_tags": {
                "enabled": True,
                "rules": [
                    {
                        "id": "config_rule",
                        "name": "配置规则",
                        "condition_type": "file_extension",
                        "condition_value": "txt",
                        "tag_names": ["文本", "配置"],
                        "enabled": True,
                        "priority": 5
                    }
                ]
            }
        }
        
        new_service = AutoTagService()
        new_service.load_rules_from_config(config)
        
        self.assertEqual(len(new_service.rules), 1)
        self.assertEqual(new_service.rules[0].name, "配置规则")
        self.assertTrue(new_service.enabled)
    
    def test_rule_management(self):
        """测试规则管理功能"""
        # 测试获取规则
        rule = self.service.get_rule("rule1")
        self.assertIsNotNone(rule)
        self.assertEqual(rule.name, "PDF文档规则")
        
        # 测试更新规则
        updated_rule = AutoTagRule(
            id="rule1",
            name="更新的PDF规则",
            condition_type=ConditionType.FILE_EXTENSION,
            condition_value="pdf,doc",
            tag_names=["文档", "更新"]
        )
        
        success = self.service.update_rule("rule1", updated_rule)
        self.assertTrue(success)
        
        # 验证更新
        rule = self.service.get_rule("rule1")
        self.assertEqual(rule.name, "更新的PDF规则")
        self.assertEqual(rule.condition_value, "pdf,doc")
        
        # 测试删除规则
        success = self.service.remove_rule("rule1")
        self.assertTrue(success)
        
        # 验证删除
        rule = self.service.get_rule("rule1")
        self.assertIsNone(rule)


def run_integration_tests():
    """运行集成测试"""
    print("开始自动标签功能集成测试...")
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestAutoTagIntegration)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    if result.wasSuccessful():
        print("\n✅ 所有自动标签集成测试通过！")
        return True
    else:
        print(f"\n❌ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        return False


if __name__ == "__main__":
    run_integration_tests()
