#!/usr/bin/env python3
"""
数据库状态检查脚本
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_database_status():
    """检查数据库状态"""
    try:
        from smartvault.data.database import Database

        # 检查数据库状态
        db = Database.create_from_config()
        cursor = db.conn.cursor()

        print('=== 数据库状态检查 ===')

        # 1. 检查文件总数
        cursor.execute('SELECT COUNT(*) FROM files')
        total_files = cursor.fetchone()[0]
        print(f'数据库中总文件数: {total_files}')

        # 2. 检查中转文件夹文件数
        cursor.execute("SELECT COUNT(*) FROM files WHERE staging_status = 'staging'")
        staging_files = cursor.fetchone()[0]
        print(f'中转文件夹文件数: {staging_files}')

        # 3. 检查设备标签关联
        cursor.execute('SELECT COUNT(*) FROM file_tags')
        tag_relations = cursor.fetchone()[0]
        print(f'文件标签关联总数: {tag_relations}')

        # 4. 检查设备标签
        cursor.execute("SELECT id, name FROM tags WHERE name LIKE '💾%'")
        device_tags = cursor.fetchall()
        print(f'设备标签数量: {len(device_tags)}')
        for tag in device_tags:
            tag_id, tag_name = tag
            cursor.execute('SELECT COUNT(*) FROM file_tags WHERE tag_id = ?', (tag_id,))
            tag_file_count = cursor.fetchone()[0]
            print(f'  - {tag_name}: {tag_file_count} 个文件')

        # 5. 检查最近添加的文件
        cursor.execute("SELECT COUNT(*) FROM files WHERE added_at > datetime('now', '-1 hour')")
        recent_files = cursor.fetchone()[0]
        print(f'最近1小时添加的文件: {recent_files}')

        # 6. 检查文件状态分布
        cursor.execute('SELECT staging_status, COUNT(*) FROM files GROUP BY staging_status')
        status_stats = cursor.fetchall()
        print(f'文件状态分布:')
        for status, count in status_stats:
            print(f'  - {status or "NULL"}: {count} 个文件')

        # 7. 检查入库方式分布
        cursor.execute('SELECT entry_type, COUNT(*) FROM files GROUP BY entry_type')
        entry_stats = cursor.fetchall()
        print(f'入库方式分布:')
        for entry_type, count in entry_stats:
            print(f'  - {entry_type}: {count} 个文件')

        # 8. 检查最近添加的文件详情（前10个）
        cursor.execute("SELECT id, name, added_at, staging_status FROM files ORDER BY added_at DESC LIMIT 10")
        recent_files_detail = cursor.fetchall()
        print(f'\n最近添加的10个文件:')
        for file_info in recent_files_detail:
            file_id, name, added_at, staging_status = file_info
            print(f'  - {name} (ID: {file_id[:8]}..., 状态: {staging_status}, 时间: {added_at})')

        # 9. 检查分页查询
        print(f'\n=== 分页查询测试 ===')

        # 测试所有文件分页
        cursor.execute('SELECT COUNT(*) FROM files')
        all_files_count = cursor.fetchone()[0]
        print(f'所有文件总数: {all_files_count}')

        cursor.execute('SELECT COUNT(*) FROM files LIMIT 100 OFFSET 0')
        first_page_count = cursor.fetchone()[0]
        print(f'第一页文件数 (LIMIT 100): {first_page_count}')

        # 测试中转文件夹分页
        cursor.execute("SELECT COUNT(*) FROM files WHERE staging_status = 'staging'")
        staging_total = cursor.fetchone()[0]
        print(f'中转文件夹总数: {staging_total}')

        cursor.execute("SELECT COUNT(*) FROM files WHERE staging_status = 'staging' LIMIT 100 OFFSET 0")
        staging_page_count = cursor.fetchone()[0]
        print(f'中转文件夹第一页 (LIMIT 100): {staging_page_count}')

        print('=== 检查完成 ===')

    except Exception as e:
        print(f'检查数据库状态失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_database_status()
