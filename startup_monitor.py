"""
启动监控工具 - 实时监控启动过程和响应状态
"""

import os
import sys
import time
import threading

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def monitor_startup():
    """监控启动过程"""
    print("🔍 启动监控器开始...")

    # Windows系统不支持SIGALRM，使用线程超时机制
    startup_timeout = 60  # 60秒超时
    startup_start_time = time.time()

    try:
        # 步骤1: 测试基础导入
        print("📦 步骤1: 测试基础导入...")
        start_time = time.time()

        from PySide6.QtWidgets import QApplication
        print(f"  ✅ PySide6导入成功 ({time.time() - start_time:.2f}s)")

        # 步骤2: 创建QApplication
        print("🚀 步骤2: 创建QApplication...")
        start_time = time.time()

        app = QApplication(sys.argv)
        app.setApplicationName("SmartVault-Monitor")
        print(f"  ✅ QApplication创建成功 ({time.time() - start_time:.2f}s)")

        # 步骤3: 测试数据库连接
        print("🗄️  步骤3: 测试数据库连接...")
        start_time = time.time()

        from smartvault.data.database import Database
        db = Database.create_from_config()
        cursor = db.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM files")
        count = cursor.fetchone()[0]
        db.close()
        print(f"  ✅ 数据库连接成功，{count}个文件 ({time.time() - start_time:.2f}s)")

        # 步骤4: 测试文件服务
        print("📁 步骤4: 测试文件服务...")
        start_time = time.time()

        from smartvault.services.file import FileService
        file_service = FileService()
        files = file_service.get_files(limit=10, offset=0)
        print(f"  ✅ 文件服务正常，获取{len(files)}个文件 ({time.time() - start_time:.2f}s)")

        # 步骤5: 测试UI组件导入
        print("🎨 步骤5: 测试UI组件导入...")
        start_time = time.time()

        from smartvault.ui.main_window import MainWindow
        print(f"  ✅ MainWindow导入成功 ({time.time() - start_time:.2f}s)")

        # 步骤6: 创建主窗口（关键步骤）
        print("🏗️  步骤6: 创建主窗口...")
        print("  ⚠️  如果在此处卡住，说明MainWindow.__init__()有问题")
        start_time = time.time()

        # 直接在主线程中创建窗口，避免线程问题
        try:
            window = MainWindow()
            print(f"  ✅ 主窗口创建成功 ({time.time() - start_time:.2f}s)")
        except Exception as e:
            print(f"  ❌ 主窗口创建失败: {e}")
            import traceback
            traceback.print_exc()
            return False

        # 步骤7: 显示窗口
        print("👁️  步骤7: 显示窗口...")
        start_time = time.time()

        window.show()
        print(f"  ✅ 窗口显示成功 ({time.time() - start_time:.2f}s)")

        # 步骤8: 测试事件循环
        print("🔄 步骤8: 测试事件循环...")
        print("  💡 程序将运行5秒后自动退出")

        # 设置自动退出定时器
        from PySide6.QtCore import QTimer
        def auto_exit():
            print("  ✅ 事件循环正常，自动退出")
            app.quit()

        timer = QTimer()
        timer.timeout.connect(auto_exit)
        timer.start(5000)  # 5秒后退出

        # 运行事件循环
        exit_code = app.exec()

        print(f"🎉 启动监控完成！退出码: {exit_code}")
        return True

    except Exception as e:
        print(f"❌ 启动监控失败: {e}")
        import traceback
        traceback.print_exc()
        return False

    finally:
        # 清理资源
        pass


def main():
    """主函数"""
    print("=" * 60)
    print("🔍 SmartVault 启动监控器")
    print("=" * 60)
    print("此工具将逐步监控启动过程，帮助定位停止响应的具体位置")
    print("=" * 60)

    success = monitor_startup()

    print("=" * 60)
    if success:
        print("✅ 监控结果: 启动过程正常")
    else:
        print("❌ 监控结果: 启动过程异常")
    print("=" * 60)

    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
