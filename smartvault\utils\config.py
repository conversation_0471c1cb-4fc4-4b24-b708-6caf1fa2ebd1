"""
配置管理模块
"""

import os
import json
import appdirs


def normalize_path(path):
    """标准化路径，统一使用正斜杠

    Args:
        path: 原始路径

    Returns:
        str: 标准化后的路径
    """
    if path:
        return path.replace('\\', '/')


def get_app_data_dir():
    """获取应用数据目录

    Returns:
        str: 应用数据目录路径
    """
    app_data_dir = appdirs.user_data_dir("SmartVault", "Mojianghu")
    os.makedirs(app_data_dir, exist_ok=True)
    return app_data_dir


def get_config_path():
    """获取配置文件路径

    Returns:
        str: 配置文件路径
    """
    return os.path.join(get_app_data_dir(), "config.json")


def load_config():
    """加载配置

    Returns:
        dict: 配置字典
    """
    config_path = get_config_path()

    # 完整的默认配置，包含所有设置页面的默认值
    default_config = {
        # 基础配置
        "library_path": normalize_path(os.path.join(get_app_data_dir(), "library")),
        "default_entry_type": "link",
        "monitor_folders": [],
        "auto_import": False,
        "delete_physical_files": True,

        # UI设置
        "ui": {
            "default_view_mode": "table",
            "default_page_size": 100
        },

        # 搜索设置
        "search": {
            "default_scope": "database",
            "max_results": 1000,
            "case_sensitive": False,
            "whole_word": False
        },

        # 高级设置
        "advanced": {
            "enable_logging": True,
            "log_level": "INFO",
            "log_file_size_mb": 10,
            "enable_ai_features": False  # AI功能总开关
        },

        # 监控设置
        "monitor": {
            "auto_start": True,
            "startup_delay_check": True,
            "delay_check_seconds": 5,
            "smart_duplicate_handling": True,
            "batch_error_display": True,
            "event_interval_ms": 500,
            "启动时自动开启": True
        },

        # 自动标签设置
        "auto_tags": {
            "enabled": True,
            "enable_ai": False,
            "rules": []
        },

        # AI功能设置
        "ai": {
            "enabled": False,  # AI功能启用状态
            "stage": "rule_based",  # AI能力阶段: rule_based, ml_basic, deep_learning
            "models": {
                "text_classifier": {
                    "enabled": False,
                    "model_path": "",
                    "confidence_threshold": 0.6
                },
                "content_analyzer": {
                    "enabled": False,
                    "model_path": "",
                    "max_content_length": 10000
                }
            },
            "features": {
                "project_detection": {
                    "enabled": True,
                    "min_files": 3,
                    "confidence_threshold": 0.7
                },
                "series_detection": {
                    "enabled": True,
                    "min_files": 2,
                    "confidence_threshold": 0.6
                },
                "behavior_learning": {
                    "enabled": True,
                    "learning_rate": 0.1,
                    "max_patterns": 1000
                },
                "smart_suggestions": {
                    "enabled": True,
                    "max_suggestions": 8,
                    "merge_with_rules": True
                }
            },
            "performance": {
                "async_processing": True,
                "cache_enabled": True,
                "cache_size_mb": 50,
                "timeout_seconds": 30
            }
        },

        # 剪贴板查重设置
        "clipboard": {
            "enabled": True,  # UI工具栏开关状态
            "detection_delay_ms": 500,
            "float_mode": "on_demand",  # "on_demand" 按需弹出, "persistent" 持续显示
            "float_duration_seconds": 10,
            "filename_cleaning": {
                "enabled": True,
                "rules": [
                    {"name": "移除下载后缀", "pattern": r" - 下载.*$", "replace": "", "enabled": True},
                    {"name": "移除文件大小", "pattern": r" \([0-9.]+[KMGT]?B\)", "replace": "", "enabled": True},
                    {"name": "移除网站标记", "pattern": r"^\[.*?\]\s*", "replace": "", "enabled": True},
                    {"name": "移除副本标记", "pattern": r" - 副本(\(\d+\))?", "replace": "", "enabled": True},
                    {"name": "移除时间戳", "pattern": r"_\d{8}_", "replace": "_", "enabled": True},
                    {"name": "移除括号数字", "pattern": r" \(\d+\)", "replace": "", "enabled": True}
                ]
            }
        }
    }

    # 如果配置文件不存在，创建默认配置
    if not os.path.exists(config_path):
        save_config(default_config)
        return default_config

    # 加载配置
    try:
        with open(config_path, "r", encoding="utf-8") as f:
            config = json.load(f)

        # 递归合并默认配置，确保所有配置项都存在
        config = merge_config_recursive(config, default_config)

        # 标准化路径
        if "library_path" in config:
            config["library_path"] = normalize_path(config["library_path"])

        return config
    except Exception as e:
        print(f"加载配置失败: {e}")
        return default_config


def merge_config_recursive(user_config, default_config):
    """递归合并配置，确保所有默认配置项都存在

    Args:
        user_config: 用户配置
        default_config: 默认配置

    Returns:
        dict: 合并后的配置
    """
    result = user_config.copy()

    for key, default_value in default_config.items():
        if key not in result:
            # 如果用户配置中没有这个键，直接使用默认值
            result[key] = default_value
        elif isinstance(default_value, dict) and isinstance(result[key], dict):
            # 如果都是字典，递归合并
            result[key] = merge_config_recursive(result[key], default_value)
        # 如果用户配置中有这个键且不是字典，保持用户配置的值

    return result


def save_config(config):
    """保存配置

    Args:
        config: 配置字典
    """
    config_path = get_config_path()

    # 标准化路径
    if "library_path" in config:
        config["library_path"] = normalize_path(config["library_path"])

    try:
        with open(config_path, "w", encoding="utf-8") as f:
            json.dump(config, f, ensure_ascii=False, indent=4)
    except Exception as e:
        print(f"保存配置失败: {e}")


def get_ui_config():
    """获取UI配置

    Returns:
        dict: UI配置字典
    """
    config = load_config()
    return config.get("ui", {
        "default_view_mode": "table",
        "default_page_size": 100
    })


def get_search_config():
    """获取搜索配置

    Returns:
        dict: 搜索配置字典
    """
    config = load_config()
    return config.get("search", {
        "default_scope": "database",
        "max_results": 1000,
        "case_sensitive": False,
        "whole_word": False
    })


def get_advanced_config():
    """获取高级配置

    Returns:
        dict: 高级配置字典
    """
    config = load_config()
    return config.get("advanced", {
        "enable_logging": True,
        "log_level": "INFO",
        "log_file_size_mb": 10
    })


def get_monitor_config():
    """获取监控配置

    Returns:
        dict: 监控配置字典
    """
    config = load_config()
    return config.get("monitor", {
        "auto_start": True,
        "startup_delay_check": True,
        "delay_check_seconds": 5,
        "smart_duplicate_handling": True,
        "batch_error_display": True,
        "启动时自动开启": True  # 监控功能启动时自动开启状态
    })


def save_monitor_status(enabled):
    """保存监控功能开关状态

    Args:
        enabled: 是否启用监控功能
    """
    config = load_config()
    if "monitor" not in config:
        config["monitor"] = {}

    config["monitor"]["启动时自动开启"] = enabled
    save_config(config)


def get_monitor_status():
    """获取监控功能开关状态

    Returns:
        bool: 监控功能是否启用
    """
    monitor_config = get_monitor_config()
    return monitor_config.get("启动时自动开启", True)


def save_clipboard_status(enabled):
    """保存剪贴板监控开关状态

    Args:
        enabled: 是否启用剪贴板监控
    """
    config = load_config()
    if "clipboard" not in config:
        config["clipboard"] = {}

    config["clipboard"]["enabled"] = enabled
    save_config(config)


def get_clipboard_status():
    """获取剪贴板监控开关状态

    Returns:
        bool: 剪贴板监控是否启用
    """
    config = load_config()
    clipboard_config = config.get("clipboard", {})
    return clipboard_config.get("enabled", True)


def get_ai_config():
    """获取AI配置

    Returns:
        dict: AI配置字典
    """
    config = load_config()
    return config.get("ai", {
        "enabled": False,
        "stage": "rule_based",
        "models": {
            "text_classifier": {
                "enabled": False,
                "model_path": "",
                "confidence_threshold": 0.6
            },
            "content_analyzer": {
                "enabled": False,
                "model_path": "",
                "max_content_length": 10000
            }
        },
        "features": {
            "project_detection": {
                "enabled": True,
                "min_files": 3,
                "confidence_threshold": 0.7
            },
            "series_detection": {
                "enabled": True,
                "min_files": 2,
                "confidence_threshold": 0.6
            },
            "behavior_learning": {
                "enabled": True,
                "learning_rate": 0.1,
                "max_patterns": 1000
            },
            "smart_suggestions": {
                "enabled": True,
                "max_suggestions": 8,
                "merge_with_rules": True
            }
        },
        "performance": {
            "async_processing": True,
            "cache_enabled": True,
            "cache_size_mb": 50,
            "timeout_seconds": 30
        }
    })


def save_ai_config(ai_config):
    """保存AI配置

    Args:
        ai_config: AI配置字典
    """
    config = load_config()
    config["ai"] = ai_config
    save_config(config)


def get_ai_status():
    """获取AI功能状态

    Returns:
        bool: AI功能是否启用
    """
    config = load_config()
    # 只检查AI配置中的启用状态
    return config.get("ai", {}).get("enabled", False)


def save_ai_status(enabled):
    """保存AI功能状态

    Args:
        enabled: 是否启用AI功能
    """
    config = load_config()

    # 更新AI配置中的启用状态
    if "ai" not in config:
        config["ai"] = {}
    config["ai"]["enabled"] = enabled

    # 同时更新自动标签中的AI设置
    if "auto_tags" not in config:
        config["auto_tags"] = {}
    config["auto_tags"]["enable_ai"] = enabled

    save_config(config)
