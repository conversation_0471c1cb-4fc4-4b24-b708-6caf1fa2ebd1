#!/usr/bin/env python3
"""
测试文件自动入库行为
"""

import sys
import os
import tempfile
import shutil
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from smartvault.services.file_monitor_service import FileMonitorService
from smartvault.services.file import FileService
from smartvault.data.database import Database


class TestAutoAddBehavior:
    """测试自动入库行为"""

    def setup_method(self):
        """测试前设置"""
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp(prefix="smartvault_auto_add_test_")
        self.monitor_dir = os.path.join(self.temp_dir, "monitor")
        os.makedirs(self.monitor_dir, exist_ok=True)

        # 创建服务实例
        self.monitor_service = FileMonitorService()
        self.file_service = FileService()

        # 记录监控事件
        self.monitor_events = []

        print(f"📁 测试目录: {self.temp_dir}")
        print(f"📁 监控目录: {self.monitor_dir}")

    def teardown_method(self):
        """测试后清理"""
        # 停止所有监控
        if hasattr(self, 'monitor_service'):
            self.monitor_service.stop_all_monitoring()

        # 清理临时目录
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
            print(f"🧹 清理测试目录: {self.temp_dir}")

    def test_auto_add_link_mode(self):
        """测试自动入库 - 链接模式

        行为期望：
        1. 监控文件夹中创建新文件时自动检测
        2. 根据配置自动添加文件到库（链接模式）
        3. 文件在库中显示，但物理文件保持在原位置
        4. 生成正确的监控事件记录
        """
        print("\n🧪 测试自动入库 - 链接模式...")

        # 设置事件回调
        def on_file_event(event_type, file_path, monitor_id):
            self.monitor_events.append({
                "type": event_type,
                "path": file_path,
                "monitor_id": monitor_id,
                "timestamp": datetime.now()
            })
            print(f"   📝 监控事件: {event_type} - {os.path.basename(file_path)}")

        # 添加监控文件夹
        monitor_id = self.monitor_service.add_monitor_folder(
            folder_path=self.monitor_dir,
            entry_mode="link",
            file_patterns=["*.txt"],
            auto_add=True,
            recursive=False
        )

        # 设置事件回调
        self.monitor_service.set_event_callback(on_file_event)

        # 启动监控
        success = self.monitor_service.start_monitoring(monitor_id)
        assert success is True
        print(f"   ✅ 监控已启动: {monitor_id}")

        # 获取入库前的文件总数
        count_before = self.file_service.get_file_count()
        print(f"   📊 入库前文件总数: {count_before}")

        # 创建唯一的测试文件名
        import uuid
        unique_name = f"auto_test_{str(uuid.uuid4())[:8]}.txt"
        test_file = os.path.join(self.monitor_dir, unique_name)
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("这是一个自动入库测试文件")
        print(f"   📄 创建测试文件: {os.path.basename(test_file)}")

        # 等待文件监控处理
        time.sleep(2)

        # 检查监控事件
        assert len(self.monitor_events) > 0
        created_events = [e for e in self.monitor_events if e["type"] == "created"]
        assert len(created_events) > 0
        print(f"   ✅ 检测到 {len(created_events)} 个创建事件")

        # 检查文件总数是否增加
        count_after = self.file_service.get_file_count()
        print(f"   📊 入库后文件总数: {count_after}")

        # 应该增加了一个文件
        assert count_after == count_before + 1

        # 查找新添加的文件（获取最新的文件，按添加时间排序）
        recent_files = self.file_service.get_files(limit=10, offset=0)  # 获取最新的10个文件
        new_files = [f for f in recent_files if f["name"] == unique_name]

        if len(new_files) == 0:
            # 如果在最新文件中没找到，尝试搜索
            print("   🔍 在最新文件中未找到，尝试搜索...")
            files_with_name = self.file_service.get_files(
                limit=1000, offset=0,
                search_keyword=unique_name,
                search_column=0  # 按名称搜索
            )
            new_files = [f for f in files_with_name if f["name"] == unique_name]
            print(f"   🔍 搜索结果: {len(new_files)} 个文件")

        assert len(new_files) == 1, f"期望找到1个文件，实际找到{len(new_files)}个"

        new_file = new_files[0]
        assert new_file["entry_type"] == "link"
        assert new_file["original_path"] == test_file
        assert new_file["library_path"] is None  # 链接模式下library_path应该为None
        print(f"   ✅ 文件已自动添加到库: {new_file['name']} ({new_file['entry_type']})")

        # 验证原文件仍在原位置
        assert os.path.exists(test_file)
        print(f"   ✅ 原文件仍在原位置: {test_file}")

        print("   🎉 链接模式自动入库测试通过")

    def test_auto_add_copy_mode(self):
        """测试自动入库 - 复制模式

        行为期望：
        1. 监控文件夹中创建新文件时自动检测
        2. 根据配置自动添加文件到库（复制模式）
        3. 文件被复制到文件库目录
        4. 原文件保持不变
        """
        print("\n🧪 测试自动入库 - 复制模式...")

        # 添加监控文件夹
        monitor_id = self.monitor_service.add_monitor_folder(
            folder_path=self.monitor_dir,
            entry_mode="copy",
            file_patterns=["*.pdf"],
            auto_add=True,
            recursive=False
        )

        # 启动监控
        success = self.monitor_service.start_monitoring(monitor_id)
        assert success is True
        print(f"   ✅ 监控已启动: {monitor_id}")

        # 获取入库前的文件总数
        count_before = self.file_service.get_file_count()

        # 创建唯一的测试文件名
        import uuid
        unique_name = f"copy_test_{str(uuid.uuid4())[:8]}.pdf"
        test_file = os.path.join(self.monitor_dir, unique_name)
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("PDF测试内容")
        print(f"   📄 创建测试文件: {os.path.basename(test_file)}")

        # 等待文件监控处理
        time.sleep(2)

        # 检查文件总数是否增加
        count_after = self.file_service.get_file_count()

        # 应该增加了一个文件
        assert count_after == count_before + 1

        # 查找新添加的文件（按名称搜索）
        files_with_name = self.file_service.get_files(
            limit=1000, offset=0,
            search_keyword=unique_name,
            search_column=0  # 按名称搜索
        )
        new_files = [f for f in files_with_name if f["name"] == unique_name]
        assert len(new_files) == 1

        new_file = new_files[0]
        assert new_file["entry_type"] == "copy"
        assert new_file["original_path"] == test_file
        assert new_file["library_path"] is not None  # 复制模式下应该有library_path
        print(f"   ✅ 文件已自动添加到库: {new_file['name']} ({new_file['entry_type']})")

        # 验证文件已复制到库目录
        assert os.path.exists(new_file["library_path"])
        print(f"   ✅ 文件已复制到库目录: {new_file['library_path']}")

        # 验证原文件仍在原位置
        assert os.path.exists(test_file)
        print(f"   ✅ 原文件仍在原位置: {test_file}")

        print("   🎉 复制模式自动入库测试通过")

    def test_file_pattern_filtering(self):
        """测试文件类型过滤

        行为期望：
        1. 只有匹配文件模式的文件才会被自动添加
        2. 不匹配的文件应该被忽略
        """
        print("\n🧪 测试文件类型过滤...")

        # 添加监控文件夹，只监控.txt文件
        monitor_id = self.monitor_service.add_monitor_folder(
            folder_path=self.monitor_dir,
            entry_mode="link",
            file_patterns=["*.txt"],
            auto_add=True,
            recursive=False
        )

        # 启动监控
        success = self.monitor_service.start_monitoring(monitor_id)
        assert success is True

        # 获取入库前的文件总数
        count_before = self.file_service.get_file_count()

        # 创建唯一的文件名
        import uuid
        unique_id = str(uuid.uuid4())[:8]

        # 创建匹配的文件
        txt_name = f"should_add_{unique_id}.txt"
        txt_file = os.path.join(self.monitor_dir, txt_name)
        with open(txt_file, 'w', encoding='utf-8') as f:
            f.write("应该被添加的txt文件")

        # 创建不匹配的文件
        doc_name = f"should_ignore_{unique_id}.doc"
        doc_file = os.path.join(self.monitor_dir, doc_name)
        with open(doc_file, 'w', encoding='utf-8') as f:
            f.write("应该被忽略的doc文件")

        print(f"   📄 创建txt文件: {os.path.basename(txt_file)}")
        print(f"   📄 创建doc文件: {os.path.basename(doc_file)}")

        # 等待文件监控处理
        time.sleep(2)

        # 检查文件总数是否正确增加
        count_after = self.file_service.get_file_count()

        # 应该只增加了一个文件（txt文件）
        assert count_after == count_before + 1

        # 查找新添加的文件（搜索两个文件名）
        txt_files = self.file_service.get_files(
            limit=1000, offset=0,
            search_keyword=txt_name,
            search_column=0
        )
        doc_files = self.file_service.get_files(
            limit=1000, offset=0,
            search_keyword=doc_name,
            search_column=0
        )

        # 应该只找到txt文件
        assert len(txt_files) == 1
        assert len(doc_files) == 0
        assert txt_files[0]["name"] == txt_name

        print(f"   ✅ 只有匹配的文件被添加: {txt_files[0]['name']}")
        print("   🎉 文件类型过滤测试通过")


def main():
    """主测试函数"""
    print("🚀 开始测试文件自动入库行为...")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    test_instance = TestAutoAddBehavior()

    try:
        # 设置测试环境
        test_instance.setup_method()

        # 运行测试
        test_instance.test_auto_add_link_mode()
        test_instance.test_auto_add_copy_mode()
        test_instance.test_file_pattern_filtering()

        print("\n🎉 所有自动入库测试通过！")
        print("✅ 文件自动入库功能正常工作")

    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

    finally:
        # 清理测试环境
        test_instance.teardown_method()

    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
