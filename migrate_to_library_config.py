#!/usr/bin/env python3
"""
SmartVault 配置迁移工具
将现有的全局配置迁移到文件库内配置系统
"""

import sys
import os
import shutil
from datetime import datetime

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def migrate_configuration():
    """执行配置迁移"""
    print("🔄 SmartVault 配置迁移工具")
    print("=" * 50)
    
    try:
        # 导入必要的模块
        from smartvault.utils.config import load_config, get_config_path
        from smartvault.services.library_config_service import get_library_config_service
        from smartvault.services.backup_service import get_backup_service
        
        # 1. 检查当前配置
        print("📋 检查当前配置...")
        global_config = load_config()
        library_path = global_config.get("library_path", "")
        
        if not library_path or not os.path.exists(library_path):
            print("❌ 未找到有效的文件库路径")
            return False
        
        print(f"📁 当前文件库路径: {library_path}")
        
        # 2. 创建备份
        print("\n💾 创建配置备份...")
        backup_service = get_backup_service()
        success, backup_path, message = backup_service.create_backup("migration")
        
        if success:
            print(f"✅ {message}")
        else:
            print(f"⚠️ 备份创建失败: {message}")
            response = input("是否继续迁移? (y/N): ")
            if response.lower() != 'y':
                return False
        
        # 3. 升级文件库结构
        print("\n🏗️ 升级文件库结构...")
        library_config_service = get_library_config_service()
        
        # 检查是否已经是新结构
        if library_config_service.validate_library(library_path):
            print("✅ 文件库结构已是最新版本")
        else:
            print("🔧 升级文件库结构...")
            success, message = library_config_service.create_library_structure(
                library_path, 
                global_config.get("library_name", "SmartVault文件库")
            )
            
            if success:
                print(f"✅ {message}")
            else:
                print(f"❌ {message}")
                return False
        
        # 4. 迁移配置
        print("\n📦 迁移配置数据...")
        library_config_service.migrate_global_config_to_library(library_path)
        
        # 5. 设置自动备份
        print("\n🔄 配置自动备份...")
        backup_service.start_auto_backup()
        
        # 6. 验证迁移结果
        print("\n🔍 验证迁移结果...")
        library_config = library_config_service.load_library_config(library_path)
        
        verification_items = [
            ("UI设置", "ui" in library_config),
            ("搜索设置", "search" in library_config),
            ("监控设置", "monitor" in library_config),
            ("备份设置", "backup" in library_config),
            ("文件操作设置", "file_operations" in library_config),
            ("高级设置", "advanced" in library_config)
        ]
        
        all_verified = True
        for item_name, verified in verification_items:
            status = "✅" if verified else "❌"
            print(f"  {status} {item_name}")
            if not verified:
                all_verified = False
        
        # 7. 生成迁移报告
        print("\n📄 生成迁移报告...")
        report_content = generate_migration_report(
            library_path, global_config, library_config, backup_path
        )
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"migration_report_{timestamp}.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"📄 迁移报告已保存: {report_file}")
        
        # 8. 显示结果
        if all_verified:
            print("\n🎉 配置迁移成功完成！")
            print("\n📋 迁移完成后的变化:")
            print("  ✅ 配置文件现在存储在文件库内")
            print("  ✅ 每个文件库都有独立的配置")
            print("  ✅ 自动备份功能已启用")
            print("  ✅ 文件库结构已标准化")
            
            print("\n💡 使用建议:")
            print("  📁 可以创建多个独立的文件库")
            print("  🔄 每个文件库都有自己的设置")
            print("  💾 配置会随文件库一起备份")
            print("  🚀 切换文件库时自动加载对应配置")
            
            return True
        else:
            print("\n⚠️ 配置迁移部分失败")
            print("请检查迁移报告了解详情")
            return False
            
    except Exception as e:
        print(f"\n❌ 迁移过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def generate_migration_report(library_path, global_config, library_config, backup_path):
    """生成迁移报告"""
    report = []
    report.append("=" * 80)
    report.append("SmartVault 配置迁移报告")
    report.append("=" * 80)
    report.append(f"迁移时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append(f"文件库路径: {library_path}")
    report.append(f"备份文件: {backup_path}")
    report.append("")
    
    # 迁移前配置
    report.append("📋 迁移前配置 (全局配置):")
    report.append(f"  - 文件库路径: {global_config.get('library_path', '未设置')}")
    report.append(f"  - 默认入库方式: {global_config.get('default_entry_type', '未设置')}")
    report.append(f"  - UI设置: {'已配置' if 'ui' in global_config else '使用默认'}")
    report.append(f"  - 搜索设置: {'已配置' if 'search' in global_config else '使用默认'}")
    report.append(f"  - 监控设置: {'已配置' if 'monitor' in global_config else '使用默认'}")
    report.append("")
    
    # 迁移后配置
    report.append("📋 迁移后配置 (文件库配置):")
    report.append(f"  - 文件库名称: {library_config.get('library_info', {}).get('name', '未设置')}")
    report.append(f"  - 创建时间: {library_config.get('library_info', {}).get('created_at', '未设置')}")
    report.append(f"  - UI设置: {'已迁移' if 'ui' in library_config else '❌ 缺失'}")
    report.append(f"  - 搜索设置: {'已迁移' if 'search' in library_config else '❌ 缺失'}")
    report.append(f"  - 监控设置: {'已迁移' if 'monitor' in library_config else '❌ 缺失'}")
    report.append(f"  - 备份设置: {'已配置' if 'backup' in library_config else '❌ 缺失'}")
    report.append(f"  - 文件操作设置: {'已配置' if 'file_operations' in library_config else '❌ 缺失'}")
    report.append("")
    
    # 新增功能
    report.append("🆕 新增功能:")
    report.append("  ✅ 自动备份系统")
    report.append("  ✅ 文件库独立配置")
    report.append("  ✅ 标准化目录结构")
    report.append("  ✅ 配置版本管理")
    report.append("")
    
    # 使用说明
    report.append("📖 使用说明:")
    report.append("  1. 配置文件现在存储在文件库的 config/ 目录中")
    report.append("  2. 每个文件库都有独立的配置，互不影响")
    report.append("  3. 自动备份功能已启用，默认每24小时备份一次")
    report.append("  4. 可以通过设置界面调整各项配置")
    report.append("  5. 切换文件库时会自动加载对应的配置")
    report.append("")
    
    # 注意事项
    report.append("⚠️ 注意事项:")
    report.append("  - 原全局配置文件仍然保留，但不再使用")
    report.append("  - 如需回退，可以使用备份文件恢复")
    report.append("  - 建议定期检查备份功能是否正常工作")
    report.append("")
    
    report.append("=" * 80)
    
    return "\n".join(report)

def main():
    """主函数"""
    print("🔄 SmartVault 配置迁移工具")
    print("📋 此工具将帮助您升级到新的配置系统")
    print("⚠️ 迁移前会自动创建备份")
    
    response = input("\n是否开始配置迁移? (y/N): ")
    if response.lower() != 'y':
        print("迁移已取消")
        return
    
    success = migrate_configuration()
    
    if success:
        print("\n🎉 配置迁移成功完成！")
        print("💡 现在您可以享受新的文件库独立配置功能")
    else:
        print("\n⚠️ 配置迁移未完全成功")
        print("🔧 请检查错误信息并重新尝试")

if __name__ == '__main__':
    main()
