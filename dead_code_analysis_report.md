# SmartVault 死代码分析报告
分析时间: 2025-05-27 18:11:40
分析文件数: 81

## 🚨 明显死代码 (建议立即删除)
发现 1 个明显的死代码方法：

- `reset_status_style()` in smartvault\ui\main_window\core.py:2206
  理由: 简单方法且无调用

## ⚠️ 可疑死代码 (需要验证)
发现 93 个可疑的死代码方法：

- `move_file()` in smartvault\data\file_system.py:137
  理由: 公共方法但无明显调用
- `move_library()` in smartvault\data\file_system.py:166
  理由: 公共方法但无明显调用
- `delete_library()` in smartvault\data\file_system.py:248
  理由: 公共方法但无明显调用
- `load_rules_from_config()` in smartvault\services\auto_tag_service.py:343
  理由: 公共方法但无明显调用
- `get_auto_tags_for_file()` in smartvault\services\auto_tag_service.py:373
  理由: 公共方法但无明显调用
- `remove_rule()` in smartvault\services\auto_tag_service.py:411
  理由: 公共方法但无明显调用
- `update_rule()` in smartvault\services\auto_tag_service.py:440
  理由: 公共方法但无明显调用
- `save_backup_config()` in smartvault\services\backup_service.py:58
  理由: 公共方法但无明显调用
- `add_cleaning_rule()` in smartvault\services\clipboard_monitor_service.py:455
  理由: 公共方法但无明显调用
- `get_cleaning_rules()` in smartvault\services\clipboard_monitor_service.py:463
  理由: 公共方法但无明显调用
... 还有 83 个

## 💡 删除建议
### 第一批 (低风险)
建议立即删除 1 个明显死代码方法
### 第二批 (需要验证)
需要逐个验证 93 个可疑方法