"""
紧凑型进度条组件，用于工具栏
"""

from PySide6.QtWidgets import (
    QProgressBar, QLabel, QHBoxLayout,
    QPushButton, QSizePolicy
)
from PySide6.QtCore import Qt, QTimer
from .progress_bar import BaseProgressBarWidget


class CompactProgressBarWidget(BaseProgressBarWidget):
    """紧凑型进度条组件，适合放在工具栏中"""

    def __init__(self, parent=None):
        """初始化

        Args:
            parent: 父窗口
        """
        super().__init__(parent)

        # 设置属性
        self.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        self.setMinimumWidth(150)
        self.setMaximumWidth(200)
        self.setMinimumHeight(25)
        self.setMaximumHeight(25)
        self.setStyleSheet("""
            QWidget {
                background-color: transparent;
            }
            QProgressBar {
                border: 1px solid #ccc;
                border-radius: 3px;
                text-align: center;
                background-color: #f8f8f8;
                max-height: 20px;
                min-height: 20px;
                font-size: 11px;
            }
            QProgressBar::chunk {
                background-color: #0078d4;
                border-radius: 2px;
                margin: 1px;
            }
            QLabel {
                font-size: 11px;
                color: #333;
                background-color: transparent;
            }
            QPushButton {
                border: none;
                background: transparent;
                color: #666;
                font-size: 12px;
                max-width: 18px;
                max-height: 18px;
                min-width: 18px;
                min-height: 18px;
            }
            QPushButton:hover {
                color: #f00;
                background-color: #f0f0f0;
                border-radius: 9px;
            }
        """)
        # 默认可见，显示待机状态
        self.setVisible(True)

        # 创建布局
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(2, 2, 2, 2)
        self.layout.setSpacing(2)

        # 创建标签和进度条
        self.label = QLabel("就绪")
        self.label.setMaximumWidth(60)
        self.label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)

        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setAlignment(Qt.AlignCenter)
        self.progress_bar.setFormat("0%")
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)

        self.cancel_button = QPushButton("×")
        self.cancel_button.setToolTip("取消")
        self.cancel_button.clicked.connect(self.on_cancel_clicked)
        self.cancel_button.setVisible(False)  # 默认隐藏取消按钮

        self.layout.addWidget(self.label)
        self.layout.addWidget(self.progress_bar, 1)  # 进度条占据更多空间
        self.layout.addWidget(self.cancel_button)

        # 自动隐藏定时器（用于自动回到待机状态）
        self.hide_timer = QTimer(self)
        self.hide_timer.setSingleShot(True)
        self.hide_timer.timeout.connect(self.reset_to_idle)

    def start_progress(self, text, maximum=100, show_cancel=True):
        """开始进度，重写基类方法"""
        super().start_progress(text, maximum, show_cancel)
        # 紧凑型进度条始终可见，无需额外显示逻辑

    def finish_progress(self, text=None, auto_reset=True, delay=3000):
        """完成进度

        Args:
            text: 完成文本
            auto_reset: 是否自动回到待机状态
            delay: 自动回到待机状态的延迟（毫秒）
        """
        if self.progress_bar:
            self.progress_bar.setMaximum(100)
            self.progress_bar.setValue(100)
        if text and self.label:
            self.label.setText(text)

        if self.cancel_button:
            self.cancel_button.setVisible(False)  # 隐藏取消按钮

        if auto_reset and self.hide_timer:
            self.hide_timer.start(delay)

    def reset_to_idle(self):
        """重置到待机状态"""
        if self.label:
            self.label.setText("就绪")
        if self.progress_bar:
            self.progress_bar.setMaximum(100)
            self.progress_bar.setValue(0)
            self.progress_bar.setFormat("0%")
        if self.cancel_button:
            self.cancel_button.setVisible(False)

    def on_cancel_clicked(self):
        """取消按钮点击事件"""
        super().on_cancel_clicked()
        self.reset_to_idle()
