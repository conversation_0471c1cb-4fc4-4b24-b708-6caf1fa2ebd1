"""
最小化主窗口 - 逐步添加功能来定位问题
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout,
    QHBoxLayout, QLabel, QPushButton, QTextEdit, QSplitter
)
from PySide6.QtCore import Qt, QTimer


class MinimalMainWindow(QMainWindow):
    """最小化主窗口 - 逐步添加功能"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("SmartVault - 最小化版本")
        self.setGeometry(100, 100, 800, 600)

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建布局
        layout = QVBoxLayout(central_widget)

        # 添加标题
        title = QLabel("SmartVault 最小化版本")
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        layout.addWidget(title)

        # 添加状态显示
        self.status_label = QLabel("正在初始化...")
        layout.addWidget(self.status_label)

        # 添加日志显示
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        layout.addWidget(self.log_text)

        # 添加按钮
        button_layout = QHBoxLayout()

        self.test_db_btn = QPushButton("测试数据库")
        self.test_db_btn.clicked.connect(self.test_database)
        button_layout.addWidget(self.test_db_btn)

        self.load_files_btn = QPushButton("加载文件")
        self.load_files_btn.clicked.connect(self.load_files)
        button_layout.addWidget(self.load_files_btn)

        self.test_ui_btn = QPushButton("测试UI组件")
        self.test_ui_btn.clicked.connect(self.test_ui_components)
        button_layout.addWidget(self.test_ui_btn)

        layout.addLayout(button_layout)

        # 添加文件显示区域
        self.file_display = QTextEdit()
        self.file_display.setPlaceholderText("文件列表将在这里显示...")
        layout.addWidget(self.file_display)

        # 设置状态栏
        self.statusBar().showMessage("最小化版本已启动")

        # 延迟初始化
        QTimer.singleShot(100, self.delayed_init)

    def log(self, message):
        """添加日志"""
        self.log_text.append(f"[{self.get_timestamp()}] {message}")
        print(message)

    def get_timestamp(self):
        """获取时间戳"""
        import datetime
        return datetime.datetime.now().strftime("%H:%M:%S")

    def delayed_init(self):
        """延迟初始化"""
        self.log("开始延迟初始化...")
        self.status_label.setText("就绪")
        self.log("延迟初始化完成")

    def test_database(self):
        """测试数据库连接"""
        self.log("测试数据库连接...")
        try:
            from smartvault.data.database import Database

            db = Database.create_from_config()
            cursor = db.conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM files")
            count = cursor.fetchone()[0]
            db.close()

            self.log(f"数据库连接成功，文件数量: {count}")
            self.status_label.setText(f"数据库: {count} 个文件")

        except Exception as e:
            self.log(f"数据库连接失败: {e}")
            self.status_label.setText("数据库连接失败")

    def load_files(self):
        """加载文件列表"""
        self.log("开始加载文件列表...")
        try:
            from smartvault.services.file import FileService

            file_service = FileService()
            files = file_service.get_files(limit=10)  # 只加载前10个文件

            # 显示文件信息
            file_info = []
            for file in files:
                file_info.append(f"- {file['name']} ({file['entry_type']})")

            self.file_display.setText("\n".join(file_info))
            self.log(f"成功加载 {len(files)} 个文件")
            self.status_label.setText(f"已加载 {len(files)} 个文件")

        except Exception as e:
            self.log(f"加载文件失败: {e}")
            self.status_label.setText("文件加载失败")
            import traceback
            self.log(traceback.format_exc())

    def test_ui_components(self):
        """测试UI组件"""
        self.log("测试UI组件...")
        try:
            # 测试导航面板
            from smartvault.ui.widgets import NavigationPanel
            nav_panel = NavigationPanel()
            self.log("导航面板创建成功")
            nav_panel.close()

            # 测试文件视图
            from smartvault.ui.views.file_table_view import FileTableViewContainer
            file_view = FileTableViewContainer()
            self.log("文件视图创建成功")
            file_view.close()

            self.log("所有UI组件测试通过")
            self.status_label.setText("UI组件正常")

        except Exception as e:
            self.log(f"UI组件测试失败: {e}")
            self.status_label.setText("UI组件异常")
            import traceback
            self.log(traceback.format_exc())


def main():
    """主函数"""
    print("🚀 启动最小化SmartVault...")

    app = QApplication(sys.argv)
    app.setApplicationName("SmartVault-Minimal")

    window = MinimalMainWindow()
    window.show()

    print("✅ 最小化版本启动成功")
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
