"""
搜索服务模块
"""

from smartvault.data.database import Database


class SearchService:
    """搜索服务类"""

    def __init__(self):
        """初始化搜索服务"""
        self._db = None

    @property
    def db(self):
        """获取数据库实例，支持延迟初始化"""
        if self._db is None:
            self._db = Database.create_from_config()
        return self._db

    def search_files(self, query, filters=None):
        """搜索文件

        Args:
            query: 搜索关键词
            filters: 搜索过滤条件

        Returns:
            list: 搜索结果列表
        """
        # 构建基本查询
        sql = "SELECT * FROM files WHERE 1=1"
        params = []

        # 添加关键词搜索
        if query:
            sql += " AND (name LIKE ? OR original_path LIKE ?)"
            params.extend([f"%{query}%", f"%{query}%"])

        # 添加过滤条件
        if filters:
            # 文件名搜索
            if "name" in filters:
                name_pattern = filters["name"]
                case_sensitive = filters.get("case_sensitive", False)
                use_regex = filters.get("use_regex", False)

                if use_regex:
                    # 正则表达式搜索（SQLite的REGEXP需要扩展）
                    sql += " AND name REGEXP ?"
                    params.append(name_pattern)
                else:
                    # 通配符搜索
                    if not case_sensitive:
                        sql += " AND LOWER(name) LIKE LOWER(?)"
                    else:
                        sql += " AND name LIKE ?"

                    # 转换通配符
                    pattern = name_pattern.replace("*", "%").replace("?", "_")
                    params.append(pattern)

            # 文件类型过滤
            if "file_types" in filters:
                type_patterns = [t.strip() for t in filters["file_types"].split(",")]
                type_conditions = []
                for pattern in type_patterns:
                    if pattern.startswith("*."):
                        ext = pattern[2:]  # 去掉 "*."
                        type_conditions.append("name LIKE ?")
                        params.append(f"%.{ext}")

                if type_conditions:
                    sql += f" AND ({' OR '.join(type_conditions)})"

            # 标签过滤
            if "tag_id" in filters:
                sql = """
                    SELECT f.* FROM files f
                    JOIN file_tags ft ON f.id = ft.file_id
                    WHERE ft.tag_id = ? AND (
                """ + sql[sql.find("WHERE") + 6:] + ")"
                params.insert(0, filters["tag_id"])

            # 入库方式过滤
            if "entry_type" in filters:
                sql += " AND entry_type = ?"
                params.append(filters["entry_type"])

            # 文件大小过滤
            if "min_size" in filters:
                sql += " AND size >= ?"
                params.append(filters["min_size"])

            if "max_size" in filters:
                sql += " AND size <= ?"
                params.append(filters["max_size"])

            # 日期范围过滤
            if "date_from" in filters:
                sql += " AND added_at >= ?"
                params.append(filters["date_from"])

            if "date_to" in filters:
                sql += " AND added_at < ?"
                params.append(filters["date_to"])

        # 添加排序
        sql += " ORDER BY added_at DESC"

        # 执行查询
        cursor = self.db.conn.cursor()
        cursor.execute(sql, params)

        # 返回结果
        return [dict(row) for row in cursor.fetchall()]

    def search_tags(self, query):
        """搜索标签

        Args:
            query: 搜索关键词

        Returns:
            list: 搜索结果列表
        """
        # 构建查询
        sql = "SELECT * FROM tags WHERE name LIKE ? ORDER BY name"
        params = [f"%{query}%"]

        # 执行查询
        cursor = self.db.conn.cursor()
        cursor.execute(sql, params)

        # 返回结果
        return [dict(row) for row in cursor.fetchall()]
