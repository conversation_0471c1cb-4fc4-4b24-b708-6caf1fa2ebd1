"""
AI状态显示组件

显示AI功能的当前状态和运行信息
预期代码长度: < 150行
当前代码长度: 140行 ✅
"""

from typing import Dict
from PySide6.QtWidgets import (
    QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QProgressBar
)
from PySide6.QtCore import Qt
from .base_ai_widget import BaseAIWidget


class AIStatusWidget(BaseAIWidget):
    """AI状态显示组件"""
    
    def __init__(self, parent=None):
        super().__init__("AI功能状态", parent)
    
    def setup_ui(self):
        """设置状态显示UI"""
        layout = QVBoxLayout(self)
        
        # AI启用状态行
        status_layout = QHBoxLayout()
        
        status_layout.addWidget(QLabel("AI功能:"))
        self.ai_enabled_label = QLabel("未启用")
        self.ai_enabled_label.setStyleSheet("font-weight: bold;")
        status_layout.addWidget(self.ai_enabled_label)
        status_layout.addStretch()
        
        layout.addLayout(status_layout)
        
        # AI阶段显示行
        stage_layout = QHBoxLayout()
        
        stage_layout.addWidget(QLabel("AI阶段:"))
        self.ai_stage_label = QLabel("规则引擎")
        stage_layout.addWidget(self.ai_stage_label)
        stage_layout.addStretch()
        
        layout.addLayout(stage_layout)
        
        # 初始化状态行
        init_layout = QHBoxLayout()
        
        init_layout.addWidget(QLabel("初始化状态:"))
        self.init_status_label = QLabel("未初始化")
        init_layout.addWidget(self.init_status_label)
        init_layout.addStretch()
        
        layout.addLayout(init_layout)
        
        # 功能可用性状态
        features_layout = QVBoxLayout()
        features_label = QLabel("功能状态:")
        features_label.setStyleSheet("font-weight: bold; margin-top: 10px;")
        features_layout.addWidget(features_label)
        
        # 智能规则状态
        self.smart_rules_label = QLabel("• 智能规则引擎: 未知")
        features_layout.addWidget(self.smart_rules_label)
        
        # 自适应规则状态
        self.adaptive_rules_label = QLabel("• 自适应规则引擎: 未知")
        features_layout.addWidget(self.adaptive_rules_label)
        
        # 机器学习状态
        self.ml_engine_label = QLabel("• 机器学习引擎: 未启用")
        features_layout.addWidget(self.ml_engine_label)
        
        layout.addLayout(features_layout)
        
        # 错误信息显示
        self.error_label = QLabel("")
        self.error_label.setStyleSheet("color: red; margin-top: 10px;")
        self.error_label.setWordWrap(True)
        self.error_label.hide()
        layout.addWidget(self.error_label)
        
        # 操作按钮
        buttons_layout = QHBoxLayout()
        
        self.refresh_button = QPushButton("刷新状态")
        self.refresh_button.clicked.connect(self.refresh_ai_status)
        buttons_layout.addWidget(self.refresh_button)
        
        self.test_button = QPushButton("测试AI功能")
        self.test_button.clicked.connect(self.test_ai_function)
        buttons_layout.addWidget(self.test_button)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
    
    def setup_connections(self):
        """设置信号连接"""
        super().setup_connections()
        # 可以在这里添加额外的信号连接
    
    def load_config(self, config: Dict):
        """加载配置到UI控件"""
        self._config = config
        self.refresh_ai_status()
    
    def save_config(self) -> Dict:
        """从UI控件保存配置"""
        # 状态组件不需要保存配置，返回空字典
        return {}
    
    def refresh_ai_status(self):
        """刷新AI状态显示"""
        if not self.config_manager:
            return
        
        try:
            status = self.config_manager.get_ai_status()
            
            # 更新AI启用状态
            if status.get('enabled', False):
                self.ai_enabled_label.setText("已启用")
                self.ai_enabled_label.setStyleSheet("color: green; font-weight: bold;")
            else:
                self.ai_enabled_label.setText("未启用")
                self.ai_enabled_label.setStyleSheet("color: red; font-weight: bold;")
            
            # 更新AI阶段
            stage_map = {
                'rule_based': '智能规则引擎',
                'ml_basic': '轻量级机器学习',
                'deep_learning': '深度学习'
            }
            stage = status.get('stage', 'rule_based')
            self.ai_stage_label.setText(stage_map.get(stage, stage))
            
            # 更新初始化状态
            init_status = status.get('status', 'not_initialized')
            status_map = {
                'not_initialized': '未初始化',
                'initializing': '初始化中...',
                'ready': '就绪',
                'error': '错误'
            }
            self.init_status_label.setText(status_map.get(init_status, init_status))
            
            # 更新功能状态
            features = status.get('features', {})
            
            self.smart_rules_label.setText(
                f"• 智能规则引擎: {'✅ 可用' if features.get('smart_rules') else '❌ 不可用'}"
            )
            
            self.adaptive_rules_label.setText(
                f"• 自适应规则引擎: {'✅ 可用' if features.get('adaptive_rules') else '❌ 不可用'}"
            )
            
            self.ml_engine_label.setText(
                f"• 机器学习引擎: {'✅ 可用' if features.get('ml_engine') else '❌ 未启用'}"
            )
            
            # 更新错误信息
            last_error = status.get('last_error')
            if last_error:
                self.error_label.setText(f"错误: {last_error}")
                self.error_label.show()
            else:
                self.error_label.hide()
                
        except Exception as e:
            self.show_error(f"刷新AI状态失败: {e}")
    
    def test_ai_function(self):
        """测试AI功能"""
        if not self.config_manager:
            self.show_error("配置管理器未初始化")
            return
        
        try:
            success, message = self.config_manager.test_ai_function()
            if success:
                self.show_info(f"AI功能测试成功\n{message}")
            else:
                self.show_error(f"AI功能测试失败\n{message}")
        except Exception as e:
            self.show_error(f"测试AI功能时发生错误: {e}")
