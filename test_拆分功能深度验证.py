#!/usr/bin/env python3
"""
拆分功能深度验证测试
基于本次经验教训，设计更全面的测试方案
"""

import sys
import os
import time
sys.path.append('.')

def test_backup_manager_real_functionality():
    """测试备份管理器的真实功能"""
    print("=" * 60)
    print("💾 备份管理器真实功能测试")
    print("=" * 60)
    
    try:
        from PySide6.QtWidgets import QApplication
        from smartvault.ui.main_window import MainWindow
        
        # 创建Qt应用
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = MainWindow()
        backup_manager = main_window.backup_manager
        
        print("✅ 主窗口和备份管理器创建成功")
        
        # 测试1: 检查依赖的服务是否存在
        print("\n🔍 测试1: 检查服务依赖")
        if hasattr(main_window, 'backup_service'):
            print("✅ backup_service 存在")
            
            # 测试服务方法调用
            try:
                status = main_window.backup_service.get_backup_status()
                print(f"✅ 备份服务状态获取成功: {status}")
            except Exception as e:
                print(f"❌ 备份服务状态获取失败: {e}")
                return False
        else:
            print("❌ backup_service 不存在")
            return False
        
        # 测试2: 状态栏组件创建
        print("\n🔍 测试2: 状态栏组件创建")
        try:
            backup_manager.setup_backup_status_display()
            if backup_manager.backup_status_label:
                print("✅ 备份状态标签创建成功")
                print(f"   标签文本: {backup_manager.backup_status_label.text()}")
            else:
                print("❌ 备份状态标签创建失败")
                return False
        except Exception as e:
            print(f"❌ 状态栏组件创建失败: {e}")
            return False
        
        # 测试3: 状态更新功能
        print("\n🔍 测试3: 状态更新功能")
        try:
            backup_manager.update_backup_status_display()
            print("✅ 状态更新功能正常")
        except Exception as e:
            print(f"❌ 状态更新功能失败: {e}")
            return False
        
        # 测试4: 右键菜单功能
        print("\n🔍 测试4: 右键菜单功能")
        try:
            from PySide6.QtCore import QPoint
            backup_manager.show_backup_context_menu(QPoint(100, 100))
            print("✅ 右键菜单创建成功")
        except Exception as e:
            print(f"❌ 右键菜单创建失败: {e}")
            return False
        
        # 测试5: 设置对话框打开
        print("\n🔍 测试5: 设置对话框打开")
        try:
            # 这个测试可能会实际打开对话框，所以我们只测试导入
            from smartvault.ui.dialogs.settings_dialog import SettingsDialog
            dialog = SettingsDialog(main_window)
            print("✅ 设置对话框创建成功")
            
            # 检查是否有switch_to_page方法
            if hasattr(dialog, 'switch_to_page'):
                print("✅ switch_to_page 方法存在")
            else:
                print("⚠️ switch_to_page 方法不存在，可能影响直接跳转到备份页面")
        except Exception as e:
            print(f"❌ 设置对话框创建失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 备份管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_clipboard_handler_real_functionality():
    """测试剪贴板处理器的真实功能"""
    print("\n" + "=" * 60)
    print("📋 剪贴板处理器真实功能测试")
    print("=" * 60)
    
    try:
        from PySide6.QtWidgets import QApplication
        from smartvault.ui.main_window import MainWindow
        
        # 创建Qt应用
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = MainWindow()
        clipboard_handler = main_window.clipboard_handler
        
        print("✅ 主窗口和剪贴板处理器创建成功")
        
        # 测试1: 检查依赖的服务是否存在
        print("\n🔍 测试1: 检查服务依赖")
        if hasattr(main_window, 'clipboard_service'):
            print("✅ clipboard_service 存在")
            
            # 测试服务属性
            try:
                is_monitoring = main_window.clipboard_service.is_monitoring
                print(f"✅ 剪贴板监控状态: {is_monitoring}")
            except Exception as e:
                print(f"❌ 剪贴板服务状态获取失败: {e}")
                return False
        else:
            print("❌ clipboard_service 不存在")
            return False
        
        # 测试2: 浮动窗口组件
        print("\n🔍 测试2: 浮动窗口组件")
        if hasattr(main_window, 'clipboard_floating_widget'):
            print("✅ clipboard_floating_widget 存在")
            
            # 测试浮动窗口方法
            try:
                main_window.clipboard_floating_widget.update_monitoring_status("测试状态")
                print("✅ 浮动窗口状态更新成功")
            except Exception as e:
                print(f"❌ 浮动窗口状态更新失败: {e}")
                return False
        else:
            print("❌ clipboard_floating_widget 不存在")
            return False
        
        # 测试3: 工具栏管理器依赖
        print("\n🔍 测试3: 工具栏管理器依赖")
        if hasattr(main_window, 'toolbar_manager'):
            print("✅ toolbar_manager 存在")
            
            # 测试工具栏更新方法
            if hasattr(main_window.toolbar_manager, 'update_clipboard_status'):
                print("✅ update_clipboard_status 方法存在")
            else:
                print("⚠️ update_clipboard_status 方法不存在")
        else:
            print("⚠️ toolbar_manager 不存在")
        
        # 测试4: 配置文件操作
        print("\n🔍 测试4: 配置文件操作")
        try:
            from smartvault.utils.config import get_clipboard_status, save_clipboard_status
            
            # 测试读取配置
            current_status = get_clipboard_status()
            print(f"✅ 配置读取成功: {current_status}")
            
            # 测试保存配置（保存当前状态，不改变）
            save_clipboard_status(current_status)
            print("✅ 配置保存成功")
        except Exception as e:
            print(f"❌ 配置文件操作失败: {e}")
            return False
        
        # 测试5: 演示功能
        print("\n🔍 测试5: 演示功能")
        try:
            clipboard_handler.show_clipboard_demo()
            print("✅ 演示功能执行成功")
            
            # 检查浮动窗口是否显示了演示内容
            if hasattr(main_window.clipboard_floating_widget, 'current_duplicate_info'):
                demo_info = main_window.clipboard_floating_widget.current_duplicate_info
                if demo_info and demo_info.get('type') == 'demo':
                    print("✅ 演示内容正确显示")
                else:
                    print("⚠️ 演示内容可能未正确显示")
        except Exception as e:
            print(f"❌ 演示功能失败: {e}")
            return False
        
        # 测试6: 文件定位功能（使用真实数据）
        print("\n🔍 测试6: 文件定位功能")
        try:
            from smartvault.services.file import FileService
            file_service = FileService()
            files = file_service.get_files(limit=1)
            
            if files:
                test_file_id = files[0]['id']
                print(f"📄 使用测试文件ID: {test_file_id}")
                
                # 测试文件定位
                clipboard_handler._locate_and_select_file(test_file_id)
                print("✅ 文件定位功能执行成功")
            else:
                print("⚠️ 数据库中没有文件，跳过文件定位测试")
        except Exception as e:
            print(f"❌ 文件定位功能失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 剪贴板处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration_scenarios():
    """测试集成场景"""
    print("\n" + "=" * 60)
    print("🔗 集成场景测试")
    print("=" * 60)
    
    try:
        from PySide6.QtWidgets import QApplication
        from smartvault.ui.main_window import MainWindow
        
        # 创建Qt应用
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = MainWindow()
        
        print("✅ 主窗口创建成功")
        
        # 场景1: 委托方法调用
        print("\n🔍 场景1: 委托方法调用")
        try:
            # 测试委托方法是否正确调用处理器
            main_window.show_clipboard_demo()
            print("✅ show_clipboard_demo 委托方法正常")
            
            # 检查是否实际调用了处理器
            demo_info = main_window.clipboard_floating_widget.current_duplicate_info
            if demo_info and demo_info.get('type') == 'demo':
                print("✅ 委托方法正确调用了处理器")
            else:
                print("❌ 委托方法未正确调用处理器")
                return False
        except Exception as e:
            print(f"❌ 委托方法调用失败: {e}")
            return False
        
        # 场景2: 信号连接完整性
        print("\n🔍 场景2: 信号连接完整性")
        try:
            # 检查浮动窗口信号连接
            signal_count = len(main_window.clipboard_floating_widget.open_file_requested.receivers())
            if signal_count > 0:
                print(f"✅ 浮动窗口信号已连接 ({signal_count} 个接收器)")
                
                # 测试信号发射
                main_window.clipboard_floating_widget.open_file_requested.emit("test-signal")
                print("✅ 信号发射成功")
            else:
                print("❌ 浮动窗口信号未连接")
                return False
        except Exception as e:
            print(f"❌ 信号连接测试失败: {e}")
            return False
        
        # 场景3: 启动时初始化顺序
        print("\n🔍 场景3: 启动时初始化顺序")
        try:
            # 检查各组件是否按正确顺序初始化
            components = [
                ('clipboard_handler', main_window.clipboard_handler),
                ('backup_manager', main_window.backup_manager),
                ('clipboard_floating_widget', main_window.clipboard_floating_widget),
                ('clipboard_service', main_window.clipboard_service),
                ('backup_service', main_window.backup_service)
            ]
            
            for name, component in components:
                if component:
                    print(f"✅ {name} 已初始化")
                else:
                    print(f"❌ {name} 未初始化")
                    return False
        except Exception as e:
            print(f"❌ 初始化顺序检查失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 集成场景测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始拆分功能深度验证测试")
    print("基于本次经验教训，设计更全面的测试方案")
    
    test_results = []
    
    # 执行测试
    test_results.append(("备份管理器真实功能", test_backup_manager_real_functionality()))
    test_results.append(("剪贴板处理器真实功能", test_clipboard_handler_real_functionality()))
    test_results.append(("集成场景", test_integration_scenarios()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 深度验证测试结果汇总")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n总计: {passed} 个测试通过, {failed} 个测试失败")
    
    if failed == 0:
        print("🎉 深度验证测试全部通过！拆分功能完全正常！")
        return True
    else:
        print("⚠️ 存在问题，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
