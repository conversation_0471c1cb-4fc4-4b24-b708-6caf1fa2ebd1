"""
快速标签菜单集成测试
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PySide6.QtWidgets import QApplication, QMenu
from PySide6.QtCore import QObject
from smartvault.ui.components.quick_tag_menu import QuickTagMenu
from smartvault.services.tag_service import TagService
from smartvault.data.database import Database


class TestQuickTagIntegration(unittest.TestCase):
    """快速标签菜单集成测试类"""

    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication(sys.argv)
        else:
            cls.app = QApplication.instance()

    def setUp(self):
        """设置测试"""
        # 创建内存数据库
        self.db = Database(":memory:")
        self.tag_service = TagService()

        # 创建快速标签菜单实例
        self.quick_tag_menu = QuickTagMenu()

        # 创建父菜单
        self.parent_menu = QMenu()

        # 创建测试标签
        self.setup_test_tags()

        # 模拟文件ID
        self.file_ids = ["test_file_1", "test_file_2"]

    def setup_test_tags(self):
        """设置测试标签"""
        # 创建顶级标签
        self.work_tag_id = self.tag_service.create_tag("工作", "#ff6b6b")
        self.personal_tag_id = self.tag_service.create_tag("个人", "#4ecdc4")
        self.important_tag_id = self.tag_service.create_tag("重要", "#45b7d1")

        # 创建子标签
        self.project_a_tag_id = self.tag_service.create_tag("项目A", "#ff9999", self.work_tag_id)
        self.project_b_tag_id = self.tag_service.create_tag("项目B", "#ffaaaa", self.work_tag_id)
        self.family_tag_id = self.tag_service.create_tag("家庭", "#7fdddd", self.personal_tag_id)

    def _clear_file_tags(self):
        """清理文件标签关联"""
        cursor = self.db.conn.cursor()
        cursor.execute("DELETE FROM file_tags")
        self.db.conn.commit()

        # 清理缓存
        self.quick_tag_menu.file_tags_cache = {}

    def test_create_menu_with_real_tags(self):
        """测试使用真实标签创建菜单"""
        # 创建菜单
        menu = self.quick_tag_menu.create_menu(self.parent_menu, self.file_ids)

        # 验证菜单创建成功
        self.assertIsInstance(menu, QMenu)
        self.assertEqual(menu.title(), "快速标签")

        # 验证菜单项
        actions = menu.actions()
        action_texts = [action.text() for action in actions if action.text()]

        # 应该包含顶级标签
        self.assertTrue(any("工作" in text for text in action_texts))
        self.assertTrue(any("个人" in text for text in action_texts))
        self.assertTrue(any("重要" in text for text in action_texts))

        # 应该包含管理选项
        self.assertTrue(any("管理标签" in text for text in action_texts))

    def test_tag_operations_with_real_database(self):
        """测试使用真实数据库的标签操作"""
        # 清理可能存在的标签关联
        self._clear_file_tags()

        # 设置文件ID
        self.quick_tag_menu.file_ids = self.file_ids
        self.quick_tag_menu._load_file_tags()

        # 初始状态：无标签
        status = self.quick_tag_menu._get_tag_status(self.work_tag_id)
        self.assertEqual(status, "none")

        # 添加标签到文件
        self.quick_tag_menu._add_tag_to_files(self.work_tag_id)

        # 重新加载标签
        self.quick_tag_menu._load_file_tags()

        # 验证状态变为全部有标签
        status = self.quick_tag_menu._get_tag_status(self.work_tag_id)
        self.assertEqual(status, "all")

        # 验证数据库中确实有标签
        file1_tags = self.tag_service.get_file_tags(self.file_ids[0])
        file2_tags = self.tag_service.get_file_tags(self.file_ids[1])

        self.assertTrue(any(tag['id'] == self.work_tag_id for tag in file1_tags))
        self.assertTrue(any(tag['id'] == self.work_tag_id for tag in file2_tags))

    def test_partial_tag_status(self):
        """测试部分标签状态"""
        # 设置文件ID
        self.quick_tag_menu.file_ids = self.file_ids

        # 只为第一个文件添加标签（使用直接方法）
        self.quick_tag_menu._add_tag_to_file_direct(self.file_ids[0], self.work_tag_id)

        # 重新加载标签
        self.quick_tag_menu._load_file_tags()

        # 验证状态为部分有标签
        status = self.quick_tag_menu._get_tag_status(self.work_tag_id)
        self.assertEqual(status, "partial")

    def test_toggle_tag_functionality(self):
        """测试标签切换功能"""
        # 清理可能存在的标签关联
        self._clear_file_tags()

        # 设置文件ID
        self.quick_tag_menu.file_ids = self.file_ids
        self.quick_tag_menu._load_file_tags()

        # 初始状态：无标签
        self.assertFalse(self.quick_tag_menu._has_any_tags())

        # 切换标签（应该添加）
        self.quick_tag_menu._toggle_tag(self.work_tag_id)

        # 重新加载并验证
        self.quick_tag_menu._load_file_tags()
        self.assertTrue(self.quick_tag_menu._has_any_tags())

        # 再次切换标签（应该移除）
        self.quick_tag_menu._toggle_tag(self.work_tag_id)

        # 重新加载并验证
        self.quick_tag_menu._load_file_tags()
        self.assertFalse(self.quick_tag_menu._has_any_tags())

    def test_clear_all_tags_functionality(self):
        """测试清除所有标签功能"""
        # 设置文件ID
        self.quick_tag_menu.file_ids = self.file_ids

        # 为文件添加多个标签（使用直接方法）
        self.quick_tag_menu._add_tag_to_file_direct(self.file_ids[0], self.work_tag_id)
        self.quick_tag_menu._add_tag_to_file_direct(self.file_ids[0], self.personal_tag_id)
        self.quick_tag_menu._add_tag_to_file_direct(self.file_ids[1], self.important_tag_id)

        # 重新加载标签
        self.quick_tag_menu._load_file_tags()

        # 验证有标签
        self.assertTrue(self.quick_tag_menu._has_any_tags())

        # 模拟用户确认清除
        with patch('smartvault.ui.components.quick_tag_menu.QMessageBox.question') as mock_question:
            from PySide6.QtWidgets import QMessageBox
            mock_question.return_value = QMessageBox.Yes

            # 清除所有标签
            self.quick_tag_menu._clear_all_tags()

        # 重新加载并验证
        self.quick_tag_menu._load_file_tags()
        self.assertFalse(self.quick_tag_menu._has_any_tags())

        # 验证数据库中确实没有标签了
        file1_tags = self.tag_service.get_file_tags(self.file_ids[0])
        file2_tags = self.tag_service.get_file_tags(self.file_ids[1])

        self.assertEqual(len(file1_tags), 0)
        self.assertEqual(len(file2_tags), 0)

    def test_hierarchical_tags_in_menu(self):
        """测试层级标签在菜单中的显示"""
        # 创建菜单
        menu = self.quick_tag_menu.create_menu(self.parent_menu, self.file_ids)

        # 获取所有子菜单
        submenus = []
        for action in menu.actions():
            if action.menu():
                submenus.append(action.menu())

        # 应该有子菜单（因为有子标签）
        self.assertTrue(len(submenus) > 0)

        # 验证子菜单包含子标签
        submenu_found = False
        for submenu in submenus:
            submenu_actions = submenu.actions()
            submenu_texts = [action.text() for action in submenu_actions if action.text()]

            if any("项目A" in text for text in submenu_texts):
                submenu_found = True
                break

        self.assertTrue(submenu_found, "应该找到包含子标签的子菜单")

    def test_color_icon_creation(self):
        """测试颜色图标创建"""
        # 测试创建颜色图标
        icon = self.quick_tag_menu._create_color_icon("#ff0000")

        # 验证图标不为空
        self.assertIsNotNone(icon)

        # 验证图标有像素图
        pixmap = icon.pixmap(16, 16)
        self.assertFalse(pixmap.isNull())

    def test_signals_emission(self):
        """测试信号发射"""
        # 创建信号接收器
        signal_received = []

        def on_tags_changed():
            signal_received.append(True)

        # 连接信号
        self.quick_tag_menu.tags_changed.connect(on_tags_changed)

        # 设置文件ID
        self.quick_tag_menu.file_ids = self.file_ids
        self.quick_tag_menu._load_file_tags()

        # 执行标签操作
        self.quick_tag_menu._toggle_tag(self.work_tag_id)

        # 验证信号被发射
        self.assertTrue(len(signal_received) > 0)

    def tearDown(self):
        """清理测试"""
        self.parent_menu.deleteLater()
        self.db.close()


if __name__ == '__main__':
    unittest.main()
