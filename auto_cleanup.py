"""
自动清理工具 - 无需用户交互
"""

import os
import sys
import sqlite3
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import psutil
    HAS_PSUTIL = True
except ImportError:
    HAS_PSUTIL = False


def auto_cleanup():
    """自动清理所有资源"""
    print("🧹 SmartVault 自动清理工具")
    print("=" * 40)
    
    # 1. 清理数据库锁定
    print("🔓 清理数据库锁定...")
    try:
        from smartvault.utils.config import load_config
        config = load_config()
        library_path = config["library_path"]
        db_path = os.path.join(library_path, "data", "smartvault.db")
        
        if os.path.exists(db_path):
            # 删除锁定文件
            lock_files = [
                db_path + "-wal",
                db_path + "-shm", 
                db_path + "-journal"
            ]
            
            for lock_file in lock_files:
                if os.path.exists(lock_file):
                    try:
                        os.remove(lock_file)
                        print(f"  ✅ 删除: {os.path.basename(lock_file)}")
                    except:
                        pass
            
            # 重建数据库连接
            try:
                conn = sqlite3.connect(db_path, timeout=1.0)
                conn.execute("PRAGMA journal_mode=DELETE")
                conn.execute("PRAGMA journal_mode=WAL")
                conn.close()
                print("  ✅ 数据库重建完成")
            except:
                print("  ⚠️  数据库重建失败")
        else:
            print("  ✅ 数据库文件不存在")
            
    except Exception as e:
        print(f"  ❌ 数据库清理失败: {e}")
    
    # 2. 清理进程
    if HAS_PSUTIL:
        print("🔪 清理残留进程...")
        killed_count = 0
        current_pid = os.getpid()
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.pid == current_pid:
                        continue
                        
                    if proc.info['name'] and 'python' in proc.info['name'].lower():
                        cmdline = proc.info['cmdline']
                        if cmdline and any('smartvault' in arg.lower() or 'run.py' in arg.lower() for arg in cmdline):
                            proc.terminate()
                            killed_count += 1
                            try:
                                proc.wait(timeout=2)
                            except:
                                proc.kill()
                                
                except:
                    continue
            
            print(f"  ✅ 清理了 {killed_count} 个进程")
        except:
            print("  ⚠️  进程清理失败")
    else:
        print("🔪 跳过进程清理 (psutil不可用)")
    
    # 3. 清理Qt设置
    print("⚙️  清理Qt设置...")
    try:
        from PySide6.QtCore import QSettings
        settings = QSettings("SmartVault", "MainWindow")
        
        # 只清理可能有问题的设置，保留用户偏好
        problematic_keys = ["geometry", "splitter_sizes"]
        for key in problematic_keys:
            if settings.contains(key):
                settings.remove(key)
                print(f"  ✅ 清理设置: {key}")
        
        settings.sync()
        print("  ✅ Qt设置清理完成")
    except Exception as e:
        print(f"  ❌ Qt设置清理失败: {e}")
    
    # 4. 清理临时文件
    print("🗑️  清理临时文件...")
    try:
        import tempfile
        temp_dir = tempfile.gettempdir()
        cleaned_count = 0
        
        for root, dirs, files in os.walk(temp_dir):
            for file in files:
                if 'smartvault' in file.lower():
                    try:
                        os.remove(os.path.join(root, file))
                        cleaned_count += 1
                    except:
                        pass
        
        print(f"  ✅ 清理了 {cleaned_count} 个临时文件")
    except:
        print("  ⚠️  临时文件清理失败")
    
    print("=" * 40)
    print("✅ 自动清理完成！")
    print("\n💡 现在可以尝试重新启动SmartVault")


if __name__ == "__main__":
    auto_cleanup()
