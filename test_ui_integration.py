#!/usr/bin/env python3
"""
测试UI集成（不依赖PyQt5）
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_import_dialogs():
    """测试对话框导入"""
    print("🔍 测试对话框导入...")
    
    try:
        # 测试核心服务导入
        from smartvault.services.auto_tag_service import (
            AutoTagRule, Condition, ConditionGroup, ConditionType, LogicOperator
        )
        print("✅ 核心服务导入成功")
        
        # 测试创建多条件规则
        condition_group = ConditionGroup(
            operator=LogicOperator.AND,
            conditions=[
                Condition(type=ConditionType.FILE_EXTENSION, value="pdf"),
                Condition(type=ConditionType.FILE_SIZE, value=">1MB")
            ]
        )
        
        rule = AutoTagRule(
            id="test_rule",
            name="测试多条件规则",
            tag_names=["测试", "多条件"],
            condition_group=condition_group
        )
        
        print("✅ 多条件规则创建成功")
        
        # 测试规则描述
        description = rule.get_description()
        print(f"✅ 规则描述: {description}")
        
        # 测试规则匹配
        test_file = {"name": "document.pdf", "size": 2 * 1024 * 1024}
        matches = rule.matches(test_file)
        print(f"✅ 规则匹配测试: {matches}")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_rule_scenarios():
    """测试规则场景"""
    print("\n🔍 测试规则场景...")
    
    try:
        from smartvault.services.auto_tag_service import (
            AutoTagRule, Condition, ConditionGroup, ConditionType, LogicOperator
        )
        
        # 场景1：简单单条件规则（向后兼容）
        simple_rule = AutoTagRule(
            id="simple_rule",
            name="PDF文档规则",
            tag_names=["PDF", "文档"],
            condition_type=ConditionType.FILE_EXTENSION,
            condition_value="pdf"
        )
        
        # 场景2：复杂多条件规则
        complex_condition = ConditionGroup(
            operator=LogicOperator.AND,
            conditions=[
                ConditionGroup(
                    operator=LogicOperator.OR,
                    conditions=[
                        Condition(type=ConditionType.FILE_EXTENSION, value="pdf"),
                        Condition(type=ConditionType.FILE_EXTENSION, value="doc,docx")
                    ]
                ),
                ConditionGroup(
                    operator=LogicOperator.OR,
                    conditions=[
                        Condition(type=ConditionType.FILE_SIZE, value=">5MB"),
                        Condition(type=ConditionType.FILE_NAME_PATTERN, value="重要|urgent")
                    ]
                )
            ]
        )
        
        complex_rule = AutoTagRule(
            id="complex_rule",
            name="重要文档规则",
            tag_names=["重要文档", "高优先级"],
            condition_group=complex_condition
        )
        
        # 测试文件
        test_files = [
            {"name": "report.pdf", "size": 1024 * 1024, "desc": "小PDF"},
            {"name": "重要报告.pdf", "size": 1024 * 1024, "desc": "重要小PDF"},
            {"name": "large_doc.doc", "size": 10 * 1024 * 1024, "desc": "大DOC"},
            {"name": "normal.txt", "size": 1024, "desc": "普通TXT"},
        ]
        
        print("\n📋 简单规则测试:")
        for file_info in test_files:
            result = simple_rule.matches(file_info)
            print(f"  📁 {file_info['desc']}: {result}")
        
        print("\n📋 复杂规则测试:")
        for file_info in test_files:
            result = complex_rule.matches(file_info)
            print(f"  📁 {file_info['desc']}: {result}")
        
        print(f"\n📝 简单规则描述: {simple_rule.get_description()}")
        print(f"📝 复杂规则描述: {complex_rule.get_description()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 场景测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_service_integration():
    """测试服务集成"""
    print("\n🔍 测试服务集成...")
    
    try:
        from smartvault.services.auto_tag_service import (
            AutoTagService, AutoTagRule, Condition, ConditionGroup, 
            ConditionType, LogicOperator
        )
        
        service = AutoTagService()
        
        # 添加混合规则
        rules = [
            # 简单规则
            AutoTagRule(
                id="rule1",
                name="图片规则",
                tag_names=["图片"],
                condition_type=ConditionType.FILE_TYPE,
                condition_value="图片"
            ),
            
            # 复杂规则
            AutoTagRule(
                id="rule2",
                name="重要大文件规则",
                tag_names=["重要", "大文件"],
                condition_group=ConditionGroup(
                    operator=LogicOperator.AND,
                    conditions=[
                        Condition(type=ConditionType.FILE_SIZE, value=">10MB"),
                        Condition(type=ConditionType.FILE_NAME_PATTERN, value="重要|important")
                    ]
                )
            )
        ]
        
        for rule in rules:
            service.add_rule(rule)
        
        # 测试文件
        test_file = {"name": "重要数据.jpg", "size": 15 * 1024 * 1024}
        auto_tags = service.get_auto_tags_for_file(test_file)
        
        print(f"📁 测试文件: {test_file['name']} ({test_file['size']//1024//1024}MB)")
        print(f"🏷️ 自动标签: {auto_tags}")
        
        return True
        
    except Exception as e:
        print(f"❌ 服务集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("=" * 60)
    print("🚀 UI集成测试")
    print("=" * 60)
    
    tests = [
        ("对话框导入", test_import_dialogs),
        ("规则场景", test_rule_scenarios),
        ("服务集成", test_service_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")
    
    print(f"\n{'='*60}")
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！多条件自动标签功能已就绪。")
        print("\n📋 功能总结:")
        print("✅ 支持单条件规则（向后兼容）")
        print("✅ 支持多条件组合规则（AND/OR逻辑）")
        print("✅ 支持嵌套条件组")
        print("✅ 智能规则描述生成")
        print("✅ 服务层集成完成")
        print("\n🎯 下一步: 在设置页面测试新的UI按钮")
    else:
        print("❌ 部分测试失败，需要进一步调试")


if __name__ == "__main__":
    main()
