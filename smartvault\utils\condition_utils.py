"""
条件描述工具模块
提供自动标签条件的描述功能
"""

from smartvault.services.auto_tag_service import ConditionType


def describe_single_condition(condition_type: ConditionType, condition_value: str) -> str:
    """描述单个条件
    
    Args:
        condition_type: 条件类型
        condition_value: 条件值
        
    Returns:
        str: 条件描述
    """
    type_names = {
        ConditionType.FILE_EXTENSION: "文件扩展名",
        ConditionType.FILE_NAME_PATTERN: "文件名模式",
        ConditionType.FILE_PATH_PATTERN: "文件路径模式",
        ConditionType.FILE_SIZE_RANGE: "文件大小范围",
        ConditionType.FILE_TYPE: "文件类型",
        ConditionType.FILE_SIZE: "文件大小",
        ConditionType.FILE_NAME_REGEX: "文件名正则表达式"
    }

    type_name = type_names.get(condition_type, "未知条件")
    return f"{type_name}匹配'{condition_value}'"
