"""
启动问题诊断和修复工具
"""

import os
import sys
import time
import sqlite3
import threading
from pathlib import Path

try:
    import psutil
    HAS_PSUTIL = True
except ImportError:
    HAS_PSUTIL = False

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class StartupDiagnostic:
    """启动诊断器"""

    def __init__(self):
        """初始化诊断器"""
        self.issues = []
        self.fixes = []

    def diagnose_startup_issues(self):
        """诊断启动问题"""
        print("🔍 开始诊断启动问题...")

        # 1. 检查进程残留
        self._check_process_residue()

        # 2. 检查数据库锁定
        self._check_database_locks()

        # 3. 检查文件句柄
        self._check_file_handles()

        # 4. 检查配置文件
        self._check_config_integrity()

        # 5. 检查资源文件
        self._check_resource_files()

        # 6. 检查线程残留
        self._check_thread_residue()

        return self.issues

    def _check_process_residue(self):
        """检查进程残留"""
        if not HAS_PSUTIL:
            print("⚠️  psutil模块不可用，跳过进程检查")
            return

        try:
            current_pid = os.getpid()
            python_processes = []

            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'python' in proc.info['name'].lower():
                        cmdline = proc.info['cmdline']
                        if cmdline and any('smartvault' in arg.lower() or 'run.py' in arg for arg in cmdline):
                            if proc.info['pid'] != current_pid:
                                python_processes.append(proc.info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            if python_processes:
                self.issues.append(f"发现 {len(python_processes)} 个SmartVault进程残留")
                for proc in python_processes:
                    print(f"  残留进程: PID {proc['pid']}")
            else:
                print("✅ 无进程残留")

        except Exception as e:
            self.issues.append(f"检查进程残留失败: {e}")

    def _check_database_locks(self):
        """检查数据库锁定"""
        try:
            from smartvault.utils.config import load_config
            config = load_config()
            library_path = config["library_path"]
            db_path = os.path.join(library_path, "data", "smartvault.db")

            if not os.path.exists(db_path):
                print("✅ 数据库文件不存在，无锁定问题")
                return

            # 尝试连接数据库
            try:
                conn = sqlite3.connect(db_path, timeout=1.0)
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM sqlite_master")
                conn.close()
                print("✅ 数据库连接正常")
            except sqlite3.OperationalError as e:
                if "locked" in str(e).lower():
                    self.issues.append("数据库被锁定")
                else:
                    self.issues.append(f"数据库连接错误: {e}")

        except Exception as e:
            self.issues.append(f"检查数据库锁定失败: {e}")

    def _check_file_handles(self):
        """检查文件句柄"""
        if not HAS_PSUTIL:
            print("⚠️  psutil模块不可用，跳过文件句柄检查")
            return

        try:
            current_process = psutil.Process()
            open_files = current_process.open_files()

            smartvault_files = [f for f in open_files if 'smartvault' in f.path.lower()]

            if len(open_files) > 100:
                self.issues.append(f"文件句柄过多: {len(open_files)}")

            if smartvault_files:
                print(f"📁 SmartVault相关文件句柄: {len(smartvault_files)}")
                for f in smartvault_files[:5]:  # 只显示前5个
                    print(f"  {f.path}")
            else:
                print("✅ 无SmartVault相关文件句柄残留")

        except Exception as e:
            print(f"⚠️  检查文件句柄失败: {e}")

    def _check_config_integrity(self):
        """检查配置文件完整性"""
        try:
            from smartvault.utils.config import load_config, get_config_path

            config_path = get_config_path()
            if not os.path.exists(config_path):
                self.issues.append("配置文件不存在")
                return

            config = load_config()
            required_keys = ["library_path", "default_entry_type"]

            missing_keys = [key for key in required_keys if key not in config]
            if missing_keys:
                self.issues.append(f"配置文件缺少必要键: {missing_keys}")
            else:
                print("✅ 配置文件完整性检查通过")

        except Exception as e:
            self.issues.append(f"配置文件检查失败: {e}")

    def _check_resource_files(self):
        """检查资源文件"""
        try:
            from smartvault.ui.resources import get_icon_path

            # 检查关键图标文件
            icon_files = ["app.png", "folder.png", "file.png"]
            missing_icons = []

            for icon in icon_files:
                try:
                    icon_path = get_icon_path(icon.replace('.png', ''))
                    if not os.path.exists(icon_path):
                        missing_icons.append(icon)
                except:
                    missing_icons.append(icon)

            if missing_icons:
                self.issues.append(f"缺少图标文件: {missing_icons}")
            else:
                print("✅ 资源文件检查通过")

        except Exception as e:
            self.issues.append(f"资源文件检查失败: {e}")

    def _check_thread_residue(self):
        """检查线程残留"""
        try:
            active_threads = threading.active_count()
            thread_names = [t.name for t in threading.enumerate()]

            if active_threads > 5:  # 主线程 + 一些系统线程
                self.issues.append(f"活跃线程过多: {active_threads}")
                print(f"  活跃线程: {thread_names}")
            else:
                print(f"✅ 线程数量正常: {active_threads}")

        except Exception as e:
            print(f"⚠️  检查线程失败: {e}")

    def fix_issues(self):
        """修复发现的问题"""
        if not self.issues:
            print("🎉 没有发现问题，无需修复！")
            return

        print(f"\n🔧 开始修复 {len(self.issues)} 个问题...")

        for issue in self.issues:
            try:
                if "进程残留" in issue:
                    self._fix_process_residue()
                elif "数据库被锁定" in issue:
                    self._fix_database_lock()
                elif "配置文件" in issue:
                    self._fix_config_issues()
                elif "文件句柄过多" in issue:
                    self._fix_file_handles()
                else:
                    print(f"⚠️  暂不支持自动修复: {issue}")
            except Exception as e:
                print(f"❌ 修复失败 '{issue}': {e}")

    def _fix_process_residue(self):
        """修复进程残留"""
        if not HAS_PSUTIL:
            print("⚠️  psutil模块不可用，无法自动修复进程残留")
            return

        try:
            current_pid = os.getpid()
            killed_count = 0

            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'python' in proc.info['name'].lower():
                        cmdline = proc.info['cmdline']
                        if cmdline and any('smartvault' in arg.lower() or 'run.py' in arg for arg in cmdline):
                            if proc.info['pid'] != current_pid:
                                proc.terminate()
                                killed_count += 1
                                print(f"  终止进程: PID {proc.info['pid']}")
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            if killed_count > 0:
                self.fixes.append(f"终止了 {killed_count} 个残留进程")
                time.sleep(1)  # 等待进程完全终止

        except Exception as e:
            print(f"❌ 修复进程残留失败: {e}")

    def _fix_database_lock(self):
        """修复数据库锁定"""
        try:
            from smartvault.utils.config import load_config
            config = load_config()
            library_path = config["library_path"]
            db_path = os.path.join(library_path, "data", "smartvault.db")

            # 尝试强制解锁
            if os.path.exists(db_path):
                # 创建备份
                backup_path = db_path + ".backup"
                if not os.path.exists(backup_path):
                    import shutil
                    shutil.copy2(db_path, backup_path)
                    print(f"  已创建数据库备份: {backup_path}")

                # 尝试重新连接
                conn = sqlite3.connect(db_path, timeout=5.0)
                conn.execute("PRAGMA journal_mode=WAL")
                conn.close()

                self.fixes.append("修复数据库锁定")
                print("✅ 数据库锁定已修复")

        except Exception as e:
            print(f"❌ 修复数据库锁定失败: {e}")

    def _fix_config_issues(self):
        """修复配置问题"""
        try:
            from smartvault.utils.config import create_default_config, save_config

            # 创建默认配置
            default_config = create_default_config()
            save_config(default_config)

            self.fixes.append("重新创建配置文件")
            print("✅ 配置文件已修复")

        except Exception as e:
            print(f"❌ 修复配置失败: {e}")

    def _fix_file_handles(self):
        """修复文件句柄问题"""
        try:
            import gc
            gc.collect()  # 强制垃圾回收

            self.fixes.append("执行垃圾回收")
            print("✅ 已执行垃圾回收")

        except Exception as e:
            print(f"❌ 修复文件句柄失败: {e}")

    def generate_report(self):
        """生成诊断报告"""
        print("\n" + "="*60)
        print("📊 启动问题诊断报告")
        print("="*60)

        print(f"发现问题数: {len(self.issues)}")
        print(f"已修复数: {len(self.fixes)}")

        if self.issues:
            print("\n🔍 发现的问题:")
            for i, issue in enumerate(self.issues, 1):
                print(f"  {i}. {issue}")

        if self.fixes:
            print("\n🔧 已应用的修复:")
            for i, fix in enumerate(self.fixes, 1):
                print(f"  {i}. {fix}")

        if len(self.fixes) >= len(self.issues):
            print("\n🎉 所有问题已修复！可以尝试重新启动。")
        elif self.fixes:
            print(f"\n⚠️  还有 {len(self.issues) - len(self.fixes)} 个问题需要手动处理")
        else:
            print("\n❌ 没有问题被自动修复，建议手动检查")


def main():
    """主函数"""
    print("🚀 SmartVault 启动问题诊断工具")
    print("="*40)

    diagnostic = StartupDiagnostic()

    # 诊断问题
    issues = diagnostic.diagnose_startup_issues()

    if issues:
        print(f"\n⚠️  发现 {len(issues)} 个问题")

        # 询问是否修复
        response = input("\n是否尝试自动修复这些问题？(y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            diagnostic.fix_issues()

    # 生成报告
    diagnostic.generate_report()

    # 建议重启
    if diagnostic.fixes:
        print("\n💡 建议:")
        print("  1. 关闭所有终端窗口")
        print("  2. 等待5秒钟")
        print("  3. 重新打开终端并启动程序")


if __name__ == "__main__":
    main()
