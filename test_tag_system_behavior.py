#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
标签系统行为测试 - 定义第二阶段标签功能的预期行为

这个测试文件定义了标签系统应该具备的核心行为，
作为第二阶段开发的指导和验收标准。
"""

import os
import sys
import tempfile
import shutil
import pytest
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from smartvault.services.tag_service import TagService
from smartvault.services.file import FileService
from smartvault.data.database import Database


class TestTagSystemBehavior:
    """标签系统行为测试类"""

    def setup_method(self):
        """测试前准备"""
        # 创建临时数据库
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = os.path.join(self.temp_dir, "test.db")
        self.db = Database(self.db_path)

        # 创建服务实例
        self.tag_service = TagService()
        self.tag_service._db = self.db  # 注入测试数据库

        self.file_service = FileService()
        self.file_service._db = self.db  # 注入测试数据库

        # 创建测试文件
        self.test_file = os.path.join(self.temp_dir, "test.txt")
        with open(self.test_file, 'w', encoding='utf-8') as f:
            f.write("测试文件内容")

    def teardown_method(self):
        """测试后清理"""
        if hasattr(self, 'db') and self.db:
            self.db.close()
        if hasattr(self, 'temp_dir') and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

    def test_create_basic_tag(self):
        """测试创建基本标签

        行为期望：
        1. 用户可以创建标签
        2. 标签有名称和颜色
        3. 标签在数据库中正确存储
        """
        # 创建标签
        tag_id = self.tag_service.create_tag("工作文档", "#FF5722")

        # 验证标签创建成功
        assert tag_id is not None
        assert len(tag_id) > 0

        # 验证标签信息正确
        tag = self.tag_service.get_tag_by_id(tag_id)
        assert tag is not None
        assert tag["name"] == "工作文档"
        assert tag["color"] == "#FF5722"
        assert tag["parent_id"] is None

    def test_create_hierarchical_tags(self):
        """测试创建层级标签

        行为期望：
        1. 用户可以创建父标签
        2. 用户可以在父标签下创建子标签
        3. 标签层级关系正确存储
        """
        # 创建父标签
        parent_id = self.tag_service.create_tag("项目文档", "#2196F3")

        # 创建子标签
        child_id = self.tag_service.create_tag("需求文档", "#03A9F4", parent_id)

        # 验证层级关系
        parent_tag = self.tag_service.get_tag_by_id(parent_id)
        child_tag = self.tag_service.get_tag_by_id(child_id)

        assert parent_tag["parent_id"] is None
        assert child_tag["parent_id"] == parent_id

        # 验证可以获取子标签列表
        children = self.tag_service.get_child_tags(parent_id)
        assert len(children) == 1
        assert children[0]["id"] == child_id

    def test_add_tag_to_file(self):
        """测试为文件添加标签

        行为期望：
        1. 用户可以为文件添加标签
        2. 一个文件可以有多个标签
        3. 标签-文件关联正确存储
        """
        # 先添加文件到库
        file_id = self.file_service.add_file(self.test_file, "link")

        # 创建标签
        tag1_id = self.tag_service.create_tag("重要", "#F44336")
        tag2_id = self.tag_service.create_tag("工作", "#FF9800")

        # 为文件添加标签
        result1 = self.tag_service.add_tag_to_file(file_id, tag1_id)
        result2 = self.tag_service.add_tag_to_file(file_id, tag2_id)

        assert result1 is True
        assert result2 is True

        # 验证文件的标签
        file_tags = self.tag_service.get_file_tags(file_id)
        assert len(file_tags) == 2

        tag_names = [tag["name"] for tag in file_tags]
        assert "重要" in tag_names
        assert "工作" in tag_names

    def test_remove_tag_from_file(self):
        """测试从文件移除标签

        行为期望：
        1. 用户可以从文件移除标签
        2. 移除标签不影响其他文件的标签
        3. 移除标签不删除标签本身
        """
        # 准备数据
        file_id = self.file_service.add_file(self.test_file, "link")
        tag_id = self.tag_service.create_tag("临时", "#9E9E9E")
        self.tag_service.add_tag_to_file(file_id, tag_id)

        # 移除标签
        result = self.tag_service.remove_tag_from_file(file_id, tag_id)
        assert result is True

        # 验证标签已移除
        file_tags = self.tag_service.get_file_tags(file_id)
        assert len(file_tags) == 0

        # 验证标签本身仍存在
        tag = self.tag_service.get_tag_by_id(tag_id)
        assert tag is not None

    def test_search_files_by_tag(self):
        """测试按标签搜索文件

        行为期望：
        1. 用户可以按标签筛选文件
        2. 搜索结果只包含有该标签的文件
        3. 支持按多个标签搜索
        """
        # 准备测试数据
        file1_id = self.file_service.add_file(self.test_file, "link")

        # 创建第二个测试文件
        test_file2 = os.path.join(self.temp_dir, "test2.txt")
        with open(test_file2, 'w', encoding='utf-8') as f:
            f.write("第二个测试文件")
        file2_id = self.file_service.add_file(test_file2, "link")

        # 创建标签
        work_tag_id = self.tag_service.create_tag("工作", "#FF9800")
        personal_tag_id = self.tag_service.create_tag("个人", "#4CAF50")

        # 为文件添加标签
        self.tag_service.add_tag_to_file(file1_id, work_tag_id)
        self.tag_service.add_tag_to_file(file2_id, personal_tag_id)
        self.tag_service.add_tag_to_file(file2_id, work_tag_id)  # file2有两个标签

        # 按工作标签搜索
        work_files = self.tag_service.get_files_by_tag(work_tag_id)
        assert len(work_files) == 2

        # 按个人标签搜索
        personal_files = self.tag_service.get_files_by_tag(personal_tag_id)
        assert len(personal_files) == 1
        assert personal_files[0]["id"] == file2_id

    def test_delete_tag(self):
        """测试删除标签

        行为期望：
        1. 用户可以删除标签
        2. 删除标签时自动移除所有文件关联
        3. 删除父标签时处理子标签（可选：级联删除或移动到根级）
        """
        # 准备数据
        file_id = self.file_service.add_file(self.test_file, "link")
        tag_id = self.tag_service.create_tag("待删除", "#607D8B")
        self.tag_service.add_tag_to_file(file_id, tag_id)

        # 验证标签和关联存在
        assert self.tag_service.get_tag_by_id(tag_id) is not None
        assert len(self.tag_service.get_file_tags(file_id)) == 1

        # 删除标签
        result = self.tag_service.delete_tag(tag_id)
        assert result is True

        # 验证标签已删除
        assert self.tag_service.get_tag_by_id(tag_id) is None

        # 验证文件关联已移除
        assert len(self.tag_service.get_file_tags(file_id)) == 0

    def test_get_all_tags(self):
        """测试获取所有标签

        行为期望：
        1. 用户可以获取所有标签列表
        2. 标签按创建时间或名称排序
        3. 包含标签的使用统计（有多少文件使用了该标签）
        """
        # 创建多个标签
        tag1_id = self.tag_service.create_tag("标签1", "#F44336")
        tag2_id = self.tag_service.create_tag("标签2", "#2196F3")
        tag3_id = self.tag_service.create_tag("标签3", "#4CAF50")

        # 获取所有标签
        all_tags = self.tag_service.get_all_tags()
        assert len(all_tags) == 3

        # 验证标签信息完整
        tag_names = [tag["name"] for tag in all_tags]
        assert "标签1" in tag_names
        assert "标签2" in tag_names
        assert "标签3" in tag_names

    def test_tag_usage_statistics(self):
        """测试标签使用统计

        行为期望：
        1. 可以获取每个标签被多少文件使用
        2. 可以获取最常用的标签
        3. 可以获取未使用的标签
        """
        # 准备数据
        file_id = self.file_service.add_file(self.test_file, "link")

        used_tag_id = self.tag_service.create_tag("已使用", "#FF9800")
        unused_tag_id = self.tag_service.create_tag("未使用", "#9E9E9E")

        self.tag_service.add_tag_to_file(file_id, used_tag_id)

        # 获取标签统计
        tag_stats = self.tag_service.get_tag_statistics()

        # 验证统计信息
        used_stat = next((stat for stat in tag_stats if stat["tag_id"] == used_tag_id), None)
        unused_stat = next((stat for stat in tag_stats if stat["tag_id"] == unused_tag_id), None)

        assert used_stat is not None
        assert used_stat["file_count"] == 1

        assert unused_stat is not None
        assert unused_stat["file_count"] == 0


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
