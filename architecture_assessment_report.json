{"assessment_date": "2025-05-26 14:31:16", "summary": {"total_files": 94, "total_lines": 24636, "high_risk_files": 8, "medium_risk_files": 9, "architecture_health": "B (一般)"}, "top_risk_files": ["FileMetrics(path='smartvault\\\\ui\\\\main_window\\\\core.py', lines=1439, classes=1, methods=49, complexity_score=34.29, last_modified='2025-05-26', risk_level='🔴 高风险')", "FileMetrics(path='smartvault\\\\ui\\\\themes.py', lines=1234, classes=1, methods=8, complexity_score=14.74, last_modified='2025-05-26', risk_level='🔴 高风险')", "FileMetrics(path='smartvault\\\\ui\\\\views\\\\file_table_view.py', lines=1154, classes=3, methods=45, complexity_score=32.44, last_modified='2025-05-26', risk_level='🔴 高风险')", "FileMetrics(path='smartvault\\\\ui\\\\models\\\\file_table_model.py', lines=1131, classes=1, methods=40, complexity_score=31.310000000000002, last_modified='2025-05-26', risk_level='🔴 高风险')", "FileMetrics(path='smartvault\\\\services\\\\tag_service.py', lines=1120, classes=1, methods=42, complexity_score=21.9, last_modified='2025-05-25', risk_level='🔴 高风险')", "FileMetrics(path='smartvault\\\\ui\\\\dialogs\\\\advanced_search_dialog.py', lines=1024, classes=1, methods=39, complexity_score=23.14, last_modified='2025-05-24', risk_level='🔴 高风险')", "FileMetrics(path='smartvault\\\\services\\\\file_monitor_service.py', lines=856, classes=1, methods=31, complexity_score=19.759999999999998, last_modified='2025-05-25', risk_level='🔴 高风险')", "FileMetrics(path='smartvault\\\\ui\\\\main_window\\\\file_ops.py', lines=801, classes=1, methods=15, complexity_score=21.21, last_modified='2025-05-26', risk_level='🔴 高风险')", "FileMetrics(path='smartvault\\\\ui\\\\dialogs\\\\tag_management_dialog.py', lines=591, classes=2, methods=24, complexity_score=15.510000000000002, last_modified='2025-05-25', risk_level='🟡 中风险')", "FileMetrics(path='smartvault\\\\ui\\\\dialogs\\\\multi_condition_auto_tag_dialog.py', lines=557, classes=2, methods=27, complexity_score=13.87, last_modified='2025-05-25', risk_level='🟡 中风险')"], "identified_risks": ["ArchitectureRisk(risk_id='AR001_smartvault\\\\services\\\\file_monitor_service.py', component='smartvault\\\\services\\\\file_monitor_service.py', description='文件过长 (856 行)，超过临界阈值', current_status='需要立即重构', impact_level='高', urgency='立即', recommendation='模块化重构，拆分为多个文件')", "ArchitectureRisk(risk_id='AR001_smartvault\\\\services\\\\tag_service.py', component='smartvault\\\\services\\\\tag_service.py', description='文件过长 (1120 行)，超过临界阈值', current_status='需要立即重构', impact_level='高', urgency='立即', recommendation='模块化重构，拆分为多个文件')", "ArchitectureRisk(risk_id='AR001_smartvault\\\\ui\\\\themes.py', component='smartvault\\\\ui\\\\themes.py', description='文件过长 (1234 行)，超过临界阈值', current_status='需要立即重构', impact_level='高', urgency='立即', recommendation='模块化重构，拆分为多个文件')", "ArchitectureRisk(risk_id='AR001_smartvault\\\\ui\\\\dialogs\\\\advanced_search_dialog.py', component='smartvault\\\\ui\\\\dialogs\\\\advanced_search_dialog.py', description='文件过长 (1024 行)，超过临界阈值', current_status='需要立即重构', impact_level='高', urgency='立即', recommendation='模块化重构，拆分为多个文件')", "ArchitectureRisk(risk_id='AR002_smartvault\\\\ui\\\\dialogs\\\\multi_condition_auto_tag_dialog.py', component='smartvault\\\\ui\\\\dialogs\\\\multi_condition_auto_tag_dialog.py', description='文件较长 (557 行)，接近警告阈值', current_status='需要监控', impact_level='中', urgency='计划中', recommendation='评估重构可行性，预防性优化')", "ArchitectureRisk(risk_id='AR002_smartvault\\\\ui\\\\dialogs\\\\tag_management_dialog.py', component='smartvault\\\\ui\\\\dialogs\\\\tag_management_dialog.py', description='文件较长 (591 行)，接近警告阈值', current_status='需要监控', impact_level='中', urgency='计划中', recommendation='评估重构可行性，预防性优化')", "ArchitectureRisk(risk_id='AR001_smartvault\\\\ui\\\\main_window\\\\core.py', component='smartvault\\\\ui\\\\main_window\\\\core.py', description='文件过长 (1439 行)，超过临界阈值', current_status='需要立即重构', impact_level='高', urgency='立即', recommendation='模块化重构，拆分为多个文件')", "ArchitectureRisk(risk_id='AR001_smartvault\\\\ui\\\\main_window\\\\file_ops.py', component='smartvault\\\\ui\\\\main_window\\\\file_ops.py', description='文件过长 (801 行)，超过临界阈值', current_status='需要立即重构', impact_level='高', urgency='立即', recommendation='模块化重构，拆分为多个文件')", "ArchitectureRisk(risk_id='AR001_smartvault\\\\ui\\\\models\\\\file_table_model.py', component='smartvault\\\\ui\\\\models\\\\file_table_model.py', description='文件过长 (1131 行)，超过临界阈值', current_status='需要立即重构', impact_level='高', urgency='立即', recommendation='模块化重构，拆分为多个文件')", "ArchitectureRisk(risk_id='AR001_smartvault\\\\ui\\\\views\\\\file_table_view.py', component='smartvault\\\\ui\\\\views\\\\file_table_view.py', description='文件过长 (1154 行)，超过临界阈值', current_status='需要立即重构', impact_level='高', urgency='立即', recommendation='模块化重构，拆分为多个文件')", "ArchitectureRisk(risk_id='AR003_smartvault\\\\services\\\\tag_service.py', component='smartvault\\\\services\\\\tag_service.py', description='复杂度过高 (分数: 21.9)', current_status='需要简化', impact_level='中', urgency='计划中', recommendation='简化逻辑，提取公共方法')", "ArchitectureRisk(risk_id='AR003_smartvault\\\\ui\\\\dialogs\\\\advanced_search_dialog.py', component='smartvault\\\\ui\\\\dialogs\\\\advanced_search_dialog.py', description='复杂度过高 (分数: 23.1)', current_status='需要简化', impact_level='中', urgency='计划中', recommendation='简化逻辑，提取公共方法')", "ArchitectureRisk(risk_id='AR003_smartvault\\\\ui\\\\main_window\\\\core.py', component='smartvault\\\\ui\\\\main_window\\\\core.py', description='复杂度过高 (分数: 34.3)', current_status='需要简化', impact_level='中', urgency='计划中', recommendation='简化逻辑，提取公共方法')", "ArchitectureRisk(risk_id='AR003_smartvault\\\\ui\\\\main_window\\\\file_ops.py', component='smartvault\\\\ui\\\\main_window\\\\file_ops.py', description='复杂度过高 (分数: 21.2)', current_status='需要简化', impact_level='中', urgency='计划中', recommendation='简化逻辑，提取公共方法')", "ArchitectureRisk(risk_id='AR003_smartvault\\\\ui\\\\models\\\\file_table_model.py', component='smartvault\\\\ui\\\\models\\\\file_table_model.py', description='复杂度过高 (分数: 31.3)', current_status='需要简化', impact_level='中', urgency='计划中', recommendation='简化逻辑，提取公共方法')", "ArchitectureRisk(risk_id='AR003_smartvault\\\\ui\\\\views\\\\file_table_view.py', component='smartvault\\\\ui\\\\views\\\\file_table_view.py', description='复杂度过高 (分数: 32.4)', current_status='需要简化', impact_level='中', urgency='计划中', recommendation='简化逻辑，提取公共方法')"], "recommendations": ["立即处理 8 个高风险项目", "计划处理 8 个中风险项目", "优先重构超长文件，采用模块化设计"]}