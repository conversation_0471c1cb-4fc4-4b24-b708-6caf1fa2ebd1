#!/usr/bin/env python3
"""
SmartVault 数据库安全修复脚本
基于安全测试结果修复发现的问题
"""

import sys
import os
import sqlite3
import shutil
import time
from datetime import datetime

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class DatabaseSecurityFix:
    """数据库安全修复类"""

    def __init__(self):
        self.start_time = time.time()
        self.log_messages = []

    def log(self, message):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        log_msg = f"[{timestamp}] {message}"
        self.log_messages.append(log_msg)
        print(log_msg)

    def get_database_path(self):
        """获取数据库路径"""
        try:
            from smartvault.utils.config import load_config
            config = load_config()
            library_path = config["library_path"]
            db_path = os.path.join(library_path, "data", "smartvault.db")
            return db_path
        except Exception as e:
            self.log(f"❌ 获取数据库路径失败: {e}")
            return None

    def create_backup(self, db_path):
        """创建数据库备份"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"{db_path}.backup_security_fix_{timestamp}"
            shutil.copy2(db_path, backup_path)
            self.log(f"✅ 数据库备份已创建: {backup_path}")
            return backup_path
        except Exception as e:
            self.log(f"❌ 创建备份失败: {e}")
            return None

    def fix_1_missing_created_at_column(self, db_path):
        """修复1: 添加缺失的created_at字段到file_tags表"""
        self.log("🔧 修复1: 检查并添加file_tags表的created_at字段...")

        try:
            conn = sqlite3.connect(db_path, timeout=30.0)
            cursor = conn.cursor()

            # 检查file_tags表结构
            cursor.execute("PRAGMA table_info(file_tags)")
            columns = [col[1] for col in cursor.fetchall()]

            if 'created_at' not in columns:
                self.log("  📝 添加created_at字段到file_tags表...")
                cursor.execute("ALTER TABLE file_tags ADD COLUMN created_at TIMESTAMP")

                # 为现有记录设置默认时间戳
                cursor.execute("UPDATE file_tags SET created_at = ? WHERE created_at IS NULL",
                             (datetime.now().isoformat(),))

                conn.commit()
                self.log("  ✅ created_at字段添加成功")
            else:
                self.log("  ✅ created_at字段已存在")

            conn.close()
            return True

        except Exception as e:
            self.log(f"  ❌ 修复失败: {e}")
            return False

    def fix_2_foreign_key_violations(self, db_path):
        """修复2: 清理外键约束违反"""
        self.log("🔧 修复2: 清理外键约束违反...")

        try:
            conn = sqlite3.connect(db_path, timeout=30.0)
            cursor = conn.cursor()

            # 启用外键检查
            cursor.execute("PRAGMA foreign_keys=ON")

            # 检查外键违反
            cursor.execute("PRAGMA foreign_key_check")
            violations = cursor.fetchall()

            if violations:
                self.log(f"  📊 发现{len(violations)}个外键违反")

                # 清理孤立的file_tags记录
                cursor.execute("""
                    DELETE FROM file_tags
                    WHERE file_id NOT IN (SELECT id FROM files)
                    OR tag_id NOT IN (SELECT id FROM tags)
                """)
                deleted_file_tags = cursor.rowcount

                # 清理孤立的tag_relations记录
                cursor.execute("""
                    DELETE FROM tag_relations
                    WHERE tag1_id NOT IN (SELECT id FROM tags)
                    OR tag2_id NOT IN (SELECT id FROM tags)
                """)
                deleted_tag_relations = cursor.rowcount

                conn.commit()
                self.log(f"  ✅ 清理完成: 删除{deleted_file_tags}个孤立文件标签, {deleted_tag_relations}个孤立标签关系")
            else:
                self.log("  ✅ 未发现外键违反")

            conn.close()
            return True

        except Exception as e:
            self.log(f"  ❌ 修复失败: {e}")
            return False

    def fix_3_future_timestamps(self, db_path):
        """修复3: 修正未来时间戳"""
        self.log("🔧 修复3: 修正未来时间戳...")

        try:
            conn = sqlite3.connect(db_path, timeout=30.0)
            cursor = conn.cursor()

            # 使用数据库的当前时间而不是Python的本地时间
            # 修正files表中的未来时间戳
            cursor.execute("""
                UPDATE files
                SET added_at = datetime('now')
                WHERE added_at > datetime('now')
            """)
            fixed_files = cursor.rowcount

            # 修正tags表中的未来时间戳
            cursor.execute("""
                UPDATE tags
                SET created_at = datetime('now')
                WHERE created_at > datetime('now')
            """)
            fixed_tags = cursor.rowcount

            # 修正file_tags表中的未来时间戳
            cursor.execute("""
                UPDATE file_tags
                SET added_at = datetime('now')
                WHERE added_at > datetime('now')
            """)
            fixed_file_tags = cursor.rowcount

            conn.commit()
            conn.close()

            total_fixed = fixed_files + fixed_tags + fixed_file_tags
            if total_fixed > 0:
                self.log(f"  ✅ 修正{total_fixed}个未来时间戳 (文件:{fixed_files}, 标签:{fixed_tags}, 关联:{fixed_file_tags})")
            else:
                self.log("  ✅ 未发现未来时间戳")

            return True

        except Exception as e:
            self.log(f"  ❌ 修复失败: {e}")
            return False

    def fix_4_database_integrity(self, db_path):
        """修复4: 数据库完整性检查和修复"""
        self.log("🔧 修复4: 数据库完整性检查...")

        try:
            conn = sqlite3.connect(db_path, timeout=30.0)
            cursor = conn.cursor()

            # 完整性检查
            cursor.execute("PRAGMA integrity_check")
            integrity_result = cursor.fetchone()[0]

            if integrity_result == "ok":
                self.log("  ✅ 数据库完整性检查通过")
            else:
                self.log(f"  ⚠️ 数据库完整性问题: {integrity_result}")

            # 重建索引
            cursor.execute("REINDEX")
            self.log("  ✅ 索引重建完成")

            # 分析统计信息
            cursor.execute("ANALYZE")
            self.log("  ✅ 统计信息分析完成")

            conn.close()
            return True

        except Exception as e:
            self.log(f"  ❌ 修复失败: {e}")
            return False

    def fix_5_optimize_database(self, db_path):
        """修复5: 数据库优化"""
        self.log("🔧 修复5: 数据库优化...")

        try:
            conn = sqlite3.connect(db_path, timeout=30.0)
            cursor = conn.cursor()

            # 清理空间
            cursor.execute("VACUUM")
            self.log("  ✅ 数据库空间清理完成")

            # 优化设置
            cursor.execute("PRAGMA optimize")
            self.log("  ✅ 数据库优化完成")

            conn.close()
            return True

        except Exception as e:
            self.log(f"  ❌ 优化失败: {e}")
            return False

    def verify_fixes(self, db_path):
        """验证修复结果"""
        self.log("🔍 验证修复结果...")

        try:
            conn = sqlite3.connect(db_path, timeout=30.0)
            cursor = conn.cursor()

            # 检查file_tags表结构
            cursor.execute("PRAGMA table_info(file_tags)")
            columns = [col[1] for col in cursor.fetchall()]
            has_created_at = 'created_at' in columns

            # 检查外键违反
            cursor.execute("PRAGMA foreign_key_check")
            fk_violations = len(cursor.fetchall())

            # 检查未来时间戳
            cursor.execute("SELECT COUNT(*) FROM files WHERE added_at > datetime('now')")
            future_files = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM tags WHERE created_at > datetime('now')")
            future_tags = cursor.fetchone()[0]

            # 检查完整性
            cursor.execute("PRAGMA integrity_check")
            integrity_ok = cursor.fetchone()[0] == "ok"

            conn.close()

            # 报告结果
            self.log("📊 修复验证结果:")
            self.log(f"  - file_tags.created_at字段: {'✅ 存在' if has_created_at else '❌ 缺失'}")
            self.log(f"  - 外键违反: {'✅ 无' if fk_violations == 0 else f'❌ {fk_violations}个'}")
            self.log(f"  - 未来时间戳: {'✅ 无' if future_files + future_tags == 0 else f'❌ {future_files + future_tags}个'}")
            self.log(f"  - 数据库完整性: {'✅ 正常' if integrity_ok else '❌ 异常'}")

            success_count = sum([has_created_at, fk_violations == 0, future_files + future_tags == 0, integrity_ok])
            self.log(f"🏆 修复成功率: {success_count}/4 ({success_count/4*100:.1f}%)")

            return success_count == 4

        except Exception as e:
            self.log(f"❌ 验证失败: {e}")
            return False

    def run_security_fixes(self):
        """运行所有安全修复"""
        self.log("🛡️ 开始 SmartVault 数据库安全修复")

        # 获取数据库路径
        db_path = self.get_database_path()
        if not db_path or not os.path.exists(db_path):
            self.log("❌ 数据库文件不存在，无法进行修复")
            return False

        self.log(f"📍 数据库路径: {db_path}")

        # 创建备份
        backup_path = self.create_backup(db_path)
        if not backup_path:
            self.log("❌ 无法创建备份，修复中止")
            return False

        # 执行修复
        fixes_passed = 0
        total_fixes = 5

        if self.fix_1_missing_created_at_column(db_path):
            fixes_passed += 1

        if self.fix_2_foreign_key_violations(db_path):
            fixes_passed += 1

        if self.fix_3_future_timestamps(db_path):
            fixes_passed += 1

        if self.fix_4_database_integrity(db_path):
            fixes_passed += 1

        if self.fix_5_optimize_database(db_path):
            fixes_passed += 1

        # 验证修复结果
        verification_passed = self.verify_fixes(db_path)

        # 生成报告
        total_time = time.time() - self.start_time
        self.log(f"🎉 安全修复完成! 通过{fixes_passed}/{total_fixes}项修复")
        self.log(f"📊 验证结果: {'✅ 通过' if verification_passed else '❌ 部分失败'}")
        self.log(f"⏱️ 总耗时: {total_time:.2f}秒")
        self.log(f"💾 备份文件: {backup_path}")

        if fixes_passed == total_fixes and verification_passed:
            self.log("✅ 数据库安全修复成功，可以安全使用")
            return True
        else:
            self.log("⚠️ 部分修复失败，建议检查日志并重新运行")
            return False

def main():
    """主函数"""
    print("🛡️ SmartVault 数据库安全修复工具")
    print("⚠️  注意: 此工具将修复安全测试中发现的问题")
    print("⚠️  修复前会自动创建备份")

    response = input("\n是否继续进行安全修复? (y/N): ")
    if response.lower() != 'y':
        print("修复已取消")
        return

    # 运行安全修复
    fixer = DatabaseSecurityFix()
    success = fixer.run_security_fixes()

    if success:
        print("\n🎉 恭喜！数据库安全修复成功")
        print("💡 建议重新运行安全测试验证修复效果")
    else:
        print("\n⚠️ 修复过程中遇到问题，请检查日志")

if __name__ == '__main__':
    main()
