"""
文件导入功能
"""

import os
import uuid
import hashlib
from datetime import datetime
from typing import Dict, Optional, Tuple


class FileImportMixin:
    """文件导入混入类"""

    def add_file(self, path, mode="link", smart_duplicate_handling=True):
        """添加文件到智能文件库（统一入口，支持智能重复处理）

        Args:
            path: 文件路径
            mode: 添加模式（link/copy/move）
            smart_duplicate_handling: 是否启用智能重复处理

        Returns:
            str: 文件ID

        Raises:
            FileNotFoundError: 文件不存在
            ValueError: 无效的添加模式
        """
        # 检查路径是否存在
        if not os.path.exists(path):
            raise FileNotFoundError(f"文件不存在: {path}")

        # 如果是目录，则递归添加目录中的所有文件
        if os.path.isdir(path):
            return self._add_directory(path, mode)

        # 检查文件是否存在
        if not self.file_system.file_exists(path):
            raise FileNotFoundError(f"文件不存在: {path}")

        # 智能重复处理
        if smart_duplicate_handling:
            duplicate_result = self._check_duplicate_file(path)
            if duplicate_result['is_duplicate']:
                return self._handle_smart_duplicate(path, duplicate_result, mode)

        # 获取文件信息
        file_info = self.file_system.get_file_info(path)

        # 计算文件哈希
        file_hash = self._calculate_file_hash(path)

        # 根据模式处理文件
        if mode == "link":
            # 创建链接
            target_path = path
        elif mode == "copy":
            # 复制文件
            target_path = self._copy_to_library(path)
        elif mode == "move":
            # 移动文件
            target_path = self._move_to_library(path)
        else:
            raise ValueError(f"无效的添加模式: {mode}")

        # 创建文件记录
        file_id = str(uuid.uuid4())
        cursor = self.db.conn.cursor()
        cursor.execute(
            """
            INSERT INTO files (
                id, name, original_path, library_path, size,
                created_at, modified_at, added_at, entry_type, file_hash, staging_status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """,
            (
                file_id,
                os.path.basename(path),
                path,
                target_path if mode != "link" else None,
                file_info["size"],
                file_info["created_at"].isoformat(),
                file_info["modified_at"].isoformat(),
                datetime.now().isoformat(),
                mode,
                file_hash,
                'staging'  # 新文件默认进入中转文件夹
            )
        )
        self.db.conn.commit()

        # 应用AI标签建议（如果启用）
        self._apply_ai_tags_if_enabled(file_id, path)

        return file_id

    def _add_directory(self, directory_path, mode="link"):
        """添加目录中的所有文件到智能文件库

        Args:
            directory_path: 目录路径
            mode: 添加模式（link/copy/move）

        Returns:
            str: 目录组ID（用于标识这批文件）

        Raises:
            FileNotFoundError: 目录不存在
        """
        if not os.path.isdir(directory_path):
            raise FileNotFoundError(f"目录不存在: {directory_path}")

        # 获取目录中的所有文件
        file_paths = []
        for root, dirs, files in os.walk(directory_path):
            for file in files:
                file_path = os.path.join(root, file)
                file_paths.append(file_path)

        # 如果目录为空，则返回None
        if not file_paths:
            return None

        # 生成文件夹组ID（用于标识同一批次添加的文件）
        folder_group_id = str(uuid.uuid4())
        folder_name = os.path.basename(directory_path)

        print(f"📁 开始添加文件夹: {folder_name} (包含 {len(file_paths)} 个文件)")

        # 添加所有文件，并设置相同的folder_group_id
        first_file_id = None
        added_files = []
        for file_path in file_paths:
            try:
                file_id = self._add_file_with_folder_group(file_path, mode, folder_group_id, folder_name)
                added_files.append(file_id)
                if first_file_id is None:
                    first_file_id = file_id
            except Exception as e:
                print(f"添加文件失败: {file_path} - {e}")

        print(f"✅ 文件夹添加完成: {folder_name} (成功添加 {len(added_files)} 个文件)")
        return folder_group_id  # 返回文件夹组ID而不是单个文件ID

    def _add_file_with_folder_group(self, path, mode, folder_group_id, folder_name):
        """添加文件并设置文件夹组信息（内部方法）

        Args:
            path: 文件路径
            mode: 添加模式
            folder_group_id: 文件夹组ID
            folder_name: 文件夹名称

        Returns:
            str: 文件ID
        """
        # 检查文件是否存在
        if not self.file_system.file_exists(path):
            raise FileNotFoundError(f"文件不存在: {path}")

        # 智能重复处理
        duplicate_result = self._check_duplicate_file(path)
        if duplicate_result['is_duplicate']:
            return self._handle_smart_duplicate(path, duplicate_result, mode)

        # 获取文件信息
        file_info = self.file_system.get_file_info(path)

        # 计算文件哈希
        file_hash = self._calculate_file_hash(path)

        # 根据模式处理文件
        if mode == "link":
            target_path = path
        elif mode == "copy":
            target_path = self._copy_to_library(path)
        elif mode == "move":
            target_path = self._move_to_library(path)
        else:
            raise ValueError(f"无效的添加模式: {mode}")

        # 创建文件记录（包含文件夹组信息）
        file_id = str(uuid.uuid4())
        cursor = self.db.conn.cursor()
        cursor.execute(
            """
            INSERT INTO files (
                id, name, original_path, library_path, size,
                created_at, modified_at, added_at, entry_type, file_hash, staging_status,
                folder_group_id, folder_name
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """,
            (
                file_id,
                os.path.basename(path),
                path,
                target_path if mode != "link" else None,
                file_info["size"],
                file_info["created_at"].isoformat(),
                file_info["modified_at"].isoformat(),
                datetime.now().isoformat(),
                mode,
                file_hash,
                'staging',  # 新文件默认进入中转文件夹
                folder_group_id,
                folder_name
            )
        )
        self.db.conn.commit()

        # 应用AI标签建议（如果启用）
        self._apply_ai_tags_if_enabled(file_id, path)

        return file_id

    def _copy_to_library(self, path):
        """复制文件到智能文件库

        Args:
            path: 文件路径

        Returns:
            str: 目标文件路径
        """
        # 生成目标路径
        target_dir = os.path.join(
            self.file_system.library_path,
            datetime.now().strftime("%Y-%m-%d")
        )
        target_path = os.path.join(target_dir, os.path.basename(path))

        # 复制文件
        return self.file_system.copy_file(path, target_path)

    def _move_to_library(self, path):
        """移动文件到智能文件库

        Args:
            path: 文件路径

        Returns:
            str: 目标文件路径
        """
        # 先复制文件
        target_path = self._copy_to_library(path)

        # 删除原文件
        os.remove(path)

        return target_path

    def _calculate_file_hash(self, file_path: str) -> str:
        """计算文件的CRC32哈希值 - 高性能文件指纹识别"""
        try:
            import zlib

            # 使用CRC32算法（比MD5快3-5倍，Python标准库支持）
            crc32_hash = 0
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(65536), b""):  # 64KB chunks
                    crc32_hash = zlib.crc32(chunk, crc32_hash)

            # 转换为无符号32位整数，然后转为十六进制字符串
            return format(crc32_hash & 0xffffffff, '08x')

        except Exception as e:
            print(f"计算文件哈希失败: {e}")
            # 降级到MD5作为备选方案（保持兼容性）
            try:
                hash_md5 = hashlib.md5()
                with open(file_path, "rb") as f:
                    for chunk in iter(lambda: f.read(65536), b""):
                        hash_md5.update(chunk)
                return hash_md5.hexdigest()
            except Exception:
                return ""

    def _check_duplicate_file(self, file_path: str) -> Dict:
        """检查文件是否重复（统一重复检查入口）"""
        try:
            filename = os.path.basename(file_path)

            # 检查同名文件
            cursor = self.db.conn.cursor()
            cursor.execute(
                "SELECT id, name, original_path, file_hash FROM files WHERE name = ?",
                (filename,)
            )
            existing_files = cursor.fetchall()

            if not existing_files:
                return {'is_duplicate': False}

            # 计算新文件的哈希值
            new_file_hash = self._calculate_file_hash(file_path)

            # 检查是否有相同内容的文件
            for existing_file in existing_files:
                existing_hash = existing_file[3] if len(existing_file) > 3 else None

                if existing_hash == new_file_hash:
                    # 内容相同的重复文件
                    return {
                        'is_duplicate': True,
                        'type': 'identical',
                        'existing_file': existing_file,
                        'new_hash': new_file_hash
                    }

            # 同名但内容不同的文件
            return {
                'is_duplicate': True,
                'type': 'same_name',
                'existing_files': existing_files,
                'new_hash': new_file_hash
            }

        except Exception as e:
            print(f"检查重复文件失败: {e}")
            return {'is_duplicate': False}

    def _handle_smart_duplicate(self, file_path: str, duplicate_result: Dict, mode: str) -> str:
        """智能处理重复文件（改进版，支持链接模式的重复文件建议）"""
        try:
            filename = os.path.basename(file_path)

            if duplicate_result['type'] == 'identical':
                # 内容完全相同的重复文件
                existing_file = duplicate_result['existing_file']

                # 如果是链接模式，提供重复文件处理建议
                if mode == "link":
                    self._handle_link_mode_duplicate(file_path, existing_file)

                print(f"⏭️ 跳过重复文件（内容相同）: {filename}")
                return existing_file[0]  # 返回现有文件ID

            elif duplicate_result['type'] == 'same_name':
                # 同名但内容不同的文件
                if mode == "link":
                    # 链接模式：只创建链接记录，不修改物理文件
                    print(f"🔗 链接模式：添加同名不同内容的文件链接: {filename}")
                    # 直接添加文件，不进行物理文件操作
                    return self._add_link_file_directly(file_path)
                else:
                    # 复制/移动模式：自动重命名
                    new_filename = self._generate_unique_filename(filename)

                    # 在临时目录中创建文件副本，避免触发监控事件
                    import tempfile
                    import shutil

                    temp_dir = tempfile.mkdtemp(prefix="smartvault_temp_")
                    temp_file_path = os.path.join(temp_dir, new_filename)

                    try:
                        # 复制到临时目录
                        shutil.copy2(file_path, temp_file_path)

                        # 标记为自创建文件（如果有监控服务的话）
                        target_dir = os.path.dirname(file_path)
                        final_file_path = os.path.join(target_dir, new_filename)

                        # 通知监控服务这是自创建的文件
                        self._mark_as_self_created_file(final_file_path)

                        # 移动到最终位置
                        shutil.move(temp_file_path, final_file_path)

                        # 递归调用添加新文件（禁用重复检查避免无限循环）
                        file_id = self.add_file(final_file_path, mode, smart_duplicate_handling=False)
                        print(f"✅ 文件已自动重命名添加: {new_filename}")
                        return file_id

                    finally:
                        # 清理临时目录
                        try:
                            shutil.rmtree(temp_dir, ignore_errors=True)
                        except:
                            pass

        except Exception as e:
            print(f"智能处理重复文件失败: {filename} - {e}")
            # 出错时直接添加原文件
            return self.add_file(file_path, mode, smart_duplicate_handling=False)

    def _handle_link_mode_duplicate(self, file_path: str, existing_file: tuple):
        """处理链接模式下的重复文件，提供用户建议"""
        try:
            filename = os.path.basename(file_path)
            existing_path = existing_file[2]  # original_path

            # 检查两个文件是否是同一个物理文件
            if os.path.samefile(file_path, existing_path):
                print(f"🔗 链接模式：文件已存在于库中: {filename}")

                # 发送文件已存在的通知（与重复文件建议不同）
                file_exists_info = {
                    'new_file': file_path,
                    'existing_file': existing_path,
                    'filename': filename,
                    'suggestion': 'file_already_exists'
                }
                self._emit_duplicate_suggestion(file_exists_info)
                return

            # 不同位置的相同内容文件，记录重复文件建议
            duplicate_info = {
                'new_file': file_path,
                'existing_file': existing_path,
                'filename': filename,
                'suggestion': 'consider_delete_duplicate'
            }

            # 发送重复文件建议事件（如果有UI监听器）
            self._emit_duplicate_suggestion(duplicate_info)

            print(f"💡 链接模式重复文件建议：{filename} 在 {existing_path} 已存在相同内容的文件")

        except Exception as e:
            print(f"处理链接模式重复文件失败: {e}")

    def _add_link_file_directly(self, file_path: str) -> str:
        """直接添加链接文件，不进行物理文件操作"""
        try:
            # 获取文件信息
            file_info = self.file_system.get_file_info(file_path)

            # 计算文件哈希
            file_hash = self._calculate_file_hash(file_path)

            # 创建文件记录
            file_id = str(uuid.uuid4())
            cursor = self.db.conn.cursor()
            cursor.execute(
                """
                INSERT INTO files (
                    id, name, original_path, library_path, size,
                    created_at, modified_at, added_at, entry_type, file_hash, staging_status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """,
                (
                    file_id,
                    os.path.basename(file_path),
                    file_path,
                    None,  # 链接模式没有library_path
                    file_info["size"],
                    file_info["created_at"].isoformat(),
                    file_info["modified_at"].isoformat(),
                    datetime.now().isoformat(),
                    "link",
                    file_hash,
                    'staging'  # 新文件默认进入中转文件夹
                )
            )
            self.db.conn.commit()

            # 应用AI标签建议（如果启用）
            self._apply_ai_tags_if_enabled(file_id, file_path)

            return file_id

        except Exception as e:
            print(f"直接添加链接文件失败: {e}")
            raise

    def _emit_duplicate_suggestion(self, duplicate_info: dict):
        """发送重复文件建议事件"""
        try:
            # 打印日志
            print(f"📋 重复文件建议：{duplicate_info['suggestion']}")
            print(f"   新文件：{duplicate_info['new_file']}")
            print(f"   已存在：{duplicate_info['existing_file']}")

            # 检查是否在批量操作中
            if hasattr(self, 'is_in_batch_operation') and self.is_in_batch_operation():
                # 批量操作中，收集建议而不立即发送信号
                if hasattr(self, 'batch_context') and self.batch_context:
                    self.batch_context['duplicate_suggestions'].append(duplicate_info)
                    print(f"📦 批量模式：收集重复文件建议 ({len(self.batch_context['duplicate_suggestions'])} 个)")
            else:
                # 单个文件操作，立即发送信号
                if hasattr(self, 'duplicate_suggestion'):
                    self.duplicate_suggestion.emit(duplicate_info)
                    print("✅ 重复文件建议信号已发送")

        except Exception as e:
            print(f"发送重复文件建议失败: {e}")

    def _mark_as_self_created_file(self, file_path: str):
        """标记文件为自创建，避免监控服务重复处理"""
        try:
            # 尝试获取监控服务实例并标记文件
            # 这是一个松耦合的设计，如果没有监控服务也不会出错
            from smartvault.ui.main_window import MainWindow

            # 通过应用程序实例获取主窗口
            from PySide6.QtWidgets import QApplication
            app = QApplication.instance()
            if app:
                for widget in app.topLevelWidgets():
                    if isinstance(widget, MainWindow) and hasattr(widget, 'monitor_service'):
                        widget.monitor_service.self_created_files.add(file_path)
                        print(f"🏷️ 已标记为自创建文件: {os.path.basename(file_path)}")
                        break
        except Exception:
            # 如果无法标记也不影响主要功能
            pass

    def _generate_unique_filename(self, filename: str) -> str:
        """生成唯一的文件名（统一命名规则）"""
        try:
            name, ext = os.path.splitext(filename)
            counter = 1

            while True:
                new_filename = f"{name}_{counter:03d}{ext}"

                # 检查数据库中是否存在
                cursor = self.db.conn.cursor()
                cursor.execute("SELECT id FROM files WHERE name = ?", (new_filename,))

                if not cursor.fetchone():
                    return new_filename

                counter += 1

                # 防止无限循环
                if counter > 999:
                    import time
                    timestamp = int(time.time())
                    return f"{name}_{timestamp}{ext}"

        except Exception as e:
            print(f"生成唯一文件名失败: {e}")
            import time
            timestamp = int(time.time())
            name, ext = os.path.splitext(filename)
            return f"{name}_{timestamp}{ext}"

    def _apply_ai_tags_if_enabled(self, file_id: str, file_path: str):
        """应用AI标签建议（如果启用）

        Args:
            file_id: 文件ID
            file_path: 文件路径
        """
        try:
            # 检查是否有AI管理器可用
            ai_manager = self._get_ai_manager()
            if not ai_manager or not ai_manager.is_available():
                return

            # 准备文件信息
            file_info = {
                'name': os.path.basename(file_path),
                'path': file_path,
                'extension': os.path.splitext(file_path)[1].lower()
            }

            # 获取AI标签建议
            suggested_tags = ai_manager.suggest_tags(file_info)

            if suggested_tags:
                print(f"🤖 AI建议标签: {suggested_tags}")

                # 应用标签到文件
                self._apply_suggested_tags(file_id, suggested_tags)

        except Exception as e:
            print(f"应用AI标签建议失败: {e}")

    def _get_ai_manager(self):
        """获取AI管理器实例"""
        try:
            # 方法1: 尝试从主窗口获取AI管理器
            from PySide6.QtWidgets import QApplication
            app = QApplication.instance()
            if app:
                for widget in app.topLevelWidgets():
                    if hasattr(widget, 'ai_manager'):
                        return widget.ai_manager

            # 方法2: 如果没有主窗口，创建临时AI管理器实例
            from smartvault.services.ai.ai_manager import AIManager
            from smartvault.utils.config import load_config, get_ai_status

            # 检查AI是否启用
            if not get_ai_status():
                return None

            # 创建临时AI管理器
            ai_manager = AIManager()
            config = load_config()

            # 初始化AI管理器
            success = ai_manager.initialize(config)
            if success and ai_manager.is_available():
                return ai_manager

            return None
        except Exception as e:
            print(f"获取AI管理器失败: {e}")
            return None

    def _apply_suggested_tags(self, file_id: str, tag_names: list):
        """应用建议的标签到文件

        Args:
            file_id: 文件ID
            tag_names: 标签名称列表
        """
        try:
            # 获取标签服务
            from smartvault.services.tag_service import TagService
            tag_service = TagService()

            applied_count = 0
            for tag_name in tag_names:
                try:
                    # 创建或获取标签
                    tag_id = tag_service.create_tag(tag_name)

                    # 添加标签到文件
                    tag_service.add_tag_to_file(file_id, tag_id)
                    applied_count += 1

                except Exception as e:
                    print(f"应用标签失败 '{tag_name}': {e}")

            if applied_count > 0:
                print(f"✅ 已应用 {applied_count} 个AI建议标签")

        except Exception as e:
            print(f"应用建议标签失败: {e}")
