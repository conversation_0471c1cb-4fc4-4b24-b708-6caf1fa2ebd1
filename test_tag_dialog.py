#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
标签管理对话框测试
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PySide6.QtWidgets import QApplication
from smartvault.ui.dialogs.tag_management_dialog import TagManagementDialog
from smartvault.services.tag_service import TagService


def setup_test_data():
    """设置测试数据"""
    print("🏷️  设置测试标签数据...")

    tag_service = TagService()

    # 创建一些测试标签
    work_tag = tag_service.create_tag("工作文档", "#FF9800")
    personal_tag = tag_service.create_tag("个人文件", "#4CAF50")
    important_tag = tag_service.create_tag("重要", "#F44336")
    project_tag = tag_service.create_tag("项目A", "#2196F3")
    archive_tag = tag_service.create_tag("归档", "#9E9E9E")

    print(f"   ✅ 创建了 5 个测试标签")
    return [work_tag, personal_tag, important_tag, project_tag, archive_tag]


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置测试数据
    setup_test_data()

    # 创建并显示标签管理对话框
    dialog = TagManagementDialog()

    print("🖥️  显示标签管理对话框...")
    print("   💡 你可以测试以下功能:")
    print("      • 查看标签列表")
    print("      • 选择标签查看详情")
    print("      • 创建新标签")
    print("      • 删除标签")
    print("      • 查看标签统计")

    dialog.show()

    # 运行应用
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
