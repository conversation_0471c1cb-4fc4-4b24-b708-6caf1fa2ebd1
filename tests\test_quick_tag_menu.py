"""
快速标签菜单功能测试
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PySide6.QtWidgets import QApplication, QMenu
from PySide6.QtCore import QObject
from smartvault.ui.components.quick_tag_menu import QuickTagMenu


class TestQuickTagMenu(unittest.TestCase):
    """快速标签菜单测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication(sys.argv)
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """设置测试"""
        # 模拟TagService
        self.mock_tag_service = Mock()
        
        # 创建快速标签菜单实例
        self.quick_tag_menu = QuickTagMenu()
        self.quick_tag_menu.tag_service = self.mock_tag_service
        
        # 创建父菜单
        self.parent_menu = QMenu()
        
        # 模拟文件ID
        self.file_ids = ["file1", "file2"]
    
    def test_create_menu_no_tags(self):
        """测试创建菜单 - 无标签情况"""
        # 设置模拟返回值
        self.mock_tag_service.get_tags.return_value = []
        self.mock_tag_service.get_file_tags.return_value = []
        
        # 创建菜单
        menu = self.quick_tag_menu.create_menu(self.parent_menu, self.file_ids)
        
        # 验证菜单创建成功
        self.assertIsInstance(menu, QMenu)
        self.assertEqual(menu.title(), "快速标签")
        
        # 验证菜单项
        actions = menu.actions()
        self.assertTrue(any("暂无标签" in action.text() for action in actions))
        self.assertTrue(any("管理标签" in action.text() for action in actions))
    
    def test_create_menu_with_tags(self):
        """测试创建菜单 - 有标签情况"""
        # 设置模拟标签数据
        mock_tags = [
            {
                'id': 'tag1',
                'name': '工作',
                'color': '#ff0000',
                'parent_id': None
            },
            {
                'id': 'tag2', 
                'name': '个人',
                'color': '#00ff00',
                'parent_id': None
            }
        ]
        
        # 设置模拟返回值
        self.mock_tag_service.get_tags.return_value = mock_tags
        self.mock_tag_service.get_file_tags.return_value = []
        self.mock_tag_service.get_child_tags.return_value = []
        
        # 创建菜单
        menu = self.quick_tag_menu.create_menu(self.parent_menu, self.file_ids)
        
        # 验证菜单创建成功
        self.assertIsInstance(menu, QMenu)
        
        # 验证标签项存在
        actions = menu.actions()
        action_texts = [action.text() for action in actions if action.text()]
        
        self.assertTrue(any("工作" in text for text in action_texts))
        self.assertTrue(any("个人" in text for text in action_texts))
        self.assertTrue(any("管理标签" in text for text in action_texts))
    
    def test_get_tag_status(self):
        """测试获取标签状态"""
        # 设置文件标签缓存
        self.quick_tag_menu.file_ids = ["file1", "file2"]
        self.quick_tag_menu.file_tags_cache = {
            "file1": {"tag1": {"id": "tag1", "name": "工作"}},
            "file2": {}
        }
        
        # 测试部分文件有标签
        status = self.quick_tag_menu._get_tag_status("tag1")
        self.assertEqual(status, "partial")
        
        # 测试所有文件都有标签
        self.quick_tag_menu.file_tags_cache["file2"]["tag1"] = {"id": "tag1", "name": "工作"}
        status = self.quick_tag_menu._get_tag_status("tag1")
        self.assertEqual(status, "all")
        
        # 测试无文件有标签
        status = self.quick_tag_menu._get_tag_status("tag2")
        self.assertEqual(status, "none")
    
    def test_add_tag_to_files(self):
        """测试为文件添加标签"""
        # 设置模拟数据
        self.quick_tag_menu.file_ids = ["file1", "file2"]
        self.quick_tag_menu.file_tags_cache = {"file1": {}, "file2": {}}
        
        # 设置模拟返回值
        mock_tag = {"id": "tag1", "name": "工作", "color": "#ff0000"}
        self.mock_tag_service.get_tag_by_id.return_value = mock_tag
        
        # 执行添加标签
        self.quick_tag_menu._add_tag_to_files("tag1")
        
        # 验证调用了添加标签方法
        self.assertEqual(self.mock_tag_service.add_tag_to_file.call_count, 2)
        self.mock_tag_service.add_tag_to_file.assert_any_call("file1", "tag1")
        self.mock_tag_service.add_tag_to_file.assert_any_call("file2", "tag1")
    
    def test_remove_tag_from_files(self):
        """测试从文件移除标签"""
        # 设置模拟数据
        self.quick_tag_menu.file_ids = ["file1", "file2"]
        self.quick_tag_menu.file_tags_cache = {
            "file1": {"tag1": {"id": "tag1", "name": "工作"}},
            "file2": {"tag1": {"id": "tag1", "name": "工作"}}
        }
        
        # 执行移除标签
        self.quick_tag_menu._remove_tag_from_files("tag1")
        
        # 验证调用了移除标签方法
        self.assertEqual(self.mock_tag_service.remove_tag_from_file.call_count, 2)
        self.mock_tag_service.remove_tag_from_file.assert_any_call("file1", "tag1")
        self.mock_tag_service.remove_tag_from_file.assert_any_call("file2", "tag1")
        
        # 验证缓存已更新
        self.assertNotIn("tag1", self.quick_tag_menu.file_tags_cache["file1"])
        self.assertNotIn("tag1", self.quick_tag_menu.file_tags_cache["file2"])
    
    def test_toggle_tag_add(self):
        """测试切换标签 - 添加标签"""
        # 设置模拟数据（无标签状态）
        self.quick_tag_menu.file_ids = ["file1"]
        self.quick_tag_menu.file_tags_cache = {"file1": {}}
        
        # 模拟get_tag_status返回none
        with patch.object(self.quick_tag_menu, '_get_tag_status', return_value='none'):
            with patch.object(self.quick_tag_menu, '_add_tag_to_files') as mock_add:
                # 执行切换标签
                self.quick_tag_menu._toggle_tag("tag1")
                
                # 验证调用了添加标签方法
                mock_add.assert_called_once_with("tag1")
    
    def test_toggle_tag_remove(self):
        """测试切换标签 - 移除标签"""
        # 设置模拟数据（全部有标签状态）
        self.quick_tag_menu.file_ids = ["file1"]
        self.quick_tag_menu.file_tags_cache = {"file1": {"tag1": {"id": "tag1"}}}
        
        # 模拟get_tag_status返回all
        with patch.object(self.quick_tag_menu, '_get_tag_status', return_value='all'):
            with patch.object(self.quick_tag_menu, '_remove_tag_from_files') as mock_remove:
                # 执行切换标签
                self.quick_tag_menu._toggle_tag("tag1")
                
                # 验证调用了移除标签方法
                mock_remove.assert_called_once_with("tag1")
    
    def test_has_any_tags(self):
        """测试检查是否有任何标签"""
        # 设置无标签情况
        self.quick_tag_menu.file_ids = ["file1", "file2"]
        self.quick_tag_menu.file_tags_cache = {"file1": {}, "file2": {}}
        
        self.assertFalse(self.quick_tag_menu._has_any_tags())
        
        # 设置有标签情况
        self.quick_tag_menu.file_tags_cache["file1"]["tag1"] = {"id": "tag1"}
        
        self.assertTrue(self.quick_tag_menu._has_any_tags())
    
    def test_load_file_tags(self):
        """测试加载文件标签"""
        # 设置模拟返回值
        mock_tags = [{"id": "tag1", "name": "工作"}]
        self.mock_tag_service.get_file_tags.return_value = mock_tags
        
        # 设置文件ID
        self.quick_tag_menu.file_ids = ["file1"]
        
        # 执行加载
        self.quick_tag_menu._load_file_tags()
        
        # 验证调用了获取文件标签方法
        self.mock_tag_service.get_file_tags.assert_called_once_with("file1")
        
        # 验证缓存已设置
        self.assertIn("file1", self.quick_tag_menu.file_tags_cache)
        self.assertIn("tag1", self.quick_tag_menu.file_tags_cache["file1"])
    
    def tearDown(self):
        """清理测试"""
        self.parent_menu.deleteLater()


if __name__ == '__main__':
    unittest.main()
