#!/usr/bin/env python3
"""
AI功能调试测试
调试AI标签未应用的问题
"""

import sys
import os
import tempfile

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ai_status_and_availability():
    """测试AI状态和可用性"""
    print("🔍 测试AI状态和可用性...")
    
    try:
        from smartvault.utils.config import get_ai_status, save_ai_status, get_ai_config
        from smartvault.services.ai.ai_manager import AIManager
        
        # 检查AI状态
        ai_status = get_ai_status()
        print(f"当前AI状态: {ai_status}")
        
        # 检查AI配置
        ai_config = get_ai_config()
        print(f"AI配置启用状态: {ai_config.get('enabled', False)}")
        print(f"高级设置AI启用: {ai_config.get('advanced', {}).get('enable_ai_features', False)}")
        
        # 临时启用AI功能进行测试
        print("\n启用AI功能进行测试...")
        save_ai_status(True)
        
        # 创建AI管理器并测试
        ai_manager = AIManager()
        config = {'advanced': {'enable_ai_features': True}, 'ai': ai_config}
        
        success = ai_manager.initialize(config)
        print(f"AI管理器初始化: {success}")
        print(f"AI可用性: {ai_manager.is_available()}")
        
        if ai_manager.is_available():
            # 测试标签建议
            test_file = {
                'name': 'config.json',
                'path': '/test/config.json',
                'extension': '.json'
            }
            suggestions = ai_manager.suggest_tags(test_file)
            print(f"标签建议: {suggestions}")
        
        # 恢复原始状态
        save_ai_status(False)
        
        return True
        
    except Exception as e:
        print(f"❌ AI状态测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_service_with_ai_enabled():
    """测试启用AI后的文件服务"""
    print("\n🔍 测试启用AI后的文件服务...")
    
    try:
        from smartvault.utils.config import save_ai_status
        from smartvault.services.file import FileService
        from smartvault.services.tag_service import TagService
        
        # 启用AI功能
        save_ai_status(True)
        
        # 创建测试文件
        test_dir = tempfile.mkdtemp(prefix="smartvault_debug_")
        test_file = os.path.join(test_dir, "test_config.json")
        
        with open(test_file, 'w') as f:
            f.write('{"name": "test", "version": "1.0"}')
        
        try:
            # 创建服务
            file_service = FileService()
            tag_service = TagService()
            
            print(f"添加测试文件: {test_file}")
            
            # 添加文件
            file_id = file_service.add_file(test_file, mode="link")
            print(f"文件ID: {file_id}")
            
            # 检查标签
            tags = tag_service.get_file_tags(file_id)
            if tags:
                tag_names = [tag['name'] for tag in tags]
                print(f"✅ 应用的标签: {tag_names}")
                return True
            else:
                print("❌ 未应用任何标签")
                
                # 调试：手动测试AI标签应用
                print("\n调试：手动测试AI标签应用...")
                file_service._apply_ai_tags_if_enabled(file_id, test_file)
                
                # 再次检查标签
                tags = tag_service.get_file_tags(file_id)
                if tags:
                    tag_names = [tag['name'] for tag in tags]
                    print(f"✅ 手动应用后的标签: {tag_names}")
                    return True
                else:
                    print("❌ 手动应用后仍无标签")
                    return False
        
        finally:
            # 清理
            import shutil
            shutil.rmtree(test_dir, ignore_errors=True)
            save_ai_status(False)
        
    except Exception as e:
        print(f"❌ 文件服务AI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_manager_access():
    """测试AI管理器访问"""
    print("\n🔍 测试AI管理器访问...")
    
    try:
        from smartvault.services.file.import_ops import FileImportMixin
        
        # 创建文件导入混合类实例
        import_mixin = FileImportMixin()
        
        # 测试获取AI管理器
        ai_manager = import_mixin._get_ai_manager()
        
        if ai_manager:
            print(f"✅ 成功获取AI管理器: {type(ai_manager).__name__}")
            print(f"AI可用性: {ai_manager.is_available()}")
            return True
        else:
            print("❌ 无法获取AI管理器")
            print("这可能是因为主窗口未运行或AI管理器未初始化")
            return False
        
    except Exception as e:
        print(f"❌ AI管理器访问测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_smart_rule_engine():
    """测试智能规则引擎"""
    print("\n🔍 测试智能规则引擎...")
    
    try:
        from smartvault.services.ai.smart_rule_engine import SmartRuleEngine
        
        # 创建规则引擎
        engine = SmartRuleEngine()
        engine.initialize({}, None, None)
        
        # 测试不同文件的标签建议
        test_cases = [
            {
                'name': 'config.json',
                'extension': '.json',
                'path': '/test/config.json'
            },
            {
                'name': 'settings.conf',
                'extension': '.conf',
                'path': '/test/settings.conf'
            },
            {
                'name': 'app.config',
                'extension': '.config',
                'path': '/test/app.config'
            }
        ]
        
        for test_case in test_cases:
            suggestions = engine.suggest_tags(test_case)
            print(f"文件: {test_case['name']} -> 标签: {suggestions}")
        
        return True
        
    except Exception as e:
        print(f"❌ 智能规则引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 AI功能调试测试")
    print("=" * 60)
    
    # 测试1: AI状态和可用性
    test1 = test_ai_status_and_availability()
    
    # 测试2: 智能规则引擎
    test2 = test_smart_rule_engine()
    
    # 测试3: AI管理器访问
    test3 = test_ai_manager_access()
    
    # 测试4: 启用AI后的文件服务
    test4 = test_file_service_with_ai_enabled()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 调试测试结果:")
    print(f"AI状态和可用性: {'✅' if test1 else '❌'}")
    print(f"智能规则引擎: {'✅' if test2 else '❌'}")
    print(f"AI管理器访问: {'✅' if test3 else '❌'}")
    print(f"启用AI后的文件服务: {'✅' if test4 else '❌'}")
    
    if all([test1, test2, test3, test4]):
        print("\n🎉 所有调试测试通过！")
        return 0
    else:
        print("\n⚠️ 发现问题，需要修复")
        return 1

if __name__ == "__main__":
    sys.exit(main())
