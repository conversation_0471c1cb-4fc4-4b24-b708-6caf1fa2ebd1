#!/usr/bin/env python3
"""
SmartVault 设置对话框修复验证测试
验证文件库切换后配置一致性问题的修复效果
"""

import sys
import os
import tempfile
import shutil

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from smartvault.utils.config import load_config, save_config, normalize_path
from smartvault.data.file_system import FileSystem


def test_path_normalization_consistency():
    """测试路径标准化一致性"""
    print("🔍 测试路径标准化一致性...")
    
    # 创建临时文件库
    temp_dir = tempfile.mkdtemp(prefix="smartvault_fix_test_")
    
    try:
        file_system = FileSystem()
        success, library_path, details = file_system.create_library(temp_dir)
        
        if not success:
            print(f"❌ 创建文件库失败: {details}")
            return False
        
        print(f"   创建的文件库路径: {library_path}")
        
        # 检查配置文件中的路径
        config = load_config()
        config_path = config["library_path"]
        print(f"   配置文件中的路径: {config_path}")
        
        # 检查标准化后的路径
        normalized_library_path = normalize_path(library_path)
        normalized_config_path = normalize_path(config_path)
        
        print(f"   标准化后的文件库路径: {normalized_library_path}")
        print(f"   标准化后的配置路径: {normalized_config_path}")
        
        # 验证一致性
        paths_consistent = normalized_library_path == normalized_config_path
        
        if paths_consistent:
            print("✅ 路径标准化一致性测试通过")
            return True
        else:
            print("❌ 路径标准化一致性测试失败")
            print(f"   期望: {normalized_library_path}")
            print(f"   实际: {normalized_config_path}")
            return False
    
    finally:
        # 清理临时目录
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)


def test_settings_dialog_simulation():
    """模拟设置对话框操作测试"""
    print("\n🔍 模拟设置对话框操作测试...")
    
    # 保存原始配置
    original_config = load_config()
    
    try:
        # 创建临时文件库
        temp_dir = tempfile.mkdtemp(prefix="smartvault_dialog_sim_")
        
        file_system = FileSystem()
        success, library_path, details = file_system.create_library(temp_dir)
        
        if not success:
            print(f"❌ 创建文件库失败: {details}")
            return False
        
        print(f"   新文件库路径: {library_path}")
        
        # 模拟设置对话框中的操作（修复后的逻辑）
        # 1. 更新配置
        config = load_config()
        config["library_path"] = library_path
        save_config(config)
        
        # 2. 标准化路径用于UI显示
        normalized_path = normalize_path(library_path)
        print(f"   UI显示路径: {normalized_path}")
        
        # 3. 重新加载配置（模拟重新打开设置对话框）
        reloaded_config = load_config()
        reloaded_path = reloaded_config["library_path"]
        reloaded_normalized = normalize_path(reloaded_path)
        
        print(f"   重新加载的配置路径: {reloaded_path}")
        print(f"   重新加载的标准化路径: {reloaded_normalized}")
        
        # 验证一致性
        ui_config_consistent = normalized_path == reloaded_normalized
        
        if ui_config_consistent:
            print("✅ 设置对话框操作模拟测试通过")
            return True
        else:
            print("❌ 设置对话框操作模拟测试失败")
            print(f"   UI显示: {normalized_path}")
            print(f"   重新加载: {reloaded_normalized}")
            return False
    
    finally:
        # 恢复原始配置
        save_config(original_config)
        
        # 清理临时目录
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)


def test_multiple_operations():
    """测试多次文件库操作的一致性"""
    print("\n🔍 测试多次文件库操作的一致性...")
    
    original_config = load_config()
    temp_dirs = []
    
    try:
        file_system = FileSystem()
        
        # 创建多个文件库并切换
        for i in range(3):
            temp_dir = tempfile.mkdtemp(prefix=f"smartvault_multi_{i}_")
            temp_dirs.append(temp_dir)
            
            success, library_path, details = file_system.create_library(temp_dir)
            
            if not success:
                print(f"❌ 创建第{i+1}个文件库失败: {details}")
                return False
            
            # 模拟设置对话框操作
            config = load_config()
            config["library_path"] = library_path
            save_config(config)
            
            # 验证配置一致性
            reloaded_config = load_config()
            normalized_saved = normalize_path(library_path)
            normalized_loaded = normalize_path(reloaded_config["library_path"])
            
            if normalized_saved != normalized_loaded:
                print(f"❌ 第{i+1}次操作后配置不一致")
                print(f"   保存: {normalized_saved}")
                print(f"   加载: {normalized_loaded}")
                return False
            
            print(f"   第{i+1}次操作: ✅ 配置一致")
        
        print("✅ 多次文件库操作一致性测试通过")
        return True
    
    finally:
        # 恢复原始配置
        save_config(original_config)
        
        # 清理所有临时目录
        for temp_dir in temp_dirs:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)


def main():
    """主函数"""
    print("🚀 SmartVault 设置对话框修复验证测试")
    print("=" * 60)
    print("目标：验证文件库切换后配置一致性问题的修复效果")
    
    # 运行所有测试
    tests = [
        test_path_normalization_consistency,
        test_settings_dialog_simulation,
        test_multiple_operations
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 发生异常: {e}")
            import traceback
            traceback.print_exc()
    
    # 输出结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    print(f"总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！设置对话框配置一致性问题已修复。")
        print("\n修复要点:")
        print("• 创建文件库后使用 normalize_path() 标准化UI显示路径")
        print("• 确保配置保存和UI更新的顺序正确")
        print("• 所有文件库操作都正确保存配置文件")
        return True
    else:
        print("\n⚠️  部分测试失败，可能还存在问题。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
