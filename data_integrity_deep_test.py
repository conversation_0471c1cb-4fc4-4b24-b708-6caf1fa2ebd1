#!/usr/bin/env python3
"""
SmartVault 数据完整性深度测试
专门针对数据完整性问题进行深度分析和测试
"""

import sys
import os
import sqlite3
import hashlib
import time
import threading
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class DataIntegrityDeepTest:
    """数据完整性深度测试类"""
    
    def __init__(self):
        self.start_time = time.time()
        self.log_messages = []
        
    def log(self, message):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        log_msg = f"[{timestamp}] {message}"
        self.log_messages.append(log_msg)
        print(log_msg)
    
    def get_database_path(self):
        """获取数据库路径"""
        try:
            from smartvault.data.database import Database
            db = Database.create_from_config()
            db_path = db.db_path
            db.close()
            return db_path
        except Exception as e:
            self.log(f"❌ 获取数据库路径失败: {e}")
            return None
    
    def get_detailed_data_checksum(self, db_path):
        """获取详细的数据校验和"""
        try:
            conn = sqlite3.connect(db_path, timeout=30.0)
            cursor = conn.cursor()
            
            checksums = {}
            
            # 获取每个表的详细校验和
            tables = ['files', 'tags', 'file_tags', 'tag_relations']
            
            for table in tables:
                try:
                    # 检查表是否存在
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table,))
                    if not cursor.fetchone():
                        checksums[table] = "table_not_exists"
                        continue
                    
                    # 获取表结构校验和
                    cursor.execute(f"PRAGMA table_info({table})")
                    schema_data = str(cursor.fetchall())
                    schema_checksum = hashlib.md5(schema_data.encode()).hexdigest()
                    
                    # 获取数据校验和
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    row_count = cursor.fetchone()[0]
                    
                    if row_count > 0:
                        # 按主键排序获取数据
                        if table == 'files':
                            cursor.execute("SELECT * FROM files ORDER BY id")
                        elif table == 'tags':
                            cursor.execute("SELECT * FROM tags ORDER BY id")
                        elif table == 'file_tags':
                            cursor.execute("SELECT * FROM file_tags ORDER BY file_id, tag_id")
                        elif table == 'tag_relations':
                            cursor.execute("SELECT * FROM tag_relations ORDER BY id")
                        
                        data = cursor.fetchall()
                        data_str = str(data)
                        data_checksum = hashlib.md5(data_str.encode()).hexdigest()
                    else:
                        data_checksum = "empty_table"
                    
                    checksums[table] = {
                        'schema': schema_checksum,
                        'data': data_checksum,
                        'count': row_count
                    }
                    
                except Exception as e:
                    checksums[table] = f"error: {e}"
            
            conn.close()
            return checksums
            
        except Exception as e:
            return f"connection_error: {e}"
    
    def test_read_only_integrity(self):
        """测试只读操作的数据完整性"""
        self.log("🔍 测试只读操作数据完整性...")
        
        try:
            db_path = self.get_database_path()
            if not db_path:
                return False
            
            # 获取初始校验和
            initial_checksum = self.get_detailed_data_checksum(db_path)
            self.log(f"  📊 初始数据状态: {self._format_checksum_summary(initial_checksum)}")
            
            # 执行大量只读操作
            read_operations = 1000
            integrity_checks = 10  # 每100次操作检查一次
            
            conn = sqlite3.connect(db_path, timeout=30.0)
            cursor = conn.cursor()
            
            for i in range(read_operations):
                try:
                    # 执行各种只读查询
                    queries = [
                        "SELECT COUNT(*) FROM files",
                        "SELECT COUNT(*) FROM tags",
                        "SELECT COUNT(*) FROM file_tags",
                        "SELECT * FROM files LIMIT 5",
                        "SELECT * FROM tags LIMIT 3",
                        "SELECT f.name, t.name FROM files f JOIN file_tags ft ON f.id = ft.file_id JOIN tags t ON ft.tag_id = t.id LIMIT 5"
                    ]
                    
                    query = queries[i % len(queries)]
                    cursor.execute(query)
                    cursor.fetchall()
                    
                    # 定期检查数据完整性
                    if (i + 1) % (read_operations // integrity_checks) == 0:
                        conn.close()  # 关闭连接重新获取校验和
                        
                        current_checksum = self.get_detailed_data_checksum(db_path)
                        
                        if self._compare_checksums(initial_checksum, current_checksum):
                            self.log(f"    ✅ 第{i+1}次操作后数据完整性正常")
                        else:
                            self.log(f"    ❌ 第{i+1}次操作后数据完整性发生变化!")
                            self.log(f"      初始: {self._format_checksum_summary(initial_checksum)}")
                            self.log(f"      当前: {self._format_checksum_summary(current_checksum)}")
                            return False
                        
                        # 重新连接
                        conn = sqlite3.connect(db_path, timeout=30.0)
                        cursor = conn.cursor()
                
                except Exception as e:
                    self.log(f"    ⚠️ 第{i+1}次操作出错: {e}")
                    return False
            
            conn.close()
            
            # 最终完整性检查
            final_checksum = self.get_detailed_data_checksum(db_path)
            if self._compare_checksums(initial_checksum, final_checksum):
                self.log("  ✅ 只读操作数据完整性测试通过")
                return True
            else:
                self.log("  ❌ 最终数据完整性检查失败")
                return False
                
        except Exception as e:
            self.log(f"  ❌ 测试失败: {e}")
            return False
    
    def _compare_checksums(self, checksum1, checksum2):
        """比较两个校验和是否相同"""
        if isinstance(checksum1, str) or isinstance(checksum2, str):
            return checksum1 == checksum2
        
        if not isinstance(checksum1, dict) or not isinstance(checksum2, dict):
            return False
        
        # 比较每个表的校验和
        for table in checksum1.keys():
            if table not in checksum2:
                return False
            
            if isinstance(checksum1[table], dict) and isinstance(checksum2[table], dict):
                if (checksum1[table]['schema'] != checksum2[table]['schema'] or
                    checksum1[table]['data'] != checksum2[table]['data'] or
                    checksum1[table]['count'] != checksum2[table]['count']):
                    return False
            else:
                if checksum1[table] != checksum2[table]:
                    return False
        
        return True
    
    def _format_checksum_summary(self, checksum):
        """格式化校验和摘要"""
        if isinstance(checksum, str):
            return checksum
        
        if not isinstance(checksum, dict):
            return str(checksum)
        
        summary = []
        for table, data in checksum.items():
            if isinstance(data, dict):
                summary.append(f"{table}({data['count']}行)")
            else:
                summary.append(f"{table}({data})")
        
        return ", ".join(summary)
    
    def test_concurrent_read_integrity(self):
        """测试并发读取的数据完整性"""
        self.log("🔄 测试并发读取数据完整性...")
        
        try:
            db_path = self.get_database_path()
            if not db_path:
                return False
            
            # 获取初始校验和
            initial_checksum = self.get_detailed_data_checksum(db_path)
            
            # 启动多个并发读取线程
            num_threads = 20
            operations_per_thread = 100
            errors = []
            
            def concurrent_reader(thread_id):
                """并发读取线程"""
                thread_errors = []
                try:
                    conn = sqlite3.connect(db_path, timeout=30.0)
                    cursor = conn.cursor()
                    
                    for i in range(operations_per_thread):
                        try:
                            # 执行复杂查询
                            cursor.execute("""
                                SELECT f.id, f.name, COUNT(ft.tag_id) as tag_count
                                FROM files f
                                LEFT JOIN file_tags ft ON f.id = ft.file_id
                                GROUP BY f.id, f.name
                                LIMIT 10 OFFSET ?
                            """, (i % 100,))
                            cursor.fetchall()
                            
                        except Exception as e:
                            thread_errors.append(f"线程{thread_id}操作{i}: {e}")
                    
                    conn.close()
                    
                except Exception as e:
                    thread_errors.append(f"线程{thread_id}连接错误: {e}")
                
                return thread_errors
            
            # 执行并发测试
            with ThreadPoolExecutor(max_workers=num_threads) as executor:
                futures = [executor.submit(concurrent_reader, i) for i in range(num_threads)]
                
                for future in futures:
                    thread_errors = future.result()
                    errors.extend(thread_errors)
            
            # 检查最终数据完整性
            final_checksum = self.get_detailed_data_checksum(db_path)
            
            total_operations = num_threads * operations_per_thread
            error_rate = len(errors) / total_operations * 100
            
            self.log(f"  📊 并发操作总数: {total_operations}")
            self.log(f"  📊 错误数: {len(errors)}")
            self.log(f"  📊 错误率: {error_rate:.3f}%")
            
            if not self._compare_checksums(initial_checksum, final_checksum):
                self.log("  ❌ 并发读取后数据完整性发生变化")
                return False
            
            if error_rate > 1:  # 错误率超过1%认为有问题
                self.log("  ❌ 并发读取错误率过高")
                return False
            
            self.log("  ✅ 并发读取数据完整性测试通过")
            return True
            
        except Exception as e:
            self.log(f"  ❌ 测试失败: {e}")
            return False
    
    def test_database_schema_integrity(self):
        """测试数据库架构完整性"""
        self.log("🏗️ 测试数据库架构完整性...")
        
        try:
            db_path = self.get_database_path()
            if not db_path:
                return False
            
            conn = sqlite3.connect(db_path, timeout=30.0)
            cursor = conn.cursor()
            
            # 检查必需的表
            required_tables = ['files', 'tags', 'file_tags']
            existing_tables = []
            
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            for row in cursor.fetchall():
                existing_tables.append(row[0])
            
            missing_tables = [table for table in required_tables if table not in existing_tables]
            if missing_tables:
                self.log(f"  ❌ 缺少必需的表: {missing_tables}")
                conn.close()
                return False
            
            # 检查表结构
            schema_issues = []
            
            # 检查files表
            cursor.execute("PRAGMA table_info(files)")
            files_columns = {col[1]: col[2] for col in cursor.fetchall()}
            required_files_columns = ['id', 'name', 'original_path', 'size', 'added_at', 'entry_type']
            
            for col in required_files_columns:
                if col not in files_columns:
                    schema_issues.append(f"files表缺少字段: {col}")
            
            # 检查tags表
            cursor.execute("PRAGMA table_info(tags)")
            tags_columns = {col[1]: col[2] for col in cursor.fetchall()}
            required_tags_columns = ['id', 'name', 'created_at']
            
            for col in required_tags_columns:
                if col not in tags_columns:
                    schema_issues.append(f"tags表缺少字段: {col}")
            
            # 检查file_tags表
            cursor.execute("PRAGMA table_info(file_tags)")
            file_tags_columns = {col[1]: col[2] for col in cursor.fetchall()}
            required_file_tags_columns = ['file_id', 'tag_id', 'added_at']
            
            for col in required_file_tags_columns:
                if col not in file_tags_columns:
                    schema_issues.append(f"file_tags表缺少字段: {col}")
            
            # 检查外键约束
            cursor.execute("PRAGMA foreign_key_check")
            fk_violations = cursor.fetchall()
            
            if fk_violations:
                schema_issues.append(f"外键约束违反: {len(fk_violations)}个")
            
            conn.close()
            
            if schema_issues:
                self.log("  ❌ 发现架构问题:")
                for issue in schema_issues:
                    self.log(f"    - {issue}")
                return False
            else:
                self.log("  ✅ 数据库架构完整性正常")
                return True
                
        except Exception as e:
            self.log(f"  ❌ 测试失败: {e}")
            return False
    
    def run_integrity_tests(self):
        """运行所有完整性测试"""
        self.log("🔍 开始 SmartVault 数据完整性深度测试")
        
        tests = [
            ("数据库架构完整性", self.test_database_schema_integrity),
            ("只读操作数据完整性", self.test_read_only_integrity),
            ("并发读取数据完整性", self.test_concurrent_read_integrity),
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            self.log(f"\n{'='*50}")
            try:
                if test_func():
                    passed_tests += 1
                    self.log(f"✅ {test_name} 通过")
                else:
                    self.log(f"❌ {test_name} 失败")
            except Exception as e:
                self.log(f"❌ {test_name} 执行异常: {e}")
        
        # 生成报告
        total_time = time.time() - self.start_time
        self.log(f"\n{'='*50}")
        self.log(f"🎉 数据完整性测试完成!")
        self.log(f"📊 通过测试: {passed_tests}/{total_tests}")
        self.log(f"⏱️ 总耗时: {total_time:.2f}秒")
        
        if passed_tests == total_tests:
            self.log("✅ 所有数据完整性测试通过，数据库状态良好")
            return True
        else:
            self.log("⚠️ 部分测试失败，建议检查数据库状态")
            return False

def main():
    """主函数"""
    print("🔍 SmartVault 数据完整性深度测试")
    print("⚠️  专门针对数据完整性问题进行深度分析")
    
    response = input("\n是否开始数据完整性测试? (y/N): ")
    if response.lower() != 'y':
        print("测试已取消")
        return
    
    # 运行完整性测试
    tester = DataIntegrityDeepTest()
    success = tester.run_integrity_tests()
    
    if success:
        print("\n🎉 恭喜！数据完整性测试全部通过")
    else:
        print("\n⚠️ 发现数据完整性问题，建议进一步检查")

if __name__ == '__main__':
    main()
