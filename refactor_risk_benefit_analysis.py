#!/usr/bin/env python3
"""
重构风险收益分析工具
基于实际经验的重构决策支持系统
"""

import json
from dataclasses import dataclass
from typing import Dict, List, Tuple
from enum import Enum

class RefactorRisk(Enum):
    """重构风险等级"""
    LOW = "低风险"
    MEDIUM = "中风险" 
    HIGH = "高风险"
    CRITICAL = "极高风险"

class RefactorBenefit(Enum):
    """重构收益等级"""
    LOW = "低收益"
    MEDIUM = "中收益"
    HIGH = "高收益"
    CRITICAL = "极高收益"

@dataclass
class FileAnalysis:
    """文件分析结果"""
    file_path: str
    lines: int
    complexity: float
    modification_frequency: str  # 高/中/低
    business_criticality: str    # 核心/重要/一般
    test_coverage: str          # 高/中/低/无
    dependency_count: int       # 依赖此文件的其他文件数量

@dataclass
class RefactorAssessment:
    """重构评估结果"""
    file_analysis: FileAnalysis
    refactor_risk: RefactorRisk
    refactor_benefit: RefactorBenefit
    risk_score: float
    benefit_score: float
    recommendation: str
    alternative_solutions: List[str]

class RefactorAnalyzer:
    """重构分析器"""
    
    def __init__(self):
        self.risk_factors = {
            'lines': {'weight': 0.2, 'thresholds': [500, 800, 1200]},
            'complexity': {'weight': 0.25, 'thresholds': [10, 20, 30]},
            'modification_frequency': {'weight': 0.2, 'scores': {'高': 0.8, '中': 0.5, '低': 0.2}},
            'business_criticality': {'weight': 0.25, 'scores': {'核心': 0.9, '重要': 0.6, '一般': 0.3}},
            'test_coverage': {'weight': 0.1, 'scores': {'无': 0.9, '低': 0.7, '中': 0.4, '高': 0.1}}
        }
        
        self.benefit_factors = {
            'maintainability_gain': 0.3,
            'extensibility_gain': 0.25,
            'readability_gain': 0.2,
            'team_collaboration_gain': 0.15,
            'performance_gain': 0.1
        }
    
    def analyze_file(self, file_analysis: FileAnalysis) -> RefactorAssessment:
        """分析文件的重构风险和收益"""
        
        # 计算风险分数
        risk_score = self._calculate_risk_score(file_analysis)
        refactor_risk = self._determine_risk_level(risk_score)
        
        # 计算收益分数
        benefit_score = self._calculate_benefit_score(file_analysis)
        refactor_benefit = self._determine_benefit_level(benefit_score)
        
        # 生成建议
        recommendation = self._generate_recommendation(risk_score, benefit_score, file_analysis)
        
        # 生成替代方案
        alternatives = self._generate_alternatives(file_analysis, risk_score, benefit_score)
        
        return RefactorAssessment(
            file_analysis=file_analysis,
            refactor_risk=refactor_risk,
            refactor_benefit=refactor_benefit,
            risk_score=risk_score,
            benefit_score=benefit_score,
            recommendation=recommendation,
            alternative_solutions=alternatives
        )
    
    def _calculate_risk_score(self, analysis: FileAnalysis) -> float:
        """计算重构风险分数 (0-1, 越高风险越大)"""
        score = 0.0
        
        # 文件行数风险
        lines_risk = min(analysis.lines / 1500, 1.0)  # 1500行为满分
        score += lines_risk * self.risk_factors['lines']['weight']
        
        # 复杂度风险
        complexity_risk = min(analysis.complexity / 40, 1.0)  # 40为满分
        score += complexity_risk * self.risk_factors['complexity']['weight']
        
        # 修改频率风险
        mod_freq_risk = self.risk_factors['modification_frequency']['scores'][analysis.modification_frequency]
        score += mod_freq_risk * self.risk_factors['modification_frequency']['weight']
        
        # 业务关键性风险
        business_risk = self.risk_factors['business_criticality']['scores'][analysis.business_criticality]
        score += business_risk * self.risk_factors['business_criticality']['weight']
        
        # 测试覆盖率风险
        test_risk = self.risk_factors['test_coverage']['scores'][analysis.test_coverage]
        score += test_risk * self.risk_factors['test_coverage']['weight']
        
        return min(score, 1.0)
    
    def _calculate_benefit_score(self, analysis: FileAnalysis) -> float:
        """计算重构收益分数 (0-1, 越高收益越大)"""
        score = 0.0
        
        # 可维护性收益 (文件越长收益越大)
        maintainability = min(analysis.lines / 1000, 1.0)
        score += maintainability * self.benefit_factors['maintainability_gain']
        
        # 可扩展性收益 (复杂度越高收益越大)
        extensibility = min(analysis.complexity / 30, 1.0)
        score += extensibility * self.benefit_factors['extensibility_gain']
        
        # 可读性收益
        readability = min((analysis.lines + analysis.complexity * 10) / 1500, 1.0)
        score += readability * self.benefit_factors['readability_gain']
        
        # 团队协作收益 (修改频率高的文件收益大)
        collaboration_scores = {'高': 0.9, '中': 0.6, '低': 0.3}
        collaboration = collaboration_scores[analysis.modification_frequency]
        score += collaboration * self.benefit_factors['team_collaboration_gain']
        
        # 性能收益 (通常较小)
        performance = 0.3  # 固定较低值
        score += performance * self.benefit_factors['performance_gain']
        
        return min(score, 1.0)
    
    def _determine_risk_level(self, risk_score: float) -> RefactorRisk:
        """确定风险等级"""
        if risk_score >= 0.8:
            return RefactorRisk.CRITICAL
        elif risk_score >= 0.6:
            return RefactorRisk.HIGH
        elif risk_score >= 0.4:
            return RefactorRisk.MEDIUM
        else:
            return RefactorRisk.LOW
    
    def _determine_benefit_level(self, benefit_score: float) -> RefactorBenefit:
        """确定收益等级"""
        if benefit_score >= 0.8:
            return RefactorBenefit.CRITICAL
        elif benefit_score >= 0.6:
            return RefactorBenefit.HIGH
        elif benefit_score >= 0.4:
            return RefactorBenefit.MEDIUM
        else:
            return RefactorBenefit.LOW
    
    def _generate_recommendation(self, risk_score: float, benefit_score: float, analysis: FileAnalysis) -> str:
        """生成重构建议"""
        risk_benefit_ratio = benefit_score / max(risk_score, 0.1)
        
        if risk_benefit_ratio >= 2.0:
            return "🟢 强烈推荐重构 - 高收益低风险"
        elif risk_benefit_ratio >= 1.5:
            return "🟡 推荐重构 - 收益大于风险，建议充分准备"
        elif risk_benefit_ratio >= 1.0:
            return "🟡 谨慎重构 - 风险收益平衡，需要详细计划"
        elif risk_benefit_ratio >= 0.7:
            return "🟠 不推荐重构 - 风险大于收益，考虑替代方案"
        else:
            return "🔴 强烈不推荐重构 - 高风险低收益，寻找其他解决方案"
    
    def _generate_alternatives(self, analysis: FileAnalysis, risk_score: float, benefit_score: float) -> List[str]:
        """生成替代解决方案"""
        alternatives = []
        
        # 基于风险等级的替代方案
        if risk_score >= 0.7:
            alternatives.extend([
                "📝 添加详细注释和文档，提高可读性",
                "🧪 增加单元测试覆盖率，降低修改风险",
                "🔧 提取部分功能为独立工具函数",
                "📋 建立代码审查机制，控制修改质量"
            ])
        
        # 基于文件特征的替代方案
        if analysis.lines > 1000:
            alternatives.append("✂️ 渐进式重构：每次只提取一个小模块")
        
        if analysis.complexity > 25:
            alternatives.append("🎯 简化复杂逻辑：提取复杂条件为独立方法")
        
        if analysis.business_criticality == "核心":
            alternatives.extend([
                "🛡️ 建立功能回归测试套件",
                "🔄 采用特性开关(Feature Toggle)进行渐进式替换"
            ])
        
        if analysis.test_coverage in ["无", "低"]:
            alternatives.append("🧪 优先建立测试覆盖，再考虑重构")
        
        return alternatives

def analyze_current_files():
    """分析当前项目的高风险文件"""
    
    # 基于架构评估结果的文件数据
    files_to_analyze = [
        FileAnalysis(
            file_path="smartvault/ui/main_window/core.py",
            lines=1439,
            complexity=34.3,
            modification_frequency="高",
            business_criticality="核心",
            test_coverage="低",
            dependency_count=15
        ),
        FileAnalysis(
            file_path="smartvault/ui/themes.py", 
            lines=1234,
            complexity=14.7,
            modification_frequency="低",
            business_criticality="一般",
            test_coverage="无",
            dependency_count=8
        ),
        FileAnalysis(
            file_path="smartvault/services/tag_service.py",
            lines=1120,
            complexity=21.9,
            modification_frequency="中",
            business_criticality="重要",
            test_coverage="中",
            dependency_count=12
        ),
        FileAnalysis(
            file_path="smartvault/ui/views/file_table_view.py",
            lines=1154,
            complexity=32.4,
            modification_frequency="中",
            business_criticality="重要",
            test_coverage="低",
            dependency_count=6
        )
    ]
    
    analyzer = RefactorAnalyzer()
    results = []
    
    print("🔍 重构风险收益分析报告")
    print("=" * 80)
    
    for file_analysis in files_to_analyze:
        assessment = analyzer.analyze_file(file_analysis)
        results.append(assessment)
        
        print(f"\n📁 文件: {file_analysis.file_path}")
        print(f"📊 基本信息: {file_analysis.lines}行, 复杂度{file_analysis.complexity}")
        print(f"⚠️  重构风险: {assessment.refactor_risk.value} (分数: {assessment.risk_score:.2f})")
        print(f"💰 重构收益: {assessment.refactor_benefit.value} (分数: {assessment.benefit_score:.2f})")
        print(f"💡 建议: {assessment.recommendation}")
        print(f"🔧 替代方案:")
        for alt in assessment.alternative_solutions[:3]:  # 只显示前3个
            print(f"   • {alt}")
    
    # 生成优先级排序
    print(f"\n🎯 重构优先级排序:")
    sorted_results = sorted(results, key=lambda x: x.benefit_score / max(x.risk_score, 0.1), reverse=True)
    
    for i, result in enumerate(sorted_results, 1):
        ratio = result.benefit_score / max(result.risk_score, 0.1)
        print(f"   {i}. {result.file_analysis.file_path} (收益/风险比: {ratio:.2f})")
    
    return results

if __name__ == "__main__":
    analyze_current_files()
