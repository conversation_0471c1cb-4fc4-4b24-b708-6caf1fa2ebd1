#!/usr/bin/env python3
"""
AI功能完整集成测试
测试完整的AI功能工作流程，包括新文件添加
"""

import sys
import os
import tempfile
import shutil
import uuid

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ai_with_new_files():
    """测试AI功能与新文件"""
    print("🔍 测试AI功能与新文件...")
    
    try:
        from smartvault.utils.config import save_ai_status
        from smartvault.services.file import FileService
        from smartvault.services.tag_service import TagService
        
        # 启用AI功能
        save_ai_status(True)
        
        # 创建唯一的测试文件
        test_dir = tempfile.mkdtemp(prefix="smartvault_ai_complete_")
        unique_id = str(uuid.uuid4())[:8]
        
        test_files = [
            (f"config_{unique_id}.json", '{"name": "test", "version": "1.0"}'),
            (f"script_{unique_id}.py", "print('Hello World')"),
            (f"readme_{unique_id}.md", "# 项目说明\n这是一个测试项目"),
            (f"settings_{unique_id}.conf", "[database]\nhost=localhost"),
        ]
        
        created_files = []
        for filename, content in test_files:
            file_path = os.path.join(test_dir, filename)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            created_files.append(file_path)
        
        try:
            # 创建服务
            file_service = FileService()
            tag_service = TagService()
            
            print(f"创建了 {len(created_files)} 个唯一测试文件")
            
            # 测试添加文件（应该触发AI标签建议）
            results = []
            for test_file in created_files:
                try:
                    filename = os.path.basename(test_file)
                    print(f"\n添加文件: {filename}")
                    
                    file_id = file_service.add_file(test_file, mode="link")
                    print(f"✅ 文件添加成功: {file_id}")
                    
                    # 检查是否有标签被应用
                    tags = tag_service.get_file_tags(file_id)
                    if tags:
                        tag_names = [tag['name'] for tag in tags]
                        print(f"✅ 应用的标签: {tag_names}")
                        results.append({
                            'file': filename,
                            'file_id': file_id,
                            'tags': tag_names,
                            'success': True
                        })
                    else:
                        print("⚠️ 未应用任何标签")
                        results.append({
                            'file': filename,
                            'file_id': file_id,
                            'tags': [],
                            'success': False
                        })
                    
                except Exception as e:
                    print(f"❌ 添加文件失败: {e}")
                    results.append({
                        'file': os.path.basename(test_file),
                        'file_id': None,
                        'tags': [],
                        'success': False,
                        'error': str(e)
                    })
            
            # 总结结果
            print(f"\n📊 测试结果总结:")
            successful_files = [r for r in results if r['success']]
            files_with_tags = [r for r in results if r['tags']]
            
            print(f"成功添加文件: {len(successful_files)}/{len(results)}")
            print(f"应用AI标签的文件: {len(files_with_tags)}/{len(results)}")
            
            for result in results:
                status = "✅" if result['success'] else "❌"
                tag_info = f" (标签: {result['tags']})" if result['tags'] else " (无标签)"
                print(f"{status} {result['file']}{tag_info}")
            
            return len(files_with_tags) > 0
            
        finally:
            # 清理测试文件
            shutil.rmtree(test_dir, ignore_errors=True)
            save_ai_status(False)
        
    except Exception as e:
        print(f"❌ AI新文件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_manager_direct():
    """直接测试AI管理器"""
    print("\n🔍 直接测试AI管理器...")
    
    try:
        from smartvault.services.ai.ai_manager import AIManager
        from smartvault.utils.config import load_config, save_ai_status
        
        # 启用AI功能
        save_ai_status(True)
        
        # 创建AI管理器
        ai_manager = AIManager()
        config = load_config()
        
        # 初始化AI管理器
        success = ai_manager.initialize(config)
        print(f"AI管理器初始化: {success}")
        print(f"AI可用性: {ai_manager.is_available()}")
        
        if ai_manager.is_available():
            # 测试不同类型文件的标签建议
            test_cases = [
                {
                    'name': 'app_config.json',
                    'path': '/test/app_config.json',
                    'extension': '.json'
                },
                {
                    'name': 'main.py',
                    'path': '/test/main.py',
                    'extension': '.py'
                },
                {
                    'name': 'README.md',
                    'path': '/test/README.md',
                    'extension': '.md'
                }
            ]
            
            all_working = True
            for test_case in test_cases:
                suggestions = ai_manager.suggest_tags(test_case)
                print(f"文件: {test_case['name']} -> 标签: {suggestions}")
                if not suggestions:
                    all_working = False
            
            # 恢复AI状态
            save_ai_status(False)
            return all_working
        
        save_ai_status(False)
        return False
        
    except Exception as e:
        print(f"❌ AI管理器直接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_settings_dialog_integration():
    """测试设置对话框集成"""
    print("\n🔍 测试设置对话框集成...")
    
    try:
        # 测试导入
        from smartvault.ui.dialogs.settings_dialog import SettingsDialog
        from smartvault.ui.dialogs.settings.pages.ai.ai_main_page import AISettingsPage
        print("✅ 设置对话框和AI页面导入成功")
        
        # 检查AI页面是否在导入列表中
        import inspect
        source = inspect.getsource(SettingsDialog)
        
        if 'AISettingsPage' in source:
            print("✅ AI设置页面已集成到设置对话框")
            return True
        else:
            print("❌ AI设置页面未集成到设置对话框")
            return False
        
    except Exception as e:
        print(f"❌ 设置对话框集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 AI功能完整集成测试")
    print("=" * 70)
    
    # 测试1: AI管理器直接测试
    test1 = test_ai_manager_direct()
    
    # 测试2: AI功能与新文件
    test2 = test_ai_with_new_files()
    
    # 测试3: 设置对话框集成
    test3 = test_settings_dialog_integration()
    
    # 总结
    print("\n" + "=" * 70)
    print("📊 完整集成测试结果总结:")
    print(f"AI管理器直接测试: {'✅ 通过' if test1 else '❌ 失败'}")
    print(f"AI功能与新文件: {'✅ 通过' if test2 else '❌ 失败'}")
    print(f"设置对话框集成: {'✅ 通过' if test3 else '❌ 失败'}")
    
    if all([test1, test2, test3]):
        print("\n🎉 所有完整集成测试通过！")
        print("✅ AI功能已完全集成并正常工作")
        print("\n🔥 AI功能集成成功总结:")
        print("   • AI设置页面已集成到主设置对话框")
        print("   • AI管理器已集成到主程序")
        print("   • AI标签建议功能正常工作")
        print("   • 文件添加时自动应用AI标签")
        print("   • 智能规则引擎正确识别文件类型")
        print("   • 配置管理和持久化正常")
        return 0
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查")
        return 1

if __name__ == "__main__":
    sys.exit(main())
