#!/usr/bin/env python3
"""
设置页面单元测试
测试各个设置页面的核心功能和逻辑
"""

import sys
import os
import unittest
import tempfile
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication
from smartvault.ui.dialogs.settings.pages import (
    UISettingsPage, SearchSettingsPage, AdvancedSettingsPage,
    MonitorSettingsPage, AutoTagSettingsPage, LibrarySettingsPage
)
from smartvault.services.file_monitor_service import FileMonitorService

class TestSettingsPages(unittest.TestCase):
    """设置页面测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        cls.app = QApplication.instance()
        if cls.app is None:
            cls.app = QApplication(sys.argv)
    
    def setUp(self):
        """设置每个测试"""
        self.test_config = {
            'library_path': '/test/path',
            'ui': {'theme': 'dark', 'font_size': 12},
            'search': {'default_scope': 'database', 'max_results': 1000},
            'monitor': {'event_interval_ms': 500},
            'auto_tag': {'enabled': True},
            'advanced': {'cache_enabled': True, 'log_level': 'INFO'}
        }
    
    def test_ui_settings_page(self):
        """测试UI设置页面"""
        print("\n🎨 测试UI设置页面...")
        
        page = UISettingsPage()
        
        # 测试页面标题
        self.assertEqual(page.get_page_title(), "UI设置")
        
        # 测试设置加载
        page.load_settings(self.test_config)
        
        # 测试设置保存
        settings = page.save_settings()
        self.assertIsInstance(settings, dict)
        
        # 测试验证
        is_valid, error_msg = page.validate_settings()
        self.assertTrue(is_valid)
        self.assertEqual(error_msg, "")
        
        print("   ✅ UI设置页面测试通过")
    
    def test_search_settings_page(self):
        """测试搜索设置页面"""
        print("\n🔍 测试搜索设置页面...")
        
        page = SearchSettingsPage()
        
        # 测试页面标题
        self.assertEqual(page.get_page_title(), "搜索设置")
        
        # 测试设置加载
        page.load_settings(self.test_config)
        
        # 测试设置保存
        settings = page.save_settings()
        self.assertIsInstance(settings, dict)
        
        # 测试验证
        is_valid, error_msg = page.validate_settings()
        self.assertTrue(is_valid)
        
        print("   ✅ 搜索设置页面测试通过")
    
    def test_monitor_settings_page(self):
        """测试监控设置页面"""
        print("\n📁 测试监控设置页面...")
        
        page = MonitorSettingsPage()
        
        # 测试页面标题
        self.assertEqual(page.get_page_title(), "文件监控设置")
        
        # 测试设置加载
        page.load_settings(self.test_config)
        
        # 测试监控服务设置
        monitor_service = FileMonitorService()
        page.set_monitor_service(monitor_service)
        self.assertIsNotNone(page.monitor_service)
        
        # 测试设置保存
        settings = page.save_settings()
        self.assertIsInstance(settings, dict)
        self.assertIn('event_interval_ms', settings)
        
        # 测试验证
        is_valid, error_msg = page.validate_settings()
        self.assertTrue(is_valid)
        
        print("   ✅ 监控设置页面测试通过")
    
    def test_auto_tag_settings_page(self):
        """测试自动标签设置页面"""
        print("\n🏷️ 测试自动标签设置页面...")
        
        page = AutoTagSettingsPage()
        
        # 测试页面标题
        self.assertEqual(page.get_page_title(), "自动标签设置")
        
        # 测试设置加载
        page.load_settings(self.test_config)
        
        # 测试设置保存
        settings = page.save_settings()
        self.assertIsInstance(settings, dict)
        
        # 测试验证
        is_valid, error_msg = page.validate_settings()
        self.assertTrue(is_valid)
        
        print("   ✅ 自动标签设置页面测试通过")
    
    def test_advanced_settings_page(self):
        """测试高级设置页面"""
        print("\n⚙️ 测试高级设置页面...")
        
        page = AdvancedSettingsPage()
        
        # 测试页面标题
        self.assertEqual(page.get_page_title(), "高级设置")
        
        # 测试设置加载
        page.load_settings(self.test_config)
        
        # 测试设置保存
        settings = page.save_settings()
        self.assertIsInstance(settings, dict)
        
        # 测试验证
        is_valid, error_msg = page.validate_settings()
        self.assertTrue(is_valid)
        
        print("   ✅ 高级设置页面测试通过")
    
    def test_library_settings_page(self):
        """测试文件库设置页面"""
        print("\n📚 测试文件库设置页面...")
        
        page = LibrarySettingsPage()
        
        # 测试页面标题
        self.assertEqual(page.get_page_title(), "文件库设置")
        
        # 测试设置加载
        page.load_settings(self.test_config)
        
        # 测试设置保存
        settings = page.save_settings()
        self.assertIsInstance(settings, dict)
        
        # 测试验证
        is_valid, error_msg = page.validate_settings()
        self.assertTrue(is_valid)
        
        print("   ✅ 文件库设置页面测试通过")
    
    def test_all_pages_consistency(self):
        """测试所有页面的一致性"""
        print("\n🔄 测试页面一致性...")
        
        pages = [
            UISettingsPage(),
            SearchSettingsPage(),
            MonitorSettingsPage(),
            AutoTagSettingsPage(),
            AdvancedSettingsPage(),
            LibrarySettingsPage()
        ]
        
        for page in pages:
            # 所有页面都应该有这些基本方法
            self.assertTrue(hasattr(page, 'get_page_title'))
            self.assertTrue(hasattr(page, 'load_settings'))
            self.assertTrue(hasattr(page, 'save_settings'))
            self.assertTrue(hasattr(page, 'validate_settings'))
            
            # 测试方法调用不出错
            title = page.get_page_title()
            self.assertIsInstance(title, str)
            self.assertTrue(len(title) > 0)
            
            page.load_settings(self.test_config)
            
            settings = page.save_settings()
            self.assertIsInstance(settings, dict)
            
            is_valid, error_msg = page.validate_settings()
            self.assertIsInstance(is_valid, bool)
            self.assertIsInstance(error_msg, str)
        
        print("   ✅ 页面一致性测试通过")

class TestSettingsIntegration(unittest.TestCase):
    """设置集成测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        cls.app = QApplication.instance()
        if cls.app is None:
            cls.app = QApplication(sys.argv)
    
    def test_settings_dialog_integration(self):
        """测试设置对话框集成"""
        print("\n🔗 测试设置对话框集成...")
        
        from smartvault.ui.dialogs import SettingsDialog
        from smartvault.ui.main_window import MainWindow
        
        # 创建主窗口和对话框
        main_window = MainWindow()
        dialog = SettingsDialog(main_window)
        
        # 设置监控服务
        dialog.set_monitor_service(main_window.monitor_service)
        
        # 测试所有页面都已创建
        expected_pages = ['library', 'monitor', 'ui', 'search', 'auto_tag', 'advanced']
        for page_key in expected_pages:
            self.assertIn(page_key, dialog.pages)
            self.assertIsNotNone(dialog.pages[page_key])
        
        # 测试监控页面的服务设置
        monitor_page = dialog.pages['monitor']
        self.assertIsNotNone(monitor_page.monitor_service)
        
        print("   ✅ 设置对话框集成测试通过")

def run_unit_tests():
    """运行单元测试"""
    print("🧪 开始设置页面单元测试...")
    
    # 创建测试套件
    suite = unittest.TestSuite()
    
    # 添加测试
    suite.addTest(unittest.makeSuite(TestSettingsPages))
    suite.addTest(unittest.makeSuite(TestSettingsIntegration))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 显示结果
    print(f"\n📊 测试结果:")
    print(f"   ✅ 成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"   ❌ 失败: {len(result.failures)}")
    print(f"   💥 错误: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"   - {test}: {traceback}")
    
    if result.errors:
        print("\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"   - {test}: {traceback}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_unit_tests()
    sys.exit(0 if success else 1)
