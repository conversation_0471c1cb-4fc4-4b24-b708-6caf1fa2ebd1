import sys
import os
sys.path.append('.')

try:
    from PySide6.QtWidgets import QApplication
    from PySide6.QtCore import QTimer
    from smartvault.ui.widgets.clipboard_floating_widget import ClipboardFloatingWidget
    
    print("测试改进后的持续显示模式...")
    
    # 创建Qt应用
    app = QApplication(sys.argv)
    
    # 创建浮动窗口
    widget = ClipboardFloatingWidget()
    
    print("=== 测试改进后的持续显示模式行为 ===")
    
    # 设置为持续显示模式
    widget.set_persistent_mode(True)
    widget.set_monitoring_enabled(True)
    print("✅ 设置为持续显示模式")
    
    # 测试监控状态显示
    print("\n--- 测试监控状态显示 ---")
    widget.show_monitoring_status()
    print(f"标题: {widget.title_label.text()}")
    print(f"内容: {widget.content_label.text()}")
    print(f"查看按钮可见: {widget.view_button.isVisible()}")
    print(f"关闭按钮可见: {widget.close_button.isVisible()}")
    print(f"当前重复信息: {widget.current_duplicate_info is not None}")
    
    # 测试显示重复信息
    print("\n--- 测试显示重复信息 ---")
    duplicate_info = {
        'type': 'file',
        'source_name': 'test.txt',
        'duplicates': [{
            'id': '1',
            'name': '测试文件.txt'
        }]
    }
    
    widget.show_duplicate(duplicate_info)
    print(f"标题: {widget.title_label.text()}")
    print(f"内容: {widget.content_label.text()}")
    print(f"查看按钮可见: {widget.view_button.isVisible()}")
    print(f"关闭按钮可见: {widget.close_button.isVisible()}")  # 应该是False
    print(f"当前重复信息: {widget.current_duplicate_info is not None}")
    print(f"定时器是否活跃: {widget.hide_timer.isActive()}")
    
    # 测试查看按钮点击
    print("\n--- 测试查看按钮点击 ---")
    print("模拟点击查看按钮...")
    widget.on_view_clicked()
    print(f"点击后标题: {widget.title_label.text()}")
    print(f"点击后内容: {widget.content_label.text()}")
    print(f"点击后查看按钮可见: {widget.view_button.isVisible()}")
    print(f"点击后关闭按钮可见: {widget.close_button.isVisible()}")
    print(f"点击后当前重复信息: {widget.current_duplicate_info is not None}")
    print(f"窗口是否可见: {widget.isVisible()}")
    
    # 测试自动回到监控状态的逻辑
    print("\n--- 测试自动状态切换逻辑 ---")
    
    # 再次显示重复信息
    widget.show_duplicate(duplicate_info)
    print("显示重复信息后:")
    print(f"  定时器活跃: {widget.hide_timer.isActive()}")
    print(f"  当前状态: 显示重复信息")
    
    # 模拟定时器触发（自动回到监控状态）
    print("\n模拟定时器触发（自动回到监控状态）...")
    widget.show_monitoring_status()  # 手动触发，模拟定时器效果
    print(f"自动切换后标题: {widget.title_label.text()}")
    print(f"自动切换后内容: {widget.content_label.text()}")
    print(f"自动切换后查看按钮可见: {widget.view_button.isVisible()}")
    print(f"自动切换后关闭按钮可见: {widget.close_button.isVisible()}")
    print(f"自动切换后当前重复信息: {widget.current_duplicate_info is not None}")
    print(f"窗口是否可见: {widget.isVisible()}")
    
    # 对比按需弹出模式
    print("\n=== 对比按需弹出模式 ===")
    widget.set_persistent_mode(False)
    print("✅ 设置为按需弹出模式")
    
    widget.show_duplicate(duplicate_info)
    print(f"显示重复信息 - 关闭按钮可见: {widget.close_button.isVisible()}")  # 应该是True
    
    print("\n=== 改进总结 ===")
    print("✅ 1. 持续显示模式下隐藏关闭按钮")
    print("   - 用户无需手动干预状态切换")
    print("   - 界面更简洁，减少用户困惑")
    print()
    print("✅ 2. 自动状态管理")
    print("   - 显示重复信息 → 自动定时回到监控状态")
    print("   - 监控状态 → 等待下次剪贴板变化")
    print("   - 完全无需用户干预")
    print()
    print("✅ 3. 智能定时器管理")
    print("   - 持续显示模式：定时器用于状态切换")
    print("   - 按需弹出模式：定时器用于隐藏窗口")
    print("   - 安全的连接断开和重连机制")
    print()
    print("✅ 4. 用户体验优化")
    print("   - 点击查看按钮：立即回到监控状态")
    print("   - 鼠标离开：启动定时器自动切换")
    print("   - 状态切换完全自动化")
    
    # 不启动事件循环，只测试功能
    widget.close()
    
except Exception as e:
    print(f"测试失败: {e}")
    import traceback
    traceback.print_exc()
