#!/usr/bin/env python3
"""
自动标签设置功能测试
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import unittest
from unittest.mock import Mock, patch
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from smartvault.ui.dialogs.settings_dialog import SettingsDialog

class TestAutoTagSettings(unittest.TestCase):
    """测试自动标签设置功能"""

    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()

    def setUp(self):
        """设置测试"""
        # 创建模拟配置
        self.mock_config = {
            "library_path": "/test/library",
            "ui": {"default_view_mode": "table"},
            "search": {"default_scope": "database"},
            "advanced": {"enable_logging": True},
            "auto_tags": {
                "enabled": True,
                "rules": []
            }
        }

        # 创建设置对话框
        with patch('smartvault.utils.config.load_config', return_value=self.mock_config):
            self.dialog = SettingsDialog()

    def test_auto_tag_tab_exists(self):
        """测试自动标签选项卡是否存在"""
        # 检查选项卡数量
        tab_count = self.dialog.tab_widget.count()
        self.assertGreaterEqual(tab_count, 6, "应该至少有6个选项卡（包括自动标签）")

        # 检查是否有自动标签选项卡
        tab_names = []
        for i in range(tab_count):
            tab_names.append(self.dialog.tab_widget.tabText(i))

        self.assertIn("自动标签", tab_names, "应该有自动标签选项卡")

    def test_auto_tag_tab_ui_elements(self):
        """测试自动标签选项卡的UI元素"""
        # 查找自动标签选项卡
        auto_tag_tab = None
        for i in range(self.dialog.tab_widget.count()):
            if self.dialog.tab_widget.tabText(i) == "自动标签":
                auto_tag_tab = self.dialog.tab_widget.widget(i)
                break

        self.assertIsNotNone(auto_tag_tab, "应该找到自动标签选项卡")

        # 检查是否有启用自动标签的复选框
        enable_checkbox = auto_tag_tab.findChild(type(None), "enable_auto_tags_check")
        if hasattr(self.dialog, 'enable_auto_tags_check'):
            self.assertIsNotNone(self.dialog.enable_auto_tags_check, "应该有启用自动标签的复选框")

    def test_auto_tag_rules_management(self):
        """测试自动标签规则管理"""
        # 检查是否有规则列表
        if hasattr(self.dialog, 'auto_tag_rules_table'):
            self.assertIsNotNone(self.dialog.auto_tag_rules_table, "应该有自动标签规则表格")

        # 检查是否有添加规则按钮
        if hasattr(self.dialog, 'add_auto_tag_rule_button'):
            self.assertIsNotNone(self.dialog.add_auto_tag_rule_button, "应该有添加规则按钮")

    def test_auto_tag_config_save(self):
        """测试自动标签配置保存"""
        # 模拟启用自动标签
        if hasattr(self.dialog, 'enable_auto_tags_check'):
            self.dialog.enable_auto_tags_check.setChecked(True)

        # 模拟保存配置
        with patch('smartvault.utils.config.save_config') as mock_save:
            # 这里我们只测试方法存在性，实际保存逻辑在后续实现
            pass

class TestAutoTagRule(unittest.TestCase):
    """测试自动标签规则"""

    def setUp(self):
        """设置测试"""
        from smartvault.services.auto_tag_service import AutoTagRule, ConditionType
        self.AutoTagRule = AutoTagRule
        self.ConditionType = ConditionType

    def test_rule_creation(self):
        """测试规则创建"""
        rule = self.AutoTagRule(
            id="test_rule",
            name="测试规则",
            condition_type=self.ConditionType.FILE_EXTENSION,
            condition_value="pdf,doc",
            tag_names=["文档", "重要"]
        )

        self.assertEqual(rule.id, "test_rule")
        self.assertEqual(rule.name, "测试规则")
        self.assertEqual(rule.condition_type, self.ConditionType.FILE_EXTENSION)
        self.assertEqual(rule.condition_value, "pdf,doc")
        self.assertEqual(rule.tag_names, ["文档", "重要"])
        self.assertTrue(rule.enabled)

    def test_file_extension_matching(self):
        """测试文件扩展名匹配"""
        rule = self.AutoTagRule(
            id="ext_rule",
            name="扩展名规则",
            condition_type=self.ConditionType.FILE_EXTENSION,
            condition_value="pdf,doc,docx",
            tag_names=["文档"]
        )

        # 测试匹配的文件
        file_info_pdf = {"name": "test.pdf", "size": 1024}
        self.assertTrue(rule.matches(file_info_pdf))

        file_info_doc = {"name": "document.doc", "size": 2048}
        self.assertTrue(rule.matches(file_info_doc))

        # 测试不匹配的文件
        file_info_txt = {"name": "readme.txt", "size": 512}
        self.assertFalse(rule.matches(file_info_txt))

    def test_file_name_pattern_matching(self):
        """测试文件名模式匹配"""
        rule = self.AutoTagRule(
            id="name_rule",
            name="文件名规则",
            condition_type=self.ConditionType.FILE_NAME_PATTERN,
            condition_value="report",
            tag_names=["报告"]
        )

        # 测试匹配的文件
        file_info_match = {"name": "monthly_report.pdf", "size": 1024}
        self.assertTrue(rule.matches(file_info_match))

        # 测试不匹配的文件
        file_info_no_match = {"name": "document.pdf", "size": 1024}
        self.assertFalse(rule.matches(file_info_no_match))

    def test_file_size_range_matching(self):
        """测试文件大小范围匹配"""
        rule = self.AutoTagRule(
            id="size_rule",
            name="大小规则",
            condition_type=self.ConditionType.FILE_SIZE_RANGE,
            condition_value="1MB-10MB",
            tag_names=["中等文件"]
        )

        # 测试匹配的文件（5MB）
        file_info_match = {"name": "video.mp4", "size": 5 * 1024 * 1024}
        self.assertTrue(rule.matches(file_info_match))

        # 测试不匹配的文件（太小）
        file_info_small = {"name": "small.txt", "size": 1024}
        self.assertFalse(rule.matches(file_info_small))

        # 测试不匹配的文件（太大）
        file_info_large = {"name": "large.zip", "size": 50 * 1024 * 1024}
        self.assertFalse(rule.matches(file_info_large))

    def test_disabled_rule(self):
        """测试禁用的规则"""
        rule = self.AutoTagRule(
            id="disabled_rule",
            name="禁用规则",
            condition_type=self.ConditionType.FILE_EXTENSION,
            condition_value="pdf",
            tag_names=["文档"],
            enabled=False
        )

        file_info = {"name": "test.pdf", "size": 1024}
        self.assertFalse(rule.matches(file_info))

def run_tests():
    """运行测试"""
    print("开始自动标签设置功能测试...")

    # 创建测试套件
    suite = unittest.TestSuite()
    suite.addTest(unittest.makeSuite(TestAutoTagSettings))
    suite.addTest(unittest.makeSuite(TestAutoTagRule))

    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    # 输出结果
    if result.wasSuccessful():
        print("\n✅ 所有自动标签设置测试通过！")
        return True
    else:
        print(f"\n❌ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        return False

if __name__ == "__main__":
    run_tests()
