#!/usr/bin/env python3
"""
AI管理器调试测试
调试AI管理器初始化和可用性问题
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_ai_manager_initialization():
    """调试AI管理器初始化过程"""
    print("🔍 调试AI管理器初始化过程...")
    
    try:
        from smartvault.services.ai.ai_manager import AIManager
        from smartvault.utils.config import save_ai_status, get_ai_status, load_config, get_ai_config
        
        # 启用AI功能
        save_ai_status(True)
        ai_status = get_ai_status()
        print(f"AI状态设置: {ai_status}")
        
        # 检查配置
        config = load_config()
        ai_config = get_ai_config()
        
        print(f"主配置中的AI状态: {config.get('advanced', {}).get('enable_ai_features', False)}")
        print(f"AI配置启用状态: {ai_config.get('enabled', False)}")
        print(f"AI配置阶段: {ai_config.get('stage', 'unknown')}")
        
        # 创建AI管理器
        ai_manager = AIManager()
        print(f"AI管理器创建成功")
        
        # 检查初始化前的状态
        print(f"初始化前 - enabled: {ai_manager.enabled}")
        print(f"初始化前 - initialization_status: {ai_manager.initialization_status}")
        print(f"初始化前 - current_stage: {ai_manager.current_stage}")
        
        # 初始化AI管理器
        print("\n开始初始化AI管理器...")
        success = ai_manager.initialize(config)
        
        print(f"初始化结果: {success}")
        print(f"初始化后 - enabled: {ai_manager.enabled}")
        print(f"初始化后 - initialization_status: {ai_manager.initialization_status}")
        print(f"初始化后 - current_stage: {ai_manager.current_stage}")
        print(f"初始化后 - last_error: {ai_manager.last_error}")
        
        # 检查可用性
        available = ai_manager.is_available()
        print(f"AI可用性: {available}")
        
        # 检查各个引擎的状态
        print(f"智能规则引擎: {ai_manager.smart_rule_engine is not None}")
        print(f"自适应规则引擎: {ai_manager.adaptive_rule_engine is not None}")
        print(f"机器学习引擎: {ai_manager.ml_engine is not None}")
        
        # 获取详细状态
        status = ai_manager.get_status()
        print(f"\n详细状态:")
        for key, value in status.items():
            print(f"  {key}: {value}")
        
        # 测试标签建议
        if available:
            test_file = {'name': 'test.json', 'extension': '.json', 'path': '/test.json'}
            suggestions = ai_manager.suggest_tags(test_file)
            print(f"\n标签建议测试: {suggestions}")
        else:
            print("\n❌ AI不可用，无法测试标签建议")
        
        # 恢复状态
        save_ai_status(False)
        
        return available
        
    except Exception as e:
        print(f"❌ AI管理器调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_ai_config_details():
    """调试AI配置详情"""
    print("\n🔍 调试AI配置详情...")
    
    try:
        from smartvault.utils.config import load_config, get_ai_config, get_ai_status
        
        # 加载配置
        config = load_config()
        ai_config = get_ai_config()
        ai_status = get_ai_status()
        
        print("配置详情:")
        print(f"AI状态函数返回: {ai_status}")
        print(f"主配置中的advanced.enable_ai_features: {config.get('advanced', {}).get('enable_ai_features', 'NOT_SET')}")
        print(f"AI配置中的enabled: {ai_config.get('enabled', 'NOT_SET')}")
        
        print(f"\nAI配置完整内容:")
        for key, value in ai_config.items():
            print(f"  {key}: {value}")
        
        print(f"\n高级配置内容:")
        advanced_config = config.get('advanced', {})
        for key, value in advanced_config.items():
            print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI配置调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_smart_rule_engine():
    """调试智能规则引擎"""
    print("\n🔍 调试智能规则引擎...")
    
    try:
        from smartvault.services.ai.smart_rule_engine import SmartRuleEngine
        
        # 创建规则引擎
        engine = SmartRuleEngine()
        print("智能规则引擎创建成功")
        
        # 初始化
        success = engine.initialize({}, None, None)
        print(f"规则引擎初始化: {success}")
        
        # 测试标签建议
        test_file = {'name': 'config.json', 'extension': '.json', 'path': '/test/config.json'}
        suggestions = engine.suggest_tags(test_file)
        print(f"规则引擎标签建议: {suggestions}")
        
        return len(suggestions) > 0
        
    except Exception as e:
        print(f"❌ 智能规则引擎调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 AI管理器调试测试")
    print("=" * 60)
    
    # 测试1: AI配置详情
    test1 = debug_ai_config_details()
    
    # 测试2: 智能规则引擎
    test2 = debug_smart_rule_engine()
    
    # 测试3: AI管理器初始化
    test3 = debug_ai_manager_initialization()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 调试测试结果:")
    print(f"AI配置详情: {'✅' if test1 else '❌'}")
    print(f"智能规则引擎: {'✅' if test2 else '❌'}")
    print(f"AI管理器初始化: {'✅' if test3 else '❌'}")
    
    if all([test1, test2, test3]):
        print("\n🎉 所有调试测试通过！")
        return 0
    else:
        print("\n⚠️ 发现问题，需要修复")
        return 1

if __name__ == "__main__":
    sys.exit(main())
