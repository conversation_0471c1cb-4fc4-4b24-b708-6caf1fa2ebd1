"""
数据库模块单元测试
"""

import os
import unittest
import tempfile
from datetime import datetime
from smartvault.data.database import Database


class TestDatabase(unittest.TestCase):
    """数据库模块测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时数据库文件
        self.temp_dir = tempfile.TemporaryDirectory()
        self.db_path = os.path.join(self.temp_dir.name, "test.db")
        self.db = Database(self.db_path)
    
    def tearDown(self):
        """测试后清理"""
        # 关闭数据库连接
        if self.db.conn:
            self.db.close()
        
        # 删除临时目录
        self.temp_dir.cleanup()
    
    def test_init_db(self):
        """测试数据库初始化"""
        # 验证数据库文件是否存在
        self.assertTrue(os.path.exists(self.db_path))
        
        # 验证数据库连接是否有效
        self.assertIsNotNone(self.db.conn)
        
        # 验证表是否已创建
        cursor = self.db.conn.cursor()
        
        # 检查files表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='files'")
        self.assertIsNotNone(cursor.fetchone())
        
        # 检查tags表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='tags'")
        self.assertIsNotNone(cursor.fetchone())
        
        # 检查file_tags表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='file_tags'")
        self.assertIsNotNone(cursor.fetchone())
    
    def test_close(self):
        """测试关闭数据库连接"""
        # 关闭数据库连接
        self.db.close()
        
        # 验证连接已关闭
        self.assertIsNone(self.db.conn)
        
        # 重新初始化数据库，以便tearDown能正常工作
        self.db = Database(self.db_path)


if __name__ == "__main__":
    unittest.main()
