#!/usr/bin/env python3
"""
SmartVault 文件库移动修复验证测试
验证修复后的文件库移动功能是否能正确删除旧文件库
"""

import sys
import os
import tempfile
import shutil
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from smartvault.utils.config import load_config, save_config
from smartvault.data.file_system import FileSystem
from smartvault.services.file import FileService


class MoveFixTester:
    """移动修复测试器"""
    
    def __init__(self):
        self.original_config = load_config()
        self.temp_dirs = []
        
    def test_move_with_database_close(self):
        """测试关闭数据库连接后的移动操作"""
        print("🔍 测试关闭数据库连接后的移动操作...")
        
        # 创建源文件库
        source_temp = tempfile.mkdtemp(prefix="smartvault_move_test_")
        self.temp_dirs.append(source_temp)
        
        file_system = FileSystem()
        success, source_library, details = file_system.create_library(source_temp)
        
        if not success:
            print(f"❌ 创建源文件库失败: {details}")
            return False
        
        print(f"   源文件库: {source_library}")
        
        # 设置为当前文件库
        config = load_config()
        config["library_path"] = source_library
        save_config(config)
        
        # 创建FileService实例并添加一些数据
        file_service = FileService()
        file_count_before = file_service.get_file_count()
        print(f"   移动前文件数: {file_count_before}")
        
        # 关键步骤：关闭数据库连接
        print("   关闭数据库连接...")
        file_service.db.close()
        file_service._db = None
        
        # 创建目标目录
        target_temp = tempfile.mkdtemp(prefix="smartvault_move_target_")
        self.temp_dirs.append(target_temp)
        
        print(f"   目标目录: {target_temp}")
        
        # 执行移动操作
        move_file_system = FileSystem(source_library)
        
        try:
            print("   开始移动操作...")
            success = move_file_system.move_library(target_temp)
            
            if success:
                print("   ✅ 移动操作成功")
                
                # 验证目标文件库
                target_library = target_temp
                target_db_path = os.path.join(target_library, "data", "smartvault.db")
                
                if os.path.exists(target_db_path):
                    print("   ✅ 目标数据库文件存在")
                    
                    # 测试新数据库连接
                    new_file_service = FileService()
                    new_file_service.switch_library(target_library)
                    file_count_after = new_file_service.get_file_count()
                    new_file_service.db.close()
                    
                    print(f"   移动后文件数: {file_count_after}")
                    
                    if file_count_after == file_count_before:
                        print("   ✅ 数据完整性验证通过")
                    else:
                        print("   ❌ 数据完整性验证失败")
                        return False
                else:
                    print("   ❌ 目标数据库文件不存在")
                    return False
                
                # 关键测试：尝试删除源文件库
                print("   测试删除源文件库...")
                try:
                    shutil.rmtree(source_library)
                    print("   ✅ 源文件库删除成功！")
                    return True
                except Exception as e:
                    print(f"   ❌ 源文件库删除失败: {e}")
                    return False
            else:
                print("   ❌ 移动操作失败")
                return False
                
        except Exception as e:
            print(f"   ❌ 移动过程中发生错误: {e}")
            return False
    
    def test_move_without_database_close(self):
        """测试不关闭数据库连接的移动操作（对比测试）"""
        print("\n🔍 测试不关闭数据库连接的移动操作（对比测试）...")
        
        # 创建源文件库
        source_temp = tempfile.mkdtemp(prefix="smartvault_no_close_test_")
        self.temp_dirs.append(source_temp)
        
        file_system = FileSystem()
        success, source_library, details = file_system.create_library(source_temp)
        
        if not success:
            print(f"❌ 创建源文件库失败: {details}")
            return False
        
        print(f"   源文件库: {source_library}")
        
        # 设置为当前文件库
        config = load_config()
        config["library_path"] = source_library
        save_config(config)
        
        # 创建FileService实例但不关闭连接
        file_service = FileService()
        file_count_before = file_service.get_file_count()
        print(f"   移动前文件数: {file_count_before}")
        
        # 注意：这里不关闭数据库连接
        print("   保持数据库连接打开...")
        
        # 创建目标目录
        target_temp = tempfile.mkdtemp(prefix="smartvault_no_close_target_")
        self.temp_dirs.append(target_temp)
        
        print(f"   目标目录: {target_temp}")
        
        # 执行移动操作
        move_file_system = FileSystem(source_library)
        
        try:
            print("   开始移动操作...")
            success = move_file_system.move_library(target_temp)
            
            if success:
                print("   ✅ 移动操作成功")
                
                # 关键测试：尝试删除源文件库
                print("   测试删除源文件库...")
                try:
                    shutil.rmtree(source_library)
                    print("   ✅ 源文件库删除成功（意外）")
                    return True
                except Exception as e:
                    print(f"   ❌ 源文件库删除失败（预期）: {e}")
                    
                    # 关闭数据库连接后再试
                    print("   关闭数据库连接后重试删除...")
                    file_service.db.close()
                    
                    try:
                        shutil.rmtree(source_library)
                        print("   ✅ 关闭连接后删除成功")
                        return True
                    except Exception as e2:
                        print(f"   ❌ 关闭连接后仍删除失败: {e2}")
                        return False
            else:
                print("   ❌ 移动操作失败")
                return False
                
        except Exception as e:
            print(f"   ❌ 移动过程中发生错误: {e}")
            return False
        finally:
            # 确保关闭连接
            if file_service._db:
                file_service.db.close()
    
    def cleanup(self):
        """清理测试环境"""
        print("\n🧹 清理测试环境...")
        
        # 恢复原始配置
        save_config(self.original_config)
        
        # 删除临时目录
        for temp_dir in self.temp_dirs:
            try:
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
                    print(f"   ✅ 已删除: {temp_dir}")
            except Exception as e:
                print(f"   ⚠️  删除失败: {temp_dir} - {e}")
    
    def run_tests(self):
        """运行所有测试"""
        print("🚀 SmartVault 文件库移动修复验证测试")
        print("=" * 60)
        
        try:
            # 测试修复后的移动操作
            test1_result = self.test_move_with_database_close()
            
            # 对比测试：不关闭数据库连接的移动操作
            test2_result = self.test_move_without_database_close()
            
        finally:
            self.cleanup()
        
        print("\n" + "=" * 60)
        print("📊 测试结果汇总")
        print("=" * 60)
        
        print(f"关闭数据库连接后移动: {'✅ 通过' if test1_result else '❌ 失败'}")
        print(f"不关闭数据库连接移动: {'✅ 通过' if test2_result else '❌ 失败'}")
        
        if test1_result:
            print("\n🎉 修复验证成功！")
            print("关闭数据库连接后，文件库移动和删除都能正常工作。")
            print("\n修复要点:")
            print("• 移动前关闭主程序的数据库连接")
            print("• 移动线程中不创建数据库连接")
            print("• 移动完成后可以正常删除旧文件库")
            return True
        else:
            print("\n⚠️  修复验证失败，可能还存在问题。")
            return False


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    try:
        tester = MoveFixTester()
        success = tester.run_tests()
        
        # 自动退出
        QTimer.singleShot(1000, app.quit)
        app.exec()
        
        return 0 if success else 1
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
