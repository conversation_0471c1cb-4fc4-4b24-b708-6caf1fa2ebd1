#!/usr/bin/env python3
"""
SmartVault 依赖安装脚本
自动安装SmartVault运行所需的所有Python包
"""

import subprocess
import sys
import os

def install_package(package_name):
    """安装指定的Python包"""
    try:
        print(f"正在安装 {package_name}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name
        ], capture_output=True, text=True, check=True)
        print(f"[OK] {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"[ERROR] {package_name} 安装失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def check_package(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        print(f"[OK] {package_name} 已安装")
        return True
    except ImportError:
        print(f"[MISSING] {package_name} 未安装")
        return False

def main():
    """主函数"""
    print("SmartVault 依赖检查和安装")
    print("=" * 50)

    # SmartVault所需的依赖包
    dependencies = [
        "PySide6",      # GUI框架
        "Pillow",       # 图像处理
        "schedule",     # 任务调度（备份服务）
        "sqlalchemy",   # 数据库ORM
    ]

    # 检查当前Python环境
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print()

    # 检查已安装的包
    print("检查已安装的依赖包...")
    installed_packages = []
    missing_packages = []

    for package in dependencies:
        if check_package(package):
            installed_packages.append(package)
        else:
            missing_packages.append(package)

    print()

    # 安装缺失的包
    if missing_packages:
        print(f"需要安装 {len(missing_packages)} 个缺失的包...")
        failed_packages = []

        for package in missing_packages:
            if not install_package(package):
                failed_packages.append(package)

        print()

        if failed_packages:
            print("[ERROR] 以下包安装失败:")
            for package in failed_packages:
                print(f"   - {package}")
            print("\n请手动安装这些包或检查网络连接。")
            return False
        else:
            print("[OK] 所有缺失的包已成功安装！")
    else:
        print("[OK] 所有依赖包都已安装！")

    print()
    print("依赖检查完成！")
    print("现在可以运行 SmartVault:")
    print("   python -m smartvault.main")

    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n[INTERRUPTED] 安装被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n[ERROR] 安装过程中发生错误: {e}")
        sys.exit(1)
