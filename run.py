"""
SmartVault 启动脚本
"""

import sys
import os

# 设置控制台编码为UTF-8，避免emoji字符编码错误
if sys.platform.startswith('win'):
    try:
        # 尝试设置控制台代码页为UTF-8
        os.system('chcp 65001 >nul 2>&1')
    except:
        pass

    # 重新配置标准输出编码
    try:
        sys.stdout.reconfigure(encoding='utf-8', errors='replace')
        sys.stderr.reconfigure(encoding='utf-8', errors='replace')
    except:
        # 如果reconfigure不可用，使用替代方案
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')

from smartvault.main import main

if __name__ == "__main__":
    main()
