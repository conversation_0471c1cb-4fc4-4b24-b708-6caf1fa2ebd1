#!/usr/bin/env python3
"""
文件监控UI集成测试
测试工具栏监控开关和UI反馈功能
"""

import sys
import os
import tempfile
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import QTimer

from smartvault.ui.main_window import MainWindow
from smartvault.services.file_monitor_service import FileMonitorService


def test_monitor_ui_integration():
    """测试监控UI集成功能"""
    print("🧪 开始文件监控UI集成测试...")

    app = QApplication(sys.argv)

    try:
        # 创建主窗口
        print("\n1️⃣ 创建主窗口...")
        main_window = MainWindow()
        main_window.show()

        # 等待UI初始化完成
        app.processEvents()
        time.sleep(1)

        # 检查监控服务是否正确初始化
        print("\n2️⃣ 检查监控服务...")
        assert hasattr(main_window, 'monitor_service'), "主窗口应该有monitor_service属性"
        assert main_window.monitor_service is not None, "监控服务应该已初始化"
        print("   ✅ 监控服务已正确初始化")

        # 检查工具栏监控按钮
        print("\n3️⃣ 检查工具栏监控按钮...")
        assert hasattr(main_window, 'toolbar_manager'), "主窗口应该有toolbar_manager"
        toolbar = main_window.toolbar_manager
        assert hasattr(toolbar, 'monitor_toggle_button'), "工具栏应该有监控开关按钮"
        print("   ✅ 监控开关按钮已创建")

        # 检查按钮初始状态
        button = toolbar.monitor_toggle_button
        print(f"   📊 按钮初始状态: {button.text()}")
        print(f"   📊 按钮工具提示: {button.toolTip()}")

        # 创建测试监控配置
        print("\n4️⃣ 创建测试监控配置...")
        test_dir = tempfile.mkdtemp(prefix="smartvault_monitor_test_")
        print(f"   📁 测试目录: {test_dir}")

        # 添加监控配置
        monitor_id = main_window.monitor_service.add_monitor_folder(
            folder_path=test_dir,
            entry_mode="link",
            file_patterns=["*.txt"],
            auto_add=True,
            recursive=False
        )
        print(f"   ✅ 监控配置已添加: {monitor_id}")

        # 更新工具栏状态
        toolbar.update_monitor_status()
        app.processEvents()
        print(f"   📊 更新后按钮状态: {button.text()}")

        # 测试启动监控
        print("\n5️⃣ 测试启动监控...")
        success = main_window.monitor_service.start_monitoring(monitor_id)
        assert success, "监控应该启动成功"

        # 更新UI状态
        toolbar.update_monitor_status()
        app.processEvents()
        print(f"   ✅ 监控已启动")
        print(f"   📊 按钮状态: {button.text()}")
        print(f"   📊 按钮样式: {'绿色' if button.isChecked() else '红色'}")

        # 测试监控事件处理
        print("\n6️⃣ 测试监控事件处理...")

        # 创建测试文件触发监控事件
        test_file = os.path.join(test_dir, "test_monitor.txt")
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("测试监控文件内容")
        print(f"   📄 创建测试文件: {test_file}")

        # 等待监控事件处理
        print("   ⏳ 等待监控事件处理...")
        for i in range(5):
            app.processEvents()
            time.sleep(0.5)
            print(f"   ⏳ 等待中... {i+1}/5")

        # 检查进度条是否显示
        if hasattr(toolbar, 'progress_bar'):
            print("   📊 进度条组件存在")
        else:
            print("   ⚠️ 进度条组件不存在")

        # 测试停止监控
        print("\n7️⃣ 测试停止监控...")
        success = main_window.monitor_service.stop_monitoring(monitor_id)
        assert success, "监控应该停止成功"

        # 更新UI状态
        toolbar.update_monitor_status()
        app.processEvents()
        print(f"   ✅ 监控已停止")
        print(f"   📊 按钮状态: {button.text()}")
        print(f"   📊 按钮样式: {'绿色' if button.isChecked() else '红色'}")

        # 测试监控开关按钮功能
        print("\n8️⃣ 测试监控开关按钮...")

        # 模拟点击按钮启动监控
        print("   🖱️ 模拟点击按钮启动监控...")
        button.click()
        app.processEvents()
        time.sleep(0.5)

        # 检查监控状态
        stats = main_window.monitor_service.get_monitor_statistics()
        print(f"   📊 监控统计: {stats}")

        # 再次点击停止监控
        print("   🖱️ 模拟点击按钮停止监控...")
        button.click()
        app.processEvents()
        time.sleep(0.5)

        # 检查最终状态
        stats = main_window.monitor_service.get_monitor_statistics()
        print(f"   📊 最终监控统计: {stats}")

        print("\n✅ 文件监控UI集成测试完成！")

        # 清理测试数据
        print("\n🧹 清理测试数据...")
        try:
            os.remove(test_file)
            os.rmdir(test_dir)
            print("   ✅ 测试数据已清理")
        except Exception as e:
            print(f"   ⚠️ 清理测试数据失败: {e}")

        # 显示测试结果对话框
        QMessageBox.information(
            main_window,
            "测试完成",
            "文件监控UI集成测试已完成！\n\n"
            "测试内容：\n"
            "✅ 监控服务初始化\n"
            "✅ 工具栏监控按钮\n"
            "✅ 监控状态更新\n"
            "✅ 监控事件处理\n"
            "✅ UI状态反馈\n"
            "✅ 开关按钮功能"
        )

        # 保持窗口打开以便手动测试
        print("\n💡 窗口将保持打开状态，您可以手动测试监控功能")
        print("   - 点击工具栏的监控按钮测试开关功能")
        print("   - 在设置中配置监控文件夹")
        print("   - 观察状态栏的监控统计信息")

        return app.exec()

    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

        QMessageBox.critical(
            None,
            "测试失败",
            f"文件监控UI集成测试失败：\n\n{e}"
        )
        return 1


if __name__ == "__main__":
    exit_code = test_monitor_ui_integration()
    sys.exit(exit_code)
