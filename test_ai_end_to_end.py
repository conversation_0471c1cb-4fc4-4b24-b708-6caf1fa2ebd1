#!/usr/bin/env python3
"""
AI功能端到端集成测试
测试完整的AI功能工作流程
"""

import sys
import os
import tempfile
import shutil

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_files():
    """创建测试文件"""
    test_dir = tempfile.mkdtemp(prefix="smartvault_ai_test_")
    
    # 创建不同类型的测试文件
    test_files = [
        ("test_project.py", "# Python项目文件\nprint('Hello World')"),
        ("document.txt", "这是一个文档文件"),
        ("config.json", '{"name": "test", "version": "1.0"}'),
        ("README.md", "# 项目说明\n这是一个测试项目"),
        ("backup_file.bak", "备份文件内容")
    ]
    
    created_files = []
    for filename, content in test_files:
        file_path = os.path.join(test_dir, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        created_files.append(file_path)
    
    return test_dir, created_files

def test_ai_tag_suggestions():
    """测试AI标签建议功能"""
    print("🔍 测试AI标签建议功能...")
    
    try:
        from smartvault.services.ai.ai_manager import AIManager
        from smartvault.utils.config import load_config
        
        # 创建AI管理器
        ai_manager = AIManager()
        config = load_config()
        
        # 初始化AI管理器
        success = ai_manager.initialize(config)
        if not success:
            print("❌ AI管理器初始化失败")
            return False
        
        # 测试不同类型文件的标签建议
        test_cases = [
            {
                'name': 'test_project.py',
                'path': '/test/test_project.py',
                'extension': '.py',
                'expected_tags': ['代码', 'Python']
            },
            {
                'name': 'document.txt',
                'path': '/test/document.txt',
                'extension': '.txt',
                'expected_tags': ['文档']
            },
            {
                'name': 'config.json',
                'path': '/test/config.json',
                'extension': '.json',
                'expected_tags': ['配置文件']
            }
        ]
        
        all_passed = True
        for test_case in test_cases:
            suggestions = ai_manager.suggest_tags(test_case)
            print(f"文件: {test_case['name']}")
            print(f"建议标签: {suggestions}")
            
            # 检查是否包含预期标签
            has_expected = any(tag in suggestions for tag in test_case['expected_tags'])
            if has_expected:
                print("✅ 包含预期标签")
            else:
                print("⚠️ 未包含预期标签")
                all_passed = False
            print()
        
        return all_passed
        
    except Exception as e:
        print(f"❌ AI标签建议测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_service_ai_integration():
    """测试文件服务AI集成"""
    print("🔍 测试文件服务AI集成...")
    
    try:
        from smartvault.services.file import FileService
        from smartvault.services.tag_service import TagService
        
        # 创建测试文件
        test_dir, test_files = create_test_files()
        
        try:
            # 创建文件服务
            file_service = FileService()
            tag_service = TagService()
            
            print(f"创建了 {len(test_files)} 个测试文件")
            
            # 测试添加文件（应该触发AI标签建议）
            added_files = []
            for test_file in test_files[:2]:  # 只测试前两个文件
                try:
                    print(f"添加文件: {os.path.basename(test_file)}")
                    file_id = file_service.add_file(test_file, mode="link")
                    added_files.append(file_id)
                    print(f"✅ 文件添加成功: {file_id}")
                    
                    # 检查是否有标签被应用
                    tags = tag_service.get_file_tags(file_id)
                    if tags:
                        tag_names = [tag['name'] for tag in tags]
                        print(f"✅ 应用的标签: {tag_names}")
                    else:
                        print("⚠️ 未应用任何标签")
                    
                except Exception as e:
                    print(f"❌ 添加文件失败: {e}")
            
            return len(added_files) > 0
            
        finally:
            # 清理测试文件
            shutil.rmtree(test_dir, ignore_errors=True)
        
    except Exception as e:
        print(f"❌ 文件服务AI集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_settings_functionality():
    """测试AI设置功能"""
    print("🔍 测试AI设置功能...")
    
    try:
        from smartvault.ui.dialogs.settings.pages.ai.utils.ai_config_helper import AIConfigManager
        from smartvault.utils.config import get_ai_status, save_ai_status
        
        # 测试配置管理器
        config_manager = AIConfigManager()
        
        # 测试加载配置
        config = config_manager.load_ai_config()
        print(f"✅ 配置加载成功: {len(config)} 项")
        
        # 测试配置验证
        is_valid, error_msg = config_manager.validate_ai_config(config)
        print(f"✅ 配置验证: {is_valid}")
        
        # 测试AI状态管理
        original_status = get_ai_status()
        print(f"原始AI状态: {original_status}")
        
        # 切换AI状态
        save_ai_status(not original_status)
        new_status = get_ai_status()
        print(f"切换后AI状态: {new_status}")
        
        # 恢复原始状态
        save_ai_status(original_status)
        restored_status = get_ai_status()
        print(f"恢复后AI状态: {restored_status}")
        
        return restored_status == original_status
        
    except Exception as e:
        print(f"❌ AI设置功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_config_persistence():
    """测试AI配置持久化"""
    print("🔍 测试AI配置持久化...")
    
    try:
        from smartvault.utils.config import load_config, save_config, get_ai_config
        
        # 加载当前配置
        config = load_config()
        original_ai_config = get_ai_config()
        
        # 修改AI配置
        test_config = original_ai_config.copy()
        test_config['stage'] = 'ml_basic'
        test_config['features']['project_detection']['enabled'] = False
        
        # 保存修改后的配置
        config['ai'] = test_config
        save_config(config)
        
        # 重新加载验证
        new_config = get_ai_config()
        
        # 检查修改是否保存成功
        stage_correct = new_config['stage'] == 'ml_basic'
        feature_correct = not new_config['features']['project_detection']['enabled']
        
        # 恢复原始配置
        config['ai'] = original_ai_config
        save_config(config)
        
        if stage_correct and feature_correct:
            print("✅ AI配置持久化正常")
            return True
        else:
            print("❌ AI配置持久化失败")
            return False
        
    except Exception as e:
        print(f"❌ AI配置持久化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 AI功能端到端集成测试")
    print("=" * 70)
    
    # 测试1: AI标签建议功能
    test1 = test_ai_tag_suggestions()
    
    # 测试2: 文件服务AI集成
    test2 = test_file_service_ai_integration()
    
    # 测试3: AI设置功能
    test3 = test_ai_settings_functionality()
    
    # 测试4: AI配置持久化
    test4 = test_ai_config_persistence()
    
    # 总结
    print("\n" + "=" * 70)
    print("📊 端到端测试结果总结:")
    print(f"AI标签建议功能: {'✅ 通过' if test1 else '❌ 失败'}")
    print(f"文件服务AI集成: {'✅ 通过' if test2 else '❌ 失败'}")
    print(f"AI设置功能: {'✅ 通过' if test3 else '❌ 失败'}")
    print(f"AI配置持久化: {'✅ 通过' if test4 else '❌ 失败'}")
    
    if all([test1, test2, test3, test4]):
        print("\n🎉 所有端到端测试通过！")
        print("✅ AI功能已完全集成并正常工作")
        return 0
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查")
        return 1

if __name__ == "__main__":
    sys.exit(main())
