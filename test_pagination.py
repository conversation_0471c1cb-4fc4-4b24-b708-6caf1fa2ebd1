"""
测试分页加载功能
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QTextEdit
from PySide6.QtCore import QTimer


class PaginationTestWindow(QMainWindow):
    """分页测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("分页加载测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 状态标签
        self.status_label = QLabel("准备测试分页加载...")
        layout.addWidget(self.status_label)
        
        # 测试按钮
        self.test_btn = QPushButton("测试加载100个文件")
        self.test_btn.clicked.connect(self.test_load_files)
        layout.addWidget(self.test_btn)
        
        self.test_all_btn = QPushButton("测试加载所有文件")
        self.test_all_btn.clicked.connect(self.test_load_all_files)
        layout.addWidget(self.test_all_btn)
        
        # 结果显示
        self.result_text = QTextEdit()
        layout.addWidget(self.result_text)
        
        # 延迟初始化
        QTimer.singleShot(100, self.init_services)
    
    def init_services(self):
        """初始化服务"""
        try:
            from smartvault.services.file import FileService
            self.file_service = FileService()
            
            total_count = self.file_service.get_file_count()
            self.status_label.setText(f"服务初始化成功，数据库中共有 {total_count} 个文件")
            
        except Exception as e:
            self.status_label.setText(f"服务初始化失败: {e}")
            import traceback
            self.result_text.setText(traceback.format_exc())
    
    def test_load_files(self):
        """测试加载100个文件"""
        try:
            self.status_label.setText("正在加载前100个文件...")
            
            files = self.file_service.get_files(limit=100, offset=0)
            
            result = f"成功加载 {len(files)} 个文件:\n\n"
            for i, file in enumerate(files[:10]):  # 只显示前10个
                result += f"{i+1}. {file['name']} ({file['entry_type']})\n"
            
            if len(files) > 10:
                result += f"... 还有 {len(files) - 10} 个文件\n"
            
            self.result_text.setText(result)
            self.status_label.setText(f"✅ 成功加载 {len(files)} 个文件")
            
        except Exception as e:
            self.status_label.setText(f"❌ 加载失败: {e}")
            import traceback
            self.result_text.setText(traceback.format_exc())
    
    def test_load_all_files(self):
        """测试加载所有文件"""
        try:
            self.status_label.setText("正在加载所有文件...")
            
            files = self.file_service.get_files(limit=999999, offset=0)
            
            result = f"成功加载 {len(files)} 个文件:\n\n"
            for i, file in enumerate(files[:10]):  # 只显示前10个
                result += f"{i+1}. {file['name']} ({file['entry_type']})\n"
            
            if len(files) > 10:
                result += f"... 还有 {len(files) - 10} 个文件\n"
            
            self.result_text.setText(result)
            self.status_label.setText(f"✅ 成功加载所有 {len(files)} 个文件")
            
        except Exception as e:
            self.status_label.setText(f"❌ 加载失败: {e}")
            import traceback
            self.result_text.setText(traceback.format_exc())


def main():
    """主函数"""
    print("🧪 启动分页加载测试...")
    
    app = QApplication(sys.argv)
    window = PaginationTestWindow()
    window.show()
    
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
