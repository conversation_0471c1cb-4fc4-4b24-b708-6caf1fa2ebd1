#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SmartVault 数据库安全与可靠性测试套件
专注于数据安全、完整性和长期稳定性
"""

import sys
import os
import time
import threading
import random
import string
import shutil
import sqlite3
import hashlib
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed

sys.path.insert(0, '.')

from smartvault.data.database import Database
from smartvault.services.file import FileService
from smartvault.services.tag_service import TagService

class DatabaseSafetyTest:
    """数据库安全测试类"""

    def __init__(self):
        self.test_results = {}
        self.start_time = None
        self.backup_dir = "database_test_backups"

    def log(self, message):
        """记录测试日志"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        print(f"[{timestamp}] {message}")

    def create_backup_dir(self):
        """创建备份目录"""
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)

    def get_database_checksum(self, db_path):
        """计算数据库文件的校验和"""
        if not os.path.exists(db_path):
            return None

        with open(db_path, 'rb') as f:
            content = f.read()
            return hashlib.md5(content).hexdigest()

    def get_data_checksum(self):
        """计算数据库内容的校验和"""
        db = Database.create_from_config()
        cursor = db.conn.cursor()

        # 获取所有表的数据并计算校验和
        checksums = {}

        # 文件表
        cursor.execute("SELECT * FROM files ORDER BY id")
        files_data = str(cursor.fetchall())
        checksums['files'] = hashlib.md5(files_data.encode()).hexdigest()

        # 标签表
        cursor.execute("SELECT * FROM tags ORDER BY id")
        tags_data = str(cursor.fetchall())
        checksums['tags'] = hashlib.md5(tags_data.encode()).hexdigest()

        # 文件标签关联表
        cursor.execute("SELECT * FROM file_tags ORDER BY file_id, tag_id")
        file_tags_data = str(cursor.fetchall())
        checksums['file_tags'] = hashlib.md5(file_tags_data.encode()).hexdigest()

        db.close()
        return checksums

    def test_1_data_integrity_verification(self):
        """测试1：数据完整性验证"""
        self.log("🛡️ 开始数据完整性验证测试...")

        # 记录初始状态
        initial_checksums = self.get_data_checksum()
        self.log(f"  📊 初始数据校验和: {initial_checksums}")

        # 执行大量读操作，验证数据不会被意外修改
        file_service = FileService()

        for i in range(100):
            # 随机查询操作
            limit = random.randint(50, 500)
            offset = random.randint(0, 6000)
            files = file_service.get_files(limit=limit, offset=offset)

            if (i + 1) % 20 == 0:
                # 验证数据完整性
                current_checksums = self.get_data_checksum()
                if current_checksums != initial_checksums:
                    self.log(f"  ❌ 第{i+1}次查询后数据校验和发生变化!")
                    return False
                self.log(f"  ✅ 第{i+1}次查询后数据完整性验证通过")

        final_checksums = self.get_data_checksum()
        integrity_ok = (final_checksums == initial_checksums)

        result = {
            'initial_checksums': initial_checksums,
            'final_checksums': final_checksums,
            'integrity_preserved': integrity_ok,
            'read_operations': 100
        }

        self.test_results['data_integrity'] = result
        self.log(f"  {'✅' if integrity_ok else '❌'} 数据完整性测试{'通过' if integrity_ok else '失败'}")
        return integrity_ok

    def test_2_transaction_safety(self):
        """测试2：事务安全性测试"""
        self.log("🛡️ 开始事务安全性测试...")

        def concurrent_transaction_worker(worker_id, iterations):
            """并发事务工作线程"""
            errors = 0
            successes = 0

            for i in range(iterations):
                try:
                    db = Database.create_from_config()
                    cursor = db.conn.cursor()

                    # 开始事务
                    cursor.execute("BEGIN TRANSACTION")

                    # 模拟复杂操作：插入标签并关联文件
                    tag_name = f"test_tag_{worker_id}_{i}_{random.randint(1000, 9999)}"
                    tag_id = f"tag_{worker_id}_{i}_{random.randint(1000, 9999)}"

                    cursor.execute(
                        "INSERT INTO tags (id, name, parent_id, created_at) VALUES (?, ?, ?, ?)",
                        (tag_id, tag_name, None, datetime.now())
                    )

                    # 随机选择一些文件进行标签关联
                    cursor.execute("SELECT id FROM files LIMIT 5 OFFSET ?", (random.randint(0, 100),))
                    file_ids = [row[0] for row in cursor.fetchall()]

                    for file_id in file_ids:
                        cursor.execute(
                            "INSERT OR IGNORE INTO file_tags (file_id, tag_id, created_at) VALUES (?, ?, ?)",
                            (file_id, tag_id, datetime.now())
                        )

                    # 随机决定是否提交事务（模拟异常情况）
                    if random.random() > 0.1:  # 90%概率提交
                        cursor.execute("COMMIT")
                        successes += 1
                    else:  # 10%概率回滚
                        cursor.execute("ROLLBACK")

                    db.close()

                except Exception as e:
                    errors += 1
                    self.log(f"    ⚠️ 工作线程{worker_id}事务{i}发生错误: {e}")
                    try:
                        if 'db' in locals():
                            db.close()
                    except:
                        pass

            return {
                'worker_id': worker_id,
                'successes': successes,
                'errors': errors
            }

        # 记录事务测试前的数据状态
        pre_test_checksums = self.get_data_checksum()

        # 启动多个并发事务
        concurrency = 8
        iterations_per_worker = 10

        self.log(f"  📊 启动{concurrency}个并发事务，每个执行{iterations_per_worker}次操作")

        with ThreadPoolExecutor(max_workers=concurrency) as executor:
            futures = [
                executor.submit(concurrent_transaction_worker, i, iterations_per_worker)
                for i in range(concurrency)
            ]

            worker_results = []
            for future in as_completed(futures):
                worker_results.append(future.result())

        # 统计结果
        total_successes = sum(r['successes'] for r in worker_results)
        total_errors = sum(r['errors'] for r in worker_results)

        # 验证数据库状态
        post_test_checksums = self.get_data_checksum()

        # 清理测试数据
        self._cleanup_test_tags()

        result = {
            'concurrent_workers': concurrency,
            'total_transactions': concurrency * iterations_per_worker,
            'successful_transactions': total_successes,
            'failed_transactions': total_errors,
            'pre_test_checksums': pre_test_checksums,
            'post_test_checksums': post_test_checksums,
            'data_consistency_maintained': True  # 需要进一步验证
        }

        self.test_results['transaction_safety'] = result
        self.log(f"  ✅ 事务安全性测试完成: {total_successes}成功, {total_errors}失败")
        return True

    def _cleanup_test_tags(self):
        """清理测试标签"""
        try:
            db = Database.create_from_config()
            cursor = db.conn.cursor()

            # 删除测试标签的关联
            cursor.execute("DELETE FROM file_tags WHERE tag_id LIKE 'tag_%'")

            # 删除测试标签
            cursor.execute("DELETE FROM tags WHERE id LIKE 'tag_%'")

            db.conn.commit()
            db.close()

            self.log("  🧹 测试标签清理完成")
        except Exception as e:
            self.log(f"  ⚠️ 清理测试标签失败: {e}")

    def test_3_database_backup_recovery(self):
        """测试3：数据库备份与恢复测试"""
        self.log("🛡️ 开始数据库备份与恢复测试...")

        self.create_backup_dir()

        # 获取数据库文件路径
        from smartvault.utils.config import load_config
        config = load_config()
        db_path = 'smartvault.db'  # 默认数据库路径

        if not os.path.exists(db_path):
            self.log(f"  ❌ 数据库文件不存在: {db_path}")
            return False

        # 记录原始数据状态
        original_checksums = self.get_data_checksum()
        original_file_checksum = self.get_database_checksum(db_path)

        self.log(f"  📊 原始数据库文件大小: {os.path.getsize(db_path) / 1024 / 1024:.2f}MB")

        # 创建备份
        backup_path = os.path.join(self.backup_dir, f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db")

        try:
            shutil.copy2(db_path, backup_path)
            backup_file_checksum = self.get_database_checksum(backup_path)

            self.log(f"  ✅ 数据库备份创建成功: {backup_path}")
            self.log(f"  📊 备份文件大小: {os.path.getsize(backup_path) / 1024 / 1024:.2f}MB")

            # 验证备份文件完整性
            if backup_file_checksum == original_file_checksum:
                self.log("  ✅ 备份文件校验和验证通过")
            else:
                self.log("  ❌ 备份文件校验和验证失败")
                return False

        except Exception as e:
            self.log(f"  ❌ 创建备份失败: {e}")
            return False

        # 模拟数据库操作（添加一些测试数据）
        self._add_test_data()
        modified_checksums = self.get_data_checksum()

        # 验证数据已被修改
        if modified_checksums != original_checksums:
            self.log("  ✅ 测试数据添加成功，数据库状态已改变")
        else:
            self.log("  ⚠️ 测试数据添加可能失败，数据库状态未改变")

        # 模拟恢复操作（从备份恢复）
        try:
            # 关闭所有数据库连接
            time.sleep(0.5)  # 等待连接关闭

            # 恢复备份
            shutil.copy2(backup_path, db_path)

            # 验证恢复后的数据
            recovered_checksums = self.get_data_checksum()

            if recovered_checksums == original_checksums:
                self.log("  ✅ 数据库恢复成功，数据完整性验证通过")
                recovery_success = True
            else:
                self.log("  ❌ 数据库恢复后数据完整性验证失败")
                recovery_success = False

        except Exception as e:
            self.log(f"  ❌ 数据库恢复失败: {e}")
            recovery_success = False

        # 清理测试数据
        self._cleanup_test_data()

        result = {
            'backup_created': True,
            'backup_path': backup_path,
            'backup_size_mb': os.path.getsize(backup_path) / 1024 / 1024,
            'backup_checksum_valid': backup_file_checksum == original_file_checksum,
            'recovery_successful': recovery_success,
            'original_checksums': original_checksums,
            'recovered_checksums': recovered_checksums if 'recovered_checksums' in locals() else None
        }

        self.test_results['backup_recovery'] = result
        return recovery_success

    def _add_test_data(self):
        """添加测试数据"""
        try:
            db = Database.create_from_config()
            cursor = db.conn.cursor()

            # 添加测试标签
            test_tag_id = f"test_backup_{int(time.time())}"
            cursor.execute(
                "INSERT INTO tags (id, name, parent_id, created_at) VALUES (?, ?, ?, ?)",
                (test_tag_id, "备份测试标签", None, datetime.now())
            )

            db.conn.commit()
            db.close()

        except Exception as e:
            self.log(f"  ⚠️ 添加测试数据失败: {e}")

    def _cleanup_test_data(self):
        """清理测试数据"""
        try:
            db = Database.create_from_config()
            cursor = db.conn.cursor()

            # 删除测试标签
            cursor.execute("DELETE FROM tags WHERE name = '备份测试标签'")

            db.conn.commit()
            db.close()

        except Exception as e:
            self.log(f"  ⚠️ 清理测试数据失败: {e}")

    def test_4_corruption_detection(self):
        """测试4：数据损坏检测"""
        self.log("🛡️ 开始数据损坏检测测试...")

        db = Database.create_from_config()
        cursor = db.conn.cursor()

        issues = []

        # 检查外键约束
        cursor.execute("PRAGMA foreign_key_check")
        fk_violations = cursor.fetchall()
        if fk_violations:
            issues.append(f"外键约束违反: {len(fk_violations)}个")
            for violation in fk_violations[:5]:  # 只显示前5个
                self.log(f"    外键违反: {violation}")

        # 检查数据库完整性
        cursor.execute("PRAGMA integrity_check")
        integrity_result = cursor.fetchone()[0]
        if integrity_result != "ok":
            issues.append(f"数据库完整性检查失败: {integrity_result}")

        # 检查索引一致性
        cursor.execute("PRAGMA quick_check")
        quick_check_result = cursor.fetchone()[0]
        if quick_check_result != "ok":
            issues.append(f"快速检查失败: {quick_check_result}")

        # 检查孤立记录
        cursor.execute("""
            SELECT COUNT(*) FROM file_tags ft
            LEFT JOIN files f ON ft.file_id = f.id
            WHERE f.id IS NULL
        """)
        orphaned_file_tags = cursor.fetchone()[0]
        if orphaned_file_tags > 0:
            issues.append(f"孤立的文件标签关联: {orphaned_file_tags}个")

        cursor.execute("""
            SELECT COUNT(*) FROM file_tags ft
            LEFT JOIN tags t ON ft.tag_id = t.id
            WHERE t.id IS NULL
        """)
        orphaned_tag_refs = cursor.fetchone()[0]
        if orphaned_tag_refs > 0:
            issues.append(f"孤立的标签引用: {orphaned_tag_refs}个")

        # 检查数据类型一致性
        cursor.execute("SELECT COUNT(*) FROM files WHERE id IS NULL OR id = ''")
        null_file_ids = cursor.fetchone()[0]
        if null_file_ids > 0:
            issues.append(f"空的文件ID: {null_file_ids}个")

        cursor.execute("SELECT COUNT(*) FROM files WHERE size < 0")
        negative_sizes = cursor.fetchone()[0]
        if negative_sizes > 0:
            issues.append(f"负数文件大小: {negative_sizes}个")

        # 检查时间戳合理性
        cursor.execute("SELECT COUNT(*) FROM files WHERE added_at > datetime('now')")
        future_dates = cursor.fetchone()[0]
        if future_dates > 0:
            issues.append(f"未来时间戳: {future_dates}个")

        db.close()

        result = {
            'issues_found': len(issues),
            'issues': issues,
            'integrity_check': integrity_result,
            'quick_check': quick_check_result,
            'foreign_key_violations': len(fk_violations),
            'orphaned_records': orphaned_file_tags + orphaned_tag_refs
        }

        if issues:
            self.log("  ⚠️ 发现数据损坏问题:")
            for issue in issues:
                self.log(f"    - {issue}")
        else:
            self.log("  ✅ 数据损坏检测通过，未发现问题")

        self.test_results['corruption_detection'] = result
        return len(issues) == 0

    def test_5_long_term_stability(self):
        """测试5：长期稳定性模拟"""
        self.log("🛡️ 开始长期稳定性模拟测试...")

        # 模拟长期使用场景：大量读写操作
        file_service = FileService()
        tag_service = TagService()

        operations_count = 500  # 模拟500次操作
        error_count = 0

        # 记录初始状态
        initial_checksums = self.get_data_checksum()

        for i in range(operations_count):
            try:
                # 随机选择操作类型
                operation = random.choice(['read_files', 'read_tags', 'count_files', 'tag_query'])

                if operation == 'read_files':
                    limit = random.randint(10, 100)
                    offset = random.randint(0, 6000)
                    files = file_service.get_files(limit=limit, offset=offset)

                elif operation == 'read_tags':
                    tags = tag_service.get_all_tags()

                elif operation == 'count_files':
                    count = file_service.get_file_count()

                elif operation == 'tag_query':
                    device_tags = tag_service.get_device_tags()
                    if device_tags:
                        tag_id = device_tags[0]['id']
                        files = tag_service.get_files_by_tag_hierarchy(tag_id, limit=50, offset=0)

                # 每100次操作检查一次数据完整性
                if (i + 1) % 100 == 0:
                    current_checksums = self.get_data_checksum()
                    if current_checksums != initial_checksums:
                        self.log(f"  ❌ 第{i+1}次操作后数据完整性发生变化!")
                        error_count += 1
                    else:
                        self.log(f"  ✅ 第{i+1}次操作后数据完整性验证通过")

            except Exception as e:
                error_count += 1
                self.log(f"  ⚠️ 第{i+1}次操作失败: {e}")

        # 最终完整性检查
        final_checksums = self.get_data_checksum()
        stability_ok = (final_checksums == initial_checksums and error_count == 0)

        result = {
            'total_operations': operations_count,
            'error_count': error_count,
            'data_integrity_maintained': final_checksums == initial_checksums,
            'stability_rating': 'excellent' if error_count == 0 else 'good' if error_count < 5 else 'poor'
        }

        self.test_results['long_term_stability'] = result
        self.log(f"  {'✅' if stability_ok else '❌'} 长期稳定性测试{'通过' if stability_ok else '失败'}")
        self.log(f"  📊 {operations_count}次操作，{error_count}次错误")

        return stability_ok

    def test_6_concurrent_access_safety(self):
        """测试6：并发访问安全性"""
        self.log("🛡️ 开始并发访问安全性测试...")

        def concurrent_reader(worker_id, iterations):
            """并发读取工作线程"""
            errors = 0
            for i in range(iterations):
                try:
                    file_service = FileService()
                    files = file_service.get_files(limit=100, offset=random.randint(0, 6000))

                    # 验证返回数据的合理性
                    if not isinstance(files, list):
                        errors += 1
                        self.log(f"    ❌ 工作线程{worker_id}: 返回数据类型错误")

                    for file in files:
                        if not isinstance(file, dict) or 'id' not in file:
                            errors += 1
                            self.log(f"    ❌ 工作线程{worker_id}: 文件数据格式错误")
                            break

                except Exception as e:
                    errors += 1
                    self.log(f"    ❌ 工作线程{worker_id}第{i+1}次读取失败: {e}")

            return {'worker_id': worker_id, 'errors': errors}

        # 启动多个并发读取线程
        concurrency = 16  # 更高的并发度
        iterations_per_worker = 25

        self.log(f"  📊 启动{concurrency}个并发读取线程，每个执行{iterations_per_worker}次操作")

        start_time = time.time()

        with ThreadPoolExecutor(max_workers=concurrency) as executor:
            futures = [
                executor.submit(concurrent_reader, i, iterations_per_worker)
                for i in range(concurrency)
            ]

            worker_results = []
            for future in as_completed(futures):
                worker_results.append(future.result())

        end_time = time.time()

        total_errors = sum(r['errors'] for r in worker_results)
        total_operations = concurrency * iterations_per_worker

        result = {
            'concurrent_workers': concurrency,
            'total_operations': total_operations,
            'total_errors': total_errors,
            'test_duration': end_time - start_time,
            'operations_per_second': total_operations / (end_time - start_time),
            'error_rate': total_errors / total_operations * 100
        }

        self.test_results['concurrent_access_safety'] = result

        safety_ok = total_errors == 0
        self.log(f"  {'✅' if safety_ok else '❌'} 并发访问安全性测试{'通过' if safety_ok else '失败'}")
        self.log(f"  📊 {total_operations}次操作，{total_errors}次错误，错误率{result['error_rate']:.2f}%")

        return safety_ok

    def generate_safety_report(self):
        """生成数据库安全测试报告"""
        self.log("📊 生成数据库安全测试报告...")

        report = []
        report.append("=" * 80)
        report.append("SmartVault 数据库安全与可靠性测试报告")
        report.append("=" * 80)
        report.append(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"测试耗时: {time.time() - self.start_time:.2f}秒")

        # 安全等级评估
        safety_scores = []

        for test_name, results in self.test_results.items():
            report.append(f"\n🛡️ {test_name.replace('_', ' ').title()}:")

            if test_name == 'data_integrity':
                integrity_ok = results['integrity_preserved']
                score = 100 if integrity_ok else 0
                safety_scores.append(score)
                report.append(f"  数据完整性: {'✅ 通过' if integrity_ok else '❌ 失败'}")
                report.append(f"  读取操作数: {results['read_operations']}")

            elif test_name == 'transaction_safety':
                success_rate = results['successful_transactions'] / results['total_transactions'] * 100
                score = min(100, success_rate)
                safety_scores.append(score)
                report.append(f"  事务成功率: {success_rate:.1f}%")
                report.append(f"  并发工作线程: {results['concurrent_workers']}")
                report.append(f"  总事务数: {results['total_transactions']}")

            elif test_name == 'backup_recovery':
                backup_ok = results['backup_created'] and results['recovery_successful']
                score = 100 if backup_ok else 0
                safety_scores.append(score)
                report.append(f"  备份创建: {'✅ 成功' if results['backup_created'] else '❌ 失败'}")
                report.append(f"  恢复测试: {'✅ 成功' if results['recovery_successful'] else '❌ 失败'}")
                report.append(f"  备份大小: {results['backup_size_mb']:.2f}MB")

            elif test_name == 'corruption_detection':
                issues = results['issues_found']
                score = max(0, 100 - issues * 10)  # 每个问题扣10分
                safety_scores.append(score)
                report.append(f"  发现问题: {issues}个")
                report.append(f"  完整性检查: {results['integrity_check']}")
                report.append(f"  快速检查: {results['quick_check']}")

            elif test_name == 'long_term_stability':
                error_rate = results['error_count'] / results['total_operations'] * 100
                score = max(0, 100 - error_rate * 20)  # 每1%错误率扣20分
                safety_scores.append(score)
                report.append(f"  稳定性评级: {results['stability_rating']}")
                report.append(f"  操作总数: {results['total_operations']}")
                report.append(f"  错误次数: {results['error_count']}")

            elif test_name == 'concurrent_access_safety':
                error_rate = results['error_rate']
                score = max(0, 100 - error_rate * 10)  # 每1%错误率扣10分
                safety_scores.append(score)
                report.append(f"  并发安全性: {'✅ 通过' if error_rate == 0 else '⚠️ 有问题'}")
                report.append(f"  错误率: {error_rate:.2f}%")
                report.append(f"  操作/秒: {results['operations_per_second']:.2f}")

        # 计算总体安全评级
        if safety_scores:
            overall_score = sum(safety_scores) / len(safety_scores)

            if overall_score >= 95:
                safety_grade = "A+ (极其安全)"
                safety_color = "🟢"
            elif overall_score >= 85:
                safety_grade = "A (非常安全)"
                safety_color = "🟢"
            elif overall_score >= 75:
                safety_grade = "B (安全)"
                safety_color = "🟡"
            elif overall_score >= 60:
                safety_grade = "C (基本安全)"
                safety_color = "🟡"
            else:
                safety_grade = "D (存在风险)"
                safety_color = "🔴"

            report.append(f"\n🏆 总体安全评级:")
            report.append(f"  {safety_color} {safety_grade} (得分: {overall_score:.1f}/100)")

        # 安全建议
        report.append(f"\n💡 安全建议:")

        if 'corruption_detection' in self.test_results:
            issues = self.test_results['corruption_detection']['issues_found']
            if issues > 0:
                report.append("  - 发现数据完整性问题，建议立即修复")
                report.append("  - 建议定期运行数据库完整性检查")

        if 'backup_recovery' in self.test_results:
            if not self.test_results['backup_recovery']['recovery_successful']:
                report.append("  - 备份恢复测试失败，请检查备份机制")
            else:
                report.append("  - 建议定期测试备份恢复流程")

        if overall_score < 90:
            report.append("  - 建议在生产环境使用前解决发现的问题")
            report.append("  - 考虑增加数据库监控和告警机制")

        report.append("  - 建议定期运行此安全测试套件")
        report.append("  - 在重要更新前务必进行完整的安全测试")

        report.append("\n" + "=" * 80)

        # 保存报告
        report_content = "\n".join(report)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"database_safety_report_{timestamp}.txt"

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        self.log(f"📄 安全测试报告已保存到: {report_file}")
        print("\n" + report_content)

        return report_content, overall_score if 'overall_score' in locals() else 0

    def run_all_safety_tests(self):
        """运行所有数据库安全测试"""
        self.start_time = time.time()
        self.log("🛡️ 开始 SmartVault 数据库安全与可靠性测试套件")
        self.log("⚠️  注意: 此测试将创建备份并进行恢复操作")

        try:
            # 运行所有安全测试
            tests_passed = 0
            total_tests = 6

            if self.test_1_data_integrity_verification():
                tests_passed += 1

            if self.test_2_transaction_safety():
                tests_passed += 1

            if self.test_3_database_backup_recovery():
                tests_passed += 1

            if self.test_4_corruption_detection():
                tests_passed += 1

            if self.test_5_long_term_stability():
                tests_passed += 1

            if self.test_6_concurrent_access_safety():
                tests_passed += 1

            # 生成报告
            report_content, safety_score = self.generate_safety_report()

            total_time = time.time() - self.start_time
            self.log(f"🎉 安全测试完成! 通过{tests_passed}/{total_tests}项测试")
            self.log(f"📊 总体安全评分: {safety_score:.1f}/100")
            self.log(f"⏱️ 总耗时: {total_time:.2f}秒")

            if tests_passed == total_tests and safety_score >= 90:
                self.log("✅ 数据库安全性评估: 优秀，可以安全使用")
            elif tests_passed >= total_tests * 0.8 and safety_score >= 75:
                self.log("⚠️ 数据库安全性评估: 良好，建议关注发现的问题")
            else:
                self.log("❌ 数据库安全性评估: 存在风险，建议修复后再使用")

            return safety_score

        except Exception as e:
            self.log(f"❌ 安全测试过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
            return 0

def main():
    """主函数"""
    print("🛡️ SmartVault 数据库安全与可靠性测试套件")
    print("⚠️  注意: 这是一个全面的安全测试，将创建备份并测试恢复")
    print("⚠️  测试过程中会进行大量数据库操作，请确保数据已备份")

    response = input("\n是否继续进行安全测试? (y/N): ")
    if response.lower() != 'y':
        print("测试已取消")
        return

    # 运行安全测试
    tester = DatabaseSafetyTest()
    safety_score = tester.run_all_safety_tests()

    if safety_score >= 90:
        print("\n🎉 恭喜！您的数据库通过了严格的安全测试")
    elif safety_score >= 75:
        print("\n⚠️ 数据库基本安全，但建议关注测试中发现的问题")
    else:
        print("\n❌ 数据库存在安全风险，强烈建议修复后再使用")

if __name__ == '__main__':
    main()
