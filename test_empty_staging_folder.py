#!/usr/bin/env python3
"""
测试空的中转文件夹显示
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

from smartvault.services.file import FileService

def test_empty_staging_folder():
    """测试空的中转文件夹显示"""
    print("🧪 测试空的中转文件夹显示...")
    
    try:
        # 初始化文件服务
        file_service = FileService()
        
        # 清空所有中转文件（将所有中转文件移出中转状态）
        print("🧹 清空中转文件夹...")
        staging_files = file_service.get_files(folder_filter_type="staging")
        
        for file_info in staging_files:
            file_service.move_from_staging(file_info['id'])
        
        print(f"已将 {len(staging_files)} 个文件移出中转状态")
        
        # 测试空的中转文件夹显示
        print("📥 测试空的中转文件夹显示...")
        
        # 获取文件夹组
        folder_groups = file_service.get_folder_groups_in_staging()
        print(f"文件夹组数量: {len(folder_groups)}")
        
        # 获取单独文件
        individual_files = file_service.get_individual_staging_files()
        print(f"单独文件数量: {len(individual_files)}")
        
        # 获取显示项目
        display_items = file_service.get_staging_display_items()
        print(f"中转文件夹显示项目总数: {len(display_items)}")
        
        if len(display_items) == 0:
            print("✅ 中转文件夹为空，这是正常状态")
            print("✅ 没有产生错误，应该显示空的文件视图")
        else:
            print(f"⚠️  中转文件夹不为空，包含 {len(display_items)} 个项目")
            for item in display_items:
                if item.get('type') == 'folder_group':
                    print(f"  📁 [文件夹] {item['folder_name']}")
                elif item.get('type') == 'individual_file':
                    print(f"  📄 [文件] {item['name']}")
        
        # 测试数据结构完整性
        print("\n🔍 测试数据结构完整性...")
        for item in display_items:
            required_fields = ['id', 'name', 'size', 'entry_type', 'added_at']
            missing_fields = []
            
            for field in required_fields:
                if field not in item:
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"❌ 项目缺少字段: {missing_fields}")
                return False
            else:
                print(f"✅ 项目 '{item['name']}' 数据结构完整")
        
        print("\n🎉 空的中转文件夹测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_empty_staging_folder()
    sys.exit(0 if success else 1)
