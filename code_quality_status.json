{"timestamp": "2025-05-27T19:06:51.078164", "file_status": {"total_lines": 2092, "non_empty_lines": 1668, "risk_level": "🟢 GOOD", "action_required": "继续监控", "threshold_warning": 2500, "threshold_critical": 3000}, "method_analysis": {"total_methods": 63, "core_methods": 8, "non_core_methods": 21, "high_risk_methods": 0}, "refactoring_suggestions": [{"type": "移动非核心功能", "description": "可以将 9 个非核心方法移到其他模块", "methods": ["_load_files_for_tag_filter", "on_tag_selected", "start_configured_monitors", "toggle_all_monitors", "_show_single_duplicate_suggestion", "_show_batch_duplicate_dialog", "_check_existing_files_in_monitored_folders", "on_monitor_event", "_show_duplicate_file_dialog"], "potential_reduction": 560, "target_modules": {"backup": "backup_manager.py", "clipboard": "clipboard_handler.py", "drag": "drag_drop_handler.py", "monitor": "monitor_manager.py", "search": "search_handler.py"}}], "quality_standards": {"core_file_path": "smartvault/ui/main_window/core.py", "warning_threshold": 2500, "critical_threshold": 3000, "method_max_lines": 100, "class_max_lines": 1500}}