"""
强制清理工具 - 解决启动问题
"""

import os
import sys
import sqlite3
import time
import signal
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import psutil
    HAS_PSUTIL = True
except ImportError:
    HAS_PSUTIL = False


def kill_smartvault_processes():
    """强制终止所有SmartVault相关进程"""
    print("🔪 强制终止SmartVault进程...")
    
    if not HAS_PSUTIL:
        print("⚠️  无法终止进程，psutil模块不可用")
        return
    
    killed_count = 0
    current_pid = os.getpid()
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.pid == current_pid:
                continue  # 跳过当前进程
                
            if proc.info['name'] and 'python' in proc.info['name'].lower():
                cmdline = proc.info['cmdline']
                if cmdline and any('smartvault' in arg.lower() or 'run.py' in arg.lower() for arg in cmdline):
                    print(f"  终止进程 PID: {proc.pid}")
                    proc.terminate()
                    killed_count += 1
                    
                    # 等待进程结束
                    try:
                        proc.wait(timeout=3)
                    except psutil.TimeoutExpired:
                        print(f"  强制杀死进程 PID: {proc.pid}")
                        proc.kill()
                        
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    print(f"✅ 已终止 {killed_count} 个进程")


def force_unlock_database():
    """强制解锁数据库"""
    print("🔓 强制解锁数据库...")
    
    try:
        from smartvault.utils.config import load_config
        config = load_config()
        library_path = config["library_path"]
        db_path = os.path.join(library_path, "data", "smartvault.db")
        
        if not os.path.exists(db_path):
            print("✅ 数据库文件不存在")
            return
        
        # 创建备份
        backup_path = db_path + f".emergency_backup_{int(time.time())}"
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"✅ 已创建紧急备份: {backup_path}")
        
        # 强制删除锁定文件
        lock_files = [
            db_path + "-wal",
            db_path + "-shm",
            db_path + "-journal"
        ]
        
        for lock_file in lock_files:
            if os.path.exists(lock_file):
                try:
                    os.remove(lock_file)
                    print(f"✅ 已删除锁定文件: {os.path.basename(lock_file)}")
                except Exception as e:
                    print(f"❌ 无法删除 {lock_file}: {e}")
        
        # 尝试重建数据库连接
        try:
            conn = sqlite3.connect(db_path, timeout=1.0)
            conn.execute("PRAGMA journal_mode=DELETE")  # 切换到DELETE模式
            conn.execute("PRAGMA journal_mode=WAL")     # 重新启用WAL模式
            conn.execute("VACUUM")  # 清理数据库
            conn.close()
            print("✅ 数据库重建完成")
        except Exception as e:
            print(f"⚠️  数据库重建失败: {e}")
            
    except Exception as e:
        print(f"❌ 强制解锁数据库失败: {e}")


def clear_qt_settings():
    """清理Qt设置"""
    print("🧹 清理Qt设置...")
    
    try:
        from PySide6.QtCore import QSettings
        
        settings = QSettings("SmartVault", "MainWindow")
        settings.clear()
        settings.sync()
        
        print("✅ Qt设置已清理")
        
    except Exception as e:
        print(f"❌ 清理Qt设置失败: {e}")


def clear_temp_files():
    """清理临时文件"""
    print("🗑️  清理临时文件...")
    
    try:
        import tempfile
        temp_dir = tempfile.gettempdir()
        
        # 查找SmartVault相关的临时文件
        temp_files = []
        for root, dirs, files in os.walk(temp_dir):
            for file in files:
                if 'smartvault' in file.lower():
                    temp_files.append(os.path.join(root, file))
        
        for temp_file in temp_files:
            try:
                os.remove(temp_file)
                print(f"  删除临时文件: {temp_file}")
            except Exception as e:
                print(f"  无法删除 {temp_file}: {e}")
        
        print(f"✅ 已清理 {len(temp_files)} 个临时文件")
        
    except Exception as e:
        print(f"❌ 清理临时文件失败: {e}")


def reset_config():
    """重置配置文件"""
    print("⚙️  重置配置文件...")
    
    try:
        from smartvault.utils.config import get_app_data_dir
        
        app_data_dir = get_app_data_dir()
        config_file = os.path.join(app_data_dir, "config.json")
        
        if os.path.exists(config_file):
            # 创建备份
            backup_file = config_file + f".backup_{int(time.time())}"
            import shutil
            shutil.copy2(config_file, backup_file)
            print(f"✅ 配置文件备份: {backup_file}")
            
            # 删除配置文件，让程序重新创建
            os.remove(config_file)
            print("✅ 配置文件已重置")
        else:
            print("✅ 配置文件不存在，无需重置")
            
    except Exception as e:
        print(f"❌ 重置配置文件失败: {e}")


def check_system_resources():
    """检查系统资源"""
    print("📊 检查系统资源...")
    
    if not HAS_PSUTIL:
        print("⚠️  无法检查系统资源")
        return
    
    try:
        # 检查内存使用
        memory = psutil.virtual_memory()
        print(f"内存使用: {memory.percent}% ({memory.used / 1024**3:.1f}GB / {memory.total / 1024**3:.1f}GB)")
        
        if memory.percent > 90:
            print("⚠️  内存使用率过高")
        
        # 检查磁盘空间
        disk = psutil.disk_usage('/')
        print(f"磁盘使用: {disk.percent}% ({disk.used / 1024**3:.1f}GB / {disk.total / 1024**3:.1f}GB)")
        
        if disk.percent > 95:
            print("⚠️  磁盘空间不足")
        
        # 检查CPU使用
        cpu_percent = psutil.cpu_percent(interval=1)
        print(f"CPU使用: {cpu_percent}%")
        
        print("✅ 系统资源检查完成")
        
    except Exception as e:
        print(f"❌ 检查系统资源失败: {e}")


def main():
    """主函数"""
    print("💥 SmartVault 强制清理工具")
    print("=" * 50)
    print("⚠️  警告: 此工具将强制清理所有SmartVault相关资源")
    print("请确保已保存重要数据！")
    print("=" * 50)
    
    # 等待用户确认
    try:
        response = input("是否继续? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("操作已取消")
            return
    except KeyboardInterrupt:
        print("\n操作已取消")
        return
    
    print("\n🚀 开始强制清理...")
    
    # 1. 终止进程
    kill_smartvault_processes()
    
    # 2. 解锁数据库
    force_unlock_database()
    
    # 3. 清理Qt设置
    clear_qt_settings()
    
    # 4. 清理临时文件
    clear_temp_files()
    
    # 5. 检查系统资源
    check_system_resources()
    
    print("\n" + "=" * 50)
    print("✅ 强制清理完成！")
    print("\n💡 建议:")
    print("1. 重启计算机以确保所有资源完全释放")
    print("2. 重新启动SmartVault应用程序")
    print("3. 如果问题持续，请检查系统日志")
    
    # 可选：重置配置
    print("\n⚙️  是否要重置配置文件? (这将清除所有设置)")
    try:
        response = input("重置配置? (y/N): ").strip().lower()
        if response in ['y', 'yes']:
            reset_config()
    except KeyboardInterrupt:
        print("\n跳过配置重置")


if __name__ == "__main__":
    main()
