#!/usr/bin/env python3
"""
B010多条件搜索UI手动测试
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from smartvault.ui.dialogs.advanced_search_dialog import AdvancedSearchDialog


class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("B010多条件搜索UI测试")
        self.resize(400, 300)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 添加测试按钮
        test_button = QPushButton("打开高级搜索对话框（含多条件搜索）")
        test_button.clicked.connect(self.open_advanced_search)
        layout.addWidget(test_button)
    
    def open_advanced_search(self):
        """打开高级搜索对话框"""
        print("\n🔧 打开高级搜索对话框...")
        
        try:
            dialog = AdvancedSearchDialog(self)
            
            # 连接搜索完成信号
            dialog.search_completed.connect(self.on_search_completed)
            
            print("   ✅ 高级搜索对话框已打开")
            print("   📋 请在对话框中测试以下功能：")
            print()
            print("   🔍 基本搜索选项卡：")
            print("      • 文件名搜索（支持通配符）")
            print("      • 文件类型选择")
            print("      • 文件大小范围")
            print("      • 添加日期范围")
            print("      • 入库方式筛选")
            print("      • 标签筛选")
            print("      • 搜索预览功能")
            print()
            print("   🔧 多条件搜索选项卡：")
            print("      • 搜索表达式输入")
            print("      • 表达式语法帮助")
            print("      • 条件构建器")
            print("      • 添加/编辑/删除条件")
            print("      • 逻辑操作符选择（AND/OR）")
            print("      • 生成搜索表达式")
            print("      • 执行多条件搜索")
            print("      • 搜索结果预览")
            print()
            print("   💾 保存的搜索选项卡：")
            print("      • 保存当前搜索条件")
            print("      • 加载已保存的搜索")
            print("      • 重命名/删除保存的搜索")
            print("      • 搜索历史记录")
            print("      • 加载历史搜索")
            print("      • 清除搜索历史")
            print()
            print("   🎯 测试建议：")
            print("      1. 在基本搜索中设置条件并保存")
            print("      2. 在多条件搜索中尝试复杂表达式")
            print("      3. 使用条件构建器创建多个条件")
            print("      4. 测试保存和加载功能")
            print("      5. 验证搜索历史记录")
            print()
            print("   📝 表达式示例：")
            print("      • name:*.txt")
            print("      • size:>10MB AND type:pdf")
            print("      • date:>2024-01-01 OR tag:重要")
            print("      • (name:report* OR name:doc*) AND size:<5MB")
            
            result = dialog.exec()
            
            if result == AdvancedSearchDialog.Accepted:
                print("   ✅ 搜索已确认")
            else:
                print("   ❌ 搜索已取消")
                
        except Exception as e:
            print(f"   ❌ 打开高级搜索对话框失败: {e}")
            import traceback
            traceback.print_exc()
    
    def on_search_completed(self, results):
        """搜索完成回调"""
        print(f"\n🔍 搜索完成，找到 {len(results)} 个结果")
        
        if results:
            print("   📋 搜索结果示例：")
            for i, file_info in enumerate(results[:10]):  # 显示前10个结果
                size_mb = file_info['size'] / (1024 * 1024)
                print(f"      {i+1}. {file_info['name']} ({size_mb:.2f} MB)")
            
            if len(results) > 10:
                print(f"      ... 还有 {len(results) - 10} 个结果")
                
            # 统计信息
            total_size = sum(f['size'] for f in results)
            avg_size = total_size / len(results) if results else 0
            print(f"   📊 统计信息：")
            print(f"      • 总文件数: {len(results)}")
            print(f"      • 总大小: {total_size / (1024 * 1024):.2f} MB")
            print(f"      • 平均大小: {avg_size / (1024 * 1024):.2f} MB")
        else:
            print("   📭 没有找到匹配的文件")
            print("   💡 建议：")
            print("      • 检查搜索条件是否过于严格")
            print("      • 尝试使用通配符（*）")
            print("      • 调整文件大小或日期范围")


def main():
    """主函数"""
    print("🚀 启动B010多条件搜索UI手动测试...")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    print("📋 测试说明：")
    print("   1. 程序将创建一个测试窗口")
    print("   2. 点击按钮打开高级搜索对话框")
    print("   3. 测试三个选项卡的所有功能")
    print("   4. 验证搜索结果的正确性")
    print()
    print("🎯 重点测试项目：")
    print("   • 多条件搜索表达式的解析和执行")
    print("   • 搜索条件构建器的易用性")
    print("   • 保存和加载搜索的完整性")
    print("   • 搜索历史记录的管理")
    print("   • 复杂逻辑组合的正确性")
    print("   • UI界面的响应性和直观性")
    print()
    
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestMainWindow()
    window.show()
    
    print("✅ 测试窗口已显示，请点击按钮开始测试")
    print("🔍 测试完成后，请验证搜索功能是否符合预期")
    
    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
