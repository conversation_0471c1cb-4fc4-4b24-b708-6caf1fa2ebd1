#!/usr/bin/env python3
"""
AI接入第二阶段功能演示

展示行为模式学习和自适应规则引擎的实际效果
"""

import sys
import os
import sqlite3
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from smartvault.services.ai.ai_manager import AIManager
from smartvault.utils.config import load_config


class MockDatabase:
    """模拟数据库连接"""
    
    def __init__(self):
        self.conn = sqlite3.connect(':memory:')
        self._create_tables()
    
    def _create_tables(self):
        """创建AI相关表"""
        cursor = self.conn.cursor()
        cursor.execute("""
            CREATE TABLE ai_learning_patterns (
                id TEXT PRIMARY KEY,
                pattern_type TEXT NOT NULL,
                pattern_data TEXT NOT NULL,
                frequency INTEGER DEFAULT 1,
                success_rate REAL DEFAULT 0.5,
                last_used TEXT NOT NULL,
                created_at TEXT NOT NULL
            )
        """)
        self.conn.commit()


def demo_behavior_learning():
    """演示行为模式学习功能"""
    print("🧠 行为模式学习演示")
    print("=" * 50)
    
    # 初始化AI管理器
    db = MockDatabase()
    config = load_config()
    config['advanced']['enable_ai_features'] = True
    
    ai_manager = AIManager()
    ai_manager.initialize(config, db=db)
    
    print("📚 模拟用户使用场景...")
    
    # 场景1：用户经常给Python文件打标签
    python_actions = [
        {
            'action_type': 'tag_applied',
            'file_info': {'name': 'main.py', 'extension': '.py', 'path': '/work/project/main.py'},
            'applied_tags': ['Python', '代码', '主程序']
        },
        {
            'action_type': 'tag_applied', 
            'file_info': {'name': 'utils.py', 'extension': '.py', 'path': '/work/project/utils.py'},
            'applied_tags': ['Python', '代码', '工具']
        },
        {
            'action_type': 'tag_applied',
            'file_info': {'name': 'test.py', 'extension': '.py', 'path': '/work/project/test.py'},
            'applied_tags': ['Python', '代码', '测试']
        }
    ]
    
    for action in python_actions:
        ai_manager.learn_from_user_action(action)
        print(f"   学习用户行为: {action['file_info']['name']} -> {action['applied_tags']}")
    
    # 场景2：测试学习效果
    print("\n🎯 测试学习效果...")
    new_python_file = {
        'name': 'new_script.py',
        'extension': '.py', 
        'path': '/work/project/new_script.py'
    }
    
    suggestions = ai_manager.suggest_tags(new_python_file)
    print(f"   新Python文件建议标签: {suggestions}")
    
    # 场景3：JavaScript文件学习
    print("\n📝 学习JavaScript文件模式...")
    js_actions = [
        {
            'action_type': 'tag_applied',
            'file_info': {'name': 'app.js', 'extension': '.js', 'path': '/work/web/app.js'},
            'applied_tags': ['JavaScript', 'Web开发', '前端']
        },
        {
            'action_type': 'tag_applied',
            'file_info': {'name': 'utils.js', 'extension': '.js', 'path': '/work/web/utils.js'},
            'applied_tags': ['JavaScript', 'Web开发', '工具']
        }
    ]
    
    for action in js_actions:
        ai_manager.learn_from_user_action(action)
        print(f"   学习用户行为: {action['file_info']['name']} -> {action['applied_tags']}")
    
    # 测试JavaScript文件建议
    new_js_file = {
        'name': 'component.js',
        'extension': '.js',
        'path': '/work/web/component.js'
    }
    
    js_suggestions = ai_manager.suggest_tags(new_js_file)
    print(f"   新JavaScript文件建议标签: {js_suggestions}")
    
    return ai_manager


def demo_adaptive_rules(ai_manager):
    """演示自适应规则引擎功能"""
    print("\n⚙️ 自适应规则引擎演示")
    print("=" * 50)
    
    # 模拟规则反馈
    print("📊 模拟规则性能反馈...")
    
    rule_feedbacks = [
        {'rule_id': 'python_rule_001', 'feedback': 'accept', 'context': {'file_type': '.py'}},
        {'rule_id': 'python_rule_001', 'feedback': 'accept', 'context': {'file_type': '.py'}},
        {'rule_id': 'python_rule_001', 'feedback': 'reject', 'context': {'file_type': '.py'}},
        {'rule_id': 'js_rule_001', 'feedback': 'accept', 'context': {'file_type': '.js'}},
        {'rule_id': 'js_rule_001', 'feedback': 'accept', 'context': {'file_type': '.js'}},
        {'rule_id': 'js_rule_001', 'feedback': 'accept', 'context': {'file_type': '.js'}},
    ]
    
    for feedback in rule_feedbacks:
        action_data = {
            'rule_feedback': True,
            'rule_id': feedback['rule_id'],
            'feedback': feedback['feedback'],
            'context': feedback['context']
        }
        ai_manager.learn_from_user_action(action_data)
        print(f"   规则反馈: {feedback['rule_id']} -> {feedback['feedback']}")
    
    # 获取规则性能统计
    print("\n📈 规则性能统计:")
    rule_stats = ai_manager.get_rule_performance_stats()
    
    if rule_stats.get('total_rules', 0) > 0:
        print(f"   总规则数: {rule_stats['total_rules']}")
        print(f"   高性能规则: {rule_stats['high_performance_rules']}")
        print(f"   中等性能规则: {rule_stats['medium_performance_rules']}")
        print(f"   低性能规则: {rule_stats['low_performance_rules']}")
        print(f"   平均性能: {rule_stats['average_performance']:.2f}")
        
        if rule_stats['top_performing_rules']:
            print("   最佳规则:")
            for rule in rule_stats['top_performing_rules'][:3]:
                print(f"     - {rule['rule_id']}: {rule['performance_score']:.2f}")
    else:
        print("   暂无规则性能数据")
    
    # 生成自适应规则
    print("\n🔄 生成自适应规则...")
    behavior_data = [
        {
            'action_type': 'tag_applied',
            'file_info': {'extension': '.md', 'path': '/docs/readme.md'},
            'applied_tags': ['文档', 'Markdown']
        },
        {
            'action_type': 'tag_applied',
            'file_info': {'extension': '.md', 'path': '/docs/guide.md'},
            'applied_tags': ['文档', 'Markdown', '指南']
        },
        {
            'action_type': 'tag_applied',
            'file_info': {'extension': '.md', 'path': '/docs/api.md'},
            'applied_tags': ['文档', 'Markdown', 'API']
        }
    ]
    
    new_rules = ai_manager.generate_adaptive_rules(behavior_data)
    print(f"   生成了 {len(new_rules)} 个自适应规则")
    
    for rule in new_rules:
        print(f"     - {rule['name']} (置信度: {rule['confidence']:.2f})")


def demo_learning_statistics(ai_manager):
    """演示学习统计功能"""
    print("\n📊 学习统计信息演示")
    print("=" * 50)
    
    stats = ai_manager.get_learning_statistics()
    
    # 行为学习统计
    if 'behavior_learning' in stats:
        behavior_stats = stats['behavior_learning']
        print("🧠 行为学习统计:")
        print(f"   学习模式总数: {behavior_stats.get('total_patterns', 0)}")
        print(f"   活跃模式数: {behavior_stats.get('active_patterns', 0)}")
        
        if behavior_stats.get('top_patterns'):
            print("   热门模式:")
            for pattern in behavior_stats['top_patterns'][:3]:
                print(f"     - {pattern['pattern_key']}: 频率={pattern['frequency']}, 评分={pattern['score']:.2f}")
        
        if behavior_stats.get('tag_usage'):
            print("   标签使用统计:")
            sorted_tags = sorted(behavior_stats['tag_usage'].items(), key=lambda x: x[1], reverse=True)
            for tag, count in sorted_tags[:5]:
                print(f"     - {tag}: {count}次")
    
    # 规则性能统计
    if 'rule_performance' in stats:
        rule_stats = stats['rule_performance']
        print("\n⚙️ 规则性能统计:")
        print(f"   规则总数: {rule_stats.get('total_rules', 0)}")
        print(f"   需要优化的规则: {rule_stats.get('rules_needing_optimization', 0)}")
        print(f"   平均性能: {rule_stats.get('average_performance', 0):.2f}")


def main():
    """主演示函数"""
    print("🎉 SmartVault AI接入第二阶段功能演示")
    print("=" * 60)
    print("本演示将展示以下功能:")
    print("1. 行为模式学习 - 从用户行为中学习标签偏好")
    print("2. 自适应规则引擎 - 规则性能评估和动态生成")
    print("3. 学习统计信息 - 完整的学习效果分析")
    print("=" * 60)
    
    try:
        # 演示行为学习
        ai_manager = demo_behavior_learning()
        
        # 演示自适应规则
        demo_adaptive_rules(ai_manager)
        
        # 演示学习统计
        demo_learning_statistics(ai_manager)
        
        print("\n🎯 演示总结")
        print("=" * 50)
        print("✅ 行为模式学习: 系统成功学习了用户的标签使用习惯")
        print("✅ 自适应规则引擎: 规则性能得到有效评估和管理")
        print("✅ 智能标签建议: 基于学习的模式提供个性化建议")
        print("✅ 数据持久化: 学习数据可以保存和加载")
        print("\n🚀 SmartVault AI第二阶段功能演示完成！")
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
