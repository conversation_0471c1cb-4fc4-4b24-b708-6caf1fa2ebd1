#!/usr/bin/env python3
"""
SmartVault 文件库配置一致性测试
专门测试文件库切换后配置文件与实际状态的一致性问题
"""

import sys
import os
import tempfile
import shutil
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from smartvault.utils.config import load_config, save_config
from smartvault.data.file_system import FileSystem


class LibraryConfigConsistencyTester:
    """文件库配置一致性测试器"""
    
    def __init__(self):
        self.test_results = []
        self.temp_dirs = []
        self.original_config = load_config()
        
    def log_test(self, test_name, success, details=""):
        """记录测试结果"""
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{status} {test_name}")
        if details:
            print(f"   详情: {details}")
        self.test_results.append({
            "name": test_name,
            "success": success,
            "details": details
        })
    
    def create_temp_library(self, name="test_library"):
        """创建临时文件库"""
        temp_dir = tempfile.mkdtemp(prefix=f"smartvault_{name}_")
        self.temp_dirs.append(temp_dir)
        
        file_system = FileSystem()
        success, library_path, details = file_system.create_library(temp_dir)
        
        if success:
            return library_path
        else:
            raise Exception(f"创建临时文件库失败: {details}")
    
    def test_create_library_config_consistency(self):
        """测试创建文件库后配置一致性"""
        print("\n🔍 测试创建文件库后配置一致性...")
        
        try:
            # 记录创建前的配置
            config_before = load_config()
            original_path = config_before["library_path"]
            
            # 创建新文件库
            file_system = FileSystem()
            temp_dir = tempfile.mkdtemp(prefix="smartvault_create_test_")
            self.temp_dirs.append(temp_dir)
            
            success, new_library_path, details = file_system.create_library(temp_dir)
            
            if not success:
                self.log_test("创建文件库成功", False, details)
                return
            
            # 检查配置是否已更新
            config_after = load_config()
            config_updated = config_after["library_path"] == new_library_path
            
            self.log_test(
                "创建文件库后配置自动更新",
                config_updated,
                f"期望: {new_library_path}, 实际: {config_after['library_path']}"
            )
            
            # 恢复原始配置
            save_config({"library_path": original_path})
            
        except Exception as e:
            self.log_test("创建文件库后配置一致性", False, str(e))
    
    def test_move_library_config_consistency(self):
        """测试移动文件库后配置一致性"""
        print("\n🔍 测试移动文件库后配置一致性...")
        
        try:
            # 创建源文件库
            source_library = self.create_temp_library("source")
            
            # 设置为当前文件库
            config = load_config()
            config["library_path"] = source_library
            save_config(config)
            
            # 创建目标目录
            target_dir = tempfile.mkdtemp(prefix="smartvault_move_target_")
            self.temp_dirs.append(target_dir)
            
            # 移动文件库
            file_system = FileSystem()
            file_system.library_path = source_library
            
            success = file_system.move_library(target_dir)
            
            if not success:
                self.log_test("移动文件库成功", False, "移动操作失败")
                return
            
            # 检查配置是否已更新
            config_after = load_config()
            expected_path = os.path.join(target_dir, "SmartVault_Lib")
            expected_path = os.path.normpath(expected_path).replace('\\', '/')
            actual_path = config_after["library_path"]
            
            config_updated = actual_path == expected_path
            
            self.log_test(
                "移动文件库后配置自动更新",
                config_updated,
                f"期望: {expected_path}, 实际: {actual_path}"
            )
            
        except Exception as e:
            self.log_test("移动文件库后配置一致性", False, str(e))
    
    def test_simulated_settings_dialog_operations(self):
        """模拟设置对话框操作测试"""
        print("\n🔍 测试模拟设置对话框操作...")
        
        try:
            # 模拟创建新文件库操作
            temp_dir = tempfile.mkdtemp(prefix="smartvault_dialog_test_")
            self.temp_dirs.append(temp_dir)
            
            file_system = FileSystem()
            success, library_path, details = file_system.create_library(temp_dir)
            
            if success:
                # 模拟设置对话框中的配置更新逻辑
                config = load_config()
                config["library_path"] = library_path
                save_config(config)
                
                # 验证配置是否正确保存
                reloaded_config = load_config()
                config_consistent = reloaded_config["library_path"] == library_path
                
                self.log_test(
                    "模拟设置对话框创建文件库配置一致",
                    config_consistent,
                    f"保存: {library_path}, 重新加载: {reloaded_config['library_path']}"
                )
            else:
                self.log_test("模拟设置对话框创建文件库", False, details)
            
            # 模拟打开现有文件库操作
            another_temp_dir = tempfile.mkdtemp(prefix="smartvault_open_test_")
            self.temp_dirs.append(another_temp_dir)
            
            success2, library_path2, details2 = file_system.create_library(another_temp_dir)
            
            if success2:
                # 模拟打开现有文件库的配置更新
                config = load_config()
                config["library_path"] = library_path2
                save_config(config)
                
                # 验证配置
                reloaded_config = load_config()
                config_consistent = reloaded_config["library_path"] == library_path2
                
                self.log_test(
                    "模拟设置对话框打开文件库配置一致",
                    config_consistent,
                    f"保存: {library_path2}, 重新加载: {reloaded_config['library_path']}"
                )
            
        except Exception as e:
            self.log_test("模拟设置对话框操作", False, str(e))
    
    def test_config_persistence_across_restarts(self):
        """测试配置在重启后的持久性"""
        print("\n🔍 测试配置持久性...")
        
        try:
            # 创建测试文件库
            test_library = self.create_temp_library("persistence_test")
            
            # 保存配置
            config = load_config()
            config["library_path"] = test_library
            save_config(config)
            
            # 模拟程序重启 - 重新加载配置
            reloaded_config = load_config()
            
            # 检查配置是否持久化
            persistence_ok = reloaded_config["library_path"] == test_library
            
            self.log_test(
                "配置持久化正常",
                persistence_ok,
                f"保存: {test_library}, 重启后加载: {reloaded_config['library_path']}"
            )
            
        except Exception as e:
            self.log_test("配置持久性测试", False, str(e))
    
    def cleanup(self):
        """清理测试环境"""
        print("\n🧹 清理测试环境...")
        
        # 恢复原始配置
        try:
            save_config(self.original_config)
            print("   ✅ 原始配置已恢复")
        except Exception as e:
            print(f"   ⚠️  恢复配置失败: {e}")
        
        # 删除临时目录
        for temp_dir in self.temp_dirs:
            try:
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
                    print(f"   ✅ 已删除临时目录: {temp_dir}")
            except Exception as e:
                print(f"   ⚠️  删除临时目录失败: {temp_dir} - {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始文件库配置一致性测试")
        print("=" * 60)
        
        try:
            # 运行各项测试
            self.test_create_library_config_consistency()
            self.test_move_library_config_consistency()
            self.test_simulated_settings_dialog_operations()
            self.test_config_persistence_across_restarts()
            
        finally:
            # 清理测试环境
            self.cleanup()
        
        # 输出测试结果
        print("\n" + "=" * 60)
        print("📊 测试结果汇总")
        print("=" * 60)
        
        passed = sum(1 for result in self.test_results if result["success"])
        total = len(self.test_results)
        
        print(f"总测试数: {total}")
        print(f"通过数: {passed}")
        print(f"失败数: {total - passed}")
        print(f"通过率: {passed/total*100:.1f}%")
        
        if passed == total:
            print("\n🎉 所有测试通过！文件库配置一致性正常。")
            return True
        else:
            print("\n⚠️  部分测试失败，配置一致性存在问题。")
            print("\n失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  ❌ {result['name']}: {result['details']}")
            return False


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    try:
        tester = LibraryConfigConsistencyTester()
        success = tester.run_all_tests()
        
        # 自动退出
        QTimer.singleShot(1000, app.quit)
        app.exec()
        
        return 0 if success else 1
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
