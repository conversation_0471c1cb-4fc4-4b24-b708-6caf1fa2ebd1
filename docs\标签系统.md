### 8.1.1 标签系统的层次结构和继承关系

#### 标签层次结构设计

##### 三层标签体系

```
顶层标签(类别标签) → 中层标签(属性标签) → 底层标签(具体标签)
```

**顶层标签**：代表主要分类，例如：
- 文档类型（Document）
- 媒体类型（Media）
- 软件类型（Software）
- 个人（Personal）
- 工作（Work）
- 学习（Study）
- 项目（Project）

**中层标签**：代表属性或子类别，例如：
- 文档类型下：报告（Report）、合同（Contract）、笔记（Note）
- 媒体类型下：图片（Image）、视频（Video）、音频（Audio）
- 软件类型下：工具（Tool）、游戏（Game）、开发（Development）

**底层标签**：具体描述，例如：
- 报告下：月度（Monthly）、年度（Annual）、财务（Financial）
- 图片下：风景（Landscape）、人物（People）、截图（Screenshot）
- 工具下：编辑器（Editor）、浏览器（Browser）、通讯（Communication）

##### 标签关系类型

1. **父子关系**：表示层级，如"文档→报告→财务"
2. **关联关系**：表示相关但非层级的标签，如"项目A"和"客户B"
3. **互斥关系**：表示不能同时应用的标签，如"个人"和"工作"（可选）

#### 标签继承机制

##### 属性继承

子标签自动继承父标签的属性和规则。例如：
- 如果"文档"标签设置了默认存储位置，则"报告"标签会继承这个位置
- 如果"媒体"标签设置了预览方式，则"图片"标签会继承这个预览方式

##### 搜索继承

搜索父标签时，会包含所有子标签的文件。例如：
- 搜索"文档"会返回所有带有"报告"、"合同"等子标签的文件
- 搜索"软件"会返回所有带有"工具"、"游戏"等子标签的文件

##### 权重继承

标签可以有权重，子标签继承父标签的基础权重并可以有自己的调整。例如：
- "工作"标签权重为10
- "项目"标签权重为8
- "紧急"标签权重为15
- 同时带有这些标签的文件在搜索结果中会有更高的排序

#### 标签应用规则

##### 多标签应用

一个文件可以同时应用多个标签，但需遵循以下规则：
- 可以应用来自不同顶层类别的标签
- 可以应用同一顶层类别下的不同分支的标签
- 不应同时应用互斥关系的标签

##### 自动标签推断

系统可以基于已有标签推断其他可能的标签：
- 如果文件有"月度"和"财务"标签，系统可以推断"报告"标签
- 如果文件有"客户A"和"合同"标签，系统可以推断"工作"标签

##### 标签传播

当文件被移动到特定文件夹时，可以自动继承该文件夹的标签：
- 移动到"项目A"文件夹的文件自动获得"项目A"标签
- 从"工作"文件夹移动出的文件可以保留"工作"标签（可配置）

