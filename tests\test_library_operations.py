"""
智能文件库操作的全面测试
"""

import os
import sys
import shutil
import tempfile
import unittest
from unittest.mock import patch

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from smartvault.data.database import Database
from smartvault.data.file_system import FileSystem
from smartvault.services.file import FileService
from smartvault.utils.config import load_config, save_config


class TestLibraryOperations(unittest.TestCase):
    """智能文件库操作测试类"""

    def setUp(self):
        """测试前准备"""
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        self.library_path = os.path.join(self.temp_dir, "library")
        self.new_library_path = os.path.join(self.temp_dir, "new_library")

        # 创建测试文件
        self.test_file = os.path.join(self.temp_dir, "test.txt")
        with open(self.test_file, 'w') as f:
            f.write("测试文件内容")

        # 模拟配置
        self.original_config = load_config()
        self.test_config = {
            "library_path": self.library_path,
            "default_entry_type": "link",
            "monitor_folders": [],
            "auto_import": False,
            "delete_physical_files": True
        }

        # 初始化文件库
        os.makedirs(self.library_path, exist_ok=True)

    def tearDown(self):
        """测试后清理"""
        # 恢复原始配置
        save_config(self.original_config)

        # 删除临时目录
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

    @patch('smartvault.utils.config.load_config')
    def test_library_initialization(self, mock_load_config):
        """测试文件库初始化"""
        mock_load_config.return_value = self.test_config

        # 初始化数据库
        db = Database()
        self.assertTrue(os.path.exists(os.path.join(self.library_path, "data")))
        self.assertTrue(os.path.exists(os.path.join(self.library_path, "data", "smartvault.db")))

        # 初始化文件系统
        fs = FileSystem()
        self.assertEqual(fs.library_path, self.library_path)

        db.close()

    @patch('smartvault.utils.config.load_config')
    @patch('smartvault.utils.config.save_config')
    def test_library_move_complete_flow(self, mock_save_config, mock_load_config):
        """测试文件库移动的完整流程"""
        mock_load_config.return_value = self.test_config

        # 1. 初始化文件服务并添加测试文件
        file_service = FileService()
        file_id = file_service.add_file(self.test_file, "copy")

        # 验证文件已添加
        files = file_service.get_files()
        self.assertEqual(len(files), 1)
        self.assertEqual(files[0]['id'], file_id)

        # 2. 移动文件库
        fs = FileSystem()

        # 创建目标目录
        os.makedirs(self.new_library_path, exist_ok=True)

        # 执行移动
        result = fs.move_library(self.new_library_path)
        self.assertTrue(result)

        # 3. 验证移动后的状态
        # 检查新位置是否有数据库
        new_db_path = os.path.join(self.new_library_path, "data", "smartvault.db")
        self.assertTrue(os.path.exists(new_db_path))

        # 检查文件是否正确移动
        # 注意：这里需要重新初始化数据库连接到新位置

    @patch('smartvault.utils.config.load_config')
    def test_library_path_consistency(self, mock_load_config):
        """测试文件库路径一致性"""
        mock_load_config.return_value = self.test_config

        # 初始化各个组件
        db = Database()
        fs = FileSystem()
        file_service = FileService()

        # 验证所有组件使用相同的路径
        self.assertEqual(fs.library_path, self.library_path)

        # 添加文件并验证路径
        file_id = file_service.add_file(self.test_file, "copy")
        file_info = file_service.get_file_by_id(file_id)

        # 验证库内文件路径正确
        self.assertTrue(file_info['library_path'].startswith(self.library_path))

        db.close()

    @patch('smartvault.utils.config.load_config')
    def test_file_path_update_after_move(self, mock_load_config):
        """测试移动后文件路径更新"""
        mock_load_config.return_value = self.test_config

        # 初始化并添加文件
        file_service = FileService()
        file_id = file_service.add_file(self.test_file, "copy")

        # 获取移动前的文件信息
        file_info_before = file_service.get_file_by_id(file_id)
        old_library_path = file_info_before['library_path']

        # 验证文件在旧库中存在
        self.assertTrue(os.path.exists(old_library_path))

        # 模拟移动文件库（这里简化处理）
        # 在实际应用中，这应该通过完整的移动流程来完成

    def test_config_update_after_move(self):
        """测试移动后配置更新"""
        # 保存初始配置
        save_config(self.test_config)

        # 模拟配置更新
        new_config = self.test_config.copy()
        new_config["library_path"] = self.new_library_path
        save_config(new_config)

        # 验证配置已更新
        loaded_config = load_config()
        self.assertEqual(loaded_config["library_path"], self.new_library_path)

    @patch('smartvault.utils.config.load_config')
    def test_database_reconnection_after_move(self, mock_load_config):
        """测试移动后数据库重连"""
        mock_load_config.return_value = self.test_config

        # 初始化数据库
        db1 = Database()
        db1_path = db1.db_path
        db1.close()

        # 更新配置到新路径
        new_config = self.test_config.copy()
        new_config["library_path"] = self.new_library_path
        mock_load_config.return_value = new_config

        # 创建新库目录
        os.makedirs(self.new_library_path, exist_ok=True)

        # 重新初始化数据库
        db2 = Database()
        db2_path = db2.db_path

        # 验证数据库路径已更新
        self.assertNotEqual(db1_path, db2_path)
        self.assertTrue(db2_path.startswith(self.new_library_path))

        db2.close()


class LibraryOperationsTester:
    """智能文件库操作测试器"""

    def __init__(self):
        """初始化测试器"""
        self.test_results = []

    def run_comprehensive_test(self):
        """运行全面测试"""
        print("🔍 开始智能文件库操作全面测试...")

        # 测试项目列表
        tests = [
            ("配置加载测试", self._test_config_loading),
            ("文件库初始化测试", self._test_library_initialization),
            ("文件添加测试", self._test_file_addition),
            ("路径一致性测试", self._test_path_consistency),
            ("移动操作测试", self._test_move_operation),
            ("重新打开测试", self._test_reopen_library),
        ]

        for test_name, test_func in tests:
            try:
                print(f"\n📋 执行: {test_name}")
                result = test_func()
                self.test_results.append((test_name, "✅ 通过", result))
                print(f"✅ {test_name} - 通过")
            except Exception as e:
                self.test_results.append((test_name, "❌ 失败", str(e)))
                print(f"❌ {test_name} - 失败: {e}")

        self._generate_report()

    def _test_config_loading(self):
        """测试配置加载"""
        config = load_config()
        assert "library_path" in config, "配置中缺少library_path"
        assert os.path.exists(config["library_path"]), f"文件库路径不存在: {config['library_path']}"
        return f"配置路径: {config['library_path']}"

    def _test_library_initialization(self):
        """测试文件库初始化"""
        from smartvault.services.file import FileService
        file_service = FileService()

        # 检查数据库连接
        assert file_service.db.conn is not None, "数据库连接失败"

        # 检查文件系统
        assert os.path.exists(file_service.file_system.library_path), "文件库路径不存在"

        return f"文件库路径: {file_service.file_system.library_path}"

    def _test_file_addition(self):
        """测试文件添加"""
        # 创建临时测试文件
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write("测试内容")
            test_file = f.name

        try:
            from smartvault.services.file import FileService
            file_service = FileService()

            # 添加文件
            file_id = file_service.add_file(test_file, "link")
            assert file_id is not None, "文件添加失败"

            # 验证文件信息
            file_info = file_service.get_file_by_id(file_id)
            assert file_info is not None, "无法获取文件信息"

            return f"文件ID: {file_id}, 类型: {file_info['entry_type']}"
        finally:
            # 清理测试文件
            if os.path.exists(test_file):
                os.unlink(test_file)

    def _test_path_consistency(self):
        """测试路径一致性"""
        from smartvault.services.file import FileService
        from smartvault.data.database import Database
        from smartvault.data.file_system import FileSystem

        file_service = FileService()
        db = Database()
        fs = FileSystem()

        # 检查路径一致性
        config = load_config()
        library_path = config["library_path"]

        assert fs.library_path == library_path, f"FileSystem路径不一致: {fs.library_path} != {library_path}"

        db_data_dir = os.path.dirname(db.db_path)
        expected_data_dir = os.path.join(library_path, "data")
        assert db_data_dir == expected_data_dir, f"数据库路径不一致: {db_data_dir} != {expected_data_dir}"

        return f"所有组件路径一致: {library_path}"

    def _test_move_operation(self):
        """测试移动操作"""
        # 这里只测试移动逻辑的准备工作
        # 实际移动需要在UI中进行
        config = load_config()
        current_path = config["library_path"]

        # 检查当前库的完整性
        data_dir = os.path.join(current_path, "data")
        db_file = os.path.join(data_dir, "smartvault.db")

        assert os.path.exists(data_dir), f"数据目录不存在: {data_dir}"
        assert os.path.exists(db_file), f"数据库文件不存在: {db_file}"

        return f"当前库完整性检查通过: {current_path}"

    def _test_reopen_library(self):
        """测试重新打开文件库"""
        # 测试重新初始化服务是否正常
        from smartvault.services.file import FileService

        # 第一次初始化
        file_service1 = FileService()
        files1 = file_service1.get_files(limit=5)

        # 第二次初始化
        file_service2 = FileService()
        files2 = file_service2.get_files(limit=5)

        # 验证结果一致
        assert len(files1) == len(files2), "重新打开后文件数量不一致"

        return f"重新打开测试通过，文件数量: {len(files1)}"

    def _generate_report(self):
        """生成测试报告"""
        print("\n" + "="*60)
        print("📊 智能文件库操作测试报告")
        print("="*60)

        passed = sum(1 for _, status, _ in self.test_results if "通过" in status)
        total = len(self.test_results)

        print(f"总测试数: {total}")
        print(f"通过数: {passed}")
        print(f"失败数: {total - passed}")
        print(f"通过率: {passed/total*100:.1f}%")

        print("\n📋 详细结果:")
        for test_name, status, details in self.test_results:
            print(f"  {status} {test_name}")
            if details:
                print(f"    详情: {details}")

        if passed == total:
            print("\n🎉 所有测试通过！智能文件库操作正常。")
        else:
            print(f"\n⚠️  有 {total - passed} 个测试失败，需要修复。")


if __name__ == "__main__":
    # 运行单元测试
    print("运行单元测试...")
    unittest.main(argv=[''], exit=False, verbosity=2)

    print("\n" + "="*60)

    # 运行综合测试
    tester = LibraryOperationsTester()
    tester.run_comprehensive_test()
