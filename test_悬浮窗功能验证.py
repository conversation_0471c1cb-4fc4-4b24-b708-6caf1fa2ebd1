#!/usr/bin/env python3
"""
悬浮窗功能验证测试
专门测试悬浮窗点击查看功能是否正常工作
"""

import sys
import os
import time
sys.path.append('.')

def test_floating_widget_with_real_data():
    """使用真实数据测试悬浮窗功能"""
    print("=" * 60)
    print("🔍 悬浮窗功能验证测试")
    print("=" * 60)
    
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import QTimer
        from smartvault.ui.widgets.clipboard_floating_widget import ClipboardFloatingWidget
        from smartvault.ui.main_window.clipboard_handler import ClipboardHandler
        from smartvault.services.file import FileService
        
        # 创建Qt应用
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建文件服务来获取真实数据
        file_service = FileService()
        
        # 获取一些真实文件数据
        files = file_service.get_files(limit=5)
        if not files:
            print("❌ 数据库中没有文件，无法进行测试")
            return False
        
        print(f"✅ 从数据库获取到 {len(files)} 个文件")
        
        # 使用第一个文件作为测试数据
        test_file = files[0]
        print(f"📄 测试文件: {test_file['name']} (ID: {test_file['id']})")
        
        # 模拟主窗口
        class MockMainWindow:
            def __init__(self):
                self.file_service = file_service
                self.clipboard_floating_widget = ClipboardFloatingWidget()
                self.clipboard_handler = ClipboardHandler(self)
                
                # 连接信号
                self.clipboard_floating_widget.open_file_requested.connect(
                    self.clipboard_handler.on_clipboard_open_file
                )
                
                # 模拟文件视图
                self.file_view = MockFileView()
                
            def show_status_message(self, message, success=True):
                print(f"📢 状态消息: {message} ({'成功' if success else '失败'})")
        
        class MockFileView:
            def get_current_model(self):
                return MockModel()
            
            def get_current_view(self):
                return MockView()
        
        class MockModel:
            def rowCount(self):
                return len(files)
            
            def index(self, row, col):
                return MockIndex(row, col)
            
            def data(self, index, role):
                if role == 256:  # Qt.UserRole
                    return files[index.row]['id']
                return files[index.row]['name']
        
        class MockView:
            def selectRow(self, row):
                print(f"🎯 选中行: {row} (文件: {files[row]['name']})")
            
            def scrollTo(self, index):
                print(f"📜 滚动到: 第{index.row + 1}行")
        
        class MockIndex:
            def __init__(self, row, col):
                self.row = row
                self.col = col
        
        # 创建模拟主窗口
        main_window = MockMainWindow()
        
        print("✅ 模拟主窗口创建成功")
        print("✅ 信号连接成功")
        
        # 创建真实的重复文件信息
        duplicate_info = {
            'type': 'file',
            'source_text': test_file['name'],
            'cleaned_name': test_file['name'],
            'duplicates': [
                {
                    'id': test_file['id'],
                    'name': test_file['name'],
                    'original_path': test_file.get('original_path', ''),
                    'library_path': test_file.get('library_path', ''),
                    'entry_type': test_file.get('entry_type', 'file')
                }
            ]
        }
        
        print(f"\n📋 创建重复文件信息:")
        print(f"   文件名: {duplicate_info['cleaned_name']}")
        print(f"   文件ID: {duplicate_info['duplicates'][0]['id']}")
        
        # 显示浮动窗口
        print("\n🪟 显示浮动窗口...")
        main_window.clipboard_floating_widget.show_duplicate(duplicate_info)
        print("✅ 浮动窗口显示成功")
        
        # 检查浮动窗口状态
        current_info = main_window.clipboard_floating_widget.current_duplicate_info
        if current_info:
            print("✅ 浮动窗口正确保存了重复文件信息")
            print(f"   保存的文件ID: {current_info['duplicates'][0]['id']}")
        else:
            print("❌ 浮动窗口未保存重复文件信息")
            return False
        
        # 模拟点击查看按钮
        print("\n🖱️ 模拟点击查看按钮...")
        
        # 检查查看按钮是否可见
        if main_window.clipboard_floating_widget.view_button.isVisible():
            print("✅ 查看按钮可见")
        else:
            print("❌ 查看按钮不可见")
            return False
        
        # 执行点击查看操作
        main_window.clipboard_floating_widget.on_view_clicked()
        print("✅ 查看按钮点击处理完成")
        
        # 等待一下让信号处理完成
        app.processEvents()
        
        print("\n🎉 悬浮窗功能验证测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_signal_connection_verification():
    """验证信号连接是否正确"""
    print("\n" + "=" * 60)
    print("🔗 信号连接验证测试")
    print("=" * 60)
    
    try:
        from PySide6.QtWidgets import QApplication
        from smartvault.ui.widgets.clipboard_floating_widget import ClipboardFloatingWidget
        from smartvault.ui.main_window.clipboard_handler import ClipboardHandler
        
        # 创建Qt应用
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建浮动窗口
        floating_widget = ClipboardFloatingWidget()
        
        # 检查信号是否存在
        if hasattr(floating_widget, 'open_file_requested'):
            print("✅ open_file_requested 信号存在")
        else:
            print("❌ open_file_requested 信号不存在")
            return False
        
        # 创建信号接收器
        signal_received = False
        received_file_id = None
        
        def signal_receiver(file_id):
            nonlocal signal_received, received_file_id
            signal_received = True
            received_file_id = file_id
            print(f"📡 接收到信号: file_id = {file_id}")
        
        # 连接信号
        floating_widget.open_file_requested.connect(signal_receiver)
        print("✅ 信号连接成功")
        
        # 设置测试数据
        test_duplicate_info = {
            'type': 'test',
            'source_text': '测试文件.txt',
            'cleaned_name': '测试文件.txt',
            'duplicates': [
                {
                    'id': 'test-signal-file-id',
                    'name': '测试文件.txt',
                    'original_path': '/test/path/测试文件.txt',
                    'library_path': 'test/测试文件.txt',
                    'entry_type': 'file'
                }
            ]
        }
        
        # 显示重复文件信息
        floating_widget.show_duplicate(test_duplicate_info)
        print("✅ 重复文件信息设置成功")
        
        # 触发查看按钮点击
        floating_widget.on_view_clicked()
        
        # 处理事件
        app.processEvents()
        
        # 验证信号是否被正确发射和接收
        if signal_received:
            print("✅ 信号发射和接收成功")
            if received_file_id == 'test-signal-file-id':
                print("✅ 信号参数正确")
                return True
            else:
                print(f"❌ 信号参数错误: 期望 'test-signal-file-id', 实际 '{received_file_id}'")
                return False
        else:
            print("❌ 信号未被接收")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始悬浮窗功能验证测试")
    print("目标: 验证悬浮窗点击查看功能是否正常工作")
    
    test_results = []
    
    # 执行测试
    test_results.append(("信号连接验证", test_signal_connection_verification()))
    test_results.append(("真实数据功能测试", test_floating_widget_with_real_data()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n总计: {passed} 个测试通过, {failed} 个测试失败")
    
    if failed == 0:
        print("🎉 悬浮窗功能验证成功！点击查看功能正常工作！")
        return True
    else:
        print("⚠️ 悬浮窗功能存在问题，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
