#!/usr/bin/env python3
"""
测试设备标签关联修复
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_device_tag_association():
    """测试设备标签关联功能"""
    try:
        from smartvault.data.database import Database
        from smartvault.services.tag_service import TagService
        
        print("=== 测试设备标签关联修复 ===")
        
        # 连接数据库
        db = Database.create_from_config()
        cursor = db.conn.cursor()
        tag_service = TagService()
        
        # 1. 检查数据库状态
        cursor.execute('SELECT COUNT(*) FROM files')
        total_files = cursor.fetchone()[0]
        print(f'数据库中总文件数: {total_files}')
        
        cursor.execute("SELECT COUNT(*) FROM files WHERE staging_status = 'staging'")
        staging_files = cursor.fetchone()[0]
        print(f'中转文件夹文件数: {staging_files}')
        
        # 2. 检查设备标签
        cursor.execute("SELECT id, name FROM tags WHERE name LIKE '💾%'")
        device_tags = cursor.fetchall()
        print(f'设备标签数量: {len(device_tags)}')
        
        for tag in device_tags:
            tag_id, tag_name = tag
            cursor.execute('SELECT COUNT(*) FROM file_tags WHERE tag_id = ?', (tag_id,))
            tag_file_count = cursor.fetchone()[0]
            print(f'  - {tag_name}: {tag_file_count} 个文件')
            
            # 检查设备标签的文件详情
            if tag_file_count > 0:
                files = tag_service.get_files_by_tag_hierarchy(tag_id)
                print(f'    通过标签服务查询到: {len(files)} 个文件')
                
                # 显示前5个文件
                for i, file_info in enumerate(files[:5]):
                    print(f'      {i+1}. {file_info["name"]} (ID: {file_info["id"][:8]}...)')
        
        # 3. 检查最近添加的文件是否有设备标签
        cursor.execute("""
            SELECT f.id, f.name, f.added_at, 
                   GROUP_CONCAT(t.name) as tags
            FROM files f
            LEFT JOIN file_tags ft ON f.id = ft.file_id
            LEFT JOIN tags t ON ft.tag_id = t.id
            WHERE f.added_at > datetime('now', '-1 hour')
            GROUP BY f.id
            ORDER BY f.added_at DESC
            LIMIT 10
        """)
        
        recent_files = cursor.fetchall()
        print(f'\n最近1小时添加的文件及其标签:')
        for file_info in recent_files:
            file_id, name, added_at, tags = file_info
            tags_str = tags if tags else "无标签"
            print(f'  - {name} (标签: {tags_str})')
        
        # 4. 检查文件夹组
        cursor.execute("""
            SELECT folder_group_id, folder_name, COUNT(*) as file_count
            FROM files 
            WHERE folder_group_id IS NOT NULL
            GROUP BY folder_group_id
            ORDER BY MAX(added_at) DESC
            LIMIT 5
        """)
        
        folder_groups = cursor.fetchall()
        print(f'\n最近的文件夹组:')
        for group_info in folder_groups:
            group_id, folder_name, file_count = group_info
            print(f'  - {folder_name}: {file_count} 个文件 (组ID: {group_id[:8]}...)')
            
            # 检查这个组的文件是否有设备标签
            cursor.execute("""
                SELECT COUNT(DISTINCT ft.tag_id) as tag_count
                FROM files f
                LEFT JOIN file_tags ft ON f.id = ft.file_id
                LEFT JOIN tags t ON ft.tag_id = t.id
                WHERE f.folder_group_id = ? AND t.name LIKE '💾%'
            """, (group_id,))
            
            device_tag_count = cursor.fetchone()[0]
            print(f'    设备标签关联: {device_tag_count} 个')
        
        db.close()
        print('\n=== 检查完成 ===')
        
    except Exception as e:
        print(f'测试失败: {e}')
        import traceback
        traceback.print_exc()

def test_pagination_logic():
    """测试分页逻辑"""
    try:
        from smartvault.services.file_service import FileService
        from smartvault.services.tag_service import TagService
        
        print("\n=== 测试分页逻辑 ===")
        
        file_service = FileService()
        tag_service = TagService()
        
        # 1. 测试所有文件计数
        all_files_count = file_service.get_file_count()
        print(f'所有文件计数: {all_files_count}')
        
        # 2. 测试中转文件夹计数
        staging_count = file_service.get_file_count(folder_filter_type="staging")
        print(f'中转文件夹计数: {staging_count}')
        
        # 3. 测试设备标签文件计数
        from smartvault.data.database import Database
        db = Database.create_from_config()
        cursor = db.conn.cursor()
        
        cursor.execute("SELECT id, name FROM tags WHERE name LIKE '💾%'")
        device_tags = cursor.fetchall()
        
        for tag in device_tags:
            tag_id, tag_name = tag
            files = tag_service.get_files_by_tag_hierarchy(tag_id)
            print(f'设备 {tag_name} 文件计数: {len(files)}')
        
        db.close()
        print('=== 分页逻辑测试完成 ===')
        
    except Exception as e:
        print(f'分页逻辑测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_device_tag_association()
    test_pagination_logic()
