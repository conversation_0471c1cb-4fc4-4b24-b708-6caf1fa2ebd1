# SmartVault

SmartVault 是一个全流程本地化的智能文件管理系统，旨在解决用户在数字时代面临的文件管理挑战。系统通过"智能文件库"概念，提供自动化分类、智能检索、查重、标注等功能，实现"用户干预极简化"的核心价值。

## 功能特点

- **智能文件库管理**：支持链接、复制和移动三种入库方式
- **多维度文件组织**：突破传统文件夹结构限制，支持一个文件属于多个类别
- **智能搜索与检索**：基于内容、标签、元数据的综合搜索
- **文件重复管理**：自动检测重复文件，节省存储空间
- **特定文件类型管理**：针对软件、视频、图片、文档等提供专门管理功能

## 安装

### 开发环境

1. 克隆仓库
   ```
   git clone https://github.com/yourusername/smartvault.git
   cd smartvault
   ```

2. 创建虚拟环境
   ```
   python -m venv venv
   venv\Scripts\activate  # Windows
   source venv/bin/activate  # Linux/Mac
   ```

3. 安装依赖
   ```
   pip install -r requirements.txt
   ```

4. 安装开发模式
   ```
   pip install -e .
   ```

## 使用方法

运行应用程序：

```
python -m smartvault.main
```

或者安装后直接运行：

```
smartvault
```

## 项目结构

```
smartvault/
├── __init__.py
├── main.py                 # 应用入口点
├── ui/                     # UI组件
│   ├── __init__.py
│   ├── main_window/        # 主窗口功能切片
│   │   ├── __init__.py     # 导出MainWindow类
│   │   ├── core.py         # 核心框架代码
│   │   ├── menu.py         # 菜单管理
│   │   ├── toolbar.py      # 工具栏管理
│   │   ├── file_ops.py     # 文件操作相关功能
│   │   └── search.py       # 搜索功能
│   ├── dialogs/            # 对话框
│   ├── views/              # 视图组件
│   └── widgets/            # 自定义控件
├── services/               # 业务服务
│   ├── __init__.py
│   ├── file/               # 文件服务功能切片
│   │   ├── __init__.py     # 导出FileService类
│   │   ├── core.py         # 核心服务功能
│   │   ├── import_ops.py   # 文件导入功能
│   │   └── operations.py   # 文件操作功能
│   ├── tag_service.py      # 标签服务
│   └── search_service.py   # 搜索服务
├── data/                   # 数据访问
│   ├── __init__.py
│   ├── database.py         # 数据库访问
│   └── file_system.py      # 文件系统操作
└── utils/                  # 工具类
    ├── __init__.py
    ├── config.py           # 配置管理
    └── logging.py          # 日志管理
```

## 开发

### 测试

运行测试：

```
pytest
```

### 构建

构建可执行文件：

```
pyinstaller --onefile --windowed --icon=icons/SmartVault.ico smartvault/main.py
```

## 许可证

[MIT](LICENSE)
