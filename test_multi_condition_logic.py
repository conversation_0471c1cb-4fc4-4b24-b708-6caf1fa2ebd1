#!/usr/bin/env python3
"""
测试多条件自动标签逻辑（不依赖GUI）
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from smartvault.services.auto_tag_service import (
    AutoTagRule, AutoTagService, Condition, ConditionGroup, 
    ConditionType, LogicOperator
)


def test_scenario_1():
    """测试场景1：文档类型 AND 大文件"""
    print("🔍 测试场景1：文档类型 AND 大文件")
    
    # 创建条件：(扩展名是pdf OR doc) AND 文件大小>1MB
    condition_group = ConditionGroup(
        operator=LogicOperator.AND,
        conditions=[
            Condition(type=ConditionType.FILE_EXTENSION, value="pdf,doc"),
            Condition(type=ConditionType.FILE_SIZE, value=">1MB")
        ]
    )
    
    rule = AutoTagRule(
        id="rule1",
        name="大文档规则",
        tag_names=["大文档", "重要"],
        condition_group=condition_group
    )
    
    print(f"规则描述: {rule.get_description()}")
    
    # 测试文件
    test_files = [
        {"name": "report.pdf", "size": 2 * 1024 * 1024, "expected": True},  # 2MB PDF
        {"name": "small.pdf", "size": 500 * 1024, "expected": False},      # 500KB PDF
        {"name": "large.txt", "size": 3 * 1024 * 1024, "expected": False}, # 3MB TXT
        {"name": "document.doc", "size": 1.5 * 1024 * 1024, "expected": True}, # 1.5MB DOC
    ]
    
    for file_info in test_files:
        result = rule.matches(file_info)
        status = "✅" if result == file_info["expected"] else "❌"
        print(f"  {status} {file_info['name']} ({file_info['size']//1024//1024}MB) -> {result}")
    
    print()


def test_scenario_2():
    """测试场景2：图片文件 OR 重要文件"""
    print("🔍 测试场景2：图片文件 OR 重要文件")
    
    # 创建条件：文件类型是图片 OR 文件名包含"重要"
    condition_group = ConditionGroup(
        operator=LogicOperator.OR,
        conditions=[
            Condition(type=ConditionType.FILE_TYPE, value="图片"),
            Condition(type=ConditionType.FILE_NAME_PATTERN, value="重要")
        ]
    )
    
    rule = AutoTagRule(
        id="rule2",
        name="图片或重要文件规则",
        tag_names=["需关注"],
        condition_group=condition_group
    )
    
    print(f"规则描述: {rule.get_description()}")
    
    # 测试文件
    test_files = [
        {"name": "photo.jpg", "size": 1024, "expected": True},      # 图片文件
        {"name": "重要报告.txt", "size": 1024, "expected": True},    # 重要文件
        {"name": "normal.txt", "size": 1024, "expected": False},    # 普通文件
        {"name": "重要图片.png", "size": 1024, "expected": True},    # 既是图片又是重要文件
    ]
    
    for file_info in test_files:
        result = rule.matches(file_info)
        status = "✅" if result == file_info["expected"] else "❌"
        print(f"  {status} {file_info['name']} -> {result}")
    
    print()


def test_scenario_3():
    """测试场景3：复杂嵌套条件"""
    print("🔍 测试场景3：复杂嵌套条件")
    
    # 创建复杂条件：(PDF OR DOC) AND (大文件 OR 重要文件)
    file_type_group = ConditionGroup(
        operator=LogicOperator.OR,
        conditions=[
            Condition(type=ConditionType.FILE_EXTENSION, value="pdf"),
            Condition(type=ConditionType.FILE_EXTENSION, value="doc,docx")
        ]
    )
    
    importance_group = ConditionGroup(
        operator=LogicOperator.OR,
        conditions=[
            Condition(type=ConditionType.FILE_SIZE, value=">2MB"),
            Condition(type=ConditionType.FILE_NAME_PATTERN, value="重要|urgent")
        ]
    )
    
    main_group = ConditionGroup(
        operator=LogicOperator.AND,
        conditions=[file_type_group, importance_group]
    )
    
    rule = AutoTagRule(
        id="rule3",
        name="重要文档规则",
        tag_names=["重要文档", "高优先级"],
        condition_group=main_group
    )
    
    print(f"规则描述: {rule.get_description()}")
    
    # 测试文件
    test_files = [
        {"name": "report.pdf", "size": 3 * 1024 * 1024, "expected": True},     # PDF + 大文件
        {"name": "urgent.doc", "size": 1024, "expected": True},                # DOC + 重要文件
        {"name": "small.pdf", "size": 1024, "expected": False},                # PDF但不大也不重要
        {"name": "重要资料.txt", "size": 5 * 1024 * 1024, "expected": False},   # 重要且大但不是PDF/DOC
        {"name": "重要报告.docx", "size": 500 * 1024, "expected": True},        # DOCX + 重要
    ]
    
    for file_info in test_files:
        result = rule.matches(file_info)
        status = "✅" if result == file_info["expected"] else "❌"
        size_mb = file_info['size'] // 1024 // 1024 if file_info['size'] > 1024*1024 else 0
        size_str = f"{size_mb}MB" if size_mb > 0 else f"{file_info['size']//1024}KB"
        print(f"  {status} {file_info['name']} ({size_str}) -> {result}")
    
    print()


def test_service_integration():
    """测试服务集成"""
    print("🔍 测试服务集成")
    
    service = AutoTagService()
    
    # 添加多个规则
    rules = [
        # 规则1：大PDF文档
        AutoTagRule(
            id="rule1",
            name="大PDF规则",
            tag_names=["PDF", "大文件"],
            condition_group=ConditionGroup(
                operator=LogicOperator.AND,
                conditions=[
                    Condition(type=ConditionType.FILE_EXTENSION, value="pdf"),
                    Condition(type=ConditionType.FILE_SIZE, value=">1MB")
                ]
            ),
            priority=10
        ),
        
        # 规则2：重要文件
        AutoTagRule(
            id="rule2",
            name="重要文件规则",
            tag_names=["重要", "需关注"],
            condition_group=ConditionGroup(
                operator=LogicOperator.OR,
                conditions=[
                    Condition(type=ConditionType.FILE_NAME_PATTERN, value="重要|urgent|important"),
                    Condition(type=ConditionType.FILE_SIZE, value=">10MB")
                ]
            ),
            priority=5
        ),
        
        # 规则3：图片文件
        AutoTagRule(
            id="rule3",
            name="图片规则",
            tag_names=["图片", "媒体"],
            condition_group=ConditionGroup(
                operator=LogicOperator.AND,
                conditions=[
                    Condition(type=ConditionType.FILE_TYPE, value="图片")
                ]
            ),
            priority=1
        )
    ]
    
    for rule in rules:
        service.add_rule(rule)
    
    print(f"已添加 {len(service.rules)} 个规则")
    
    # 测试文件
    test_files = [
        {"name": "重要报告.pdf", "size": 2 * 1024 * 1024},  # 应该匹配规则1和2
        {"name": "photo.jpg", "size": 500 * 1024},          # 应该匹配规则3
        {"name": "huge_file.txt", "size": 15 * 1024 * 1024}, # 应该匹配规则2
        {"name": "small.txt", "size": 1024},                 # 不应该匹配任何规则
    ]
    
    for file_info in test_files:
        auto_tags = service.get_auto_tags_for_file(file_info)
        size_mb = file_info['size'] // 1024 // 1024 if file_info['size'] > 1024*1024 else 0
        size_str = f"{size_mb}MB" if size_mb > 0 else f"{file_info['size']//1024}KB"
        print(f"  📁 {file_info['name']} ({size_str}) -> 标签: {auto_tags}")
    
    print()


def test_backward_compatibility():
    """测试向后兼容性"""
    print("🔍 测试向后兼容性")
    
    # 创建旧版本格式的规则
    old_rule = AutoTagRule(
        id="old_rule",
        name="旧版PDF规则",
        tag_names=["PDF文档"],
        condition_type=ConditionType.FILE_EXTENSION,
        condition_value="pdf"
    )
    
    print(f"旧版规则描述: {old_rule.get_description()}")
    
    # 测试匹配
    test_files = [
        {"name": "document.pdf", "size": 1024, "expected": True},
        {"name": "document.txt", "size": 1024, "expected": False},
    ]
    
    for file_info in test_files:
        result = old_rule.matches(file_info)
        status = "✅" if result == file_info["expected"] else "❌"
        print(f"  {status} {file_info['name']} -> {result}")
    
    print()


def main():
    """主函数"""
    print("=" * 60)
    print("🚀 多条件自动标签功能测试")
    print("=" * 60)
    print()
    
    try:
        test_scenario_1()
        test_scenario_2()
        test_scenario_3()
        test_service_integration()
        test_backward_compatibility()
        
        print("✅ 所有测试完成！多条件自动标签功能工作正常。")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
