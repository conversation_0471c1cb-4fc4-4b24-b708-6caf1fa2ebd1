"""
添加文件对话框UI测试
"""

import os
import sys
import unittest
import tempfile
from unittest.mock import patch, MagicMock

from PySide6.QtWidgets import QApplication, QFileDialog
from PySide6.QtTest import QTest
from PySide6.QtCore import Qt

# 确保能够导入 smartvault 模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from smartvault.ui.dialogs import AddFileDialog


class TestAddFileDialog(unittest.TestCase):
    """添加文件对话框UI测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类前准备"""
        # 创建应用程序实例
        cls.app = QApplication.instance()
        if cls.app is None:
            cls.app = QApplication([])
    
    def setUp(self):
        """测试前准备"""
        # 创建临时目录
        self.temp_dir = tempfile.TemporaryDirectory()
        
        # 创建测试文件
        self.test_file_path = os.path.join(self.temp_dir.name, "test_file.txt")
        with open(self.test_file_path, "w", encoding="utf-8") as f:
            f.write("This is a test file.")
        
        # 模拟文件对话框
        self.patcher = patch('PySide6.QtWidgets.QFileDialog.getOpenFileNames')
        self.mock_get_open_file_names = self.patcher.start()
        self.mock_get_open_file_names.return_value = ([self.test_file_path], "")
        
        # 创建对话框
        self.dialog = AddFileDialog()
    
    def tearDown(self):
        """测试后清理"""
        # 关闭对话框
        self.dialog.close()
        
        # 停止模拟
        self.patcher.stop()
        
        # 删除临时目录
        self.temp_dir.cleanup()
    
    def test_dialog_title(self):
        """测试对话框标题"""
        self.assertEqual(self.dialog.windowTitle(), "添加文件")
    
    def test_dialog_size(self):
        """测试对话框大小"""
        self.assertGreaterEqual(self.dialog.width(), 400)
        self.assertGreaterEqual(self.dialog.height(), 300)
    
    def test_add_file_button(self):
        """测试添加文件按钮"""
        # 验证添加文件按钮是否存在
        self.assertIsNotNone(self.dialog.add_file_button)
        
        # 点击添加文件按钮
        QTest.mouseClick(self.dialog.add_file_button, Qt.LeftButton)
        
        # 验证文件对话框是否被调用
        self.mock_get_open_file_names.assert_called_once()
        
        # 验证文件列表是否包含测试文件
        self.assertEqual(len(self.dialog.file_list), 1)
        self.assertEqual(self.dialog.file_list[0], self.test_file_path)
        
        # 验证文件列表控件是否显示测试文件
        self.assertEqual(self.dialog.file_list_widget.count(), 1)
        self.assertEqual(self.dialog.file_list_widget.item(0).text(), self.test_file_path)
    
    def test_entry_type_selection(self):
        """测试入库方式选择"""
        # 验证入库方式单选按钮是否存在
        self.assertIsNotNone(self.dialog.link_radio)
        self.assertIsNotNone(self.dialog.copy_radio)
        self.assertIsNotNone(self.dialog.move_radio)
        
        # 验证默认选择链接模式
        self.assertTrue(self.dialog.link_radio.isChecked())
        self.assertFalse(self.dialog.copy_radio.isChecked())
        self.assertFalse(self.dialog.move_radio.isChecked())
        
        # 选择复制模式
        self.dialog.copy_radio.setChecked(True)
        
        # 验证入库方式
        self.assertEqual(self.dialog.get_entry_type(), "copy")
        
        # 选择移动模式
        self.dialog.move_radio.setChecked(True)
        
        # 验证入库方式
        self.assertEqual(self.dialog.get_entry_type(), "move")


if __name__ == "__main__":
    unittest.main()
