"""
进度条组件基类和实现
"""

from PySide6.QtWidgets import (
    QWidget, QProgressBar, QLabel, QHBoxLayout, QVBoxLayout,
    QPushButton, QSizePolicy
)
from PySide6.QtCore import Qt, Signal, QTimer


class BaseProgressBarWidget(QWidget):
    """进度条基类，提供通用功能"""

    # 自定义信号
    canceled = Signal()  # 取消信号

    def __init__(self, parent=None):
        """初始化基类

        Args:
            parent: 父窗口
        """
        super().__init__(parent)

        # 子类需要实现的组件
        self.label = None
        self.progress_bar = None
        self.cancel_button = None
        self.hide_timer = None

    def start_progress(self, text, maximum=100, show_cancel=True):
        """开始进度

        Args:
            text: 进度文本
            maximum: 最大值
            show_cancel: 是否显示取消按钮
        """
        if self.label:
            self.label.setText(text)
        if self.progress_bar:
            self.progress_bar.setMaximum(maximum)
            self.progress_bar.setValue(0)
            self.progress_bar.setFormat("%p%")
        if self.cancel_button:
            self.cancel_button.setVisible(show_cancel)
        if self.hide_timer:
            self.hide_timer.stop()

    def set_progress(self, value, text=None):
        """设置进度

        Args:
            value: 进度值
            text: 进度文本
        """
        if self.progress_bar:
            self.progress_bar.setValue(value)
        if text and self.label:
            self.label.setText(text)

    def set_indeterminate(self, text=None):
        """设置为不确定进度

        Args:
            text: 进度文本
        """
        if self.progress_bar:
            self.progress_bar.setMaximum(0)
            self.progress_bar.setMinimum(0)
        if text and self.label:
            self.label.setText(text)

    def on_cancel_clicked(self):
        """取消按钮点击事件"""
        self.canceled.emit()


class ProgressBarWidget(BaseProgressBarWidget):
    """大型进度条组件，用于对话框等场景"""

    def __init__(self, parent=None):
        """初始化

        Args:
            parent: 父窗口
        """
        super().__init__(parent)

        # 设置属性
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.setMinimumHeight(50)
        self.setMaximumHeight(50)
        self.setStyleSheet("""
            QProgressBar {
                border: 1px solid #ccc;
                border-radius: 5px;
                text-align: center;
                background-color: #f0f0f0;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                width: 10px;
                margin: 0.5px;
            }
        """)
        # 默认隐藏，但在需要时会显示
        self.setVisible(False)

        # 创建布局
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(5, 5, 5, 5)
        self.layout.setSpacing(2)

        # 创建标签和进度条
        self.top_layout = QHBoxLayout()
        self.label = QLabel("进度")
        self.cancel_button = QPushButton("取消")
        self.cancel_button.setMaximumWidth(60)
        self.cancel_button.clicked.connect(self.on_cancel_clicked)
        self.top_layout.addWidget(self.label)
        self.top_layout.addStretch()
        self.top_layout.addWidget(self.cancel_button)

        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setAlignment(Qt.AlignCenter)

        self.layout.addLayout(self.top_layout)
        self.layout.addWidget(self.progress_bar)

        # 自动隐藏定时器
        self.hide_timer = QTimer(self)
        self.hide_timer.setSingleShot(True)
        self.hide_timer.timeout.connect(self.hide)

    def start_progress(self, text, maximum=100, show_cancel=True):
        """开始进度，重写基类方法以添加显示逻辑"""
        super().start_progress(text, maximum, show_cancel)
        self.setVisible(True)

    def finish_progress(self, text=None, auto_hide=True, delay=3000):
        """完成进度

        Args:
            text: 完成文本
            auto_hide: 是否自动隐藏
            delay: 自动隐藏延迟（毫秒）
        """
        if self.progress_bar:
            self.progress_bar.setMaximum(100)
            self.progress_bar.setValue(100)
        if text and self.label:
            self.label.setText(text)

        if auto_hide and self.hide_timer:
            self.hide_timer.start(delay)

    def on_cancel_clicked(self):
        """取消按钮点击事件"""
        super().on_cancel_clicked()
        self.hide()
