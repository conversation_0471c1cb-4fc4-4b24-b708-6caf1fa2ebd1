"""
测试完全禁用数据加载的启动
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """主函数"""
    print("🧪 测试禁用数据加载的启动...")

    try:
        from PySide6.QtWidgets import QApplication
        from smartvault.ui.main_window import MainWindow

        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("SmartVault-NoData")
        print("✅ QApplication创建成功")

        # 临时禁用数据加载
        from smartvault.ui.main_window.core import MainWindowCore
        original_load_initial_data = MainWindowCore.load_initial_data
        MainWindowCore.load_initial_data = lambda self, show_message=True: print("数据加载已禁用")

        try:
            print("🏗️  创建主窗口（禁用数据加载）...")
            window = MainWindow()
            print("✅ 主窗口创建成功")

            print("👁️  显示窗口...")
            window.show()
            print("✅ 窗口显示成功")

            print("🔄 测试事件循环（3秒后自动退出）...")

            # 设置自动退出
            from PySide6.QtCore import QTimer
            def auto_exit():
                print("✅ 测试成功，自动退出")
                app.quit()

            timer = QTimer()
            timer.timeout.connect(auto_exit)
            timer.start(3000)

            # 运行事件循环
            exit_code = app.exec()
            print(f"程序退出，退出码: {exit_code}")

            return exit_code

        finally:
            # 恢复原始方法
            MainWindowCore.load_initial_data = original_load_initial_data

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
