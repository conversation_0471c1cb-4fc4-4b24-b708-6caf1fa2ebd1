# ============= Python =============
# 字节码
__pycache__/
*.py[cod]
*$py.class

# 虚拟环境
venv/
env/
ENV/
.env
.venv/
.python-version

# 分发/打包
dist/
build/
*.egg-info/
*.egg
*.whl

# 单元测试/覆盖率报告
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
coverage.xml
*.cover

# ============= 系统 =============
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# ============= IDE =============
# VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# PyCharm
.idea/
*.iml
*.iws
*.ipr
.idea_modules/

# ============= 项目特定 =============
# 数据目录
/data/
/output/

# 备份文件
/backup/

# 测试样本
/test_sample/

# 日志
*.log
logs/

# 数据库
*.db
*.sqlite
*.sqlite3

# 敏感配置
secrets.json
local_settings.py

# 编译文件
*.exe
*.spec

# 临时文件
*.tmp
*.bak
*.swp
*~

# 排除配置文件模板
!config.yaml.template
!settings.yaml.template

# 包含项目配置文件
!smartvault/config/*.yaml

# 包含测试目录但排除测试数据
!/tests/
/tests/data/

# 包含图标
!/icons/
/icons/*
!icons/SmartVault.ico

# SmartVault 特定
/smartvault/library/
/smartvault/config/*.json
!/smartvault/config/*.json.template