import sys
import os
sys.path.append('.')

try:
    from smartvault.services.clipboard_monitor_service import ClipboardMonitorService
    from smartvault.utils.config import load_config
    
    print("测试剪贴板监控服务数据库连接...")
    
    # 检查配置
    config = load_config()
    library_path = config.get("library_path", "")
    print(f"文件库路径: {library_path}")
    
    if library_path:
        db_path = os.path.join(library_path, "data", "smartvault.db")
        print(f"数据库路径: {db_path}")
        print(f"数据库文件存在: {os.path.exists(db_path)}")
    
    # 创建剪贴板监控服务实例
    print("\n创建剪贴板监控服务...")
    clipboard_service = ClipboardMonitorService()
    
    # 测试数据库连接
    print("\n测试数据库连接...")
    connection_result = clipboard_service._ensure_database_connection()
    print(f"数据库连接结果: {connection_result}")
    
    if connection_result and clipboard_service.db:
        print("✅ 数据库连接成功")
        print(f"数据库路径: {clipboard_service.db.db_path}")
        
        # 测试简单查询
        try:
            cursor = clipboard_service.db.conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM files")
            count = cursor.fetchone()[0]
            print(f"数据库中文件数量: {count}")
        except Exception as e:
            print(f"查询数据库失败: {e}")
    else:
        print("❌ 数据库连接失败")
    
except Exception as e:
    print(f"测试失败: {e}")
    import traceback
    traceback.print_exc()
