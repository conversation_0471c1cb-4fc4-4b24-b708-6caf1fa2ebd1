#!/usr/bin/env python3
"""
检查UI文件长度
"""

import os
import glob

def check_file_lengths():
    """检查UI相关文件的长度"""
    
    # 检查设置页面文件
    print("📊 设置页面文件长度统计")
    print("=" * 50)
    
    settings_files = glob.glob('smartvault/ui/dialogs/settings/pages/*.py')
    settings_total = 0
    
    for file_path in sorted(settings_files):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = len(f.readlines())
                settings_total += lines
                filename = os.path.basename(file_path)
                status = "🟢" if lines < 300 else "🟡" if lines < 500 else "🔴"
                print(f"{status} {filename}: {lines} lines")
        except Exception as e:
            print(f"❌ {file_path}: Error reading file - {e}")
    
    print(f"\n📈 设置页面总计: {settings_total} lines")
    
    # 检查主要UI文件
    print("\n📊 主要UI文件长度统计")
    print("=" * 50)
    
    main_ui_files = [
        'smartvault/ui/dialogs/settings_dialog.py',
        'smartvault/ui/main_window/core.py',
        'smartvault/ui/dialogs/add_file_dialog.py',
        'smartvault/ui/dialogs/monitor_config_dialog.py'
    ]
    
    main_total = 0
    for file_path in main_ui_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = len(f.readlines())
                    main_total += lines
                    filename = os.path.basename(file_path)
                    status = "🟢" if lines < 500 else "🟡" if lines < 1000 else "🔴"
                    print(f"{status} {filename}: {lines} lines")
            except Exception as e:
                print(f"❌ {file_path}: Error reading file - {e}")
        else:
            print(f"❌ {file_path}: File not found")
    
    print(f"\n📈 主要UI文件总计: {main_total} lines")
    
    # 总结
    print(f"\n📊 总体统计")
    print("=" * 50)
    print(f"设置页面文件数: {len(settings_files)}")
    print(f"设置页面总行数: {settings_total}")
    print(f"主要UI文件总行数: {main_total}")
    print(f"UI代码总行数: {settings_total + main_total}")
    
    # 建议
    print(f"\n💡 代码长度评估")
    print("=" * 50)
    print("🟢 < 300行: 良好")
    print("🟡 300-500行: 可接受") 
    print("🔴 > 500行: 需要考虑拆分")

if __name__ == "__main__":
    check_file_lengths()
