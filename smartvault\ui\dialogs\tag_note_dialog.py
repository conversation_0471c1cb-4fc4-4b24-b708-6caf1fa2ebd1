"""
文件备注管理对话框
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QTextEdit, QMessageBox
)
from PySide6.QtCore import Qt, Signal

from smartvault.services.file import FileService


class TagNoteDialog(QDialog):
    """文件备注管理对话框"""

    # 信号：备注发生变化时发出
    notes_changed = Signal()

    def __init__(self, file_id, parent=None):
        """初始化对话框

        Args:
            file_id: 文件ID
            parent: 父窗口
        """
        super().__init__(parent)
        self.file_id = file_id
        self.file_service = FileService()

        self.init_ui()
        self.load_data()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("编辑文件备注")
        self.setModal(True)
        self.resize(400, 300)

        # 主布局
        layout = QVBoxLayout(self)

        # 文件信息
        self.file_info_label = QLabel()
        layout.addWidget(self.file_info_label)

        # 备注编辑
        layout.addWidget(QLabel("文件备注:"))
        self.note_edit = QTextEdit()
        self.note_edit.setPlaceholderText("为此文件添加备注...")
        layout.addWidget(self.note_edit)

        # 对话框按钮
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()

        self.save_btn = QPushButton("保存")
        self.save_btn.clicked.connect(self.save_note)
        buttons_layout.addWidget(self.save_btn)

        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)

        layout.addLayout(buttons_layout)

    def load_data(self):
        """加载数据"""
        try:
            # 获取文件信息
            file_info = self.file_service.get_file_by_id(self.file_id)
            if file_info:
                self.file_info_label.setText(f"文件: {file_info['name']}")

                # 加载现有备注
                current_note = file_info.get('note', '') or ''
                self.note_edit.setPlainText(current_note)
            else:
                self.file_info_label.setText("文件信息加载失败")

        except Exception as e:
            QMessageBox.warning(self, "错误", f"加载数据失败: {e}")

    def save_note(self):
        """保存备注"""
        try:
            note = self.note_edit.toPlainText().strip()
            success = self.file_service.update_file_note(self.file_id, note)

            if success:
                self.notes_changed.emit()
                self.accept()  # 关闭对话框
            else:
                QMessageBox.warning(self, "错误", "保存备注失败")

        except Exception as e:
            QMessageBox.warning(self, "错误", f"保存备注失败: {e}")


if __name__ == "__main__":
    import sys
    from PySide6.QtWidgets import QApplication

    app = QApplication(sys.argv)

    # 测试对话框
    dialog = TagNoteDialog("test_file_id")
    dialog.show()

    sys.exit(app.exec())
