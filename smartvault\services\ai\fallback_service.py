"""
AI功能降级处理服务

当AI功能不可用时，提供基础的降级功能
"""

import os
from typing import Dict, List


class FallbackService:
    """AI功能降级处理器"""
    
    def __init__(self):
        # 基础的文件类型到标签的映射
        self.extension_tag_mapping = {
            # 文档类型
            '.pdf': ['文档', 'PDF'],
            '.doc': ['文档', 'Word'],
            '.docx': ['文档', 'Word'],
            '.txt': ['文档', '文本'],
            '.md': ['文档', 'Markdown'],
            '.rtf': ['文档', 'RTF'],
            
            # 图片类型
            '.jpg': ['图片', '照片'],
            '.jpeg': ['图片', '照片'],
            '.png': ['图片', 'PNG'],
            '.gif': ['图片', 'GIF'],
            '.bmp': ['图片', 'BMP'],
            '.svg': ['图片', '矢量图'],
            '.webp': ['图片', 'WebP'],
            
            # 视频类型
            '.mp4': ['视频', 'MP4'],
            '.avi': ['视频', 'AVI'],
            '.mkv': ['视频', 'MKV'],
            '.mov': ['视频', 'MOV'],
            '.wmv': ['视频', 'WMV'],
            '.flv': ['视频', 'FLV'],
            
            # 音频类型
            '.mp3': ['音频', 'MP3'],
            '.wav': ['音频', 'WAV'],
            '.flac': ['音频', 'FLAC'],
            '.aac': ['音频', 'AAC'],
            '.ogg': ['音频', 'OGG'],
            
            # 压缩文件
            '.zip': ['压缩包', 'ZIP'],
            '.rar': ['压缩包', 'RAR'],
            '.7z': ['压缩包', '7Z'],
            '.tar': ['压缩包', 'TAR'],
            '.gz': ['压缩包', 'GZ'],
            
            # 代码文件
            '.py': ['代码', 'Python'],
            '.js': ['代码', 'JavaScript'],
            '.html': ['代码', 'HTML'],
            '.css': ['代码', 'CSS'],
            '.java': ['代码', 'Java'],
            '.cpp': ['代码', 'C++'],
            '.c': ['代码', 'C'],
            '.php': ['代码', 'PHP'],
            '.sql': ['代码', 'SQL'],
            
            # 数据文件
            '.xlsx': ['数据', 'Excel'],
            '.xls': ['数据', 'Excel'],
            '.csv': ['数据', 'CSV'],
            '.json': ['数据', 'JSON'],
            '.xml': ['数据', 'XML'],
            
            # 设计文件
            '.psd': ['设计', 'Photoshop'],
            '.ai': ['设计', 'Illustrator'],
            '.sketch': ['设计', 'Sketch'],
            '.fig': ['设计', 'Figma'],
            
            # 可执行文件
            '.exe': ['软件', '可执行文件'],
            '.msi': ['软件', '安装包'],
            '.dmg': ['软件', 'Mac安装包'],
            '.deb': ['软件', 'Debian包'],
            '.rpm': ['软件', 'RPM包'],
        }
        
        # 文件名关键词到标签的映射
        self.keyword_tag_mapping = {
            # 项目相关
            'project': ['项目'],
            'demo': ['演示', '示例'],
            'test': ['测试'],
            'backup': ['备份'],
            'temp': ['临时'],
            'draft': ['草稿'],
            
            # 重要性
            'important': ['重要'],
            'urgent': ['紧急'],
            'final': ['最终版'],
            'v1': ['版本1'],
            'v2': ['版本2'],
            'version': ['版本'],
            
            # 工作相关
            'work': ['工作'],
            'personal': ['个人'],
            'meeting': ['会议'],
            'report': ['报告'],
            'presentation': ['演示文稿'],
            'proposal': ['提案'],
            
            # 学习相关
            'study': ['学习'],
            'course': ['课程'],
            'tutorial': ['教程'],
            'note': ['笔记'],
            'homework': ['作业'],
            
            # 时间相关
            '2024': ['2024年'],
            '2023': ['2023年'],
            'january': ['1月'],
            'february': ['2月'],
            'march': ['3月'],
            'april': ['4月'],
            'may': ['5月'],
            'june': ['6月'],
            'july': ['7月'],
            'august': ['8月'],
            'september': ['9月'],
            'october': ['10月'],
            'november': ['11月'],
            'december': ['12月'],
        }
    
    def suggest_tags_fallback(self, file_info: Dict) -> List[str]:
        """基于文件扩展名和文件名的降级标签建议
        
        Args:
            file_info: 文件信息字典
            
        Returns:
            List[str]: 建议的标签列表
        """
        suggestions = []
        
        # 基于文件扩展名
        extension = file_info.get('extension', '').lower()
        if extension in self.extension_tag_mapping:
            suggestions.extend(self.extension_tag_mapping[extension])
        
        # 基于文件名关键词
        filename = file_info.get('name', '').lower()
        for keyword, tags in self.keyword_tag_mapping.items():
            if keyword in filename:
                suggestions.extend(tags)
        
        # 基于文件路径
        file_path = file_info.get('path', '').lower()
        for keyword, tags in self.keyword_tag_mapping.items():
            if keyword in file_path:
                suggestions.extend(tags)
        
        # 去重并保持顺序
        unique_suggestions = []
        seen = set()
        for tag in suggestions:
            if tag not in seen:
                seen.add(tag)
                unique_suggestions.append(tag)
        
        return unique_suggestions[:5]  # 最多返回5个建议
    
    def extract_content_fallback(self, file_info: Dict) -> Dict:
        """基于元数据的内容提取降级方案
        
        Args:
            file_info: 文件信息字典
            
        Returns:
            Dict: 提取的内容信息
        """
        content = {
            'type': 'unknown',
            'description': '',
            'keywords': [],
            'confidence': 0.3  # 降级方案的置信度较低
        }
        
        extension = file_info.get('extension', '').lower()
        filename = file_info.get('name', '')
        
        # 基于扩展名确定文件类型
        if extension in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.webp']:
            content['type'] = 'image'
            content['description'] = '图片文件'
        elif extension in ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv']:
            content['type'] = 'video'
            content['description'] = '视频文件'
        elif extension in ['.mp3', '.wav', '.flac', '.aac', '.ogg']:
            content['type'] = 'audio'
            content['description'] = '音频文件'
        elif extension in ['.pdf', '.doc', '.docx', '.txt', '.md', '.rtf']:
            content['type'] = 'document'
            content['description'] = '文档文件'
        elif extension in ['.py', '.js', '.html', '.css', '.java', '.cpp', '.c']:
            content['type'] = 'code'
            content['description'] = '代码文件'
        
        # 从文件名提取关键词
        keywords = []
        for keyword in self.keyword_tag_mapping.keys():
            if keyword in filename.lower():
                keywords.append(keyword)
        content['keywords'] = keywords
        
        return content
    
    def classify_file_fallback(self, file_path: str) -> List[str]:
        """基于文件路径的分类降级方案
        
        Args:
            file_path: 文件路径
            
        Returns:
            List[str]: 分类结果
        """
        categories = []
        
        # 基于文件夹名称分类
        path_parts = file_path.lower().split(os.sep)
        
        for part in path_parts:
            if 'document' in part or '文档' in part:
                categories.append('文档')
            elif 'image' in part or '图片' in part or 'photo' in part:
                categories.append('图片')
            elif 'video' in part or '视频' in part:
                categories.append('视频')
            elif 'music' in part or '音乐' in part or 'audio' in part:
                categories.append('音频')
            elif 'download' in part or '下载' in part:
                categories.append('下载')
            elif 'desktop' in part or '桌面' in part:
                categories.append('桌面')
            elif 'work' in part or '工作' in part:
                categories.append('工作')
            elif 'project' in part or '项目' in part:
                categories.append('项目')
        
        return list(set(categories))  # 去重
