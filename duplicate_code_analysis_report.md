# SmartVault 重复代码分析报告
分析时间: 2025-05-27 18:05:21
分析文件数: 80

## 📊 重复代码统计
- 总重复组数: 99
- 安全合并组数: 55
- 极低风险: 0 组
- 低风险: 55 组
- 中风险: 26 组
- 高风险: 18 组

## 🟡 低风险组 (谨慎合并)
### 组 1: identical_body
- `_describe_single_condition(self, condition_type, condition_value)` in smartvault\services\auto_tag_service.py:329
- `_describe_single_condition(self, condition_type, condition_value)` in smartvault\ui\dialogs\multi_condition_auto_tag_dialog.py:478
**风险因素**: 跨文件重复
**合并策略**: extract_common_method

### 组 2: identical_body
- `every(self, interval)` in smartvault\services\backup_service.py:24
- `hours(self)` in smartvault\services\backup_service.py:26
- `do(self, func)` in smartvault\services\backup_service.py:28
**风险因素**: 包含公共方法
**合并策略**: extract_common_method

### 组 3: identical_body
- `clear(self)` in smartvault\services\backup_service.py:30
- `run_pending(self)` in smartvault\services\backup_service.py:32
- `_handle_file_modified(self, file_path, monitor_id)` in smartvault\services\file_monitor_service.py:427
- `_handle_file_moved(self, file_path, monitor_id)` in smartvault\services\file_monitor_service.py:432
- `setup_ui(self)` in smartvault\ui\dialogs\settings\base\base_page.py:24
- `reset_to_defaults(self)` in smartvault\ui\dialogs\settings\base\base_page.py:54
- `reset_to_defaults(self)` in smartvault\ui\dialogs\settings\pages\library_page.py:149
**风险因素**: 跨文件重复, 包含公共方法
**合并策略**: extract_common_method

## 💡 合并建议
### ✅ 可以安全合并: 55 组
- 这些组风险极低，可以立即进行合并
- 建议采用提取公共方法的策略
- 合并后进行完整的功能测试