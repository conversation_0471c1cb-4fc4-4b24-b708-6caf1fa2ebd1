#!/usr/bin/env python3
"""
AI功能第二阶段测试脚本 - 系列检测功能

测试增强的文件系列检测功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from smartvault.services.ai.smart_rule_engine import SmartRuleEngine


def create_test_series_files():
    """创建测试系列文件数据"""
    
    test_series = {
        'version_series': [
            {'name': 'document_v1', 'extension': '.pdf'},
            {'name': 'document_v2', 'extension': '.pdf'},
            {'name': 'document_v3', 'extension': '.pdf'},
        ],
        'semantic_version': [
            {'name': 'app_1.0.0', 'extension': '.zip'},
            {'name': 'app_1.1.0', 'extension': '.zip'},
            {'name': 'app_2.0.0', 'extension': '.zip'},
            {'name': 'app_2.1.0-beta', 'extension': '.zip'},
        ],
        'date_series': [
            {'name': 'report_2024-01', 'extension': '.xlsx'},
            {'name': 'report_2024-02', 'extension': '.xlsx'},
            {'name': 'report_2024-03', 'extension': '.xlsx'},
        ],
        'sequence_series': [
            {'name': 'tutorial_part1', 'extension': '.mp4'},
            {'name': 'tutorial_part2', 'extension': '.mp4'},
            {'name': 'tutorial_part3', 'extension': '.mp4'},
        ],
        'chinese_sequence': [
            {'name': '教程_第1章', 'extension': '.pdf'},
            {'name': '教程_第2章', 'extension': '.pdf'},
            {'name': '教程_第3章', 'extension': '.pdf'},
        ],
        'alphabetic_series': [
            {'name': 'section_A', 'extension': '.docx'},
            {'name': 'section_B', 'extension': '.docx'},
            {'name': 'section_C', 'extension': '.docx'},
        ],
        'roman_numeral': [
            {'name': 'chapter_I', 'extension': '.pdf'},
            {'name': 'chapter_II', 'extension': '.pdf'},
            {'name': 'chapter_III', 'extension': '.pdf'},
        ],
        'revision_series': [
            {'name': 'design_rev1', 'extension': '.psd'},
            {'name': 'design_rev2', 'extension': '.psd'},
            {'name': 'design_rev3', 'extension': '.psd'},
        ],
        'draft_series': [
            {'name': 'proposal_draft1', 'extension': '.docx'},
            {'name': 'proposal_draft2', 'extension': '.docx'},
            {'name': 'proposal_draft3', 'extension': '.docx'},
        ],
        'backup_series': [
            {'name': 'database_backup1', 'extension': '.sql'},
            {'name': 'database_backup2', 'extension': '.sql'},
            {'name': 'database_backup3', 'extension': '.sql'},
        ],
        'copy_series': [
            {'name': 'image_copy1', 'extension': '.jpg'},
            {'name': 'image_copy2', 'extension': '.jpg'},
        ],
        'mixed_extensions': [  # 不同扩展名，不应该被识别为系列
            {'name': 'file_v1', 'extension': '.pdf'},
            {'name': 'file_v2', 'extension': '.docx'},
        ],
        'non_series': [  # 不是系列的文件
            {'name': 'random_file1', 'extension': '.txt'},
            {'name': 'another_document', 'extension': '.txt'},
        ]
    }
    
    return test_series


def test_series_pattern_detection():
    """测试系列模式检测"""
    print("=== 测试系列模式检测 ===")
    
    engine = SmartRuleEngine()
    engine.initialize({}, None)
    
    test_series = create_test_series_files()
    
    for series_name, files in test_series.items():
        print(f"\n📚 测试系列: {series_name}")
        print(f"文件: {[f['name'] + f['extension'] for f in files]}")
        
        # 检测系列
        series_results = engine.detect_file_series(files)
        
        if series_results:
            for result in series_results:
                print(f"✅ 检测到系列:")
                print(f"   系列名: {result['series_name']}")
                print(f"   模式类型: {result['pattern_type']}")
                print(f"   置信度: {result['confidence']:.3f}")
                print(f"   建议标签: {result['suggested_tag']}")
                print(f"   文件数: {len(result['files'])}")
        else:
            print("❌ 未检测到系列")


def test_pattern_regex_matching():
    """测试模式正则表达式匹配"""
    print("\n=== 测试模式正则表达式匹配 ===")
    
    engine = SmartRuleEngine()
    
    # 测试各种模式
    test_cases = [
        {
            'pattern_name': 'semantic_version',
            'test_names': ['app_1.0.0', 'app_1.1.0', 'app_2.0.0-beta.1'],
            'expected_matches': 3
        },
        {
            'pattern_name': 'version_pattern',
            'test_names': ['doc_v1', 'doc_v2.1', 'doc_v3.0.1-alpha'],
            'expected_matches': 3
        },
        {
            'pattern_name': 'sequence_pattern',
            'test_names': ['tutorial_part1', 'lesson_chapter2', 'book_第3章'],
            'expected_matches': 3
        },
        {
            'pattern_name': 'alphabetic_series',
            'test_names': ['section_A', 'section_B', 'section_C'],
            'expected_matches': 3
        },
        {
            'pattern_name': 'roman_numeral',
            'test_names': ['chapter_I', 'chapter_II', 'chapter_III', 'chapter_IV'],
            'expected_matches': 4
        }
    ]
    
    for test_case in test_cases:
        pattern_name = test_case['pattern_name']
        test_names = test_case['test_names']
        expected = test_case['expected_matches']
        
        # 找到对应的模式
        pattern_info = None
        for pattern in engine.series_patterns:
            if pattern['name'] == pattern_name:
                pattern_info = pattern
                break
        
        if pattern_info:
            result = engine._detect_pattern_by_regex(test_names, pattern_info)
            print(f"\n模式: {pattern_name}")
            print(f"测试文件名: {test_names}")
            print(f"检测结果: {result}")
            print(f"期望匹配数: {expected}, 实际匹配数: {result.get('match_count', 0)}")
        else:
            print(f"❌ 未找到模式: {pattern_name}")


def test_sequence_continuity():
    """测试序列连续性检查"""
    print("\n=== 测试序列连续性检查 ===")
    
    engine = SmartRuleEngine()
    
    # 连续序列
    continuous_files = [
        {'name': 'file_1', 'extension': '.txt'},
        {'name': 'file_2', 'extension': '.txt'},
        {'name': 'file_3', 'extension': '.txt'},
        {'name': 'file_4', 'extension': '.txt'},
    ]
    
    # 不连续序列
    discontinuous_files = [
        {'name': 'file_1', 'extension': '.txt'},
        {'name': 'file_3', 'extension': '.txt'},
        {'name': 'file_7', 'extension': '.txt'},
        {'name': 'file_10', 'extension': '.txt'},
    ]
    
    print("连续序列测试:")
    continuous_result = engine.detect_file_series(continuous_files)
    if continuous_result:
        print(f"  置信度: {continuous_result[0]['confidence']:.3f}")
    
    print("不连续序列测试:")
    discontinuous_result = engine.detect_file_series(discontinuous_files)
    if discontinuous_result:
        print(f"  置信度: {discontinuous_result[0]['confidence']:.3f}")


def test_common_prefix_extraction():
    """测试公共前缀提取"""
    print("\n=== 测试公共前缀提取 ===")
    
    engine = SmartRuleEngine()
    
    test_cases = [
        ['project_report_v1', 'project_report_v2', 'project_report_v3'],
        ['data_analysis_2024_01', 'data_analysis_2024_02'],
        ['tutorial_part_1', 'tutorial_part_2', 'tutorial_part_3'],
        ['file', 'document'],  # 无公共前缀
        ['same_name'],  # 单个文件
    ]
    
    for names in test_cases:
        prefix = engine._extract_common_prefix(names)
        print(f"文件名: {names}")
        print(f"公共前缀: '{prefix}'")
        print()


def test_enhanced_similarity_check():
    """测试增强的相似性检查"""
    print("\n=== 测试增强的相似性检查 ===")
    
    engine = SmartRuleEngine()
    
    test_pairs = [
        # 应该相似的文件对
        ({'name': 'document_v1', 'extension': '.pdf'}, {'name': 'document_v2', 'extension': '.pdf'}),
        ({'name': 'report_2024_01', 'extension': '.xlsx'}, {'name': 'report_2024_02', 'extension': '.xlsx'}),
        ({'name': 'chapter_I', 'extension': '.pdf'}, {'name': 'chapter_II', 'extension': '.pdf'}),
        
        # 不应该相似的文件对
        ({'name': 'document_v1', 'extension': '.pdf'}, {'name': 'report_v1', 'extension': '.pdf'}),
        ({'name': 'file1', 'extension': '.txt'}, {'name': 'file1', 'extension': '.pdf'}),  # 不同扩展名
        ({'name': 'random_file', 'extension': '.txt'}, {'name': 'another_file', 'extension': '.txt'}),
    ]
    
    for file1, file2 in test_pairs:
        similar = engine._are_files_similar(file1, file2)
        print(f"{file1['name']}{file1['extension']} vs {file2['name']}{file2['extension']}: {'相似' if similar else '不相似'}")


def main():
    """主测试函数"""
    print("SmartVault AI功能第二阶段测试 - 系列检测")
    print("=" * 60)
    
    try:
        test_pattern_regex_matching()
        test_series_pattern_detection()
        test_sequence_continuity()
        test_common_prefix_extraction()
        test_enhanced_similarity_check()
        
        print("\n" + "=" * 60)
        print("✅ 系列检测功能测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
