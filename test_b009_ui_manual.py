#!/usr/bin/env python3
"""
B009高级搜索UI手动测试
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from smartvault.ui.dialogs.advanced_search_dialog import AdvancedSearchDialog


class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("B009高级搜索UI测试")
        self.resize(400, 200)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 添加测试按钮
        test_button = QPushButton("打开高级搜索对话框")
        test_button.clicked.connect(self.open_advanced_search)
        layout.addWidget(test_button)
    
    def open_advanced_search(self):
        """打开高级搜索对话框"""
        print("\n🔧 打开高级搜索对话框...")
        
        try:
            dialog = AdvancedSearchDialog(self)
            
            # 连接搜索完成信号
            dialog.search_completed.connect(self.on_search_completed)
            
            print("   ✅ 高级搜索对话框已打开")
            print("   📋 请在对话框中测试以下功能：")
            print("      1. 基本搜索条件设置")
            print("      2. 高级条件设置（大小、日期、入库方式）")
            print("      3. 文件类型选择（包括自定义类型）")
            print("      4. 搜索选项（区分大小写、正则表达式）")
            print("      5. 搜索预览功能")
            print("      6. 完整搜索功能")
            print("      7. 清除条件功能")
            print("      8. 结果表格显示")
            
            result = dialog.exec()
            
            if result == AdvancedSearchDialog.Accepted:
                print("   ✅ 搜索已确认")
            else:
                print("   ❌ 搜索已取消")
                
        except Exception as e:
            print(f"   ❌ 打开高级搜索对话框失败: {e}")
            import traceback
            traceback.print_exc()
    
    def on_search_completed(self, results):
        """搜索完成回调"""
        print(f"\n🔍 搜索完成，找到 {len(results)} 个结果")
        
        if results:
            print("   📋 搜索结果示例：")
            for i, file_info in enumerate(results[:5]):  # 显示前5个结果
                print(f"      {i+1}. {file_info['name']} ({file_info['size']} 字节)")
            
            if len(results) > 5:
                print(f"      ... 还有 {len(results) - 5} 个结果")
        else:
            print("   📭 没有找到匹配的文件")


def main():
    """主函数"""
    print("🚀 启动B009高级搜索UI手动测试...")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    print("📋 测试说明：")
    print("   1. 程序将创建一个测试窗口")
    print("   2. 点击按钮打开高级搜索对话框")
    print("   3. 在对话框中测试各种搜索功能")
    print("   4. 验证搜索结果的正确性")
    print()
    print("🎯 重点测试项目：")
    print("   • 搜索条件设置的完整性")
    print("   • 搜索预览功能的准确性")
    print("   • 搜索结果表格的显示")
    print("   • 复杂搜索条件的组合")
    print("   • UI界面的响应性和易用性")
    print()
    
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestMainWindow()
    window.show()
    
    print("✅ 测试窗口已显示，请点击按钮开始测试")
    
    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
