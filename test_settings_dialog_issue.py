#!/usr/bin/env python3
"""
SmartVault 设置对话框问题重现测试
重现用户报告的问题：创建新文件库后，设置对话框显示的路径不正确
"""

import sys
import os
import tempfile
import shutil
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from smartvault.utils.config import load_config, save_config
from smartvault.ui.dialogs.settings_dialog import SettingsDialog
from smartvault.ui.main_window.core import MainWindowCore


class SettingsDialogIssueTester:
    """设置对话框问题测试器"""
    
    def __init__(self):
        self.test_results = []
        self.temp_dirs = []
        self.original_config = load_config()
        
    def log_test(self, test_name, success, details=""):
        """记录测试结果"""
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{status} {test_name}")
        if details:
            print(f"   详情: {details}")
        self.test_results.append({
            "name": test_name,
            "success": success,
            "details": details
        })
    
    def test_settings_dialog_path_display(self):
        """测试设置对话框路径显示问题"""
        print("\n🔍 重现设置对话框路径显示问题...")
        
        try:
            # 步骤1: 记录当前配置
            config_before = load_config()
            original_path = config_before["library_path"]
            print(f"   原始文件库路径: {original_path}")
            
            # 步骤2: 创建主窗口（模拟真实环境）
            main_window = MainWindowCore()
            
            # 步骤3: 创建设置对话框
            settings_dialog = SettingsDialog(main_window)
            
            # 步骤4: 检查设置对话框初始显示的路径
            initial_display_path = settings_dialog.path_label.text()
            print(f"   设置对话框初始显示路径: {initial_display_path}")
            
            # 步骤5: 模拟创建新文件库操作
            temp_dir = tempfile.mkdtemp(prefix="smartvault_settings_test_")
            self.temp_dirs.append(temp_dir)
            
            # 直接调用文件系统创建文件库（模拟设置对话框中的操作）
            success, new_library_path, details = settings_dialog.file_system.create_library(temp_dir)
            
            if not success:
                self.log_test("创建新文件库", False, details)
                return
            
            print(f"   新创建的文件库路径: {new_library_path}")
            
            # 步骤6: 模拟设置对话框中的配置更新逻辑
            settings_dialog.config["library_path"] = new_library_path
            save_config(settings_dialog.config)
            
            # 步骤7: 模拟UI更新
            settings_dialog.path_label.setText(new_library_path)
            
            # 步骤8: 模拟通知主窗口
            if hasattr(main_window, 'on_library_changed'):
                main_window.on_library_changed(new_library_path)
            
            # 步骤9: 检查配置文件是否正确更新
            config_after_create = load_config()
            config_updated = config_after_create["library_path"] == new_library_path
            
            self.log_test(
                "创建文件库后配置正确更新",
                config_updated,
                f"期望: {new_library_path}, 实际: {config_after_create['library_path']}"
            )
            
            # 步骤10: 模拟重新打开设置对话框
            settings_dialog2 = SettingsDialog(main_window)
            reopened_display_path = settings_dialog2.path_label.text()
            print(f"   重新打开设置对话框显示路径: {reopened_display_path}")
            
            # 步骤11: 检查重新打开后的路径显示是否正确
            # 标准化路径进行比较
            expected_normalized = os.path.normpath(new_library_path).replace('\\', '/')
            actual_normalized = os.path.normpath(reopened_display_path).replace('\\', '/')
            
            path_display_correct = expected_normalized == actual_normalized
            
            self.log_test(
                "重新打开设置对话框路径显示正确",
                path_display_correct,
                f"期望: {expected_normalized}, 实际: {actual_normalized}"
            )
            
            # 步骤12: 检查配置文件与显示是否一致
            config_final = load_config()
            config_display_consistent = (
                os.path.normpath(config_final["library_path"]).replace('\\', '/') == 
                actual_normalized
            )
            
            self.log_test(
                "配置文件与界面显示一致",
                config_display_consistent,
                f"配置: {config_final['library_path']}, 显示: {reopened_display_path}"
            )
            
            # 清理
            main_window.close()
            
        except Exception as e:
            self.log_test("设置对话框路径显示测试", False, str(e))
            import traceback
            traceback.print_exc()
    
    def test_config_reload_in_settings_dialog(self):
        """测试设置对话框中的配置重新加载"""
        print("\n🔍 测试设置对话框配置重新加载...")
        
        try:
            # 创建测试文件库
            temp_dir = tempfile.mkdtemp(prefix="smartvault_reload_test_")
            self.temp_dirs.append(temp_dir)
            
            from smartvault.data.file_system import FileSystem
            file_system = FileSystem()
            success, test_library_path, details = file_system.create_library(temp_dir)
            
            if not success:
                self.log_test("创建测试文件库", False, details)
                return
            
            # 手动更新配置文件
            config = load_config()
            config["library_path"] = test_library_path
            save_config(config)
            
            # 创建设置对话框
            main_window = MainWindowCore()
            settings_dialog = SettingsDialog(main_window)
            
            # 检查设置对话框是否正确加载了新配置
            displayed_path = settings_dialog.path_label.text()
            expected_normalized = os.path.normpath(test_library_path).replace('\\', '/')
            actual_normalized = os.path.normpath(displayed_path).replace('\\', '/')
            
            config_loaded_correctly = expected_normalized == actual_normalized
            
            self.log_test(
                "设置对话框正确加载配置",
                config_loaded_correctly,
                f"期望: {expected_normalized}, 实际: {actual_normalized}"
            )
            
            # 清理
            main_window.close()
            
        except Exception as e:
            self.log_test("配置重新加载测试", False, str(e))
    
    def cleanup(self):
        """清理测试环境"""
        print("\n🧹 清理测试环境...")
        
        # 恢复原始配置
        try:
            save_config(self.original_config)
            print("   ✅ 原始配置已恢复")
        except Exception as e:
            print(f"   ⚠️  恢复配置失败: {e}")
        
        # 删除临时目录
        for temp_dir in self.temp_dirs:
            try:
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
                    print(f"   ✅ 已删除临时目录: {temp_dir}")
            except Exception as e:
                print(f"   ⚠️  删除临时目录失败: {temp_dir} - {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始设置对话框问题重现测试")
        print("=" * 60)
        print("目标：重现用户报告的问题 - 创建新文件库后设置对话框显示路径不正确")
        
        try:
            # 运行测试
            self.test_settings_dialog_path_display()
            self.test_config_reload_in_settings_dialog()
            
        finally:
            # 清理测试环境
            self.cleanup()
        
        # 输出测试结果
        print("\n" + "=" * 60)
        print("📊 测试结果汇总")
        print("=" * 60)
        
        passed = sum(1 for result in self.test_results if result["success"])
        total = len(self.test_results)
        
        print(f"总测试数: {total}")
        print(f"通过数: {passed}")
        print(f"失败数: {total - passed}")
        print(f"通过率: {passed/total*100:.1f}%")
        
        if passed == total:
            print("\n🎉 所有测试通过！设置对话框工作正常。")
            print("如果用户仍然遇到问题，可能是其他原因导致的。")
            return True
        else:
            print("\n⚠️  重现了用户报告的问题！")
            print("\n失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  ❌ {result['name']}: {result['details']}")
            return False


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    try:
        tester = SettingsDialogIssueTester()
        success = tester.run_all_tests()
        
        # 自动退出
        QTimer.singleShot(1000, app.quit)
        app.exec()
        
        return 0 if success else 1
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
