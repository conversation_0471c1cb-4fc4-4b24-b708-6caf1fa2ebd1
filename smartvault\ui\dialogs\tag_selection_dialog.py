"""
标签选择对话框

用于在自动标签规则中选择现有标签
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, 
    QLabel, QTreeWidget, QTreeWidgetItem, QPushButton,
    QDialogButtonBox, QCheckBox, QLineEdit
)
from PySide6.QtCore import Qt
from smartvault.services.tag_service import TagService


class TagSelectionDialog(QDialog):
    """标签选择对话框"""

    def __init__(self, parent=None, selected_tags=None):
        """初始化标签选择对话框
        
        Args:
            parent: 父窗口
            selected_tags: 已选择的标签名称列表
        """
        super().__init__(parent)
        self.setWindowTitle("选择标签")
        self.setModal(True)
        self.resize(400, 500)
        
        # 初始化服务
        self.tag_service = TagService()
        
        # 已选择的标签
        self.selected_tags = selected_tags or []
        self.selected_tag_items = []
        
        # 初始化UI
        self.init_ui()
        
        # 加载标签
        self.load_tags()

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 说明标签
        info_label = QLabel("请选择要添加的标签：")
        layout.addWidget(info_label)
        
        # 搜索框
        search_layout = QHBoxLayout()
        search_label = QLabel("搜索：")
        search_layout.addWidget(search_label)
        
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入标签名称进行搜索...")
        self.search_edit.textChanged.connect(self.filter_tags)
        search_layout.addWidget(self.search_edit)
        
        layout.addLayout(search_layout)
        
        # 标签树
        self.tag_tree = QTreeWidget()
        self.tag_tree.setHeaderLabels(["标签名称", "文件数"])
        self.tag_tree.setRootIsDecorated(True)
        self.tag_tree.itemChanged.connect(self.on_item_changed)
        layout.addWidget(self.tag_tree)
        
        # 选择操作按钮
        button_layout = QHBoxLayout()
        
        self.select_all_btn = QPushButton("全选")
        self.select_all_btn.clicked.connect(self.select_all)
        button_layout.addWidget(self.select_all_btn)
        
        self.clear_all_btn = QPushButton("清除")
        self.clear_all_btn.clicked.connect(self.clear_all)
        button_layout.addWidget(self.clear_all_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # 对话框按钮
        buttons = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)

    def load_tags(self):
        """加载标签树"""
        try:
            # 获取标签树结构
            tag_tree = self.tag_service.get_tag_tree()
            
            # 清空现有项
            self.tag_tree.clear()
            
            # 添加标签项
            for tag in tag_tree:
                self.add_tag_item(None, tag)
            
            # 展开所有项
            self.tag_tree.expandAll()
            
        except Exception as e:
            print(f"加载标签失败: {e}")

    def add_tag_item(self, parent_item, tag_data):
        """添加标签项到树中
        
        Args:
            parent_item: 父项，None表示根项
            tag_data: 标签数据
        """
        # 创建项
        if parent_item is None:
            item = QTreeWidgetItem(self.tag_tree)
        else:
            item = QTreeWidgetItem(parent_item)
        
        # 设置文本
        item.setText(0, tag_data["name"])
        item.setText(1, str(tag_data.get("file_count", 0)))
        
        # 设置数据
        item.setData(0, Qt.ItemDataRole.UserRole, tag_data)
        
        # 设置复选框
        item.setFlags(item.flags() | Qt.ItemFlag.ItemIsUserCheckable)
        
        # 检查是否已选择
        if tag_data["name"] in self.selected_tags:
            item.setCheckState(0, Qt.CheckState.Checked)
            self.selected_tag_items.append(item)
        else:
            item.setCheckState(0, Qt.CheckState.Unchecked)
        
        # 添加子标签
        for child_tag in tag_data.get("children", []):
            self.add_tag_item(item, child_tag)

    def filter_tags(self, text):
        """过滤标签
        
        Args:
            text: 搜索文本
        """
        def filter_item(item):
            """递归过滤项"""
            tag_data = item.data(0, Qt.ItemDataRole.UserRole)
            if not tag_data:
                return False
            
            # 检查当前项是否匹配
            matches = text.lower() in tag_data["name"].lower()
            
            # 检查子项
            child_matches = False
            for i in range(item.childCount()):
                child_item = item.child(i)
                if filter_item(child_item):
                    child_matches = True
            
            # 显示/隐藏项
            visible = matches or child_matches
            item.setHidden(not visible)
            
            return visible
        
        # 过滤所有根项
        for i in range(self.tag_tree.topLevelItemCount()):
            root_item = self.tag_tree.topLevelItem(i)
            filter_item(root_item)

    def on_item_changed(self, item, column):
        """项状态改变时的处理
        
        Args:
            item: 改变的项
            column: 列索引
        """
        if column != 0:  # 只处理第一列的复选框
            return
        
        tag_data = item.data(0, Qt.ItemDataRole.UserRole)
        if not tag_data:
            return
        
        # 更新选择状态
        if item.checkState(0) == Qt.CheckState.Checked:
            if item not in self.selected_tag_items:
                self.selected_tag_items.append(item)
        else:
            if item in self.selected_tag_items:
                self.selected_tag_items.remove(item)

    def select_all(self):
        """全选所有可见标签"""
        def select_item(item):
            if not item.isHidden():
                item.setCheckState(0, Qt.CheckState.Checked)
            for i in range(item.childCount()):
                select_item(item.child(i))
        
        for i in range(self.tag_tree.topLevelItemCount()):
            select_item(self.tag_tree.topLevelItem(i))

    def clear_all(self):
        """清除所有选择"""
        def clear_item(item):
            item.setCheckState(0, Qt.CheckState.Unchecked)
            for i in range(item.childCount()):
                clear_item(item.child(i))
        
        for i in range(self.tag_tree.topLevelItemCount()):
            clear_item(self.tag_tree.topLevelItem(i))

    def get_selected_tags(self):
        """获取选择的标签
        
        Returns:
            list: 选择的标签数据列表
        """
        selected_tags = []
        
        def collect_selected(item):
            if item.checkState(0) == Qt.CheckState.Checked:
                tag_data = item.data(0, Qt.ItemDataRole.UserRole)
                if tag_data:
                    selected_tags.append(tag_data)
            
            for i in range(item.childCount()):
                collect_selected(item.child(i))
        
        for i in range(self.tag_tree.topLevelItemCount()):
            collect_selected(self.tag_tree.topLevelItem(i))
        
        return selected_tags
