#!/usr/bin/env python3
"""
标签功能端到端测试
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import unittest
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from smartvault.data.database import Database
from smartvault.services.tag_service import TagService
from smartvault.services.file.core import FileServiceCore
from smartvault.ui.models.file_table_model import FileTableModel

class TestTagsEndToEnd(unittest.TestCase):
    """标签功能端到端测试"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """设置测试"""
        # 创建内存数据库进行测试
        self.db = Database(":memory:")
        self.tag_service = TagService()
        self.tag_service._db = self.db  # 注入测试数据库
        
        self.file_service = FileServiceCore()
        self.file_service._db = self.db  # 注入测试数据库
        
        # 创建文件表格模型
        self.model = FileTableModel()
        self.model.set_tag_service(self.tag_service)
        
        # 创建测试数据
        self._create_test_data()
    
    def _create_test_data(self):
        """创建测试数据"""
        # 创建测试文件
        cursor = self.db.conn.cursor()
        
        # 插入测试文件
        test_files = [
            ("file1", "document.pdf", "/path/to/document.pdf", 1024, "2023-01-01T00:00:00", "2023-01-01T00:00:00", "2023-01-01T00:00:00", "link"),
            ("file2", "code.py", "/path/to/code.py", 2048, "2023-01-02T00:00:00", "2023-01-02T00:00:00", "2023-01-02T00:00:00", "link"),
            ("file3", "image.jpg", "/path/to/image.jpg", 4096, "2023-01-03T00:00:00", "2023-01-03T00:00:00", "2023-01-03T00:00:00", "link"),
        ]
        
        for file_data in test_files:
            cursor.execute(
                "INSERT INTO files (id, name, original_path, size, created_at, modified_at, added_at, entry_type) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
                file_data
            )
        
        self.db.conn.commit()
        
        # 创建测试标签
        self.work_tag_id = self.tag_service.create_tag("工作", "#FF5722")
        self.personal_tag_id = self.tag_service.create_tag("个人", "#2196F3")
        self.doc_tag_id = self.tag_service.create_tag("文档", "#FFC107", self.work_tag_id)
        self.code_tag_id = self.tag_service.create_tag("代码", "#9C27B0", self.work_tag_id)
        
        # 为文件添加标签
        self.tag_service.add_tag_to_file("file1", self.doc_tag_id)
        self.tag_service.add_tag_to_file("file1", self.work_tag_id)
        self.tag_service.add_tag_to_file("file2", self.code_tag_id)
        self.tag_service.add_tag_to_file("file2", self.work_tag_id)
        self.tag_service.add_tag_to_file("file3", self.personal_tag_id)
    
    def test_complete_workflow(self):
        """测试完整的标签工作流程"""
        print("开始端到端测试...")
        
        # 1. 加载文件到模型
        files = [
            {"id": "file1", "name": "document.pdf", "original_path": "/path/to/document.pdf", "size": 1024, "added_at": "2023-01-01T00:00:00", "entry_type": "link"},
            {"id": "file2", "name": "code.py", "original_path": "/path/to/code.py", "size": 2048, "added_at": "2023-01-02T00:00:00", "entry_type": "link"},
            {"id": "file3", "name": "image.jpg", "original_path": "/path/to/image.jpg", "size": 4096, "added_at": "2023-01-03T00:00:00", "entry_type": "link"},
        ]
        
        self.model.setFiles(files)
        
        # 2. 验证标签列存在
        self.assertEqual(self.model.columnCount(), 6, "应该有6列（包括标签列）")
        self.assertEqual(self.model.COLUMNS[self.model.TAGS_COLUMN], "标签", "标签列名称应该正确")
        
        # 3. 验证文件标签显示
        # 文件1应该显示"文档 • 工作"
        index1 = self.model.index(0, self.model.TAGS_COLUMN)
        tags_display1 = self.model.data(index1, Qt.DisplayRole)
        self.assertIn("文档", tags_display1, "文件1应该显示文档标签")
        self.assertIn("工作", tags_display1, "文件1应该显示工作标签")
        self.assertIn(" • ", tags_display1, "应该使用圆点分隔符")
        
        # 文件2应该显示"代码 • 工作"
        index2 = self.model.index(1, self.model.TAGS_COLUMN)
        tags_display2 = self.model.data(index2, Qt.DisplayRole)
        self.assertIn("代码", tags_display2, "文件2应该显示代码标签")
        self.assertIn("工作", tags_display2, "文件2应该显示工作标签")
        
        # 文件3应该显示"个人"
        index3 = self.model.index(2, self.model.TAGS_COLUMN)
        tags_display3 = self.model.data(index3, Qt.DisplayRole)
        self.assertEqual(tags_display3, "个人", "文件3应该只显示个人标签")
        
        # 4. 验证工具提示
        tooltip1 = self.model.data(index1, Qt.ToolTipRole)
        self.assertIn("文件标签:", tooltip1, "工具提示应该包含标签信息")
        self.assertIn("文档", tooltip1, "工具提示应该包含文档标签")
        self.assertIn("工作", tooltip1, "工具提示应该包含工作标签")
        
        # 5. 验证标签颜色
        foreground1 = self.model.data(index1, Qt.ForegroundRole)
        self.assertIsNotNone(foreground1, "标签列应该有特殊的前景色")
        
        # 6. 测试标签管理功能
        # 添加新标签
        new_tag_id = self.tag_service.create_tag("重要", "#F44336")
        self.tag_service.add_tag_to_file("file1", new_tag_id)
        
        # 刷新标签缓存
        self.model.refresh_file_tags(["file1"])
        
        # 验证新标签显示
        updated_tags_display = self.model.data(index1, Qt.DisplayRole)
        self.assertIn("重要", updated_tags_display, "应该显示新添加的重要标签")
        
        # 7. 测试标签移除
        self.tag_service.remove_tag_from_file("file1", new_tag_id)
        self.model.refresh_file_tags(["file1"])
        
        # 验证标签被移除
        final_tags_display = self.model.data(index1, Qt.DisplayRole)
        self.assertNotIn("重要", final_tags_display, "重要标签应该被移除")
        
        print("端到端测试完成！")
    
    def test_performance_with_many_files(self):
        """测试大量文件的性能"""
        print("开始性能测试...")
        
        # 创建大量文件数据
        many_files = []
        for i in range(100):
            file_data = {
                "id": f"perf_file{i}",
                "name": f"file{i}.txt",
                "original_path": f"/path/to/file{i}.txt",
                "size": 1024 * (i + 1),
                "added_at": "2023-01-01T00:00:00",
                "entry_type": "link"
            }
            many_files.append(file_data)
        
        # 加载到模型
        import time
        start_time = time.time()
        self.model.setFiles(many_files)
        load_time = time.time() - start_time
        
        # 验证加载时间合理（应该在1秒内）
        self.assertLess(load_time, 1.0, f"加载100个文件应该在1秒内完成，实际用时: {load_time:.2f}秒")
        
        # 验证缓存工作正常
        self.assertLessEqual(len(self.model.file_tags_cache), 
                           self.model.tags_cache_size_limit,
                           "缓存大小应该被限制")
        
        print(f"性能测试完成！加载100个文件用时: {load_time:.2f}秒")
    
    def tearDown(self):
        """清理测试"""
        if hasattr(self, 'db'):
            self.db.close()

def run_tests():
    """运行测试"""
    print("开始标签功能端到端测试...")
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestTagsEndToEnd)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    if result.wasSuccessful():
        print("\n✅ 所有端到端测试通过！")
        return True
    else:
        print(f"\n❌ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        return False

if __name__ == "__main__":
    run_tests()
