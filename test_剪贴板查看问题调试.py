#!/usr/bin/env python3
"""
剪贴板查看问题调试
专门调试悬浮窗点击查看功能的问题
"""

import sys
import os
sys.path.append('.')

def test_clipboard_view_issue():
    """测试剪贴板查看问题"""
    print("=" * 60)
    print("🔍 剪贴板查看问题调试")
    print("=" * 60)
    
    try:
        from PySide6.QtWidgets import QApplication
        from smartvault.ui.main_window import MainWindow
        from smartvault.services.file import FileService
        
        # 创建Qt应用
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("✅ Qt应用创建成功")
        
        # 创建主窗口
        print("🪟 创建主窗口...")
        main_window = MainWindow()
        print("✅ 主窗口创建成功")
        
        # 获取一些真实文件数据
        file_service = FileService()
        files = file_service.get_files(limit=5)
        
        if not files:
            print("❌ 数据库中没有文件，无法测试")
            return False
        
        test_file = files[0]
        print(f"📄 测试文件: {test_file['name']} (ID: {test_file['id']})")
        
        # 检查文件视图状态
        print("\n📊 检查文件视图状态:")
        print(f"   文件视图存在: {hasattr(main_window, 'file_view')}")
        if hasattr(main_window, 'file_view'):
            print(f"   文件视图类型: {type(main_window.file_view).__name__}")
            
            # 检查当前模型和视图
            model = main_window.file_view.get_current_model()
            current_view = main_window.file_view.get_current_view()
            
            print(f"   当前模型: {type(model).__name__ if model else 'None'}")
            print(f"   当前视图: {type(current_view).__name__ if current_view else 'None'}")
            
            if model:
                print(f"   模型行数: {model.rowCount()}")
                
                # 检查前几行的数据
                print("   前5行数据:")
                for row in range(min(5, model.rowCount())):
                    index = model.index(row, 0)
                    file_id = model.data(index, 256)  # Qt.UserRole
                    file_name = model.data(index, 0)  # DisplayRole
                    print(f"     行{row}: ID={file_id}, 名称={file_name}")
        
        # 创建测试重复文件信息
        duplicate_info = {
            'type': 'file',
            'source_text': test_file['name'],
            'cleaned_name': test_file['name'],
            'duplicates': [
                {
                    'id': test_file['id'],
                    'name': test_file['name'],
                    'original_path': test_file.get('original_path', ''),
                    'library_path': test_file.get('library_path', ''),
                    'entry_type': test_file.get('entry_type', 'file')
                }
            ]
        }
        
        print(f"\n🪟 显示浮动窗口...")
        main_window.clipboard_floating_widget.show_duplicate(duplicate_info)
        print("✅ 浮动窗口显示成功")
        
        # 检查浮动窗口状态
        current_info = main_window.clipboard_floating_widget.current_duplicate_info
        if current_info:
            print("✅ 浮动窗口保存了重复文件信息")
            print(f"   文件ID: {current_info['duplicates'][0]['id']}")
        else:
            print("❌ 浮动窗口未保存重复文件信息")
            return False
        
        # 检查查看按钮状态
        view_button = main_window.clipboard_floating_widget.view_button
        print(f"\n🔘 查看按钮状态:")
        print(f"   按钮存在: {view_button is not None}")
        print(f"   按钮可见: {view_button.isVisible() if view_button else 'N/A'}")
        print(f"   按钮启用: {view_button.isEnabled() if view_button else 'N/A'}")
        
        # 测试点击查看按钮
        print(f"\n🖱️ 测试点击查看按钮...")
        
        # 直接调用点击处理方法
        print("📡 调用 on_view_clicked()...")
        main_window.clipboard_floating_widget.on_view_clicked()
        
        # 等待一下让事件处理完成
        app.processEvents()
        
        print("✅ 查看按钮点击处理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_file_location():
    """直接测试文件定位功能"""
    print("\n" + "=" * 60)
    print("🎯 直接测试文件定位功能")
    print("=" * 60)
    
    try:
        from PySide6.QtWidgets import QApplication
        from smartvault.ui.main_window import MainWindow
        from smartvault.services.file import FileService
        
        # 创建Qt应用
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = MainWindow()
        
        # 获取测试文件
        file_service = FileService()
        files = file_service.get_files(limit=1)
        
        if not files:
            print("❌ 数据库中没有文件，无法测试")
            return False
        
        test_file = files[0]
        print(f"📄 测试文件: {test_file['name']} (ID: {test_file['id']})")
        
        # 直接调用文件定位方法
        print("🎯 直接调用文件定位方法...")
        clipboard_handler = main_window.clipboard_handler
        clipboard_handler._locate_and_select_file(test_file['id'])
        
        print("✅ 文件定位方法调用完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始剪贴板查看问题调试")
    
    test_results = []
    
    # 执行测试
    test_results.append(("剪贴板查看问题调试", test_clipboard_view_issue()))
    test_results.append(("直接文件定位测试", test_direct_file_location()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n总计: {passed} 个测试通过, {failed} 个测试失败")
    
    if failed == 0:
        print("🎉 所有测试通过！")
        return True
    else:
        print("⚠️ 存在问题，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
