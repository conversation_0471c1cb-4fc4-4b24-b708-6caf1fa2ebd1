"""
简单启动脚本 - 绕过所有检查
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """简单启动"""
    print("🚀 SmartVault 简单启动")
    
    try:
        from PySide6.QtWidgets import QApplication
        from smartvault.ui.main_window import MainWindow
        from smartvault.ui.resources import get_icon
        
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("SmartVault")
        app.setOrganizationName("SmartVault")
        app.setApplicationVersion("1.0.0")
        print("✅ 应用程序实例已创建")
        
        # 设置应用图标
        try:
            app.setWindowIcon(get_icon("app"))
        except Exception as e:
            print(f"⚠️  设置应用图标失败: {e}")
        
        # 创建主窗口
        print("🏗️  创建主窗口...")
        window = MainWindow()
        print("✅ 主窗口已创建")
        
        # 显示窗口
        window.show()
        print("✅ 主窗口已显示")
        
        # 运行事件循环
        print("🔄 进入事件循环")
        exit_code = app.exec()
        
        # 清理资源
        print("🧹 清理资源...")
        if window:
            window.close()
            window = None
        
        app.quit()
        print("✅ 程序正常退出")
        
        return exit_code
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
