#!/usr/bin/env python3
"""
测试监控设置对话框功能
"""

import sys
import os
import tempfile
import shutil
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication
from smartvault.ui.dialogs.settings_dialog import SettingsDialog, MonitorConfigDialog
from smartvault.services.file_monitor_service import FileMonitorService


def test_monitor_config_dialog():
    """测试监控配置对话框"""
    print("\n🧪 测试监控配置对话框...")
    
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    # 创建临时测试目录
    temp_dir = tempfile.mkdtemp(prefix="smartvault_monitor_test_")
    print(f"   📁 创建测试目录: {temp_dir}")
    
    try:
        # 测试添加模式
        print("\n   1️⃣ 测试添加模式...")
        dialog = MonitorConfigDialog()
        dialog.path_edit.setText(temp_dir)
        dialog.mode_combo.setCurrentText("copy")
        dialog.patterns_edit.setPlainText("*.txt\n*.pdf")
        dialog.auto_add_check.setChecked(True)
        dialog.recursive_check.setChecked(False)
        
        config = dialog.get_config()
        print(f"   ✅ 配置获取成功: {config}")
        
        assert config["folder_path"] == temp_dir
        assert config["entry_mode"] == "copy"
        assert config["file_patterns"] == ["*.txt", "*.pdf"]
        assert config["auto_add"] is True
        assert config["recursive"] is False
        
        # 测试编辑模式
        print("\n   2️⃣ 测试编辑模式...")
        existing_config = {
            "folder_path": temp_dir,
            "entry_mode": "link",
            "file_patterns": ["*.doc"],
            "auto_add": False,
            "recursive": True
        }
        
        edit_dialog = MonitorConfigDialog(config=existing_config)
        edit_config = edit_dialog.get_config()
        print(f"   ✅ 编辑配置获取成功: {edit_config}")
        
        assert edit_config["folder_path"] == temp_dir
        assert edit_config["entry_mode"] == "link"
        assert edit_config["file_patterns"] == ["*.doc"]
        assert edit_config["auto_add"] is False
        assert edit_config["recursive"] is True
        
        print("   ✅ 监控配置对话框测试通过")
        
    finally:
        # 清理测试目录
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
            print(f"   🧹 清理测试目录: {temp_dir}")


def test_monitor_service_integration():
    """测试监控服务集成"""
    print("\n🧪 测试监控服务集成...")
    
    # 创建临时测试目录
    temp_dir = tempfile.mkdtemp(prefix="smartvault_monitor_service_test_")
    print(f"   📁 创建测试目录: {temp_dir}")
    
    try:
        # 创建监控服务
        monitor_service = FileMonitorService()
        
        # 添加监控文件夹
        print("\n   1️⃣ 添加监控文件夹...")
        monitor_id = monitor_service.add_monitor_folder(
            folder_path=temp_dir,
            entry_mode="link",
            file_patterns=["*.txt", "*.pdf"],
            auto_add=True,
            recursive=True
        )
        print(f"   ✅ 监控ID: {monitor_id}")
        
        # 获取所有监控配置
        print("\n   2️⃣ 获取所有监控配置...")
        configs = monitor_service.get_all_monitors()
        print(f"   ✅ 找到 {len(configs)} 个监控配置")
        
        assert len(configs) >= 1
        config = next((c for c in configs if c["id"] == monitor_id), None)
        assert config is not None
        assert config["folder_path"] == temp_dir
        assert config["entry_mode"] == "link"
        assert config["file_patterns"] == ["*.txt", "*.pdf"]
        
        # 更新监控配置
        print("\n   3️⃣ 更新监控配置...")
        success = monitor_service.update_monitor_config(
            monitor_id,
            entry_mode="copy",
            auto_add=False
        )
        assert success is True
        
        # 验证更新
        updated_config = monitor_service.get_monitor_config(monitor_id)
        assert updated_config["entry_mode"] == "copy"
        assert updated_config["auto_add"] is False
        print("   ✅ 配置更新成功")
        
        # 启动监控
        print("\n   4️⃣ 启动监控...")
        success = monitor_service.start_monitoring(monitor_id)
        assert success is True
        
        # 检查状态
        status = monitor_service.get_monitor_status(monitor_id)
        assert status["is_active"] is True
        print("   ✅ 监控启动成功")
        
        # 停止监控
        print("\n   5️⃣ 停止监控...")
        success = monitor_service.stop_monitoring(monitor_id)
        assert success is True
        
        status = monitor_service.get_monitor_status(monitor_id)
        assert status["is_active"] is False
        print("   ✅ 监控停止成功")
        
        # 删除监控
        print("\n   6️⃣ 删除监控...")
        success = monitor_service.remove_monitor_folder(monitor_id)
        assert success is True
        
        # 验证删除
        remaining_configs = monitor_service.get_all_monitors()
        remaining_config = next((c for c in remaining_configs if c["id"] == monitor_id), None)
        assert remaining_config is None
        print("   ✅ 监控删除成功")
        
        print("   ✅ 监控服务集成测试通过")
        
    finally:
        # 清理
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
            print(f"   🧹 清理测试目录: {temp_dir}")


def main():
    """主测试函数"""
    print("🚀 开始测试监控设置对话框功能...")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 测试监控配置对话框
        test_monitor_config_dialog()
        
        # 测试监控服务集成
        test_monitor_service_integration()
        
        print("\n🎉 所有测试通过！")
        print("✅ 监控设置对话框功能正常")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
