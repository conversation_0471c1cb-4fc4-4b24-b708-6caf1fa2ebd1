"""
文件详情视图
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QScrollArea, 
    QFrame, QSplitter, QListWidget, QListWidgetItem, QTextEdit
)
from PySide6.QtCore import Qt, Signal, QSize, QFileInfo, QDateTime
from PySide6.QtGui import QPixmap, QFont, QPalette
from smartvault.ui.resources import get_icon
import os
from datetime import datetime


class FileDetailsView(QWidget):
    """文件详情视图"""
    
    # 自定义信号
    file_activated = Signal(str)  # 文件激活信号
    file_selected = Signal(str)   # 文件选中信号
    
    def __init__(self, parent=None):
        """初始化视图
        
        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        
        self.files = []
        self.current_file = None
        
        # 创建主布局
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        # 创建主布局
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：文件列表
        self.create_file_list()
        splitter.addWidget(self.file_list_widget)
        
        # 右侧：文件详情
        self.create_details_panel()
        splitter.addWidget(self.details_widget)
        
        # 设置分割器比例 (30% : 70%)
        splitter.setSizes([300, 700])
        
        main_layout.addWidget(splitter)
    
    def create_file_list(self):
        """创建文件列表"""
        self.file_list_widget = QWidget()
        layout = QVBoxLayout(self.file_list_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 标题
        title_label = QLabel("文件列表")
        title_label.setFont(QFont("", 10, QFont.Bold))
        layout.addWidget(title_label)
        
        # 文件列表
        self.file_list = QListWidget()
        self.file_list.setAlternatingRowColors(True)
        self.file_list.itemClicked.connect(self.on_file_item_clicked)
        self.file_list.itemDoubleClicked.connect(self.on_file_item_double_clicked)
        layout.addWidget(self.file_list)
    
    def create_details_panel(self):
        """创建详情面板"""
        self.details_widget = QWidget()
        layout = QVBoxLayout(self.details_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 标题
        title_label = QLabel("文件详情")
        title_label.setFont(QFont("", 10, QFont.Bold))
        layout.addWidget(title_label)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # 详情内容容器
        self.details_content = QWidget()
        self.details_layout = QVBoxLayout(self.details_content)
        self.details_layout.setContentsMargins(10, 10, 10, 10)
        self.details_layout.setSpacing(10)
        
        # 添加默认提示
        self.create_empty_details()
        
        scroll_area.setWidget(self.details_content)
        layout.addWidget(scroll_area)
    
    def create_empty_details(self):
        """创建空详情提示"""
        self.clear_details()
        
        empty_label = QLabel("请选择一个文件查看详情")
        empty_label.setAlignment(Qt.AlignCenter)
        empty_label.setStyleSheet("color: #888; font-size: 14px;")
        self.details_layout.addWidget(empty_label)
        self.details_layout.addStretch()
    
    def clear_details(self):
        """清除详情内容"""
        while self.details_layout.count():
            child = self.details_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
    
    def create_file_details(self, file_info):
        """创建文件详情显示
        
        Args:
            file_info: 文件信息字典
        """
        self.clear_details()
        
        # 文件基本信息
        self.create_basic_info_section(file_info)
        
        # 文件路径信息
        self.create_path_info_section(file_info)
        
        # 文件属性信息
        self.create_attributes_section(file_info)
        
        # 标签信息
        self.create_tags_section(file_info)
        
        # 添加弹性空间
        self.details_layout.addStretch()
    
    def create_basic_info_section(self, file_info):
        """创建基本信息区域"""
        section_frame = self.create_section_frame("基本信息")
        layout = section_frame.layout()
        
        # 文件名
        self.add_info_row(layout, "文件名:", file_info.get("name", "未知"))
        
        # 文件类型
        file_type = file_info.get("file_type", "未知")
        self.add_info_row(layout, "文件类型:", file_type)
        
        # 文件大小
        size = file_info.get("size", 0)
        size_str = self.format_file_size(size)
        self.add_info_row(layout, "文件大小:", size_str)
        
        # 添加时间
        added_time = file_info.get("added_time", "")
        if added_time:
            try:
                # 假设时间格式为 ISO 格式
                dt = datetime.fromisoformat(added_time.replace('Z', '+00:00'))
                time_str = dt.strftime("%Y-%m-%d %H:%M:%S")
            except:
                time_str = str(added_time)
            self.add_info_row(layout, "添加时间:", time_str)
        
        self.details_layout.addWidget(section_frame)
    
    def create_path_info_section(self, file_info):
        """创建路径信息区域"""
        section_frame = self.create_section_frame("路径信息")
        layout = section_frame.layout()
        
        # 入库类型
        entry_type = file_info.get("entry_type", "未知")
        type_map = {"link": "链接", "copy": "复制", "move": "移动"}
        type_str = type_map.get(entry_type, entry_type)
        self.add_info_row(layout, "入库类型:", type_str)
        
        # 原始路径
        original_path = file_info.get("original_path", "")
        if original_path:
            self.add_info_row(layout, "原始路径:", original_path, wrap=True)
        
        # 库路径
        library_path = file_info.get("library_path", "")
        if library_path:
            self.add_info_row(layout, "库路径:", library_path, wrap=True)
        
        self.details_layout.addWidget(section_frame)
    
    def create_attributes_section(self, file_info):
        """创建属性信息区域"""
        section_frame = self.create_section_frame("文件属性")
        layout = section_frame.layout()
        
        # 获取实际文件路径
        file_path = file_info.get("original_path", "")
        if file_info.get("entry_type") in ["copy", "move"]:
            file_path = file_info.get("library_path", "")
        
        if file_path and os.path.exists(file_path):
            file_stat = os.stat(file_path)
            
            # 修改时间
            mtime = datetime.fromtimestamp(file_stat.st_mtime)
            self.add_info_row(layout, "修改时间:", mtime.strftime("%Y-%m-%d %H:%M:%S"))
            
            # 访问时间
            atime = datetime.fromtimestamp(file_stat.st_atime)
            self.add_info_row(layout, "访问时间:", atime.strftime("%Y-%m-%d %H:%M:%S"))
            
            # 文件权限
            mode = oct(file_stat.st_mode)[-3:]
            self.add_info_row(layout, "权限:", mode)
        else:
            self.add_info_row(layout, "状态:", "文件不存在或无法访问")
        
        self.details_layout.addWidget(section_frame)
    
    def create_tags_section(self, file_info):
        """创建标签信息区域"""
        section_frame = self.create_section_frame("标签")
        layout = section_frame.layout()
        
        # TODO: 从数据库获取文件的标签信息
        # 这里先显示占位信息
        tags = file_info.get("tags", [])
        if tags:
            tags_str = ", ".join([tag.get("name", "") for tag in tags])
            self.add_info_row(layout, "标签:", tags_str, wrap=True)
        else:
            self.add_info_row(layout, "标签:", "无")
        
        self.details_layout.addWidget(section_frame)
    
    def create_section_frame(self, title):
        """创建区域框架
        
        Args:
            title: 区域标题
            
        Returns:
            QFrame: 区域框架
        """
        frame = QFrame()
        frame.setFrameStyle(QFrame.Box)
        frame.setStyleSheet("""
            QFrame {
                border: 1px solid #ddd;
                border-radius: 5px;
                background-color: #fafafa;
            }
        """)
        
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(5)
        
        # 标题
        title_label = QLabel(title)
        title_label.setFont(QFont("", 9, QFont.Bold))
        title_label.setStyleSheet("color: #333; border: none; background: none;")
        layout.addWidget(title_label)
        
        return frame
    
    def add_info_row(self, layout, label_text, value_text, wrap=False):
        """添加信息行
        
        Args:
            layout: 布局
            label_text: 标签文本
            value_text: 值文本
            wrap: 是否换行显示
        """
        if wrap:
            # 换行显示
            label = QLabel(label_text)
            label.setFont(QFont("", 8, QFont.Bold))
            label.setStyleSheet("color: #555; border: none; background: none;")
            layout.addWidget(label)
            
            value = QLabel(str(value_text))
            value.setWordWrap(True)
            value.setStyleSheet("color: #333; border: none; background: none; margin-left: 10px;")
            layout.addWidget(value)
        else:
            # 同行显示
            row_layout = QHBoxLayout()
            
            label = QLabel(label_text)
            label.setFont(QFont("", 8, QFont.Bold))
            label.setStyleSheet("color: #555; border: none; background: none;")
            label.setMinimumWidth(80)
            row_layout.addWidget(label)
            
            value = QLabel(str(value_text))
            value.setStyleSheet("color: #333; border: none; background: none;")
            row_layout.addWidget(value)
            row_layout.addStretch()
            
            layout.addLayout(row_layout)
    
    def format_file_size(self, size):
        """格式化文件大小
        
        Args:
            size: 文件大小（字节）
            
        Returns:
            str: 格式化后的大小
        """
        if size < 1024:
            return f"{size} B"
        elif size < 1024 * 1024:
            return f"{size / 1024:.1f} KB"
        elif size < 1024 * 1024 * 1024:
            return f"{size / (1024 * 1024):.1f} MB"
        else:
            return f"{size / (1024 * 1024 * 1024):.1f} GB"
    
    def on_file_item_clicked(self, item):
        """文件项点击事件"""
        file_info = item.data(Qt.UserRole)
        if file_info:
            self.current_file = file_info
            self.create_file_details(file_info)
            self.file_selected.emit(file_info["id"])
    
    def on_file_item_double_clicked(self, item):
        """文件项双击事件"""
        file_info = item.data(Qt.UserRole)
        if file_info:
            self.file_activated.emit(file_info["id"])
            
            # 查找主窗口对象并调用打开文件方法
            main_window = self
            while main_window and not hasattr(main_window, 'on_open_file'):
                main_window = main_window.parent()
            
            if main_window and hasattr(main_window, 'on_open_file'):
                main_window.on_open_file(file_info["id"])
    
    def set_files(self, files):
        """设置文件列表
        
        Args:
            files: 文件信息字典列表
        """
        self.files = files
        self.file_list.clear()
        
        for file_info in files:
            item = QListWidgetItem()
            item.setText(file_info.get("name", "未知文件"))
            item.setData(Qt.UserRole, file_info)
            
            # 设置图标
            icon = self.get_file_icon(file_info)
            item.setIcon(icon)
            
            self.file_list.addItem(item)
        
        # 如果有文件，默认选中第一个
        if files:
            self.file_list.setCurrentRow(0)
            self.on_file_item_clicked(self.file_list.item(0))
        else:
            self.create_empty_details()
    
    def get_file_icon(self, file_info):
        """获取文件图标"""
        file_path = file_info.get("original_path", "")
        if file_info.get("entry_type") in ["copy", "move"]:
            file_path = file_info.get("library_path", "")
        
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()
        
        # 常见文件类型图标映射
        icon_map = {
            '.txt': 'text_file',
            '.doc': 'word_file',
            '.docx': 'word_file',
            '.pdf': 'pdf_file',
            '.jpg': 'image_file',
            '.jpeg': 'image_file',
            '.png': 'image_file',
            '.gif': 'image_file',
            '.mp3': 'audio_file',
            '.mp4': 'video_file',
            '.avi': 'video_file',
            '.zip': 'archive_file',
            '.rar': 'archive_file',
        }
        
        icon_name = icon_map.get(ext, 'default_file')
        return get_icon(icon_name)
    
    def add_file(self, file_info):
        """添加文件"""
        self.files.append(file_info)
        
        item = QListWidgetItem()
        item.setText(file_info.get("name", "未知文件"))
        item.setData(Qt.UserRole, file_info)
        item.setIcon(self.get_file_icon(file_info))
        
        self.file_list.addItem(item)
    
    def append_files(self, files):
        """批量添加文件"""
        for file_info in files:
            self.add_file(file_info)
    
    def remove_file(self, file_id):
        """移除文件"""
        for i in range(self.file_list.count()):
            item = self.file_list.item(i)
            file_info = item.data(Qt.UserRole)
            if file_info and file_info.get("id") == file_id:
                self.file_list.takeItem(i)
                self.files = [f for f in self.files if f.get("id") != file_id]
                
                # 如果删除的是当前选中的文件，清除详情
                if self.current_file and self.current_file.get("id") == file_id:
                    self.current_file = None
                    self.create_empty_details()
                break
    
    def get_selected_file_ids(self):
        """获取选中的文件ID列表"""
        current_item = self.file_list.currentItem()
        if current_item:
            file_info = current_item.data(Qt.UserRole)
            if file_info:
                return [file_info["id"]]
        return []
    
    def clear_selection(self):
        """清除选择"""
        self.file_list.clearSelection()
        self.current_file = None
        self.create_empty_details()
    
    def select_all(self):
        """选择全部"""
        self.file_list.selectAll()
    
    def get_file_count(self):
        """获取文件数量"""
        return len(self.files)
