#!/usr/bin/env python3
"""
AI功能第一阶段测试脚本

测试智能规则引擎的基础功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from smartvault.services.ai.ai_manager import AIManager
from smartvault.services.ai.smart_rule_engine import SmartRuleEngine
from smartvault.services.ai.fallback_service import FallbackService
from smartvault.utils.config import get_ai_config


def test_fallback_service():
    """测试降级服务"""
    print("=== 测试降级服务 ===")
    
    fallback = FallbackService()
    
    # 测试文件信息
    test_files = [
        {
            'name': 'document',
            'extension': '.pdf',
            'path': '/work/project/document.pdf'
        },
        {
            'name': 'image',
            'extension': '.jpg',
            'path': '/personal/photos/image.jpg'
        },
        {
            'name': 'code_v1',
            'extension': '.py',
            'path': '/project/src/code_v1.py'
        }
    ]
    
    for file_info in test_files:
        suggestions = fallback.suggest_tags_fallback(file_info)
        print(f"文件: {file_info['name']}{file_info['extension']}")
        print(f"建议标签: {suggestions}")
        print()


def test_smart_rule_engine():
    """测试智能规则引擎"""
    print("=== 测试智能规则引擎 ===")
    
    engine = SmartRuleEngine()
    engine.initialize({}, None)
    
    # 测试项目文件检测
    print("1. 测试项目文件检测")
    # 由于需要真实文件夹，这里只测试基础逻辑
    test_files = [
        {'name': 'main', 'extension': '.py', 'full_name': 'main.py'},
        {'name': 'config', 'extension': '.json', 'full_name': 'config.json'},
        {'name': 'README', 'extension': '.md', 'full_name': 'README.md'},
        {'name': 'requirements', 'extension': '.txt', 'full_name': 'requirements.txt'}
    ]
    
    # 测试系列文件检测
    print("2. 测试系列文件检测")
    series_files = [
        {'name': 'document_v1', 'extension': '.pdf'},
        {'name': 'document_v2', 'extension': '.pdf'},
        {'name': 'document_v3', 'extension': '.pdf'}
    ]
    
    series_result = engine.detect_file_series(series_files)
    print(f"系列检测结果: {len(series_result)} 个系列")
    for series in series_result:
        print(f"  系列名: {series['series_name']}")
        print(f"  模式: {series['pattern_type']}")
        print(f"  置信度: {series['confidence']:.2f}")
        print(f"  文件数: {len(series['files'])}")
    
    # 测试标签建议
    print("\n3. 测试标签建议")
    test_file = {
        'name': 'project_report_v2',
        'extension': '.pdf',
        'path': '/work/projects/myproject/project_report_v2.pdf'
    }
    
    suggestions = engine.suggest_tags(test_file)
    print(f"文件: {test_file['name']}{test_file['extension']}")
    print(f"AI建议标签: {suggestions}")


def test_ai_manager():
    """测试AI管理器"""
    print("=== 测试AI管理器 ===")
    
    # 创建AI管理器
    ai_manager = AIManager()
    
    # 测试配置
    test_config = {
        "advanced": {
            "enable_ai_features": True
        },
        "ai": {
            "enabled": True,
            "stage": "rule_based"
        }
    }
    
    # 初始化
    success = ai_manager.initialize(test_config)
    print(f"AI管理器初始化: {'成功' if success else '失败'}")
    
    # 获取状态
    status = ai_manager.get_status()
    print(f"AI状态: {status}")
    
    # 测试标签建议
    test_file = {
        'name': 'test_project',
        'extension': '.py',
        'path': '/work/test_project.py',
        'folder_path': '/work'
    }
    
    suggestions = ai_manager.suggest_tags(test_file)
    print(f"AI标签建议: {suggestions}")
    
    # 测试完整AI建议
    full_suggestions = ai_manager.get_ai_suggestions_for_file(test_file)
    print(f"完整AI建议: {full_suggestions}")


def test_config_integration():
    """测试配置集成"""
    print("=== 测试配置集成 ===")
    
    try:
        ai_config = get_ai_config()
        print(f"AI配置加载成功: {ai_config.keys()}")
        print(f"AI启用状态: {ai_config.get('enabled', False)}")
        print(f"AI阶段: {ai_config.get('stage', 'unknown')}")
        
    except Exception as e:
        print(f"配置加载失败: {e}")


def main():
    """主测试函数"""
    print("SmartVault AI功能第一阶段测试")
    print("=" * 50)
    
    try:
        # 测试各个组件
        test_fallback_service()
        test_smart_rule_engine()
        test_ai_manager()
        test_config_integration()
        
        print("=" * 50)
        print("✅ 第一阶段基础架构测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
