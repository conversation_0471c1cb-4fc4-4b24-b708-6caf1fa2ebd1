#!/usr/bin/env python3
"""
直接检查数据库内容
"""

import sqlite3
import sys
import os

def check_database():
    """直接检查数据库"""
    
    # 数据库路径
    db_path = "D:/temp4/SmartVault_Lib/data/smartvault.db"
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    print(f"数据库文件: {db_path}")
    print(f"文件大小: {os.path.getsize(db_path)} bytes")
    print("=" * 50)
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 1. 检查文件总数
        cursor.execute('SELECT COUNT(*) as count FROM files')
        total_files = cursor.fetchone()['count']
        print(f'数据库中总文件数: {total_files}')
        
        # 2. 检查中转文件夹文件数
        cursor.execute("SELECT COUNT(*) as count FROM files WHERE staging_status = 'staging'")
        staging_files = cursor.fetchone()['count']
        print(f'中转文件夹文件数: {staging_files}')
        
        # 3. 检查设备标签关联
        cursor.execute('SELECT COUNT(*) as count FROM file_tags')
        tag_relations = cursor.fetchone()['count']
        print(f'文件标签关联总数: {tag_relations}')
        
        # 4. 检查设备标签
        cursor.execute("SELECT id, name FROM tags WHERE name LIKE '💾%'")
        device_tags = cursor.fetchall()
        print(f'设备标签数量: {len(device_tags)}')
        for tag in device_tags:
            tag_id, tag_name = tag['id'], tag['name']
            cursor.execute('SELECT COUNT(*) as count FROM file_tags WHERE tag_id = ?', (tag_id,))
            tag_file_count = cursor.fetchone()['count']
            print(f'  - {tag_name}: {tag_file_count} 个文件')
        
        # 5. 检查文件状态分布
        cursor.execute('SELECT staging_status, COUNT(*) as count FROM files GROUP BY staging_status')
        status_stats = cursor.fetchall()
        print(f'文件状态分布:')
        for row in status_stats:
            status, count = row['staging_status'], row['count']
            print(f'  - {status or "NULL"}: {count} 个文件')
        
        # 6. 检查入库方式分布
        cursor.execute('SELECT entry_type, COUNT(*) as count FROM files GROUP BY entry_type')
        entry_stats = cursor.fetchall()
        print(f'入库方式分布:')
        for row in entry_stats:
            entry_type, count = row['entry_type'], row['count']
            print(f'  - {entry_type}: {count} 个文件')
        
        # 7. 检查最近添加的文件详情（前10个）
        cursor.execute("SELECT id, name, added_at, staging_status FROM files ORDER BY added_at DESC LIMIT 10")
        recent_files_detail = cursor.fetchall()
        print(f'\n最近添加的10个文件:')
        for row in recent_files_detail:
            file_id, name, added_at, staging_status = row['id'], row['name'], row['added_at'], row['staging_status']
            print(f'  - {name} (ID: {file_id[:8]}..., 状态: {staging_status}, 时间: {added_at})')
        
        # 8. 检查分页查询问题
        print(f'\n=== 分页查询测试 ===')
        
        # 测试所有文件分页
        cursor.execute('SELECT id, name FROM files ORDER BY added_at DESC LIMIT 100 OFFSET 0')
        first_page_files = cursor.fetchall()
        print(f'第一页实际返回文件数: {len(first_page_files)}')
        
        # 测试中转文件夹分页
        cursor.execute("SELECT id, name FROM files WHERE staging_status = 'staging' ORDER BY added_at DESC LIMIT 100 OFFSET 0")
        staging_page_files = cursor.fetchall()
        print(f'中转文件夹第一页实际返回文件数: {len(staging_page_files)}')
        
        # 9. 检查是否有设备标签关联的文件
        if device_tags:
            for tag in device_tags:
                tag_id, tag_name = tag['id'], tag['name']
                cursor.execute("""
                    SELECT f.id, f.name 
                    FROM files f 
                    JOIN file_tags ft ON f.id = ft.file_id 
                    WHERE ft.tag_id = ? 
                    LIMIT 5
                """, (tag_id,))
                device_files = cursor.fetchall()
                print(f'\n设备 {tag_name} 的文件示例:')
                for file_row in device_files:
                    print(f'  - {file_row["name"]} (ID: {file_row["id"][:8]}...)')
        
        conn.close()
        print('\n=== 检查完成 ===')
        
    except Exception as e:
        print(f'检查数据库失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_database()
