"""
快速标签菜单演示脚本
"""

import sys
import os
from PySide6.QtWidgets import QA<PERSON><PERSON>, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PySide6.QtCore import Qt

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from smartvault.ui.components.quick_tag_menu import QuickTagMenu
from smartvault.services.tag_service import TagService
from smartvault.data.database import Database


class QuickTagMenuDemo(QMainWindow):
    """快速标签菜单演示窗口"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("快速标签菜单演示")
        self.setGeometry(100, 100, 400, 300)

        # 初始化数据库和服务
        self.init_database()

        # 创建UI
        self.init_ui()

        # 创建测试数据
        self.create_test_data()

    def init_database(self):
        """初始化数据库"""
        try:
            # 使用临时数据库
            self.db_manager = Database(":memory:")
            self.tag_service = TagService()
            print("数据库初始化成功")
        except Exception as e:
            print(f"数据库初始化失败: {e}")

    def init_ui(self):
        """初始化用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)

        # 标题
        title_label = QLabel("快速标签菜单演示")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 20px;")
        layout.addWidget(title_label)

        # 说明
        info_label = QLabel(
            "右键点击下面的按钮来测试快速标签菜单功能。\n"
            "菜单将显示可用的标签，您可以快速添加或移除标签。"
        )
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("margin: 10px; color: #666;")
        layout.addWidget(info_label)

        # 测试按钮
        self.test_button = QPushButton("右键点击测试快速标签菜单")
        self.test_button.setContextMenuPolicy(Qt.CustomContextMenu)
        self.test_button.customContextMenuRequested.connect(self.show_quick_tag_menu)
        self.test_button.setStyleSheet(
            "QPushButton {"
            "    padding: 20px;"
            "    font-size: 14px;"
            "    background-color: #f0f0f0;"
            "    border: 2px solid #ccc;"
            "    border-radius: 5px;"
            "}"
            "QPushButton:hover {"
            "    background-color: #e0e0e0;"
            "}"
        )
        layout.addWidget(self.test_button)

        # 状态标签
        self.status_label = QLabel("准备就绪")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("margin: 10px; color: #333;")
        layout.addWidget(self.status_label)

        layout.addStretch()

    def create_test_data(self):
        """创建测试数据"""
        try:
            # 创建一些测试标签
            tag1_id = self.tag_service.create_tag("工作", "#ff6b6b")
            tag2_id = self.tag_service.create_tag("个人", "#4ecdc4")
            tag3_id = self.tag_service.create_tag("重要", "#45b7d1")

            # 创建子标签
            self.tag_service.create_tag("项目A", "#ff9999", tag1_id)
            self.tag_service.create_tag("项目B", "#ffaaaa", tag1_id)
            self.tag_service.create_tag("家庭", "#7fdddd", tag2_id)
            self.tag_service.create_tag("爱好", "#99eeee", tag2_id)

            # 创建一些测试文件ID（仅用于演示，不需要实际文件）
            self.test_file_ids = ["demo_file_1", "demo_file_2", "demo_file_3"]

            self.status_label.setText("测试数据创建成功 - 已创建7个标签，准备演示快速标签菜单")
            print("测试数据创建成功")

        except Exception as e:
            self.status_label.setText(f"创建测试数据失败: {e}")
            print(f"创建测试数据失败: {e}")

    def show_quick_tag_menu(self, position):
        """显示快速标签菜单"""
        try:
            # 创建快速标签菜单
            quick_tag_menu = QuickTagMenu(self)

            # 连接标签变化信号
            quick_tag_menu.tags_changed.connect(self.on_tags_changed)

            # 创建父菜单
            from PySide6.QtWidgets import QMenu
            menu = QMenu(self)

            # 添加快速标签子菜单
            tag_submenu = quick_tag_menu.create_menu(menu, self.test_file_ids)
            menu.addMenu(tag_submenu)

            # 添加分隔符和其他选项
            menu.addSeparator()
            menu.addAction("其他操作...")

            # 显示菜单
            global_pos = self.test_button.mapToGlobal(position)
            menu.exec(global_pos)

            self.status_label.setText("快速标签菜单已显示")

        except Exception as e:
            self.status_label.setText(f"显示菜单失败: {e}")
            print(f"显示菜单失败: {e}")

    def on_tags_changed(self):
        """处理标签变化事件"""
        self.status_label.setText("标签已更新！")
        print("标签发生变化")

        # 显示当前文件的标签状态
        try:
            for file_id in self.test_file_ids:
                tags = self.tag_service.get_file_tags(file_id)
                tag_names = [tag['name'] for tag in tags]
                print(f"文件 {file_id} 的标签: {', '.join(tag_names) if tag_names else '无标签'}")
        except Exception as e:
            print(f"获取文件标签失败: {e}")


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 创建演示窗口
    demo = QuickTagMenuDemo()
    demo.show()

    # 运行应用
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
