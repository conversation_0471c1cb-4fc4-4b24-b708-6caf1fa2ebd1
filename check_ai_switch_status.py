#!/usr/bin/env python3
"""
AI设置开关状态检查

检查AI相关开关的状态、持久化保存和重复开关问题
"""

import sys
import os
import json

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from smartvault.utils.config import load_config, save_config, get_ai_status, save_ai_status, get_ai_config


def check_ai_switch_locations():
    """检查AI开关的位置分布"""
    print("🔍 AI开关位置分布检查")
    print("=" * 60)

    locations = {
        "高级设置页面": {
            "文件": "smartvault/ui/dialogs/settings/pages/advanced_page.py",
            "开关": "enable_ai_features_check",
            "状态": "✅ 存在" if os.path.exists("smartvault/ui/dialogs/settings/pages/advanced_page.py") and "enable_ai_features_check" in open("smartvault/ui/dialogs/settings/pages/advanced_page.py", 'r', encoding='utf-8').read() else "❌ 缺失",
            "说明": "高级设置页面中的AI总开关"
        },
        "AI设置页面": {
            "文件": "smartvault/ui/dialogs/settings/pages/ai/components/ai_features_widget.py",
            "开关": "ai_enabled_checkbox",
            "状态": "✅ 存在",
            "说明": "AI功能开关组件中的总开关"
        },
        "自动标签页面": {
            "文件": "smartvault/ui/dialogs/settings/pages/auto_tag_page.py",
            "开关": "enable_ai_auto_tags_check",
            "状态": "✅ 存在",
            "说明": "自动标签页面中的AI辅助标签开关"
        },
        "配置文件": {
            "文件": "smartvault/utils/config.py",
            "开关": "advanced.enable_ai_features + ai.enabled + auto_tags.enable_ai",
            "状态": "✅ 存在",
            "说明": "配置系统中的多层AI开关"
        }
    }

    for location, info in locations.items():
        print(f"{info['状态']} {location}")
        print(f"   文件: {info['文件']}")
        print(f"   开关: {info['开关']}")
        print(f"   说明: {info['说明']}")
        print()

    return locations


def check_ai_config_structure():
    """检查AI配置结构"""
    print("📋 AI配置结构检查")
    print("=" * 60)

    try:
        config = load_config()

        # 检查高级设置中的AI开关
        advanced_ai = config.get("advanced", {}).get("enable_ai_features", None)
        print(f"高级设置.enable_ai_features: {advanced_ai}")

        # 检查AI配置中的开关
        ai_enabled = config.get("ai", {}).get("enabled", None)
        print(f"AI配置.enabled: {ai_enabled}")

        # 检查自动标签中的AI开关
        auto_tags_ai = config.get("auto_tags", {}).get("enable_ai", None)
        print(f"自动标签.enable_ai: {auto_tags_ai}")

        # 检查AI功能状态
        ai_status = get_ai_status()
        print(f"AI功能状态 (get_ai_status): {ai_status}")

        # 检查AI配置
        ai_config = get_ai_config()
        print(f"AI配置完整性: {len(ai_config)} 项配置")

        # 分析配置一致性
        print("\n🔍 配置一致性分析:")
        if advanced_ai is not None and ai_enabled is not None:
            if advanced_ai == ai_enabled:
                print("✅ 高级设置和AI配置中的开关状态一致")
            else:
                print("❌ 高级设置和AI配置中的开关状态不一致")
                print(f"   高级设置: {advanced_ai}")
                print(f"   AI配置: {ai_enabled}")

        return config

    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return None


def check_switch_redundancy():
    """检查开关重复性"""
    print("\n🔄 开关重复性检查")
    print("=" * 60)

    redundancy_analysis = {
        "AI总开关": {
            "位置": [
                "高级设置.enable_ai_features (缺失)",
                "AI设置页面.ai_enabled_checkbox",
                "配置文件.ai.enabled"
            ],
            "功能": "控制整个AI功能的启用/禁用",
            "问题": "高级设置页面缺少AI总开关，可能导致用户无法在高级设置中控制AI"
        },
        "AI自动标签开关": {
            "位置": [
                "自动标签页面.enable_ai_auto_tags_check",
                "配置文件.auto_tags.enable_ai"
            ],
            "功能": "控制AI自动标签建议功能",
            "问题": "功能单一，无重复问题"
        },
        "AI子功能开关": {
            "位置": [
                "AI设置页面.project_enabled_checkbox",
                "AI设置页面.series_enabled_checkbox",
                "AI设置页面.learning_enabled_checkbox",
                "AI设置页面.suggestions_enabled_checkbox"
            ],
            "功能": "控制AI的具体子功能",
            "问题": "无重复问题，功能明确"
        }
    }

    for switch_type, info in redundancy_analysis.items():
        print(f"📌 {switch_type}")
        print(f"   位置: {', '.join(info['位置'])}")
        print(f"   功能: {info['功能']}")
        print(f"   问题: {info['问题']}")
        print()

    return redundancy_analysis


def check_persistence():
    """检查持久化保存"""
    print("💾 持久化保存检查")
    print("=" * 60)

    try:
        # 获取当前配置
        original_config = load_config()
        original_ai_status = get_ai_status()

        print(f"原始AI状态: {original_ai_status}")

        # 测试保存AI状态
        print("\n🧪 测试AI状态保存...")

        # 切换AI状态
        new_status = not original_ai_status
        save_ai_status(new_status)

        # 重新加载配置验证
        updated_config = load_config()
        updated_ai_status = get_ai_status()

        print(f"更新后AI状态: {updated_ai_status}")

        # 验证保存是否成功
        if updated_ai_status == new_status:
            print("✅ AI状态保存成功")

            # 检查各个配置位置是否都更新了
            advanced_enabled = updated_config.get("advanced", {}).get("enable_ai_features", False)
            ai_enabled = updated_config.get("ai", {}).get("enabled", False)
            auto_tags_enabled = updated_config.get("auto_tags", {}).get("enable_ai", False)

            print(f"   高级设置.enable_ai_features: {advanced_enabled}")
            print(f"   AI配置.enabled: {ai_enabled}")
            print(f"   自动标签.enable_ai: {auto_tags_enabled}")

            if advanced_enabled == ai_enabled == auto_tags_enabled == new_status:
                print("✅ 所有配置位置都正确更新")
            else:
                print("❌ 配置位置更新不一致")
        else:
            print("❌ AI状态保存失败")

        # 恢复原始状态
        save_ai_status(original_ai_status)
        print(f"\n🔄 已恢复原始AI状态: {original_ai_status}")

        return True

    except Exception as e:
        print(f"❌ 持久化测试失败: {e}")
        return False


def check_ai_config_helper():
    """检查AI配置管理器"""
    print("\n🛠️ AI配置管理器检查")
    print("=" * 60)

    try:
        from smartvault.ui.dialogs.settings.pages.ai.utils.ai_config_helper import AIConfigManager

        config_manager = AIConfigManager()

        # 测试加载配置
        ai_config = config_manager.load_ai_config()
        print(f"✅ AI配置加载成功: {len(ai_config)} 项")

        # 测试配置验证
        is_valid, error_msg = config_manager.validate_ai_config(ai_config)
        if is_valid:
            print("✅ AI配置验证通过")
        else:
            print(f"❌ AI配置验证失败: {error_msg}")

        # 测试保存配置
        test_config = ai_config.copy()
        test_config['enabled'] = not test_config.get('enabled', False)

        # 注意：这里只是测试配置管理器的保存方法，实际保存被注释了
        success = config_manager.save_ai_config(test_config)
        if success:
            print("✅ AI配置保存方法可用")
        else:
            print("❌ AI配置保存方法失败")

        # 检查配置管理器的TODO项
        print("\n⚠️ 配置管理器待完成项:")
        print("   - save_ai_config方法中的实际保存逻辑 (TODO: 实际保存到配置文件)")

        return True

    except Exception as e:
        print(f"❌ AI配置管理器检查失败: {e}")
        return False


def generate_recommendations():
    """生成改进建议"""
    print("\n💡 改进建议")
    print("=" * 60)

    recommendations = [
        {
            "问题": "高级设置页面缺少AI总开关",
            "建议": "在advanced_page.py中添加AI功能总开关",
            "优先级": "高",
            "实施": "添加enable_ai_features复选框到高级设置页面"
        },
        {
            "问题": "AI配置管理器的保存功能未完成",
            "建议": "完成AIConfigManager.save_ai_config的实际保存逻辑",
            "优先级": "高",
            "实施": "调用smartvault.utils.config.save_ai_config函数"
        },
        {
            "问题": "配置一致性可能存在问题",
            "建议": "确保所有AI开关状态保持同步",
            "优先级": "中",
            "实施": "统一使用save_ai_status函数更新所有相关配置"
        },
        {
            "问题": "用户可能在不同页面看到不同的AI开关",
            "建议": "明确各个开关的层级关系和作用域",
            "优先级": "中",
            "实施": "添加开关说明和层级关系文档"
        }
    ]

    for i, rec in enumerate(recommendations, 1):
        print(f"{i}. {rec['问题']}")
        print(f"   建议: {rec['建议']}")
        print(f"   优先级: {rec['优先级']}")
        print(f"   实施: {rec['实施']}")
        print()

    return recommendations


def main():
    """主检查函数"""
    print("🔍 SmartVault AI设置开关状态全面检查")
    print("=" * 80)

    # 检查开关位置
    locations = check_ai_switch_locations()

    # 检查配置结构
    config = check_ai_config_structure()

    # 检查开关重复性
    redundancy = check_switch_redundancy()

    # 检查持久化
    persistence_ok = check_persistence()

    # 检查AI配置管理器
    config_manager_ok = check_ai_config_helper()

    # 生成改进建议
    recommendations = generate_recommendations()

    # 总结
    print("📊 检查结果总结")
    print("=" * 60)

    issues_found = []

    # 统计问题
    if any(loc["状态"] == "❌ 缺失" for loc in locations.values()):
        issues_found.append("缺少高级设置页面的AI总开关")

    if not persistence_ok:
        issues_found.append("持久化保存存在问题")

    if not config_manager_ok:
        issues_found.append("AI配置管理器存在问题")

    if issues_found:
        print("❌ 发现以下问题:")
        for issue in issues_found:
            print(f"   • {issue}")
    else:
        print("✅ 所有检查项目都通过")

    print(f"\n📋 改进建议数量: {len(recommendations)}")
    print("🎯 建议优先处理高优先级问题")

    return len(issues_found) == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
