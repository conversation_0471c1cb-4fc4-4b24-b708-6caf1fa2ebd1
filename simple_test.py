#!/usr/bin/env python3
"""
简单测试多条件功能
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("开始导入...")

try:
    from smartvault.services.auto_tag_service import ConditionType
    print("✅ ConditionType 导入成功")
    
    from smartvault.services.auto_tag_service import LogicOperator
    print("✅ LogicOperator 导入成功")
    
    from smartvault.services.auto_tag_service import Condition
    print("✅ Condition 导入成功")
    
    # 测试创建单个条件
    condition = Condition(type=ConditionType.FILE_EXTENSION, value="pdf")
    print("✅ 单个条件创建成功")
    
    # 测试条件匹配
    file_info = {"name": "test.pdf", "size": 1024}
    result = condition.matches(file_info)
    print(f"✅ 条件匹配测试: {result}")
    
    from smartvault.services.auto_tag_service import ConditionGroup
    print("✅ ConditionGroup 导入成功")
    
    # 测试创建条件组
    group = ConditionGroup(operator=LogicOperator.AND)
    group.conditions.append(condition)
    print("✅ 条件组创建成功")
    
    # 测试条件组匹配
    result = group.matches(file_info)
    print(f"✅ 条件组匹配测试: {result}")
    
    from smartvault.services.auto_tag_service import AutoTagRule
    print("✅ AutoTagRule 导入成功")
    
    # 测试创建规则
    rule = AutoTagRule(
        id="test",
        name="测试规则",
        tag_names=["测试"],
        condition_group=group
    )
    print("✅ 规则创建成功")
    
    # 测试规则匹配
    result = rule.matches(file_info)
    print(f"✅ 规则匹配测试: {result}")
    
    print("🎉 所有基础测试通过！")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
