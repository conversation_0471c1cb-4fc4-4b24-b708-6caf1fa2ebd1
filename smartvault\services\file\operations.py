"""
文件操作功能
"""

import os
import shutil
from smartvault.utils.config import load_config


class FileOperationsMixin:
    """文件操作混入类"""

    def get_library_path(self):
        """获取智能文件库路径

        Returns:
            str: 智能文件库路径
        """
        config = load_config()
        return config.get("library_path", "")

    def is_path_in_library(self, path):
        """判断路径是否在智能文件库内

        Args:
            path: 文件路径

        Returns:
            bool: 是否在智能文件库内
        """
        if not path:
            return False

        library_path = self.get_library_path()
        if not library_path:
            return False

        # 规范化路径
        path = os.path.normpath(path)
        library_path = os.path.normpath(library_path)

        return path.startswith(library_path)

    def update_file_location(self, file_id):
        """更新文件位置信息

        Args:
            file_id: 文件ID

        Returns:
            bool: 是否成功更新
        """
        # 获取文件信息
        file_info = self.get_file_by_id(file_id)
        if not file_info:
            return False

        # 确定文件路径
        file_path = None
        if file_info["entry_type"] == "link":
            file_path = file_info["original_path"]
        else:
            file_path = file_info["library_path"]

        # 检查文件是否在库内
        is_in_library = self.is_path_in_library(file_path)

        # 如果文件位置与entry_type不一致，更新entry_type
        if is_in_library and file_info["entry_type"] == "link":
            # 文件在库内但entry_type是link，更新为copy
            cursor = self.db.conn.cursor()
            cursor.execute(
                "UPDATE files SET entry_type = 'copy', library_path = ? WHERE id = ?",
                (file_path, file_id)
            )
            self.db.conn.commit()
            print(f"文件 {file_id} 位于库内，已更新entry_type为copy")
            return True
        elif not is_in_library and file_info["entry_type"] in ["copy", "move"]:
            # 文件在库外但entry_type是copy或move，更新为link
            cursor = self.db.conn.cursor()
            cursor.execute(
                "UPDATE files SET entry_type = 'link', original_path = ?, library_path = NULL WHERE id = ?",
                (file_path, file_id)
            )
            self.db.conn.commit()
            print(f"文件 {file_id} 位于库外，已更新entry_type为link")
            return True

        return False

    def open_file(self, file_id):
        """打开文件

        Args:
            file_id: 文件ID

        Returns:
            bool: 是否成功打开文件

        Raises:
            FileNotFoundError: 文件不存在
        """
        # 获取文件信息
        file_info = self.get_file_by_id(file_id)
        if not file_info:
            raise FileNotFoundError(f"文件不存在: {file_id}")

        # 确定文件路径
        if file_info["entry_type"] == "link":
            file_path = file_info["original_path"]
        else:
            file_path = file_info["library_path"]

        # 检查文件是否存在
        if not os.path.exists(file_path):
            # 更新文件状态为不可用
            cursor = self.db.conn.cursor()
            cursor.execute(
                "UPDATE files SET is_available = 0 WHERE id = ?",
                (file_id,)
            )
            self.db.conn.commit()

            raise FileNotFoundError(f"文件不存在: {file_path}")

        # 使用系统默认程序打开文件
        try:
            os.startfile(file_path)
            return True
        except Exception as e:
            print(f"打开文件失败: {e}")
            return False

    def remove_file(self, file_id, delete_physical=False, use_recycle_bin=True):
        """从智能文件库移除文件

        Args:
            file_id: 文件ID
            delete_physical: 是否删除物理文件
            use_recycle_bin: 是否使用回收站（仅当delete_physical=True时有效）

        Returns:
            bool: 是否成功移除文件

        Raises:
            FileNotFoundError: 文件不存在
        """
        # 获取文件信息
        file_info = self.get_file_by_id(file_id)
        if not file_info:
            raise FileNotFoundError(f"文件不存在: {file_id}")

        # 如果需要删除物理文件
        if delete_physical:
            file_path = None

            # 确定要删除的文件路径
            if file_info["entry_type"] == "link":
                file_path = file_info["original_path"]
            else:
                file_path = file_info["library_path"]

            # 规范化路径，确保使用正确的路径分隔符
            file_path = os.path.normpath(file_path)
            print(f"准备删除物理文件: {file_path}, 使用回收站: {use_recycle_bin}")

            # 检查文件是否存在
            if file_path and os.path.exists(file_path):
                try:
                    if use_recycle_bin:
                        # 尝试使用回收站删除文件
                        print(f"尝试使用回收站删除文件: {file_path}")
                        try:
                            import send2trash
                            send2trash.send2trash(file_path)
                            print(f"文件已移动到回收站: {file_path}")
                        except ImportError:
                            print(f"send2trash模块未安装，降级为直接删除: {file_path}")
                            # 降级为直接删除
                            if os.path.isdir(file_path):
                                os.rmdir(file_path)
                            else:
                                os.remove(file_path)
                            print(f"文件已直接删除: {file_path}")
                    else:
                        # 直接删除文件
                        print(f"直接删除文件: {file_path}")
                        if os.path.isdir(file_path):
                            os.rmdir(file_path)
                        else:
                            os.remove(file_path)
                        print(f"文件已删除: {file_path}")
                except Exception as e:
                    print(f"删除物理文件失败: {file_path} - {e}")
                    import traceback
                    traceback.print_exc()
                    # 重新抛出异常，让调用者知道删除失败
                    raise RuntimeError(f"删除物理文件失败: {e}")
            else:
                print(f"文件不存在，无法删除: {file_path}")
                # 无论是什么类型的文件，如果物理文件不存在，我们都只删除数据库记录
                # 这样可以清理那些已经在文件系统中被移除的文件的记录
                print(f"物理文件不存在，仅删除数据库记录")

        # 从数据库中移除文件
        cursor = self.db.conn.cursor()
        cursor.execute("DELETE FROM files WHERE id = ?", (file_id,))
        cursor.execute("DELETE FROM file_tags WHERE file_id = ?", (file_id,))
        self.db.conn.commit()

        return True

    def rename_file(self, file_id, new_name):
        """重命名文件

        Args:
            file_id: 文件ID
            new_name: 新文件名

        Returns:
            bool: 是否成功重命名

        Raises:
            FileNotFoundError: 文件不存在
            ValueError: 新文件名无效
        """
        # 检查新文件名是否有效
        if not new_name or len(new_name.strip()) == 0:
            raise ValueError("新文件名不能为空")

        # 获取文件信息
        file_info = self.get_file_by_id(file_id)
        if not file_info:
            raise FileNotFoundError(f"文件不存在: {file_id}")

        # 确定文件路径
        if file_info["entry_type"] == "link":
            # 链接文件，需要重命名原始文件
            old_path = file_info["original_path"]
            new_path = os.path.join(os.path.dirname(old_path), new_name)
        else:
            # 复制或移动的文件，需要重命名库内文件
            old_path = file_info["library_path"]
            new_path = os.path.join(os.path.dirname(old_path), new_name)

        # 检查文件是否存在
        if not os.path.exists(old_path):
            raise FileNotFoundError(f"文件不存在: {old_path}")

        # 检查目标文件是否已存在
        if os.path.exists(new_path):
            raise FileExistsError(f"文件已存在: {new_path}")

        try:
            # 重命名文件
            os.rename(old_path, new_path)

            # 更新数据库
            cursor = self.db.conn.cursor()

            if file_info["entry_type"] == "link":
                # 更新原始路径
                cursor.execute(
                    "UPDATE files SET original_path = ?, name = ? WHERE id = ?",
                    (new_path, new_name, file_id)
                )
            else:
                # 更新库内路径
                cursor.execute(
                    "UPDATE files SET library_path = ?, name = ? WHERE id = ?",
                    (new_path, new_name, file_id)
                )

            self.db.conn.commit()
            return True
        except Exception as e:
            print(f"重命名文件失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def move_file_to_folder(self, file_id, target_folder):
        """移动文件到指定文件夹

        Args:
            file_id: 文件ID
            target_folder: 目标文件夹路径

        Returns:
            bool: 是否成功移动

        Raises:
            FileNotFoundError: 文件不存在
            ValueError: 目标文件夹无效
        """
        # 检查目标文件夹是否有效
        if not target_folder or not os.path.isdir(target_folder):
            raise ValueError(f"目标文件夹无效: {target_folder}")

        # 获取文件信息
        file_info = self.get_file_by_id(file_id)
        if not file_info:
            raise FileNotFoundError(f"文件不存在: {file_id}")

        # 确定文件路径
        if file_info["entry_type"] == "link":
            # 链接文件，需要移动原始文件
            old_path = file_info["original_path"]
            new_path = os.path.join(target_folder, os.path.basename(old_path))
        else:
            # 复制或移动的文件，需要移动库内文件
            old_path = file_info["library_path"]
            new_path = os.path.join(target_folder, os.path.basename(old_path))

        # 检查文件是否存在
        if not os.path.exists(old_path):
            raise FileNotFoundError(f"文件不存在: {old_path}")

        # 检查目标文件是否已存在
        if os.path.exists(new_path):
            raise FileExistsError(f"目标位置已存在同名文件: {new_path}")

        try:
            # 移动文件
            shutil.move(old_path, new_path)

            # 更新数据库中的文件路径
            cursor = self.db.conn.cursor()

            if file_info["entry_type"] == "link":
                # 更新原始路径
                cursor.execute(
                    "UPDATE files SET original_path = ? WHERE id = ?",
                    (new_path, file_id)
                )
            else:
                # 更新库内路径
                cursor.execute(
                    "UPDATE files SET library_path = ? WHERE id = ?",
                    (new_path, file_id)
                )

            self.db.conn.commit()

            # 检查文件的新位置并更新entry_type
            print(f"检查文件的新位置并更新entry_type")
            self.update_file_location(file_id)

            # 处理文件移动后的状态和标签更新
            self._handle_file_move_post_processing(file_id, old_path, new_path)

            self.db.conn.commit()
            return True
        except Exception as e:
            print(f"移动文件失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _handle_file_move_post_processing(self, file_id, old_path, new_path):
        """处理文件移动后的状态和标签更新

        Args:
            file_id: 文件ID
            old_path: 原路径
            new_path: 新路径
        """
        try:
            print(f"🔄 处理文件移动后的状态和标签更新: {file_id}")

            # 1. 检查是否从中转文件夹移出，如果是则清除中转状态
            self._check_and_clear_staging_status(file_id, old_path, new_path)

            # 2. 检查是否从导航栏文件夹移出，如果是则移除对应的文件夹标签
            self._check_and_remove_folder_tags(file_id, old_path, new_path)

        except Exception as e:
            print(f"处理文件移动后的状态和标签更新失败: {e}")
            import traceback
            traceback.print_exc()

    def _check_and_clear_staging_status(self, file_id, old_path, new_path):
        """检查并清除中转状态

        Args:
            file_id: 文件ID
            old_path: 原路径
            new_path: 新路径
        """
        try:
            # 获取文件信息
            file_info = self.get_file_by_id(file_id)
            if not file_info:
                return

            # 检查文件是否在中转状态
            if file_info.get("staging_status") != "staging":
                return

            # 获取中转文件夹路径（通常是库路径下的staging文件夹）
            from smartvault.utils.config import load_config
            config = load_config()
            library_path = config.get("library_path", "")

            if not library_path:
                return

            # 规范化路径
            library_path = os.path.normpath(library_path)
            old_path_norm = os.path.normpath(old_path)
            new_path_norm = os.path.normpath(new_path)

            # 检查是否从中转区域移出
            staging_folder = os.path.join(library_path, "中转")
            staging_folder_norm = os.path.normpath(staging_folder)

            # 如果原路径在中转文件夹内，新路径不在中转文件夹内，则清除中转状态
            old_in_staging = old_path_norm.startswith(staging_folder_norm)
            new_in_staging = new_path_norm.startswith(staging_folder_norm)

            if old_in_staging and not new_in_staging:
                print(f"📤 文件从中转文件夹移出，清除中转状态: {file_id}")
                cursor = self.db.conn.cursor()
                cursor.execute(
                    "UPDATE files SET staging_status = 'normal' WHERE id = ?",
                    (file_id,)
                )
                print(f"✅ 已清除文件 {file_id} 的中转状态")

        except Exception as e:
            print(f"检查并清除中转状态失败: {e}")

    def _check_and_remove_folder_tags(self, file_id, old_path, new_path):
        """检查并移除文件夹标签

        Args:
            file_id: 文件ID
            old_path: 原路径
            new_path: 新路径
        """
        try:
            # 导入标签服务
            from smartvault.services.tag_service import TagService
            tag_service = TagService()

            # 获取文件的所有标签
            file_tags = tag_service.get_file_tags(file_id)
            if not file_tags:
                return

            # 获取库路径
            from smartvault.utils.config import load_config
            config = load_config()
            library_path = config.get("library_path", "")

            if not library_path:
                return

            # 规范化路径
            library_path = os.path.normpath(library_path)
            old_path_norm = os.path.normpath(old_path)
            new_path_norm = os.path.normpath(new_path)

            # 检查每个文件夹标签
            for tag in file_tags:
                tag_name = tag.get('name', '')

                # 检查是否是文件夹标签（以📁开头）
                if not tag_name.startswith('📁'):
                    continue

                # 提取文件夹名称
                folder_name = tag_name[1:]  # 去掉📁前缀

                # 构建文件夹路径
                folder_path = os.path.join(library_path, folder_name)
                folder_path_norm = os.path.normpath(folder_path)

                # 检查是否从该文件夹移出
                old_in_folder = old_path_norm.startswith(folder_path_norm)
                new_in_folder = new_path_norm.startswith(folder_path_norm)

                if old_in_folder and not new_in_folder:
                    print(f"📁 文件从文件夹 '{folder_name}' 移出，移除对应标签: {file_id}")
                    if tag_service.remove_tag_from_file(file_id, tag['id']):
                        print(f"✅ 已移除文件 {file_id} 的文件夹标签: {folder_name}")
                    else:
                        print(f"❌ 移除文件 {file_id} 的文件夹标签失败: {folder_name}")

        except Exception as e:
            print(f"检查并移除文件夹标签失败: {e}")

    def move_to_staging(self, file_id):
        """将文件移动到中转状态

        Args:
            file_id: 文件ID

        Returns:
            bool: 是否成功移动到中转状态

        Raises:
            FileNotFoundError: 文件不存在
        """
        # 获取文件信息
        file_info = self.get_file_by_id(file_id)
        if not file_info:
            raise FileNotFoundError(f"文件不存在: {file_id}")

        # 检查文件是否已经在中转状态
        if file_info.get("staging_status") == "staging":
            print(f"文件 {file_id} 已经在中转状态")
            return True

        try:
            # 更新文件状态为中转
            cursor = self.db.conn.cursor()
            cursor.execute(
                "UPDATE files SET staging_status = 'staging' WHERE id = ?",
                (file_id,)
            )
            self.db.conn.commit()

            print(f"文件 {file_id} 已移动到中转状态")
            return True
        except Exception as e:
            print(f"移动文件到中转状态失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def move_from_staging(self, file_id):
        """将文件从中转状态移动到正常状态

        Args:
            file_id: 文件ID

        Returns:
            bool: 是否成功移出中转状态

        Raises:
            FileNotFoundError: 文件不存在
        """
        # 获取文件信息
        file_info = self.get_file_by_id(file_id)
        if not file_info:
            raise FileNotFoundError(f"文件不存在: {file_id}")

        # 检查文件是否在中转状态
        if file_info.get("staging_status") != "staging":
            print(f"文件 {file_id} 不在中转状态")
            return True

        try:
            # 更新文件状态为正常
            cursor = self.db.conn.cursor()
            cursor.execute(
                "UPDATE files SET staging_status = 'normal' WHERE id = ?",
                (file_id,)
            )
            self.db.conn.commit()

            print(f"文件 {file_id} 已移出中转状态")
            return True
        except Exception as e:
            print(f"移出中转状态失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def toggle_staging_status(self, file_id):
        """切换文件的中转状态

        Args:
            file_id: 文件ID

        Returns:
            bool: 是否成功切换状态

        Raises:
            FileNotFoundError: 文件不存在
        """
        # 获取文件信息
        file_info = self.get_file_by_id(file_id)
        if not file_info:
            raise FileNotFoundError(f"文件不存在: {file_id}")

        # 根据当前状态切换
        current_status = file_info.get("staging_status", "normal")
        if current_status == "staging":
            return self.move_from_staging(file_id)
        else:
            return self.move_to_staging(file_id)

    def batch_move_to_staging(self, file_ids):
        """批量将文件移动到中转状态

        Args:
            file_ids: 文件ID列表

        Returns:
            dict: 包含成功和失败文件ID的字典
        """
        success_files = []
        failed_files = []

        for file_id in file_ids:
            try:
                if self.move_to_staging(file_id):
                    success_files.append(file_id)
                else:
                    failed_files.append(file_id)
            except Exception as e:
                print(f"批量移动文件 {file_id} 到中转状态失败: {e}")
                failed_files.append(file_id)

        return {
            "success": success_files,
            "failed": failed_files
        }

    def batch_move_from_staging(self, file_ids):
        """批量将文件从中转状态移动到正常状态

        Args:
            file_ids: 文件ID列表

        Returns:
            dict: 包含成功和失败文件ID的字典
        """
        success_files = []
        failed_files = []

        for file_id in file_ids:
            try:
                if self.move_from_staging(file_id):
                    success_files.append(file_id)
                else:
                    failed_files.append(file_id)
            except Exception as e:
                print(f"批量移出文件 {file_id} 中转状态失败: {e}")
                failed_files.append(file_id)

        return {
            "success": success_files,
            "failed": failed_files
        }