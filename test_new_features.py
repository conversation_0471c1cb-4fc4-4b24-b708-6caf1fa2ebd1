"""
测试新功能：
1. 标签导航调试信息
2. 新的自动标签规则条件类型
"""

def test_auto_tag_new_conditions():
    """测试新的自动标签条件类型"""
    print("🧪 测试新的自动标签条件类型...")
    
    try:
        from smartvault.services.auto_tag_service import AutoTagRule, ConditionType
        
        # 测试文件大小条件
        print("  1. 测试文件大小条件")
        
        # 创建文件大小规则
        size_rule = AutoTagRule(
            name="大文件规则",
            condition_type=ConditionType.FILE_SIZE,
            condition_value=">10MB",
            tag_names=["大文件"],
            priority=5
        )
        
        # 测试文件信息
        large_file = {"name": "video.mp4", "size": 50 * 1024 * 1024}  # 50MB
        small_file = {"name": "doc.txt", "size": 1024}  # 1KB
        
        assert size_rule.matches(large_file) == True, "大文件应该匹配"
        assert size_rule.matches(small_file) == False, "小文件不应该匹配"
        print("    ✅ 文件大小条件测试通过")
        
        # 测试正则表达式条件
        print("  2. 测试文件名正则表达式条件")
        
        regex_rule = AutoTagRule(
            name="报告文件规则",
            condition_type=ConditionType.FILE_NAME_REGEX,
            condition_value=r"^report_\d{4}\.pdf$",
            tag_names=["报告"],
            priority=5
        )
        
        # 测试文件
        report_file = {"name": "report_2024.pdf", "size": 1024}
        other_file = {"name": "document.pdf", "size": 1024}
        
        assert regex_rule.matches(report_file) == True, "报告文件应该匹配"
        assert regex_rule.matches(other_file) == False, "其他文件不应该匹配"
        print("    ✅ 正则表达式条件测试通过")
        
        # 测试不同的大小比较操作符
        print("  3. 测试不同的大小比较操作符")
        
        test_cases = [
            (">5MB", {"size": 10 * 1024 * 1024}, True),   # 10MB > 5MB
            ("<5MB", {"size": 3 * 1024 * 1024}, True),    # 3MB < 5MB
            (">=5MB", {"size": 5 * 1024 * 1024}, True),   # 5MB >= 5MB
            ("<=5MB", {"size": 5 * 1024 * 1024}, True),   # 5MB <= 5MB
            ("=5MB", {"size": 5 * 1024 * 1024}, True),    # 5MB = 5MB (±1KB)
        ]
        
        for condition, file_info, expected in test_cases:
            rule = AutoTagRule(
                name="测试规则",
                condition_type=ConditionType.FILE_SIZE,
                condition_value=condition,
                tag_names=["测试"],
                priority=5
            )
            file_info["name"] = "test.txt"
            result = rule.matches(file_info)
            assert result == expected, f"条件 {condition} 测试失败"
        
        print("    ✅ 大小比较操作符测试通过")
        
        print("  🎉 所有新条件类型测试通过！")
        return True
        
    except Exception as e:
        print(f"  ❌ 新条件类型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_tag_navigation_debug():
    """测试标签导航调试功能"""
    print("\n🧪 测试标签导航调试功能...")
    
    try:
        from smartvault.services.tag_service import TagService
        
        # 创建标签服务
        tag_service = TagService()
        
        # 创建测试标签
        tag_id = tag_service.create_tag("导航测试", color="#FF0000", weight=8)
        print(f"  创建测试标签: {tag_id}")
        
        # 测试标签层级查询（这是导航点击时调用的方法）
        files = tag_service.get_files_by_tag_hierarchy(tag_id)
        print(f"  标签文件查询: {len(files)} 个文件")
        
        # 测试标签信息获取
        tag = tag_service.get_tag_by_id(tag_id)
        print(f"  标签信息: {tag['name']}")
        
        print("  ✅ 标签导航相关功能正常")
        print("  💡 现在点击标签时应该能看到调试信息")
        return True
        
    except Exception as e:
        print(f"  ❌ 标签导航测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_ui_imports():
    """测试UI组件导入"""
    print("\n🧪 测试UI组件导入...")
    
    try:
        # 测试自动标签规则对话框
        from smartvault.ui.dialogs.auto_tag_rule_dialog import AutoTagRuleDialog
        print("  ✅ 自动标签规则对话框导入成功")
        
        # 测试标签选择对话框
        from smartvault.ui.dialogs.tag_selection_dialog import TagSelectionDialog
        print("  ✅ 标签选择对话框导入成功")
        
        # 测试标签导航面板
        from smartvault.ui.components.tag_navigation_panel import TagNavigationPanel
        print("  ✅ 标签导航面板导入成功")
        
        print("  🎉 所有UI组件导入正常！")
        return True
        
    except Exception as e:
        print(f"  ❌ UI组件导入失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 新功能测试")
    print("=" * 50)
    
    results = []
    
    # 测试1：UI组件导入
    results.append(test_ui_imports())
    
    # 测试2：新的自动标签条件类型
    results.append(test_auto_tag_new_conditions())
    
    # 测试3：标签导航调试
    results.append(test_tag_navigation_debug())
    
    # 总结
    print("\n📊 测试结果总结:")
    print(f"  通过测试: {sum(results)}/{len(results)}")
    
    if all(results):
        print("🎉 所有新功能测试通过！")
        print("\n📋 新功能说明:")
        print("  1. ✅ 标签导航已添加调试信息，点击标签时会显示详细日志")
        print("  2. ✅ 自动标签规则新增两种条件类型：")
        print("     - 文件大小：支持 >10MB, <500KB, >=1GB, <=100MB 等比较操作")
        print("     - 文件名正则表达式：支持严格的正则表达式匹配")
        print("  3. ✅ 所有UI组件正常工作，语法错误已修复")
    else:
        print("⚠️  部分测试未通过，可能需要进一步检查")
    
    return all(results)


if __name__ == "__main__":
    main()
