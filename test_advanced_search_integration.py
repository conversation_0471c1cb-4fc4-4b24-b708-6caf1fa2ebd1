#!/usr/bin/env python3
"""
测试高级搜索按钮集成
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from smartvault.ui.views.file_table_view import FileTableViewContainer
from smartvault.ui.dialogs.advanced_search_dialog import AdvancedSearchDialog


class TestMainWindow(QMainWindow):
    """测试主窗口"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("高级搜索按钮集成测试")
        self.resize(1000, 700)

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)

        # 创建文件表格视图
        self.file_view = FileTableViewContainer()
        layout.addWidget(self.file_view)

        # 模拟一些文件数据
        self.setup_test_data()

    def setup_test_data(self):
        """设置测试数据"""
        test_files = [
            {
                "id": "1",
                "name": "测试文档.txt",
                "size": 1024,
                "entry_type": "link",
                "added_at": "2024-05-24T10:00:00",
                "original_path": "C:/test/测试文档.txt",
                "library_path": ""
            },
            {
                "id": "2",
                "name": "报告.pdf",
                "size": 2048000,
                "entry_type": "copy",
                "added_at": "2024-05-23T15:30:00",
                "original_path": "C:/documents/报告.pdf",
                "library_path": "/library/报告.pdf"
            },
            {
                "id": "3",
                "name": "图片.jpg",
                "size": 512000,
                "entry_type": "move",
                "added_at": "2024-05-22T09:15:00",
                "original_path": "C:/images/图片.jpg",
                "library_path": "/library/图片.jpg"
            }
        ]

        self.file_view.set_files(test_files)

    def on_advanced_search(self):
        """高级搜索方法（由文件表格视图调用）"""
        print("\n🔍 高级搜索按钮被点击！")

        try:
            dialog = AdvancedSearchDialog(self)

            # 连接搜索完成信号
            dialog.search_completed.connect(self.on_search_completed)

            print("   ✅ 高级搜索对话框已打开")
            print("   📋 请测试以下功能：")
            print("      • 基本搜索选项卡")
            print("      • 多条件搜索选项卡")
            print("      • 保存的搜索选项卡")
            print("      • 搜索预览和执行")

            result = dialog.exec()

            if result == AdvancedSearchDialog.Accepted:
                print("   ✅ 搜索已确认")
            else:
                print("   ❌ 搜索已取消")

        except Exception as e:
            print(f"   ❌ 打开高级搜索对话框失败: {e}")
            import traceback
            traceback.print_exc()

    def on_search_completed(self, results):
        """搜索完成回调"""
        print(f"\n🔍 搜索完成，找到 {len(results)} 个结果")

        if results:
            print("   📋 搜索结果：")
            for i, file_info in enumerate(results):
                print(f"      {i+1}. {file_info['name']} ({file_info['size']} 字节)")

            # 更新文件视图显示搜索结果
            self.file_view.set_files(results)
            print("   ✅ 文件视图已更新为搜索结果")
        else:
            print("   📭 没有找到匹配的文件")


def test_advanced_search_integration():
    """测试高级搜索按钮集成"""
    print("🚀 测试高级搜索按钮集成")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    try:
        # 创建QApplication实例
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        print("\n🧪 测试1: 文件表格视图组件")

        # 创建文件表格视图
        file_view = FileTableViewContainer()

        # 验证高级搜索按钮存在
        assert hasattr(file_view, 'advanced_search_button')
        print("   ✅ 高级搜索按钮已添加到文件表格视图")

        # 验证按钮属性
        button = file_view.advanced_search_button
        assert button.text() == "高级搜索"
        assert button.maximumWidth() == 80
        print("   ✅ 高级搜索按钮属性正确")

        # 验证点击事件方法存在
        assert hasattr(file_view, 'on_advanced_search_clicked')
        print("   ✅ 高级搜索按钮点击事件方法存在")

        print("\n🧪 测试2: 主窗口集成")

        # 创建测试主窗口
        main_window = TestMainWindow()

        # 验证主窗口有高级搜索方法
        assert hasattr(main_window, 'on_advanced_search')
        print("   ✅ 主窗口高级搜索方法存在")

        # 验证文件视图在主窗口中
        assert hasattr(main_window, 'file_view')
        print("   ✅ 文件视图已集成到主窗口")

        print("\n🧪 测试3: 按钮样式和布局")

        # 检查按钮样式
        button_style = file_view.advanced_search_button.styleSheet()
        assert "background-color: #4CAF50" in button_style
        assert "color: white" in button_style
        print("   ✅ 高级搜索按钮样式正确")

        # 检查布局中的按钮位置 - 需要找到搜索控件的布局
        # 搜索控件在顶部工具栏中，需要遍历找到
        search_layout = None
        for i in range(file_view.top_toolbar_layout.count()):
            item = file_view.top_toolbar_layout.itemAt(i)
            if item and item.widget():
                widget = item.widget()
                if hasattr(widget, 'layout') and widget.layout():
                    layout = widget.layout()
                    # 检查是否包含高级搜索按钮
                    for j in range(layout.count()):
                        sub_item = layout.itemAt(j)
                        if sub_item and sub_item.widget() == file_view.advanced_search_button:
                            search_layout = layout
                            break
                if search_layout:
                    break
        button_index = -1
        for i in range(search_layout.count()):
            item = search_layout.itemAt(i)
            if item and item.widget() == file_view.advanced_search_button:
                button_index = i
                break

        assert button_index > 0  # 按钮应该在搜索框和下拉框之后
        print(f"   ✅ 高级搜索按钮在布局中的位置正确 (索引: {button_index})")

        print("\n🎉 高级搜索按钮集成测试全部通过！")
        print("✅ 高级搜索按钮已正确添加到文件表格视图")
        print("✅ 按钮样式和属性配置正确")
        print("✅ 点击事件处理机制完整")
        print("✅ 主窗口集成正常")

        return True

    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🚀 开始测试高级搜索按钮集成...")

    # 先运行自动化测试
    success = test_advanced_search_integration()

    if success:
        print("\n🎯 启动UI手动测试...")
        print("📋 测试说明：")
        print("   1. 程序将显示一个包含文件表格的窗口")
        print("   2. 在搜索框右侧可以看到绿色的'高级搜索'按钮")
        print("   3. 点击按钮将打开高级搜索对话框")
        print("   4. 测试搜索功能并验证结果显示")
        print()

        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        # 创建测试窗口
        window = TestMainWindow()
        window.show()

        print("✅ 测试窗口已显示")
        print("🔍 请点击'高级搜索'按钮测试功能")

        # 运行应用程序
        sys.exit(app.exec())
    else:
        print("\n❌ 自动化测试失败，跳过UI测试")
        sys.exit(1)


if __name__ == "__main__":
    main()
