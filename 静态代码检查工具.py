#!/usr/bin/env python3
"""
静态代码检查工具
专门检查拆分后的模块是否存在潜在问题
避免运行代码，只进行静态分析
"""

import os
import re
import sys

def check_data_role_usage():
    """检查数据角色使用是否正确"""
    print("=" * 60)
    print("🔍 检查数据角色使用")
    print("=" * 60)

    issues = []

    # 检查的文件列表
    files_to_check = [
        'smartvault/ui/main_window/clipboard_handler.py',
        'smartvault/ui/main_window/backup_manager.py'
    ]

    for file_path in files_to_check:
        if not os.path.exists(file_path):
            print(f"⚠️ 文件不存在: {file_path}")
            continue

        print(f"\n📄 检查文件: {file_path}")

        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')

        # 检查Qt.UserRole的使用
        user_role_pattern = r'Qt\.UserRole(?!\s*\+)'
        for i, line in enumerate(lines, 1):
            if re.search(user_role_pattern, line):
                # 检查是否是备用方案（向后兼容）
                context_start = max(0, i - 3)
                context_lines = lines[context_start:i + 2]
                is_fallback = any('备用方案' in line or '向后兼容' in line or 'fallback' in line.lower()
                                for line in context_lines)

                if is_fallback:
                    print(f"ℹ️ 第{i}行: Qt.UserRole作为备用方案使用（合理）")
                    print(f"   {line.strip()}")
                else:
                    print(f"⚠️ 第{i}行: 可能应该使用FileIdRole而非Qt.UserRole")
                    print(f"   {line.strip()}")
                    issues.append(f"{file_path}:{i} - 数据角色使用")

        # 检查FileIdRole的使用
        file_id_role_count = len(re.findall(r'FileIdRole', content))
        if file_id_role_count > 0:
            print(f"✅ 找到 {file_id_role_count} 处FileIdRole使用")

        # 检查model.data调用
        data_calls = re.findall(r'model\.data\([^)]+\)', content)
        for call in data_calls:
            print(f"📊 数据调用: {call}")

    return issues

def check_dependency_safety():
    """检查依赖安全性"""
    print("\n" + "=" * 60)
    print("🔗 检查依赖安全性")
    print("=" * 60)

    issues = []

    files_to_check = [
        'smartvault/ui/main_window/clipboard_handler.py',
        'smartvault/ui/main_window/backup_manager.py'
    ]

    # 需要检查的可选依赖
    optional_deps = [
        'toolbar_manager',
        'main_tab_widget',
        'switch_to_page'
    ]

    for file_path in files_to_check:
        if not os.path.exists(file_path):
            continue

        print(f"\n📄 检查文件: {file_path}")

        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')

        # 检查可选依赖的使用
        for dep in optional_deps:
            pattern = rf'\.{dep}(?!\s*=)'
            matches = []
            for i, line in enumerate(lines, 1):
                if re.search(pattern, line):
                    matches.append((i, line.strip()))

            if matches:
                print(f"\n🔍 依赖 '{dep}' 的使用:")
                for line_num, line_content in matches:
                    # 检查是否有hasattr保护
                    context_start = max(0, line_num - 3)
                    context_lines = lines[context_start:line_num + 2]
                    has_protection = any('hasattr' in line for line in context_lines)

                    if has_protection:
                        print(f"✅ 第{line_num}行: 有hasattr保护")
                    else:
                        print(f"⚠️ 第{line_num}行: 缺少hasattr保护")
                        print(f"   {line_content}")
                        issues.append(f"{file_path}:{line_num} - 缺少依赖保护")

    return issues

def check_signal_connections():
    """检查信号连接"""
    print("\n" + "=" * 60)
    print("📡 检查信号连接")
    print("=" * 60)

    issues = []

    # 检查core.py中的信号连接
    core_file = 'smartvault/ui/main_window/core.py'
    if os.path.exists(core_file):
        print(f"\n📄 检查文件: {core_file}")

        with open(core_file, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')

        # 查找信号连接
        connect_pattern = r'\.connect\('
        for i, line in enumerate(lines, 1):
            if re.search(connect_pattern, line):
                print(f"📡 第{i}行: {line.strip()}")

                # 检查是否连接到拆分的处理器
                if 'clipboard_handler' in line or 'backup_manager' in line:
                    print(f"✅ 连接到拆分的处理器")
                elif 'self.' in line and ('clipboard' in line or 'backup' in line):
                    print(f"⚠️ 可能需要更新为处理器方法")
                    issues.append(f"{core_file}:{i} - 信号连接可能需要更新")

    return issues

def check_import_statements():
    """检查导入语句"""
    print("\n" + "=" * 60)
    print("📦 检查导入语句")
    print("=" * 60)

    issues = []

    files_to_check = [
        'smartvault/ui/main_window/clipboard_handler.py',
        'smartvault/ui/main_window/backup_manager.py'
    ]

    for file_path in files_to_check:
        if not os.path.exists(file_path):
            continue

        print(f"\n📄 检查文件: {file_path}")

        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')

        # 检查导入语句
        import_lines = [line for line in lines if line.strip().startswith(('import ', 'from '))]

        for line in import_lines:
            print(f"📦 {line.strip()}")

        # 检查延迟导入
        lazy_imports = []
        for i, line in enumerate(lines, 1):
            if 'import ' in line and not line.strip().startswith(('import ', 'from ')):
                lazy_imports.append((i, line.strip()))

        if lazy_imports:
            print(f"\n🔄 延迟导入:")
            for line_num, line_content in lazy_imports:
                print(f"   第{line_num}行: {line_content}")

    return issues

def check_method_completeness():
    """检查方法完整性"""
    print("\n" + "=" * 60)
    print("🔧 检查方法完整性")
    print("=" * 60)

    issues = []

    # 检查备份管理器
    backup_file = 'smartvault/ui/main_window/backup_manager.py'
    if os.path.exists(backup_file):
        print(f"\n📄 检查备份管理器方法:")

        with open(backup_file, 'r', encoding='utf-8') as f:
            content = f.read()

        expected_methods = [
            'start_backup_service',
            'stop_backup_service',
            'get_backup_status',
            'setup_backup_status_display',
            'update_backup_status_display',
            'on_backup_completed',
            'on_backup_failed'
        ]

        for method in expected_methods:
            if f'def {method}(' in content:
                print(f"✅ {method}")
            else:
                print(f"❌ {method}")
                issues.append(f"{backup_file} - 缺少方法: {method}")

    # 检查剪贴板处理器
    clipboard_file = 'smartvault/ui/main_window/clipboard_handler.py'
    if os.path.exists(clipboard_file):
        print(f"\n📄 检查剪贴板处理器方法:")

        with open(clipboard_file, 'r', encoding='utf-8') as f:
            content = f.read()

        expected_methods = [
            'start_clipboard_monitor_if_enabled',
            'toggle_clipboard_monitor',
            'show_clipboard_demo',
            'on_clipboard_duplicate_found',
            'on_clipboard_open_file',
            '_locate_and_select_file'
        ]

        for method in expected_methods:
            if f'def {method}(' in content:
                print(f"✅ {method}")
            else:
                print(f"❌ {method}")
                issues.append(f"{clipboard_file} - 缺少方法: {method}")

    return issues

def main():
    """主检查函数"""
    print("🚀 开始静态代码检查")
    print("专门检查拆分后的模块是否存在潜在问题")

    all_issues = []

    # 执行各项检查
    all_issues.extend(check_data_role_usage())
    all_issues.extend(check_dependency_safety())
    all_issues.extend(check_signal_connections())
    all_issues.extend(check_import_statements())
    all_issues.extend(check_method_completeness())

    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 静态检查结果汇总")
    print("=" * 60)

    if not all_issues:
        print("🎉 静态检查未发现问题！")
        return True
    else:
        print(f"⚠️ 发现 {len(all_issues)} 个潜在问题:")
        for i, issue in enumerate(all_issues, 1):
            print(f"{i}. {issue}")

        print("\n💡 建议:")
        print("1. 优先修复数据角色使用问题")
        print("2. 添加依赖保护检查")
        print("3. 验证信号连接正确性")
        print("4. 确保方法完整性")

        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
