"""
颜色显示组件
提供颜色可视化显示功能
"""

from PySide6.QtWidgets import QWidget, QHBoxLayout, QLabel, QPushButton
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QColor, QPalette
from typing import Union


class ColorDisplayWidget(QWidget):
    """颜色显示组件

    显示颜色代码和对应的颜色块
    """

    color_changed = Signal(str)  # 颜色改变信号

    def __init__(self, color: Union[str, QColor] = "#CCCCCC",
                 show_text: bool = True,
                 editable: bool = False,
                 parent=None):
        """初始化颜色显示组件

        Args:
            color: 初始颜色
            show_text: 是否显示颜色代码文本
            editable: 是否可编辑（点击选择颜色）
            parent: 父组件
        """
        super().__init__(parent)

        if isinstance(color, str):
            self._color = QColor(color)
        elif isinstance(color, QColor):
            self._color = color
        else:
            self._color = QColor("#CCCCCC")  # 默认颜色
        self.show_text = show_text
        self.editable = editable

        self.init_ui()
        self.update_display()

    def init_ui(self):
        """初始化UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)

        # 颜色块
        self.color_block = QLabel()
        self.color_block.setFixedSize(20, 20)
        self.color_block.setStyleSheet("""
            QLabel {
                border: 1px solid #ccc;
                border-radius: 3px;
            }
        """)
        layout.addWidget(self.color_block)

        # 颜色代码文本（可选）
        if self.show_text:
            self.color_text = QLabel()
            self.color_text.setStyleSheet("color: #666666; font-family: monospace;")
            layout.addWidget(self.color_text)

        # 如果可编辑，设置点击事件
        if self.editable:
            self.color_block.mousePressEvent = self._on_color_block_clicked
            self.color_block.setCursor(Qt.PointingHandCursor)
            if self.show_text:
                self.color_text.mousePressEvent = self._on_color_block_clicked
                self.color_text.setCursor(Qt.PointingHandCursor)

    def _on_color_block_clicked(self, event):
        """颜色块点击事件"""
        if self.editable:
            from PySide6.QtWidgets import QColorDialog
            color = QColorDialog.getColor(self._color, self)
            if color.isValid():
                self.set_color(color)
                self.color_changed.emit(color.name())

    def set_color(self, color: Union[str, QColor]):
        """设置颜色

        Args:
            color: 新颜色
        """
        if isinstance(color, str):
            color = QColor(color)

        if color.isValid():
            self._color = color
            self.update_display()

    def get_color(self) -> QColor:
        """获取当前颜色

        Returns:
            QColor: 当前颜色
        """
        return self._color

    def get_color_name(self) -> str:
        """获取颜色代码

        Returns:
            str: 颜色代码（如 #3776AB）
        """
        return self._color.name()

    def update_display(self):
        """更新显示"""
        if not self._color.isValid():
            self._color = QColor("#CCCCCC")

        # 更新颜色块
        color_name = self._color.name()
        self.color_block.setStyleSheet(f"""
            QLabel {{
                background-color: {color_name};
                border: 1px solid #ccc;
                border-radius: 3px;
            }}
        """)

        # 更新颜色代码文本
        if self.show_text and hasattr(self, 'color_text'):
            self.color_text.setText(color_name)


class ColorPickerButton(QPushButton):
    """颜色选择按钮

    结合了颜色显示和选择功能的按钮
    """

    color_changed = Signal(str)  # 颜色改变信号

    def __init__(self, color: Union[str, QColor] = "#CCCCCC", parent=None):
        """初始化颜色选择按钮

        Args:
            color: 初始颜色
            parent: 父组件
        """
        super().__init__(parent)

        if isinstance(color, str):
            self._color = QColor(color)
        elif isinstance(color, QColor):
            self._color = color
        else:
            self._color = QColor("#CCCCCC")  # 默认颜色

        self.setFixedSize(60, 30)
        self.clicked.connect(self._choose_color)
        self.update_button_style()

    def _choose_color(self):
        """选择颜色"""
        from PySide6.QtWidgets import QColorDialog
        color = QColorDialog.getColor(self._color, self)
        if color.isValid():
            self.set_color(color)
            self.color_changed.emit(color.name())

    def set_color(self, color: Union[str, QColor]):
        """设置颜色

        Args:
            color: 新颜色
        """
        if isinstance(color, str):
            color = QColor(color)

        if color.isValid():
            self._color = color
            self.update_button_style()

    def get_color(self) -> QColor:
        """获取当前颜色

        Returns:
            QColor: 当前颜色
        """
        return self._color

    def get_color_name(self) -> str:
        """获取颜色代码

        Returns:
            str: 颜色代码
        """
        return self._color.name()

    def update_button_style(self):
        """更新按钮样式"""
        if not self._color.isValid():
            self._color = QColor("#CCCCCC")

        color_name = self._color.name()

        # 根据颜色亮度选择文字颜色
        from smartvault.ui.utils.color_utils import get_contrast_color
        text_color = get_contrast_color(self._color).name()

        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {color_name};
                color: {text_color};
                border: 1px solid #ccc;
                border-radius: 4px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                border: 2px solid #0078d4;
            }}
            QPushButton:pressed {{
                background-color: {color_name};
                border: 2px solid #005a9e;
            }}
        """)

        # 设置按钮文本为颜色代码
        self.setText(color_name.upper())
