"""
启动问题诊断工具
"""

import os
import sys
import sqlite3
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import psutil
    HAS_PSUTIL = True
except ImportError:
    HAS_PSUTIL = False
    print("⚠️  psutil模块不可用，部分检查将被跳过")


def check_processes():
    """检查SmartVault相关进程"""
    print("\n🔍 检查进程残留...")
    
    if not HAS_PSUTIL:
        print("⚠️  无法检查进程，psutil模块不可用")
        return
    
    try:
        smartvault_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] and 'python' in proc.info['name'].lower():
                    cmdline = proc.info['cmdline']
                    if cmdline and any('smartvault' in arg.lower() or 'run.py' in arg.lower() for arg in cmdline):
                        smartvault_processes.append(proc)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if smartvault_processes:
            print(f"❌ 发现 {len(smartvault_processes)} 个SmartVault相关进程:")
            for proc in smartvault_processes:
                print(f"  PID: {proc.pid}, 命令: {' '.join(proc.info['cmdline'])}")
            return False
        else:
            print("✅ 无SmartVault进程残留")
            return True
    except Exception as e:
        print(f"❌ 检查进程失败: {e}")
        return False


def check_database():
    """检查数据库状态"""
    print("\n🔍 检查数据库状态...")
    
    try:
        from smartvault.utils.config import load_config
        config = load_config()
        library_path = config["library_path"]
        db_path = os.path.join(library_path, "data", "smartvault.db")
        
        print(f"数据库路径: {db_path}")
        
        if not os.path.exists(db_path):
            print("✅ 数据库文件不存在，无锁定问题")
            return True
        
        # 检查数据库文件大小
        db_size = os.path.getsize(db_path)
        print(f"数据库文件大小: {db_size / 1024 / 1024:.2f} MB")
        
        # 检查WAL和SHM文件
        wal_path = db_path + "-wal"
        shm_path = db_path + "-shm"
        
        if os.path.exists(wal_path):
            wal_size = os.path.getsize(wal_path)
            print(f"WAL文件大小: {wal_size / 1024:.2f} KB")
            if wal_size > 1024 * 1024:  # 1MB
                print("⚠️  WAL文件过大，可能存在未提交的事务")
        
        if os.path.exists(shm_path):
            print("📁 发现SHM文件")
        
        # 尝试连接数据库
        try:
            conn = sqlite3.connect(db_path, timeout=2.0)
            cursor = conn.cursor()
            
            # 检查数据库完整性
            cursor.execute("PRAGMA integrity_check")
            integrity = cursor.fetchone()[0]
            
            if integrity == "ok":
                print("✅ 数据库完整性检查通过")
            else:
                print(f"❌ 数据库完整性问题: {integrity}")
                return False
            
            # 检查表数量
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            table_count = cursor.fetchone()[0]
            print(f"数据库表数量: {table_count}")
            
            # 检查文件数量
            cursor.execute("SELECT COUNT(*) FROM files")
            file_count = cursor.fetchone()[0]
            print(f"文件记录数量: {file_count}")
            
            conn.close()
            print("✅ 数据库连接正常")
            return True
            
        except sqlite3.OperationalError as e:
            if "locked" in str(e).lower():
                print("❌ 数据库被锁定")
                return False
            else:
                print(f"❌ 数据库连接错误: {e}")
                return False
                
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")
        return False


def check_config_files():
    """检查配置文件"""
    print("\n🔍 检查配置文件...")
    
    try:
        from smartvault.utils.config import get_app_data_dir, load_config
        
        app_data_dir = get_app_data_dir()
        config_file = os.path.join(app_data_dir, "config.json")
        
        print(f"配置文件路径: {config_file}")
        
        if not os.path.exists(config_file):
            print("⚠️  配置文件不存在")
            return True
        
        # 尝试加载配置
        config = load_config()
        print(f"智能文件库路径: {config.get('library_path', '未设置')}")
        
        # 检查智能文件库目录
        library_path = config.get("library_path")
        if library_path and os.path.exists(library_path):
            print("✅ 智能文件库目录存在")
        else:
            print("❌ 智能文件库目录不存在")
            return False
        
        print("✅ 配置文件正常")
        return True
        
    except Exception as e:
        print(f"❌ 检查配置文件失败: {e}")
        return False


def check_qt_settings():
    """检查Qt设置"""
    print("\n🔍 检查Qt设置...")
    
    try:
        from PySide6.QtCore import QSettings
        
        settings = QSettings("SmartVault", "MainWindow")
        
        # 检查几何设置
        geometry = settings.value("geometry")
        if geometry:
            print("📁 发现窗口几何设置")
        
        # 检查分割器设置
        splitter_sizes = settings.value("splitter_sizes")
        if splitter_sizes:
            print("📁 发现分割器设置")
        
        print("✅ Qt设置检查完成")
        return True
        
    except Exception as e:
        print(f"❌ 检查Qt设置失败: {e}")
        return False


def fix_database_lock():
    """修复数据库锁定"""
    print("\n🔧 尝试修复数据库锁定...")
    
    try:
        from smartvault.utils.config import load_config
        config = load_config()
        library_path = config["library_path"]
        db_path = os.path.join(library_path, "data", "smartvault.db")
        
        if not os.path.exists(db_path):
            print("✅ 数据库文件不存在，无需修复")
            return True
        
        # 创建备份
        backup_path = db_path + f".backup_{int(time.time())}"
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"✅ 已创建数据库备份: {backup_path}")
        
        # 删除WAL和SHM文件
        wal_path = db_path + "-wal"
        shm_path = db_path + "-shm"
        
        if os.path.exists(wal_path):
            os.remove(wal_path)
            print("✅ 已删除WAL文件")
        
        if os.path.exists(shm_path):
            os.remove(shm_path)
            print("✅ 已删除SHM文件")
        
        # 尝试重新连接数据库
        conn = sqlite3.connect(db_path, timeout=5.0)
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("VACUUM")  # 清理数据库
        conn.close()
        
        print("✅ 数据库锁定修复完成")
        return True
        
    except Exception as e:
        print(f"❌ 修复数据库锁定失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 SmartVault 启动问题诊断工具")
    print("=" * 50)
    
    issues = []
    
    # 检查各种问题
    if not check_processes():
        issues.append("进程残留")
    
    if not check_database():
        issues.append("数据库问题")
    
    if not check_config_files():
        issues.append("配置文件问题")
    
    if not check_qt_settings():
        issues.append("Qt设置问题")
    
    # 总结
    print("\n" + "=" * 50)
    if issues:
        print(f"❌ 发现 {len(issues)} 个问题:")
        for issue in issues:
            print(f"  - {issue}")
        
        # 尝试修复
        print("\n🔧 尝试自动修复...")
        if "数据库问题" in issues:
            fix_database_lock()
        
        print("\n💡 建议:")
        print("1. 重启计算机以清理所有残留进程和资源")
        print("2. 如果问题持续，考虑重新安装应用程序")
        print("3. 检查磁盘空间是否充足")
        
    else:
        print("✅ 未发现明显问题")
        print("💡 如果仍然无法启动，可能是其他原因:")
        print("1. 系统资源不足")
        print("2. 防病毒软件干扰")
        print("3. Python环境问题")


if __name__ == "__main__":
    main()
