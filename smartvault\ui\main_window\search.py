"""
主窗口搜索功能
"""

from PySide6.QtWidgets import QLineEdit, QHBoxLayout, QWidget, QMessageBox


class SearchFeatureMixin:
    """搜索功能混入类"""

    def __init__(self):
        """初始化搜索功能"""
        # 这个方法会在MainWindowCore的__init__之后被调用
        self.search_box = None  # 保留search_box属性避免报错

    def on_search(self, query=None):
        """执行搜索

        Args:
            query: 搜索查询字符串，如果为None则显示所有文件
        """
        if query is None or not query:
            # 显示所有文件
            self.load_initial_data()
            return

        # 使用后台线程执行搜索
        from PySide6.QtCore import QThread, Signal, QObject

        class SearchWorker(QObject):
            finished = Signal(list, Exception)

            def __init__(self, query):
                super().__init__()
                self.query = query

            def run(self):
                try:
                    from smartvault.services.search_service import SearchService
                    search_service = SearchService()
                    results = search_service.search_files(self.query)
                    self.finished.emit(results, None)
                except Exception as e:
                    self.finished.emit([], e)

        # 显示搜索状态
        self.statusBar().showMessage(f"正在搜索 '{query}'...")

        # 创建并启动搜索线程
        self.search_thread = QThread()
        self.search_worker = SearchWorker(query)
        self.search_worker.moveToThread(self.search_thread)
        self.search_worker.finished.connect(self._on_search_finished)
        self.search_thread.started.connect(self.search_worker.run)
        self.search_thread.start()

    def _on_search_finished(self, results, error):
        """搜索完成回调"""
        try:
            if error:
                raise error

            # 显示搜索结果
            self.file_view.set_files(results)

            # 更新状态栏
            self.statusBar().showMessage(f"搜索完成，找到 {len(results)} 个结果")
        except Exception as e:
            QMessageBox.critical(self, "搜索失败", f"搜索失败: {e}")
            self.statusBar().showMessage("搜索失败")
        finally:
            # 清理线程
            self.search_thread.quit()
            self.search_thread.wait()

    def on_advanced_search(self):
        """高级搜索"""
        try:
            from smartvault.ui.dialogs.advanced_search_dialog import AdvancedSearchDialog

            dialog = AdvancedSearchDialog(self)

            # 连接搜索完成信号
            dialog.search_completed.connect(self._on_advanced_search_completed)

            # 显示对话框
            dialog.exec()

        except Exception as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "错误", f"打开高级搜索失败: {e}")

    def _on_advanced_search_completed(self, results):
        """高级搜索完成回调

        Args:
            results: 搜索结果列表
        """
        try:
            # 显示搜索结果
            self.file_view.set_files(results)

            # 更新状态栏
            self.statusBar().showMessage(f"高级搜索完成，找到 {len(results)} 个结果")

        except Exception as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "错误", f"显示搜索结果失败: {e}")
