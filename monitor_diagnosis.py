#!/usr/bin/env python3
"""
监控状态持久化问题诊断脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def diagnose_monitor_persistence():
    """诊断监控状态持久化问题"""
    print("=== 监控状态持久化问题诊断 ===\n")
    
    # 1. 检查配置文件功能
    print("1. 检查配置文件功能")
    try:
        from smartvault.utils.config import save_monitor_status, get_monitor_status, load_config, get_config_path
        
        config_path = get_config_path()
        print(f"   配置文件路径: {config_path}")
        print(f"   配置文件存在: {os.path.exists(config_path)}")
        
        # 读取当前配置
        config = load_config()
        print(f"   当前配置: {config}")
        
        # 测试状态保存和读取
        print("\n   测试状态保存和读取:")
        original_status = get_monitor_status()
        print(f"   原始状态: {original_status}")
        
        # 保存相反状态
        new_status = not original_status
        save_monitor_status(new_status)
        read_status = get_monitor_status()
        print(f"   保存状态: {new_status}, 读取状态: {read_status}")
        
        # 恢复原始状态
        save_monitor_status(original_status)
        
        if read_status == new_status:
            print("   ✅ 配置文件状态保存功能正常")
        else:
            print("   ❌ 配置文件状态保存功能异常")
            
    except Exception as e:
        print(f"   ❌ 配置文件功能检查失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 2. 检查监控服务数据库状态
    print("\n2. 检查监控服务数据库状态")
    try:
        from smartvault.services.file_monitor_service import FileMonitorService
        
        monitor_service = FileMonitorService()
        
        # 获取所有监控配置
        all_monitors = monitor_service.get_all_monitors()
        print(f"   数据库中监控配置数量: {len(all_monitors)}")
        
        for i, config in enumerate(all_monitors):
            print(f"   监控 {i+1}: {config['folder_path']}")
            print(f"     is_active: {config['is_active']}")
            print(f"     auto_add: {config['auto_add']}")
            
        # 检查活动监控
        active_monitors = [config for config in all_monitors if config.get('is_active', False)]
        print(f"   数据库中活动监控数量: {len(active_monitors)}")
        
        # 检查运行中监控
        stats = monitor_service.get_monitor_statistics()
        print(f"   运行中监控数量: {stats['running_monitors']}")
        print(f"   总监控数量: {stats['total_monitors']}")
        
    except Exception as e:
        print(f"   ❌ 监控服务检查失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 3. 检查启动逻辑
    print("\n3. 检查启动逻辑")
    try:
        from smartvault.ui.main_window.core import MainWindowCore
        
        # 模拟启动逻辑检查
        print("   检查 start_configured_monitors 方法存在性...")
        if hasattr(MainWindowCore, 'start_configured_monitors'):
            print("   ✅ start_configured_monitors 方法存在")
        else:
            print("   ❌ start_configured_monitors 方法不存在")
            
        # 检查配置读取逻辑
        from smartvault.utils.config import get_monitor_status
        monitor_enabled = get_monitor_status()
        print(f"   当前配置中监控启用状态: {monitor_enabled}")
        
    except Exception as e:
        print(f"   ❌ 启动逻辑检查失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 4. 分析问题原因
    print("\n4. 问题分析")
    print("   可能的问题原因:")
    print("   a) 配置文件状态与数据库状态不同步")
    print("   b) 启动时只检查数据库 is_active 字段，忽略配置文件状态")
    print("   c) 工具栏切换状态时没有同时更新数据库 is_active 字段")
    
    # 5. 检查关键逻辑
    print("\n5. 检查关键逻辑")
    try:
        from smartvault.services.file_monitor_service import FileMonitorService
        
        monitor_service = FileMonitorService()
        all_monitors = monitor_service.get_all_monitors()
        
        print("   当前数据库状态:")
        for config in all_monitors:
            print(f"     {config['folder_path']}: is_active={config['is_active']}")
            
        from smartvault.utils.config import get_monitor_status
        config_status = get_monitor_status()
        print(f"   配置文件状态: {config_status}")
        
        print("\n   问题诊断:")
        if config_status and not any(config['is_active'] for config in all_monitors):
            print("   ❌ 发现问题: 配置文件显示启用，但数据库中所有监控都是非活动状态")
            print("   解决方案: 需要在启动时同步配置文件状态到数据库")
        elif not config_status and any(config['is_active'] for config in all_monitors):
            print("   ❌ 发现问题: 配置文件显示禁用，但数据库中有活动监控")
            print("   解决方案: 需要在启动时同步配置文件状态到数据库")
        else:
            print("   ✅ 配置文件与数据库状态一致")
            
    except Exception as e:
        print(f"   ❌ 关键逻辑检查失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    diagnose_monitor_persistence()

if __name__ == "__main__":
    main()
