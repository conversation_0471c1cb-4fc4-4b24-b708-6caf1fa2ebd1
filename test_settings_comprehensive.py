#!/usr/bin/env python3
"""
设置对话框综合测试脚本
测试重构后的设置页面各个选项卡的功能
"""

import sys
import os
import time
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import QTimer
from smartvault.ui.main_window import MainWindow
from smartvault.ui.dialogs import SettingsDialog

class SettingsTestRunner:
    """设置对话框测试运行器"""
    
    def __init__(self):
        self.app = QApplication.instance()
        if self.app is None:
            self.app = QApplication(sys.argv)
        
        self.main_window = None
        self.settings_dialog = None
        self.test_results = {}
        
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("🧪 开始设置对话框综合测试...")
        
        try:
            # 1. 初始化测试环境
            self.setup_test_environment()
            
            # 2. 测试各个页面
            self.test_all_pages()
            
            # 3. 显示测试结果
            self.show_test_results()
            
            # 4. 进行手动测试
            self.start_manual_test()
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    def setup_test_environment(self):
        """设置测试环境"""
        print("\n1️⃣ 设置测试环境...")
        
        # 创建主窗口
        self.main_window = MainWindow()
        print("   ✅ 主窗口已创建")
        
        # 创建设置对话框
        self.settings_dialog = SettingsDialog(self.main_window)
        self.settings_dialog.set_monitor_service(self.main_window.monitor_service)
        print("   ✅ 设置对话框已创建")
        
    def test_all_pages(self):
        """测试所有页面"""
        print("\n2️⃣ 测试所有设置页面...")
        
        pages_to_test = [
            ('library', '文件库设置'),
            ('monitor', '文件监控设置'),
            ('ui', 'UI设置'),
            ('search', '搜索设置'),
            ('auto_tag', '自动标签设置'),
            ('advanced', '高级设置')
        ]
        
        for page_key, page_title in pages_to_test:
            self.test_single_page(page_key, page_title)
    
    def test_single_page(self, page_key, page_title):
        """测试单个页面"""
        print(f"\n   📋 测试 {page_title}...")
        
        try:
            page = self.settings_dialog.pages.get(page_key)
            if not page:
                self.test_results[page_key] = {'status': 'FAIL', 'error': '页面未找到'}
                print(f"      ❌ 页面未找到")
                return
            
            # 测试基本功能
            tests = {
                'page_creation': self.test_page_creation(page),
                'settings_load': self.test_settings_load(page),
                'settings_save': self.test_settings_save(page),
                'validation': self.test_validation(page),
                'page_title': self.test_page_title(page, page_title)
            }
            
            # 特殊测试
            if page_key == 'monitor':
                tests['monitor_service'] = self.test_monitor_service(page)
                tests['monitor_list'] = self.test_monitor_list(page)
            
            # 统计结果
            passed = sum(1 for result in tests.values() if result)
            total = len(tests)
            
            self.test_results[page_key] = {
                'status': 'PASS' if passed == total else 'PARTIAL',
                'passed': passed,
                'total': total,
                'details': tests
            }
            
            print(f"      ✅ {passed}/{total} 测试通过")
            
        except Exception as e:
            self.test_results[page_key] = {'status': 'ERROR', 'error': str(e)}
            print(f"      ❌ 测试出错: {e}")
    
    def test_page_creation(self, page):
        """测试页面创建"""
        return page is not None
    
    def test_settings_load(self, page):
        """测试设置加载"""
        try:
            config = {'test_key': 'test_value'}
            page.load_settings(config)
            return True
        except Exception:
            return False
    
    def test_settings_save(self, page):
        """测试设置保存"""
        try:
            settings = page.save_settings()
            return isinstance(settings, dict)
        except Exception:
            return False
    
    def test_validation(self, page):
        """测试验证功能"""
        try:
            is_valid, error_msg = page.validate_settings()
            return isinstance(is_valid, bool) and isinstance(error_msg, str)
        except Exception:
            return False
    
    def test_page_title(self, page, expected_title):
        """测试页面标题"""
        try:
            title = page.get_page_title()
            return title == expected_title
        except Exception:
            return False
    
    def test_monitor_service(self, page):
        """测试监控服务设置"""
        return page.monitor_service is not None
    
    def test_monitor_list(self, page):
        """测试监控列表"""
        try:
            table = page.monitor_table
            return table.rowCount() >= 0  # 至少不出错
        except Exception:
            return False
    
    def show_test_results(self):
        """显示测试结果"""
        print("\n3️⃣ 测试结果汇总...")
        
        total_pages = len(self.test_results)
        passed_pages = sum(1 for result in self.test_results.values() 
                          if result.get('status') == 'PASS')
        
        print(f"   📊 总体结果: {passed_pages}/{total_pages} 页面完全通过")
        
        for page_key, result in self.test_results.items():
            status = result.get('status', 'UNKNOWN')
            if status == 'PASS':
                print(f"   ✅ {page_key}: 全部通过")
            elif status == 'PARTIAL':
                passed = result.get('passed', 0)
                total = result.get('total', 0)
                print(f"   ⚠️ {page_key}: 部分通过 ({passed}/{total})")
            else:
                error = result.get('error', '未知错误')
                print(f"   ❌ {page_key}: 失败 - {error}")
    
    def start_manual_test(self):
        """开始手动测试"""
        print("\n4️⃣ 开始手动测试...")
        print("   💡 设置对话框将打开，请手动测试以下功能：")
        print("      1. 切换各个选项卡")
        print("      2. 修改设置项")
        print("      3. 点击确定/取消按钮")
        print("      4. 验证设置是否正确保存")
        
        # 显示对话框
        self.settings_dialog.show()
        
        # 设置定时器提示
        QTimer.singleShot(2000, self.show_manual_test_tips)
    
    def show_manual_test_tips(self):
        """显示手动测试提示"""
        tips = """
手动测试清单：

📋 文件库设置:
  □ 选择文件库路径
  □ 创建新文件库
  □ 验证路径有效性

📋 文件监控设置:
  □ 查看监控配置列表
  □ 添加新监控文件夹
  □ 编辑监控配置
  □ 删除监控配置

📋 UI设置:
  □ 切换主题
  □ 调整字体大小
  □ 修改界面选项

📋 搜索设置:
  □ 设置默认搜索范围
  □ 调整搜索选项
  □ 修改历史记录设置

📋 自动标签设置:
  □ 配置自动标签规则
  □ 测试规则匹配

📋 高级设置:
  □ 调整缓存设置
  □ 修改日志级别
  □ 其他高级选项

完成测试后请关闭对话框。
        """
        
        msg = QMessageBox(self.settings_dialog)
        msg.setWindowTitle("手动测试清单")
        msg.setText(tips)
        msg.setStandardButtons(QMessageBox.Ok)
        msg.exec()

def main():
    """主函数"""
    tester = SettingsTestRunner()
    tester.run_comprehensive_test()
    
    # 运行应用程序
    sys.exit(tester.app.exec())

if __name__ == "__main__":
    main()
