#!/usr/bin/env python3
"""
第二阶段：自定义文件夹交互增强功能测试

测试内容：
1. 新建文件夹后的自动刷新和选中
2. 文件右键菜单"添加到文件夹"功能
3. 文件右键菜单"移动到文件夹"功能
4. 批量文件操作
5. 导航面板的文件夹管理
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMessageBox, QInputDialog
from PySide6.QtCore import QTimer, Qt
from PySide6.QtTest import QTest

from smartvault.ui.main_window import MainWindow
from smartvault.services.tag_service import TagService
from smartvault.services.file import FileService


class Stage2FolderEnhancementTester:
    """第二阶段文件夹增强功能测试器"""

    def __init__(self):
        self.app = QApplication.instance() or QApplication(sys.argv)
        self.main_window = None
        self.tag_service = None
        self.file_service = None
        self.test_results = []

    def setup(self):
        """设置测试环境"""
        try:
            print("🔧 设置测试环境...")

            # 创建主窗口
            self.main_window = MainWindow()
            self.tag_service = self.main_window.tag_service
            self.file_service = self.main_window.file_service

            # 显示主窗口
            self.main_window.show()
            QTest.qWait(1000)  # 等待界面加载

            print("✅ 测试环境设置完成")
            return True

        except Exception as e:
            print(f"❌ 设置测试环境失败: {e}")
            return False

    def test_create_folder_with_auto_refresh(self):
        """测试新建文件夹后的自动刷新和选中功能"""
        print("\n📁 测试1: 新建文件夹自动刷新和选中")

        try:
            # 获取导航面板
            navigation_panel = self.main_window.navigation_panel

            # 记录创建前的文件夹数量
            folder_tags_before = self.tag_service.get_folder_tags()
            folder_count_before = len(folder_tags_before)
            print(f"创建前文件夹数量: {folder_count_before}")

            # 创建测试文件夹
            test_folder_name = f"测试文件夹_{int(time.time())}"
            tag_id = self.tag_service.create_folder_tag(test_folder_name)

            # 调用增强版的添加文件夹方法
            navigation_panel.refresh_folder_tree()
            navigation_panel.select_folder_by_tag_id(tag_id)

            QTest.qWait(500)  # 等待界面更新

            # 验证文件夹是否创建成功
            folder_tags_after = self.tag_service.get_folder_tags()
            folder_count_after = len(folder_tags_after)

            if folder_count_after > folder_count_before:
                print(f"✅ 文件夹创建成功: {test_folder_name}")
                print(f"✅ 文件夹数量增加: {folder_count_before} -> {folder_count_after}")

                # 验证文件夹是否在导航面板中显示
                # 这里可以添加更多的UI验证逻辑

                self.test_results.append(("新建文件夹自动刷新", True, "文件夹创建并刷新成功"))
                return True
            else:
                print("❌ 文件夹创建失败")
                self.test_results.append(("新建文件夹自动刷新", False, "文件夹创建失败"))
                return False

        except Exception as e:
            print(f"❌ 测试新建文件夹失败: {e}")
            self.test_results.append(("新建文件夹自动刷新", False, f"异常: {e}"))
            return False

    def test_add_files_to_folder_menu(self):
        """测试文件右键菜单"添加到文件夹"功能"""
        print("\n📂 测试2: 文件右键菜单添加到文件夹")

        try:
            # 确保有文件夹可用
            folder_tags = self.tag_service.get_folder_tags()
            if not folder_tags:
                # 创建一个测试文件夹
                test_folder_name = f"测试文件夹_{int(time.time())}"
                folder_tag_id = self.tag_service.create_folder_tag(test_folder_name)
                folder_tags = [{'id': folder_tag_id, 'name': f"📁{test_folder_name}"}]

            # 获取文件视图
            file_view = self.main_window.file_view.table_view

            # 检查是否有文件
            if file_view.model.rowCount() == 0:
                print("❌ 没有文件可供测试")
                self.test_results.append(("添加到文件夹菜单", False, "没有文件可供测试"))
                return False

            # 模拟选择第一个文件
            first_index = file_view.model.index(0, 0)
            file_view.setCurrentIndex(first_index)
            file_id = file_view.model.data(first_index, file_view.model.FileIdRole)

            if not file_id:
                print("❌ 无法获取文件ID")
                self.test_results.append(("添加到文件夹菜单", False, "无法获取文件ID"))
                return False

            print(f"选中文件ID: {file_id}")

            # 测试添加文件到文件夹的功能
            folder_tag_id = folder_tags[0]['id']

            # 记录添加前的关联数量
            file_tags_before = self.tag_service.get_file_tags(file_id)

            # 调用添加文件到文件夹的方法
            success = self.tag_service.add_tag_to_file(file_id, folder_tag_id)

            if success:
                # 验证关联是否成功
                file_tags_after = self.tag_service.get_file_tags(file_id)

                if len(file_tags_after) > len(file_tags_before):
                    print("✅ 文件成功添加到文件夹")
                    self.test_results.append(("添加到文件夹菜单", True, "文件成功添加到文件夹"))
                    return True
                else:
                    print("❌ 文件添加到文件夹失败")
                    self.test_results.append(("添加到文件夹菜单", False, "文件添加失败"))
                    return False
            else:
                print("❌ 添加文件到文件夹操作失败")
                self.test_results.append(("添加到文件夹菜单", False, "添加操作失败"))
                return False

        except Exception as e:
            print(f"❌ 测试添加到文件夹失败: {e}")
            self.test_results.append(("添加到文件夹菜单", False, f"异常: {e}"))
            return False

    def test_move_files_to_folder_menu(self):
        """测试文件右键菜单"移动到文件夹"功能"""
        print("\n🔄 测试3: 文件右键菜单移动到文件夹")

        try:
            # 确保有文件夹可用
            folder_tags = self.tag_service.get_folder_tags()
            if not folder_tags:
                # 创建一个测试文件夹
                test_folder_name = f"移动测试文件夹_{int(time.time())}"
                folder_tag_id = self.tag_service.create_folder_tag(test_folder_name)
                folder_tags = [{'id': folder_tag_id, 'name': f"📁{test_folder_name}"}]

            # 获取文件视图
            file_view = self.main_window.file_view.table_view

            # 检查是否有文件
            if file_view.model.rowCount() == 0:
                print("❌ 没有文件可供测试")
                self.test_results.append(("移动到文件夹菜单", False, "没有文件可供测试"))
                return False

            # 模拟选择第一个文件
            first_index = file_view.model.index(0, 0)
            file_view.setCurrentIndex(first_index)
            file_id = file_view.model.data(first_index, file_view.model.FileIdRole)

            if not file_id:
                print("❌ 无法获取文件ID")
                self.test_results.append(("移动到文件夹菜单", False, "无法获取文件ID"))
                return False

            print(f"选中文件ID: {file_id}")

            # 测试移动文件到文件夹的功能
            folder_tag_id = folder_tags[0]['id']

            # 检查文件是否在中转状态
            file_info = self.file_service.get_file_by_id(file_id)
            was_in_staging = file_info and file_info.get("staging_status") == "staging"

            # 调用移动文件到文件夹的方法
            success = self.tag_service.add_tag_to_file(file_id, folder_tag_id)

            if success:
                # 如果文件原来在中转状态，验证是否已移出
                if was_in_staging:
                    self.file_service.move_from_staging(file_id)
                    updated_file_info = self.file_service.get_file_by_id(file_id)
                    current_staging_status = updated_file_info.get("staging_status", "normal")

                    if current_staging_status != "staging":
                        print("✅ 文件成功移动到文件夹并移出中转状态")
                        self.test_results.append(("移动到文件夹菜单", True, "文件移动成功并移出中转状态"))
                        return True
                    else:
                        print("❌ 文件移动成功但未移出中转状态")
                        self.test_results.append(("移动到文件夹菜单", False, "未移出中转状态"))
                        return False
                else:
                    print("✅ 文件成功移动到文件夹")
                    self.test_results.append(("移动到文件夹菜单", True, "文件移动成功"))
                    return True
            else:
                print("❌ 移动文件到文件夹操作失败")
                self.test_results.append(("移动到文件夹菜单", False, "移动操作失败"))
                return False

        except Exception as e:
            print(f"❌ 测试移动到文件夹失败: {e}")
            self.test_results.append(("移动到文件夹菜单", False, f"异常: {e}"))
            return False

    def test_batch_file_operations(self):
        """测试批量文件操作"""
        print("\n📦 测试4: 批量文件操作")

        try:
            # 确保有文件夹可用
            folder_tags = self.tag_service.get_folder_tags()
            if not folder_tags:
                # 创建一个测试文件夹
                test_folder_name = f"批量测试文件夹_{int(time.time())}"
                folder_tag_id = self.tag_service.create_folder_tag(test_folder_name)
                folder_tags = [{'id': folder_tag_id, 'name': f"📁{test_folder_name}"}]

            # 获取文件视图
            file_view = self.main_window.file_view.table_view

            # 检查是否有足够的文件
            if file_view.model.rowCount() < 3:
                print("❌ 文件数量不足，无法测试批量操作")
                self.test_results.append(("批量文件操作", False, "文件数量不足"))
                return False

            # 模拟选择前3个文件
            file_ids = []
            for i in range(3):
                index = file_view.model.index(i, 0)
                file_id = file_view.model.data(index, file_view.model.FileIdRole)
                if file_id:
                    file_ids.append(file_id)

            if len(file_ids) < 3:
                print("❌ 无法获取足够的文件ID")
                self.test_results.append(("批量文件操作", False, "无法获取足够的文件ID"))
                return False

            print(f"选中文件IDs: {file_ids}")

            # 测试批量添加文件到文件夹
            folder_tag_id = folder_tags[0]['id']
            success_count = 0

            for file_id in file_ids:
                if self.tag_service.add_tag_to_file(file_id, folder_tag_id):
                    success_count += 1

            if success_count == len(file_ids):
                print(f"✅ 批量操作成功: {success_count}/{len(file_ids)} 个文件添加到文件夹")
                self.test_results.append(("批量文件操作", True, f"成功处理 {success_count} 个文件"))
                return True
            else:
                print(f"⚠️ 批量操作部分成功: {success_count}/{len(file_ids)} 个文件添加到文件夹")
                self.test_results.append(("批量文件操作", False, f"仅成功处理 {success_count}/{len(file_ids)} 个文件"))
                return False

        except Exception as e:
            print(f"❌ 测试批量文件操作失败: {e}")
            self.test_results.append(("批量文件操作", False, f"异常: {e}"))
            return False

    def test_navigation_panel_management(self):
        """测试导航面板的文件夹管理"""
        print("\n🧭 测试5: 导航面板文件夹管理")

        try:
            navigation_panel = self.main_window.navigation_panel

            # 测试刷新文件夹树
            navigation_panel.refresh_folder_tree()
            QTest.qWait(500)

            # 获取当前文件夹列表
            folder_tags = self.tag_service.get_folder_tags()

            if folder_tags:
                print(f"✅ 导航面板显示 {len(folder_tags)} 个文件夹")

                # 测试选中文件夹功能
                first_folder = folder_tags[0]
                navigation_panel.select_folder_by_tag_id(first_folder['id'])
                QTest.qWait(500)

                print(f"✅ 成功选中文件夹: {first_folder['name']}")
                self.test_results.append(("导航面板管理", True, f"成功管理 {len(folder_tags)} 个文件夹"))
                return True
            else:
                print("⚠️ 导航面板中没有文件夹")
                self.test_results.append(("导航面板管理", True, "导航面板正常，但没有文件夹"))
                return True

        except Exception as e:
            print(f"❌ 测试导航面板管理失败: {e}")
            self.test_results.append(("导航面板管理", False, f"异常: {e}"))
            return False

    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始第二阶段：自定义文件夹交互增强功能测试")
        print("=" * 60)

        if not self.setup():
            return False

        # 运行所有测试
        tests = [
            self.test_create_folder_with_auto_refresh,
            self.test_add_files_to_folder_menu,
            self.test_move_files_to_folder_menu,
            self.test_batch_file_operations,
            self.test_navigation_panel_management
        ]

        passed = 0
        total = len(tests)

        for test in tests:
            if test():
                passed += 1
            QTest.qWait(1000)  # 测试间隔

        # 显示测试结果
        self.show_test_results(passed, total)

        return passed == total

    def show_test_results(self, passed, total):
        """显示测试结果"""
        print("\n" + "=" * 60)
        print("📊 第二阶段测试结果汇总")
        print("=" * 60)

        for test_name, success, message in self.test_results:
            status = "✅ 通过" if success else "❌ 失败"
            print(f"{status} {test_name}: {message}")

        print(f"\n总体结果: {passed}/{total} 个测试通过")

        if passed == total:
            print("🎉 所有测试通过！第二阶段功能实现成功！")
        else:
            print(f"⚠️ 有 {total - passed} 个测试失败，需要进一步检查")

        print("=" * 60)


def main():
    """主函数"""
    tester = Stage2FolderEnhancementTester()

    try:
        success = tester.run_all_tests()

        # 保持窗口打开以便手动验证
        print("\n💡 测试完成，窗口将保持打开状态以便手动验证...")
        print("请手动测试以下功能：")
        print("1. 右键点击导航面板的'自定义文件夹'，选择'添加文件夹'")
        print("2. 右键点击文件，查看'添加到文件夹'和'移动到文件夹'菜单")
        print("3. 验证新建文件夹后是否自动刷新和选中")
        print("4. 测试批量选择文件的文件夹操作")

        # 等待用户关闭窗口
        if tester.main_window:
            tester.app.exec()

        return 0 if success else 1

    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        return 1
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
