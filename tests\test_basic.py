"""
基本功能测试
"""

import os
import sys
import pytest
from PySide6.QtWidgets import QApplication

# 确保能够导入 smartvault 模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from smartvault.ui.main_window import MainWindow


@pytest.fixture
def app(qtbot):
    """创建应用程序实例"""
    application = QApplication.instance()
    if application is None:
        application = QApplication([])
    
    return application


@pytest.fixture
def main_window(app, qtbot):
    """创建主窗口实例"""
    window = MainWindow()
    qtbot.addWidget(window)
    return window


def test_window_title(main_window):
    """测试窗口标题"""
    assert main_window.windowTitle() == "SmartVault"


def test_window_size(main_window):
    """测试窗口大小"""
    assert main_window.width() >= 800
    assert main_window.height() >= 600
