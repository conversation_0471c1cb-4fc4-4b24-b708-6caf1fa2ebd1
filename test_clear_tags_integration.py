#!/usr/bin/env python3
"""
清除标签功能集成测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from smartvault.data.database import Database
from smartvault.services.tag_service import TagService
from smartvault.services.file.core import FileServiceCore

def test_clear_tags_integration():
    """测试清除标签功能的集成"""
    print("开始清除标签功能集成测试...")

    # 初始化服务
    print("初始化服务...")
    db_service = Database.create_from_config()
    tag_service = TagService()
    file_service = FileServiceCore()

    try:
        # 获取一些有标签的文件
        print("获取文件列表...")
        files = file_service.get_files(limit=5)

        if not files:
            print("没有找到文件，请先添加一些文件到智能文件库")
            return

        print(f"找到 {len(files)} 个文件")

        # 检查哪些文件有标签
        files_with_tags = []
        for file in files:
            file_id = file['id']
            tags = tag_service.get_file_tags(file_id)
            if tags:
                files_with_tags.append((file, tags))
                print(f"文件 {file['name']} 有 {len(tags)} 个标签: {[tag['name'] for tag in tags]}")

        if not files_with_tags:
            print("没有找到有标签的文件，请先为一些文件添加标签")
            return

        # 选择第一个有标签的文件进行清除测试
        test_file, original_tags = files_with_tags[0]
        file_id = test_file['id']
        file_name = test_file['name']

        print(f"\n测试文件: {file_name}")
        print(f"原始标签: {[tag['name'] for tag in original_tags]}")

        # 清除所有标签
        print("清除所有标签...")
        cleared_count = 0
        for tag in original_tags:
            if tag_service.remove_tag_from_file(file_id, tag['id']):
                cleared_count += 1
                print(f"  已移除标签: {tag['name']}")

        print(f"共清除了 {cleared_count} 个标签")

        # 验证标签已被清除
        print("验证标签清除结果...")
        remaining_tags = tag_service.get_file_tags(file_id)

        if not remaining_tags:
            print("✅ 标签清除成功！文件现在没有任何标签")
        else:
            print(f"❌ 标签清除失败！文件仍有 {len(remaining_tags)} 个标签: {[tag['name'] for tag in remaining_tags]}")

        # 恢复原始标签（可选）
        print("\n恢复原始标签...")
        for tag in original_tags:
            tag_service.add_tag_to_file(file_id, tag['id'])
            print(f"  已恢复标签: {tag['name']}")

        # 验证恢复结果
        restored_tags = tag_service.get_file_tags(file_id)
        print(f"恢复后的标签: {[tag['name'] for tag in restored_tags]}")

        print("\n✅ 清除标签功能集成测试完成！")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

    finally:
        if db_service:
            db_service.close()

def test_clear_tags_ui_integration():
    """测试清除标签UI集成"""
    print("\n开始清除标签UI集成测试...")

    try:
        from smartvault.ui.main_window.file_ops import FileOperationsMixin
        from unittest.mock import Mock

        # 创建模拟的主窗口
        mock_main_window = Mock()
        mock_main_window.show_status_message = Mock()

        # 混入文件操作功能
        class TestMainWindow(FileOperationsMixin):
            def __init__(self):
                self.show_status_message = mock_main_window.show_status_message

        test_window = TestMainWindow()

        # 模拟清除标签操作
        print("模拟清除标签操作...")

        # 这里我们只测试方法存在性，实际的UI测试需要完整的Qt环境
        assert hasattr(test_window, 'on_clear_file_tags'), "应该有清除标签方法"

        print("✅ 清除标签UI集成测试完成！")

    except Exception as e:
        print(f"❌ UI测试失败: {e}")

if __name__ == "__main__":
    test_clear_tags_integration()
    test_clear_tags_ui_integration()
