#!/usr/bin/env python3
"""
多条件自动标签规则编辑对话框
"""

import uuid
from typing import List, Optional, Union
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QGroupBox,
    QLineEdit, QCheckBox, QSpinBox, QComboBox, QPushButton,
    QLabel, QDialogButtonBox, QTreeWidget, QTreeWidgetItem,
    QMessageBox, QSplitter, QTextEdit, QFrame
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from smartvault.services.auto_tag_service import (
    AutoTagRule, Condition, ConditionGroup, ConditionType, LogicOperator
)


class ConditionTreeWidget(QTreeWidget):
    """条件树形控件"""

    condition_changed = Signal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setHeaderLabels(["条件", "操作"])
        self.setRootIsDecorated(True)
        self.setAlternatingRowColors(True)

        # 设置列宽
        self.setColumnWidth(0, 300)
        self.setColumnWidth(1, 100)

        # 连接信号
        self.itemChanged.connect(self.on_item_changed)

    def on_item_changed(self, item, column):
        """条件项改变时触发"""
        self.condition_changed.emit()

    def add_condition_group(self, parent_item=None, operator=LogicOperator.AND):
        """添加条件组"""
        group_item = QTreeWidgetItem()
        group_item.setText(0, f"条件组 ({operator.value.upper()})")
        group_item.setData(0, Qt.UserRole, {"type": "group", "operator": operator})

        if parent_item:
            parent_item.addChild(group_item)
        else:
            self.addTopLevelItem(group_item)

        # 添加操作按钮
        self._add_action_buttons(group_item)

        self.expandItem(group_item)
        self.condition_changed.emit()
        return group_item

    def add_condition(self, parent_item=None, condition_type=ConditionType.FILE_EXTENSION, value=""):
        """添加单个条件"""
        condition_item = QTreeWidgetItem()
        self._update_condition_text(condition_item, condition_type, value)
        condition_item.setData(0, Qt.UserRole, {
            "type": "condition",
            "condition_type": condition_type,
            "value": value
        })

        if parent_item:
            parent_item.addChild(condition_item)
        else:
            self.addTopLevelItem(condition_item)

        # 添加操作按钮
        self._add_action_buttons(condition_item)

        self.condition_changed.emit()
        return condition_item

    def _update_condition_text(self, item, condition_type, value):
        """更新条件文本"""
        type_names = {
            ConditionType.FILE_EXTENSION: "文件扩展名",
            ConditionType.FILE_NAME_PATTERN: "文件名模式",
            ConditionType.FILE_PATH_PATTERN: "文件路径模式",
            ConditionType.FILE_SIZE_RANGE: "文件大小范围",
            ConditionType.FILE_TYPE: "文件类型",
            ConditionType.FILE_SIZE: "文件大小",
            ConditionType.FILE_NAME_REGEX: "文件名正则表达式"
        }

        type_name = type_names.get(condition_type, "未知条件")
        item.setText(0, f"{type_name}: {value or '(未设置)'}")

    def _add_action_buttons(self, item):
        """添加操作按钮"""
        button_widget = QFrame()
        button_layout = QHBoxLayout(button_widget)
        button_layout.setContentsMargins(2, 2, 2, 2)

        # 编辑按钮
        edit_btn = QPushButton("编辑")
        edit_btn.setMaximumSize(50, 25)
        edit_btn.clicked.connect(lambda: self._edit_item(item))
        button_layout.addWidget(edit_btn)

        # 删除按钮
        delete_btn = QPushButton("删除")
        delete_btn.setMaximumSize(50, 25)
        delete_btn.clicked.connect(lambda: self._delete_item(item))
        button_layout.addWidget(delete_btn)

        self.setItemWidget(item, 1, button_widget)

    def _edit_item(self, item):
        """编辑条件项"""
        data = item.data(0, Qt.UserRole)
        if data["type"] == "group":
            # 编辑条件组
            self._edit_group(item, data)
        else:
            # 编辑单个条件
            self._edit_condition(item, data)

    def _edit_group(self, item, data):
        """编辑条件组"""
        # 简单的操作符切换
        current_op = data["operator"]
        new_op = LogicOperator.OR if current_op == LogicOperator.AND else LogicOperator.AND

        data["operator"] = new_op
        item.setData(0, Qt.UserRole, data)
        item.setText(0, f"条件组 ({new_op.value.upper()})")

        self.condition_changed.emit()

    def _edit_condition(self, item, data):
        """编辑单个条件"""
        from .condition_edit_dialog import ConditionEditDialog

        dialog = ConditionEditDialog(
            self,
            condition_type=data["condition_type"],
            condition_value=data["value"]
        )

        if dialog.exec_() == QDialog.Accepted:
            new_type, new_value = dialog.get_condition()
            data["condition_type"] = new_type
            data["value"] = new_value
            item.setData(0, Qt.UserRole, data)
            self._update_condition_text(item, new_type, new_value)
            self.condition_changed.emit()

    def _delete_item(self, item):
        """删除条件项"""
        parent = item.parent()
        if parent:
            parent.removeChild(item)
        else:
            index = self.indexOfTopLevelItem(item)
            self.takeTopLevelItem(index)

        self.condition_changed.emit()

    def get_condition_group(self) -> Optional[ConditionGroup]:
        """获取条件组结构"""
        if self.topLevelItemCount() == 0:
            return None

        if self.topLevelItemCount() == 1:
            return self._item_to_condition_group(self.topLevelItem(0))
        else:
            # 多个顶级项，创建一个AND组包含它们
            root_group = ConditionGroup(operator=LogicOperator.AND)
            for i in range(self.topLevelItemCount()):
                item = self.topLevelItem(i)
                condition = self._item_to_condition_group(item)
                if condition:
                    root_group.conditions.append(condition)
            return root_group

    def _item_to_condition_group(self, item) -> Union[ConditionGroup, Condition, None]:
        """将树项转换为条件组或条件"""
        data = item.data(0, Qt.UserRole)
        if not data:
            return None

        if data["type"] == "group":
            group = ConditionGroup(operator=data["operator"])
            for i in range(item.childCount()):
                child_item = item.child(i)
                child_condition = self._item_to_condition_group(child_item)
                if child_condition:
                    group.conditions.append(child_condition)
            return group
        else:
            return Condition(
                type=data["condition_type"],
                value=data["value"]
            )

    def set_condition_group(self, condition_group: ConditionGroup):
        """设置条件组"""
        self.clear()
        if condition_group:
            self._condition_group_to_item(condition_group, None)
        self.expandAll()

    def _condition_group_to_item(self, condition_group: Union[ConditionGroup, Condition], parent_item):
        """将条件组转换为树项"""
        if isinstance(condition_group, ConditionGroup):
            group_item = self.add_condition_group(parent_item, condition_group.operator)
            for condition in condition_group.conditions:
                self._condition_group_to_item(condition, group_item)
        else:
            self.add_condition(parent_item, condition_group.type, condition_group.value)


class MultiConditionAutoTagDialog(QDialog):
    """多条件自动标签规则编辑对话框"""

    def __init__(self, parent=None, rule=None):
        """初始化对话框

        Args:
            parent: 父窗口
            rule: 现有规则（编辑模式）
        """
        super().__init__(parent)
        self.rule = rule
        self.is_edit_mode = rule is not None

        self.setWindowTitle("编辑自动标签规则" if self.is_edit_mode else "添加自动标签规则")
        self.resize(800, 600)

        # 初始化标签服务（延迟导入）
        self.tag_service = None

        self.init_ui()

        if self.is_edit_mode:
            self.load_rule_data()

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)

        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)

        # 左侧：基本信息和标签设置
        left_widget = self._create_left_panel()
        splitter.addWidget(left_widget)

        # 右侧：条件设置
        right_widget = self._create_right_panel()
        splitter.addWidget(right_widget)

        # 设置分割器比例
        splitter.setSizes([300, 500])

        # 按钮
        buttons = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        buttons.accepted.connect(self.accept_rule)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)

    def _create_left_panel(self):
        """创建左侧面板"""
        widget = QFrame()
        layout = QVBoxLayout(widget)

        # 基本信息组
        basic_group = QGroupBox("基本信息")
        basic_layout = QFormLayout(basic_group)

        # 规则名称
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("请输入规则名称")
        basic_layout.addRow("规则名称:", self.name_edit)

        # 启用状态
        self.enabled_check = QCheckBox("启用此规则")
        self.enabled_check.setChecked(True)
        basic_layout.addRow("", self.enabled_check)

        # 优先级
        self.priority_spin = QSpinBox()
        self.priority_spin.setRange(0, 100)
        self.priority_spin.setValue(0)
        self.priority_spin.setToolTip("优先级越高的规则越先执行")
        basic_layout.addRow("优先级:", self.priority_spin)

        layout.addWidget(basic_group)

        # 标签设置组
        tags_group = QGroupBox("自动添加的标签")
        tags_layout = QVBoxLayout(tags_group)

        # 标签输入
        tags_input_layout = QHBoxLayout()
        self.tags_edit = QLineEdit()
        self.tags_edit.setPlaceholderText("请输入标签名称，多个标签用逗号分隔")
        tags_input_layout.addWidget(self.tags_edit)

        self.select_tags_button = QPushButton("选择标签")
        self.select_tags_button.clicked.connect(self.on_select_tags)
        tags_input_layout.addWidget(self.select_tags_button)

        tags_layout.addLayout(tags_input_layout)

        # 标签说明
        tags_help_label = QLabel("可以输入新标签名称，也可以点击\"选择标签\"从现有标签中选择")
        tags_help_label.setWordWrap(True)
        tags_help_label.setStyleSheet("color: #666666; font-size: 12px;")
        tags_layout.addWidget(tags_help_label)

        layout.addWidget(tags_group)

        # 预览组
        preview_group = QGroupBox("规则预览")
        preview_layout = QVBoxLayout(preview_group)

        self.preview_text = QTextEdit()
        self.preview_text.setMaximumHeight(150)
        self.preview_text.setReadOnly(True)
        self.preview_text.setStyleSheet("background-color: #f5f5f5; border: 1px solid #ddd;")
        preview_layout.addWidget(self.preview_text)

        layout.addWidget(preview_group)

        layout.addStretch()

        return widget

    def _create_right_panel(self):
        """创建右侧面板"""
        widget = QFrame()
        layout = QVBoxLayout(widget)

        # 条件设置组
        condition_group = QGroupBox("匹配条件")
        condition_layout = QVBoxLayout(condition_group)

        # 工具栏
        toolbar_layout = QHBoxLayout()

        add_condition_btn = QPushButton("添加条件")
        add_condition_btn.clicked.connect(self.add_condition)
        toolbar_layout.addWidget(add_condition_btn)

        add_group_btn = QPushButton("添加条件组")
        add_group_btn.clicked.connect(self.add_condition_group)
        toolbar_layout.addWidget(add_group_btn)

        toolbar_layout.addStretch()

        condition_layout.addLayout(toolbar_layout)

        # 条件树
        self.condition_tree = ConditionTreeWidget()
        self.condition_tree.condition_changed.connect(self.update_preview)
        condition_layout.addWidget(self.condition_tree)

        layout.addWidget(condition_group)

        return widget

    def add_condition(self):
        """添加条件"""
        # 获取当前选中的项
        current_item = self.condition_tree.currentItem()

        # 如果选中的是条件组，则添加到该组下
        if current_item and current_item.data(0, Qt.UserRole).get("type") == "group":
            parent_item = current_item
        else:
            parent_item = None

        self.condition_tree.add_condition(parent_item)

    def add_condition_group(self):
        """添加条件组"""
        # 获取当前选中的项
        current_item = self.condition_tree.currentItem()

        # 如果选中的是条件组，则添加到该组下
        if current_item and current_item.data(0, Qt.UserRole).get("type") == "group":
            parent_item = current_item
        else:
            parent_item = None

        self.condition_tree.add_condition_group(parent_item)

    def on_select_tags(self):
        """选择标签"""
        try:
            # 延迟导入标签服务
            if self.tag_service is None:
                from smartvault.services.tag_service import TagService
                self.tag_service = TagService()

            # 获取所有标签
            all_tags = self.tag_service.get_all_tags()

            if not all_tags:
                QMessageBox.information(self, "提示", "当前没有可用的标签，请先创建一些标签")
                return

            # 创建标签选择对话框
            from smartvault.ui.dialogs.tag_selection_dialog import TagSelectionDialog

            # 获取当前已选择的标签
            current_tags = [tag.strip() for tag in self.tags_edit.text().split(",") if tag.strip()]

            dialog = TagSelectionDialog(self, selected_tags=current_tags)
            if dialog.exec() == QDialog.Accepted:
                selected_tags = dialog.get_selected_tags()
                tag_names = [tag['name'] for tag in selected_tags]
                self.tags_edit.setText(", ".join(tag_names))

        except Exception as e:
            QMessageBox.warning(self, "错误", f"选择标签失败: {e}")

    def update_preview(self):
        """更新规则预览"""
        name = self.name_edit.text().strip()
        tags = self.tags_edit.text().strip()
        enabled = self.enabled_check.isChecked()
        priority = self.priority_spin.value()

        if not name:
            name = "未命名规则"

        preview_text = f"规则名称: {name}\n"
        preview_text += f"状态: {'启用' if enabled else '禁用'}\n"
        preview_text += f"优先级: {priority}\n"

        # 获取条件描述
        condition_group = self.condition_tree.get_condition_group()
        if condition_group:
            condition_desc = self._describe_condition_group(condition_group)
            preview_text += f"条件: {condition_desc}\n"
        else:
            preview_text += "条件: 无条件\n"

        preview_text += f"操作: 自动添加标签\"{tags or '(未设置)'}\""

        self.preview_text.setPlainText(preview_text)

    def _describe_condition_group(self, group: Union[ConditionGroup, Condition], level: int = 0) -> str:
        """描述条件组"""
        if isinstance(group, Condition):
            return self._describe_single_condition(group.type, group.value)

        if not group.conditions:
            return "无条件"

        operator_text = " 且 " if group.operator == LogicOperator.AND else " 或 "

        descriptions = []
        for condition in group.conditions:
            desc = self._describe_condition_group(condition, level + 1)
            if isinstance(condition, ConditionGroup) and len(group.conditions) > 1:
                descriptions.append(f"({desc})")
            else:
                descriptions.append(desc)

        return operator_text.join(descriptions)

    def _describe_single_condition(self, condition_type: ConditionType, condition_value: str) -> str:
        """描述单个条件"""
        from smartvault.utils.condition_utils import describe_single_condition
        return describe_single_condition(condition_type, condition_value)

    def load_rule_data(self):
        """加载规则数据"""
        if not self.rule:
            return

        self.name_edit.setText(self.rule.name)
        self.enabled_check.setChecked(self.rule.enabled)
        self.priority_spin.setValue(self.rule.priority)
        self.tags_edit.setText(', '.join(self.rule.tag_names))

        # 加载条件
        if self.rule.condition_group:
            self.condition_tree.set_condition_group(self.rule.condition_group)
        elif self.rule.condition_type and self.rule.condition_value is not None:
            # 兼容旧版本单条件
            single_condition = Condition(
                type=self.rule.condition_type,
                value=self.rule.condition_value
            )
            condition_group = ConditionGroup(
                operator=LogicOperator.AND,
                conditions=[single_condition]
            )
            self.condition_tree.set_condition_group(condition_group)

        self.update_preview()

    def accept_rule(self):
        """接受规则"""
        # 验证输入
        name = self.name_edit.text().strip()
        if not name:
            QMessageBox.warning(self, "错误", "请输入规则名称")
            return

        tags_text = self.tags_edit.text().strip()
        if not tags_text:
            QMessageBox.warning(self, "错误", "请输入要添加的标签")
            return

        tag_names = [tag.strip() for tag in tags_text.split(',') if tag.strip()]
        if not tag_names:
            QMessageBox.warning(self, "错误", "请输入有效的标签名称")
            return

        condition_group = self.condition_tree.get_condition_group()
        if not condition_group or not condition_group.conditions:
            QMessageBox.warning(self, "错误", "请至少添加一个匹配条件")
            return

        # 创建规则
        self.result_rule = AutoTagRule(
            id=self.rule.id if self.is_edit_mode else str(uuid.uuid4()),
            name=name,
            tag_names=tag_names,
            enabled=self.enabled_check.isChecked(),
            priority=self.priority_spin.value(),
            condition_group=condition_group
        )

        self.accept()

    def get_rule(self):
        """获取规则"""
        return getattr(self, 'result_rule', None)
