import sys
import os
sys.path.append('.')

try:
    from smartvault.services.clipboard_monitor_service import ClipboardMonitorService
    from smartvault.utils.config import load_config, save_config
    
    print("测试剪贴板监控服务改进...")
    
    # 创建剪贴板监控服务实例（不需要Qt应用）
    service = ClipboardMonitorService()
    
    # 测试用例
    test_cases = [
        {
            "name": "有扩展名的文件名",
            "text": "aint a party.mp3",
            "description": "应该被处理"
        },
        {
            "name": "没有扩展名的文本",
            "text": "aint a party",
            "description": "清洗规则关闭时应该被处理"
        },
        {
            "name": "复杂文件名",
            "text": "测试1文件_- Ab古아み.txt",
            "description": "应该被处理"
        },
        {
            "name": "过短文本",
            "text": "a",
            "description": "应该被跳过"
        },
        {
            "name": "空文本",
            "text": "",
            "description": "应该被跳过"
        }
    ]
    
    print("\n=== 测试1: 清洗规则启用时 ===")
    # 确保有启用的清洗规则
    service.cleaning_rules = service._get_default_rules()
    
    for test_case in test_cases:
        text = test_case["text"]
        cleaned = service._clean_filename(text)
        print(f"输入: '{text}'")
        print(f"输出: '{cleaned}'")
        print(f"预期: {test_case['description']}")
        print(f"结果: {'✅ 通过' if cleaned else '❌ 跳过'}")
        print("-" * 50)
    
    print("\n=== 测试2: 清洗规则关闭时 ===")
    # 禁用所有清洗规则
    service.cleaning_rules = []
    
    for test_case in test_cases:
        text = test_case["text"]
        cleaned = service._clean_filename(text)
        print(f"输入: '{text}'")
        print(f"输出: '{cleaned}'")
        print(f"预期: {test_case['description']}")
        
        # 对于清洗规则关闭的情况，调整预期
        if test_case["name"] == "没有扩展名的文本":
            expected = "现在应该被处理（规则关闭时）"
        else:
            expected = test_case['description']
        
        print(f"预期: {expected}")
        print(f"结果: {'✅ 通过' if cleaned else '❌ 跳过'}")
        print("-" * 50)
    
    print("\n=== 测试3: 检查配置中的清洗规则状态 ===")
    config = load_config()
    clipboard_config = config.get("clipboard", {})
    cleaning_config = clipboard_config.get("filename_cleaning", {})
    
    print(f"清洗功能启用: {cleaning_config.get('enabled', True)}")
    rules = cleaning_config.get("rules", [])
    enabled_rules = [rule for rule in rules if rule.get("enabled", True)]
    print(f"启用的规则数量: {len(enabled_rules)}")
    
    if enabled_rules:
        print("启用的规则:")
        for rule in enabled_rules:
            print(f"  - {rule.get('name', '未知')}")
    else:
        print("没有启用的规则")
    
    print("\n=== 修复总结 ===")
    print("1. ✅ 修复了文件名判断逻辑")
    print("   - 清洗规则启用时：清洗后检查是否有扩展名")
    print("   - 清洗规则关闭时：直接使用原文本（只做长度检查）")
    print("2. ✅ 简化了浮动窗口显示内容")
    print("   - 发现相似文件名时只显示库中的文件名")
    print("   - 避免显示用户复制的复杂文本导致界面异常")
    
except Exception as e:
    print(f"测试失败: {e}")
    import traceback
    traceback.print_exc()
