"""
B022数据库修复脚本

解决数据库锁定和迁移问题，确保B022测试能够正常运行
"""

import os
import sqlite3
import shutil
import time
from pathlib import Path


def fix_database():
    """修复数据库问题"""
    print("🔧 开始修复数据库问题...")
    
    try:
        # 1. 获取数据库路径
        from smartvault.utils.config import load_config
        config = load_config()
        library_path = config["library_path"]
        db_path = os.path.join(library_path, "data", "smartvault.db")
        
        print(f"数据库路径: {db_path}")
        
        # 2. 检查数据库文件
        if not os.path.exists(db_path):
            print("✅ 数据库文件不存在，无需修复")
            return True
        
        # 3. 创建备份
        backup_path = db_path + f".backup_{int(time.time())}"
        shutil.copy2(db_path, backup_path)
        print(f"✅ 已创建备份: {backup_path}")
        
        # 4. 强制关闭所有连接
        print("🔄 强制关闭数据库连接...")
        
        # 删除WAL和SHM文件
        wal_path = db_path + "-wal"
        shm_path = db_path + "-shm"
        journal_path = db_path + "-journal"
        
        for lock_file in [wal_path, shm_path, journal_path]:
            if os.path.exists(lock_file):
                try:
                    os.remove(lock_file)
                    print(f"  ✅ 删除锁定文件: {os.path.basename(lock_file)}")
                except Exception as e:
                    print(f"  ⚠️  无法删除 {lock_file}: {e}")
        
        # 5. 重新连接并检查结构
        print("🔍 检查数据库结构...")
        conn = sqlite3.connect(db_path, timeout=10.0)
        conn.execute("PRAGMA journal_mode=DELETE")  # 禁用WAL模式
        
        cursor = conn.cursor()
        
        # 检查tags表结构
        cursor.execute("PRAGMA table_info(tags)")
        columns = {col[1]: col[2] for col in cursor.fetchall()}
        
        # 添加缺失的weight字段
        if 'weight' not in columns:
            print("  🔄 添加weight字段...")
            cursor.execute("ALTER TABLE tags ADD COLUMN weight INTEGER DEFAULT 5")
            print("  ✅ weight字段添加成功")
        else:
            print("  ✅ weight字段已存在")
        
        # 检查tag_relations表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='tag_relations'")
        if not cursor.fetchone():
            print("  🔄 创建tag_relations表...")
            cursor.execute('''
                CREATE TABLE tag_relations (
                    id TEXT PRIMARY KEY,
                    tag1_id TEXT NOT NULL,
                    tag2_id TEXT NOT NULL,
                    relation_type TEXT NOT NULL DEFAULT 'related',
                    strength REAL DEFAULT 0.5,
                    created_at TIMESTAMP NOT NULL,
                    FOREIGN KEY (tag1_id) REFERENCES tags (id),
                    FOREIGN KEY (tag2_id) REFERENCES tags (id),
                    UNIQUE(tag1_id, tag2_id, relation_type)
                )
            ''')
            print("  ✅ tag_relations表创建成功")
        else:
            print("  ✅ tag_relations表已存在")
        
        # 提交更改
        conn.commit()
        conn.close()
        
        print("✅ 数据库修复完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据库修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_database_connection():
    """测试数据库连接"""
    print("\n🧪 测试数据库连接...")
    
    try:
        from smartvault.services.tag_service import TagService
        
        # 创建标签服务
        tag_service = TagService()
        
        # 测试基本操作
        print("  1. 测试标签创建...")
        tag_id = tag_service.create_tag("测试标签", weight=8, color="#FF0000")
        print(f"     ✅ 标签创建成功: {tag_id}")
        
        print("  2. 测试标签查询...")
        tag = tag_service.get_tag_by_id(tag_id)
        assert tag["name"] == "测试标签"
        assert tag["weight"] == 8
        print("     ✅ 标签查询成功")
        
        print("  3. 测试权重排序...")
        sorted_tags = tag_service.get_tags_sorted_by_weight()
        print(f"     ✅ 权重排序成功: {len(sorted_tags)} 个标签")
        
        print("  4. 测试统计功能...")
        stats = tag_service.get_tag_usage_statistics()
        print(f"     ✅ 统计功能正常: {stats['total_tags']} 个标签")
        
        print("  5. 测试关联关系...")
        tag2_id = tag_service.create_tag("相关标签", weight=6)
        relation_id = tag_service.add_tag_relation(tag_id, tag2_id, strength=0.8)
        print(f"     ✅ 关联关系创建成功: {relation_id}")
        
        related = tag_service.get_related_tags(tag_id)
        print(f"     ✅ 相关标签查询成功: {len(related)} 个")
        
        print("✅ 数据库连接测试全部通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🚀 B022数据库修复脚本")
    print("=" * 50)
    
    # 1. 修复数据库
    if not fix_database():
        print("❌ 数据库修复失败，退出")
        return False
    
    # 2. 测试连接
    if not test_database_connection():
        print("❌ 数据库连接测试失败，退出")
        return False
    
    print("\n🎉 数据库修复和测试全部完成！")
    print("现在可以运行B022综合测试了。")
    return True


if __name__ == "__main__":
    main()
