"""
监控设置页面
从原 settings_dialog.py 中的 create_monitor_tab 方法迁移而来
"""

from PySide6.QtWidgets import (
    QVBoxLayout, QHBoxLayout, QGroupBox, QLabel, QSpinBox,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView
)
from PySide6.QtCore import Qt
from typing import Tuple
from ..base.base_page import BaseSettingsPage


class MonitorSettingsPage(BaseSettingsPage):
    """监控设置页面"""

    def __init__(self, parent=None):
        """初始化页面"""
        super().__init__(parent)
        self.monitor_service = None

    def setup_ui(self):
        """设置UI界面"""
        layout = QVBoxLayout(self)

        # 监控控制说明
        control_info_group = QGroupBox("监控控制")
        control_info_layout = QVBoxLayout(control_info_group)

        control_info_label = QLabel(
            "💡 文件监控的启动和停止请使用主界面工具栏左侧的监控总开关。\n"
            "此设置页面仅用于配置监控文件夹和规则。"
        )
        control_info_label.setWordWrap(True)
        control_info_label.setStyleSheet("""
            QLabel {
                background-color: #e8f4fd;
                border: 1px solid #bee5eb;
                border-radius: 4px;
                padding: 8px;
                color: #0c5460;
            }
        """)
        control_info_layout.addWidget(control_info_label)
        layout.addWidget(control_info_group)

        # 监控文件夹列表
        list_group = QGroupBox("监控文件夹配置")
        list_layout = QVBoxLayout(list_group)

        # 创建表格
        self.monitor_table = QTableWidget()
        self.monitor_table.setColumnCount(5)
        self.monitor_table.setHorizontalHeaderLabels([
            "文件夹路径", "入库模式", "文件类型", "自动添加", "递归监控"
        ])

        # 设置表格属性
        header = self.monitor_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.resizeSection(0, 350)  # 文件夹路径列宽
        header.resizeSection(1, 80)   # 入库模式列宽
        header.resizeSection(2, 120)  # 文件类型列宽
        header.resizeSection(3, 80)   # 自动添加列宽
        header.resizeSection(4, 80)   # 递归监控列宽

        self.monitor_table.setSelectionBehavior(QTableWidget.SelectRows)
        list_layout.addWidget(self.monitor_table)

        # 按钮区域（只保留配置管理按钮）
        buttons_layout = QHBoxLayout()

        add_button = QPushButton("添加监控文件夹")
        add_button.clicked.connect(self.on_add_monitor_folder)
        buttons_layout.addWidget(add_button)

        edit_button = QPushButton("编辑监控设置")
        edit_button.clicked.connect(self.on_edit_monitor_config)
        buttons_layout.addWidget(edit_button)

        remove_button = QPushButton("删除监控")
        remove_button.clicked.connect(self.on_remove_monitor_folder)
        buttons_layout.addWidget(remove_button)

        buttons_layout.addStretch()

        list_layout.addLayout(buttons_layout)
        layout.addWidget(list_group)

        # 监控高级设置
        advanced_group = QGroupBox("高级设置")
        advanced_layout = QVBoxLayout(advanced_group)

        # 事件处理间隔
        interval_layout = QHBoxLayout()
        interval_layout.addWidget(QLabel("事件处理间隔 (毫秒):"))

        self.event_interval_spin = QSpinBox()
        self.event_interval_spin.setRange(100, 5000)
        self.event_interval_spin.setSingleStep(100)
        self.event_interval_spin.setToolTip("文件事件处理的最小间隔，避免频繁处理同一文件")
        interval_layout.addWidget(self.event_interval_spin)
        interval_layout.addWidget(QLabel("(推荐: 500ms)"))
        interval_layout.addStretch()

        advanced_layout.addLayout(interval_layout)

        layout.addWidget(advanced_group)
        layout.addStretch()

    def set_monitor_service(self, monitor_service):
        """设置监控服务实例

        Args:
            monitor_service: FileMonitorService实例
        """
        self.monitor_service = monitor_service

    def load_settings(self, config: dict):
        """从配置加载设置到UI控件

        Args:
            config: 配置字典
        """
        self.config = config
        monitor_config = config.get("monitor", {})

        # 事件处理间隔
        self.event_interval_spin.setValue(monitor_config.get("event_interval_ms", 500))

        # 加载监控配置
        self.refresh_monitor_list()

    def save_settings(self) -> dict:
        """从UI控件保存设置到配置

        Returns:
            dict: 监控设置字典
        """
        return {
            "event_interval_ms": self.event_interval_spin.value()
        }

    def validate_settings(self) -> Tuple[bool, str]:
        """验证设置有效性

        Returns:
            tuple: (是否有效, 错误信息)
        """
        # 验证事件处理间隔
        if self.event_interval_spin.value() < 100:
            return False, "事件处理间隔不能少于100毫秒"

        return True, ""

    def reset_to_defaults(self):
        """重置为默认设置"""
        self.event_interval_spin.setValue(500)

    def get_page_title(self) -> str:
        """获取页面标题

        Returns:
            str: 页面标题
        """
        return "文件监控设置"

    def refresh_monitor_list(self):
        """刷新监控列表"""
        if not self.monitor_service:
            return

        try:
            configs = self.monitor_service.get_all_monitors()
            self.monitor_table.setRowCount(len(configs))

            for row, config in enumerate(configs):
                # 文件夹路径
                folder_item = QTableWidgetItem(config.get("folder_path", ""))
                # 将监控ID存储在item的用户数据中
                folder_item.setData(Qt.UserRole, config.get("id", ""))
                self.monitor_table.setItem(row, 0, folder_item)

                # 入库模式
                entry_mode = config.get("entry_mode", "link")
                mode_text = {"link": "链接", "copy": "复制", "move": "移动"}.get(entry_mode, entry_mode)
                self.monitor_table.setItem(row, 1, QTableWidgetItem(mode_text))

                # 文件类型
                patterns = config.get("file_patterns", [])
                patterns_text = ", ".join(patterns) if patterns else "所有文件"
                self.monitor_table.setItem(row, 2, QTableWidgetItem(patterns_text))

                # 自动添加
                auto_add = "是" if config.get("auto_add", True) else "否"
                self.monitor_table.setItem(row, 3, QTableWidgetItem(auto_add))

                # 递归监控
                recursive = "是" if config.get("recursive", True) else "否"
                self.monitor_table.setItem(row, 4, QTableWidgetItem(recursive))

        except Exception as e:
            print(f"刷新监控列表失败: {e}")

    def on_add_monitor_folder(self):
        """添加监控文件夹"""
        from PySide6.QtWidgets import QMessageBox
        from ...monitor_config_dialog import MonitorConfigDialog

        if not self.monitor_service:
            QMessageBox.warning(self, "错误", "监控服务未初始化")
            return

        # 打开添加对话框
        dialog = MonitorConfigDialog(parent=self)
        if dialog.exec() == MonitorConfigDialog.Accepted:
            try:
                # 获取配置
                config = dialog.get_config()

                # 添加监控文件夹
                monitor_id = self.monitor_service.add_monitor_folder(
                    config["folder_path"],
                    entry_mode=config["entry_mode"],
                    file_patterns=config["file_patterns"],
                    auto_add=config["auto_add"],
                    recursive=config["recursive"],
                    auto_dedup=config["auto_dedup"]
                )

                self.refresh_monitor_list()
                QMessageBox.information(self, "成功", f"已添加监控文件夹: {config['folder_path']}")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"添加监控文件夹失败: {e}")

    def on_edit_monitor_config(self):
        """编辑监控设置"""
        from PySide6.QtWidgets import QMessageBox
        from ...monitor_config_dialog import MonitorConfigDialog

        current_row = self.monitor_table.currentRow()
        if current_row < 0:
            QMessageBox.information(self, "提示", "请先选择要编辑的监控配置")
            return

        # 获取选中的监控配置
        folder_item = self.monitor_table.item(current_row, 0)
        if not folder_item:
            return

        monitor_id = folder_item.data(Qt.UserRole)
        if not monitor_id:
            QMessageBox.warning(self, "错误", "无法获取监控配置ID")
            return

        # 获取完整的监控配置
        if not self.monitor_service:
            QMessageBox.warning(self, "错误", "监控服务未初始化")
            return

        config = self.monitor_service.get_monitor_config(monitor_id)
        if not config:
            QMessageBox.warning(self, "错误", "无法获取监控配置详情")
            return

        # 打开编辑对话框
        dialog = MonitorConfigDialog(config=config, parent=self)
        if dialog.exec() == MonitorConfigDialog.Accepted:
            try:
                # 获取新配置
                new_config = dialog.get_config()

                # 更新监控配置
                success = self.monitor_service.update_monitor_config(
                    monitor_id,
                    entry_mode=new_config["entry_mode"],
                    file_patterns=new_config["file_patterns"],
                    auto_add=new_config["auto_add"],
                    recursive=new_config["recursive"]
                )

                if success:
                    self.refresh_monitor_list()
                    QMessageBox.information(self, "成功", "监控配置已更新")
                else:
                    QMessageBox.warning(self, "错误", "更新监控配置失败")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"更新监控配置失败: {e}")

    def on_remove_monitor_folder(self):
        """删除监控文件夹"""
        from PySide6.QtWidgets import QMessageBox

        current_row = self.monitor_table.currentRow()
        if current_row < 0:
            QMessageBox.information(self, "提示", "请先选择要删除的监控配置")
            return

        folder_item = self.monitor_table.item(current_row, 0)
        if not folder_item:
            return

        folder_path = folder_item.text()
        monitor_id = folder_item.data(Qt.UserRole)

        if not monitor_id:
            QMessageBox.warning(self, "错误", "无法获取监控配置ID")
            return

        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除监控文件夹吗？\n{folder_path}",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                if self.monitor_service:
                    # 调用监控服务删除监控配置
                    success = self.monitor_service.remove_monitor_folder(monitor_id)
                    if success:
                        self.refresh_monitor_list()
                        QMessageBox.information(self, "成功", "监控配置已删除")
                    else:
                        QMessageBox.warning(self, "错误", "删除监控配置失败")
                else:
                    QMessageBox.warning(self, "错误", "监控服务未初始化")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"删除监控配置失败: {e}")
