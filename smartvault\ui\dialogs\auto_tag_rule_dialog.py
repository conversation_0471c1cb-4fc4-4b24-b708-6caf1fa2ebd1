#!/usr/bin/env python3
"""
自动标签规则编辑对话框
"""

import uuid
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QLineEdit, QComboBox, QSpinBox,
    QPushButton, QDialogButtonBox, QGroupBox, QCheckBox,
    QMessageBox
)
from smartvault.services.auto_tag_service import AutoTagRule, ConditionType
from smartvault.services.tag_service import TagService


class AutoTagRuleDialog(QDialog):
    """自动标签规则编辑对话框"""

    def __init__(self, parent=None, rule=None):
        """初始化对话框

        Args:
            parent: 父窗口
            rule: 现有规则（编辑模式）
        """
        super().__init__(parent)
        self.rule = rule
        self.is_edit_mode = rule is not None

        self.setWindowTitle("编辑自动标签规则" if self.is_edit_mode else "添加自动标签规则")
        self.resize(500, 600)

        # 初始化标签服务
        self.tag_service = TagService()

        self.init_ui()

        if self.is_edit_mode:
            self.load_rule_data()

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)

        # 基本信息组
        basic_group = QGroupBox("基本信息")
        basic_layout = QFormLayout(basic_group)

        # 规则名称
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("请输入规则名称")
        basic_layout.addRow("规则名称:", self.name_edit)

        # 启用状态
        self.enabled_check = QCheckBox("启用此规则")
        self.enabled_check.setChecked(True)
        basic_layout.addRow("", self.enabled_check)

        # 优先级
        self.priority_spin = QSpinBox()
        self.priority_spin.setRange(0, 100)
        self.priority_spin.setValue(0)
        self.priority_spin.setToolTip("优先级越高的规则越先执行")
        basic_layout.addRow("优先级:", self.priority_spin)

        layout.addWidget(basic_group)

        # 条件设置组
        condition_group = QGroupBox("匹配条件")
        condition_layout = QFormLayout(condition_group)

        # 条件类型
        self.condition_type_combo = QComboBox()
        self.condition_type_combo.addItems([
            "文件扩展名",
            "文件名模式",
            "文件路径模式",
            "文件大小范围",
            "文件类型",
            "文件大小",
            "文件名正则表达式"
        ])
        self.condition_type_combo.currentTextChanged.connect(self.on_condition_type_changed)
        condition_layout.addRow("条件类型:", self.condition_type_combo)

        # 条件值
        self.condition_value_edit = QLineEdit()
        self.condition_value_edit.setPlaceholderText("请输入条件值")
        condition_layout.addRow("条件值:", self.condition_value_edit)

        # 条件说明
        self.condition_help_label = QLabel()
        self.condition_help_label.setWordWrap(True)
        self.condition_help_label.setStyleSheet("color: #666666; font-size: 12px;")
        condition_layout.addRow("", self.condition_help_label)

        layout.addWidget(condition_group)

        # 标签设置组
        tags_group = QGroupBox("自动添加的标签")
        tags_layout = QVBoxLayout(tags_group)

        # 标签输入
        tags_input_layout = QHBoxLayout()
        self.tags_edit = QLineEdit()
        self.tags_edit.setPlaceholderText("请输入标签名称，多个标签用逗号分隔")
        tags_input_layout.addWidget(self.tags_edit)

        self.select_tags_button = QPushButton("选择标签")
        self.select_tags_button.clicked.connect(self.on_select_tags)
        tags_input_layout.addWidget(self.select_tags_button)

        tags_layout.addLayout(tags_input_layout)

        # 标签说明
        tags_help_label = QLabel("可以输入新标签名称，也可以点击\"选择标签\"从现有标签中选择")
        tags_help_label.setWordWrap(True)
        tags_help_label.setStyleSheet("color: #666666; font-size: 12px;")
        tags_layout.addWidget(tags_help_label)

        layout.addWidget(tags_group)

        # 预览组
        preview_group = QGroupBox("规则预览")
        preview_layout = QVBoxLayout(preview_group)

        self.preview_label = QLabel()
        self.preview_label.setWordWrap(True)
        self.preview_label.setStyleSheet("background-color: #f5f5f5; padding: 10px; border: 1px solid #ddd;")
        preview_layout.addWidget(self.preview_label)

        layout.addWidget(preview_group)

        # 按钮
        buttons = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        buttons.accepted.connect(self.accept_rule)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)

        # 初始化条件说明
        self.on_condition_type_changed()

        # 连接信号以更新预览
        self.name_edit.textChanged.connect(self.update_preview)
        self.condition_type_combo.currentTextChanged.connect(self.update_preview)
        self.condition_value_edit.textChanged.connect(self.update_preview)
        self.tags_edit.textChanged.connect(self.update_preview)
        self.enabled_check.toggled.connect(self.update_preview)
        self.priority_spin.valueChanged.connect(self.update_preview)

        # 初始化预览
        self.update_preview()

    def on_condition_type_changed(self):
        """条件类型改变时的处理"""
        condition_type = self.condition_type_combo.currentText()

        help_texts = {
            "文件扩展名": "输入文件扩展名，多个扩展名用逗号分隔。例如：pdf,doc,docx",
            "文件名模式": "输入文件名匹配模式，支持正则表达式。例如：report 或 .*报告.*",
            "文件路径模式": "输入文件路径匹配模式，支持正则表达式。例如：/Documents/ 或 .*工作.*",
            "文件大小范围": "输入文件大小范围。例如：1MB-10MB 或 500KB-2GB",
            "文件类型": "选择文件类型。可选：图片、文档、表格、演示、视频、音频、压缩、代码、可执行",
            "文件大小": "输入文件大小条件，支持比较操作符。例如：>10MB, <500KB, >=1GB, <=100MB",
            "文件名正则表达式": "输入严格的正则表达式匹配文件名。例如：^report_\\d{4}\\.pdf$ 或 .*[Bb]ackup.*"
        }

        placeholders = {
            "文件扩展名": "pdf,doc,docx",
            "文件名模式": "report",
            "文件路径模式": "/Documents/",
            "文件大小范围": "1MB-10MB",
            "文件类型": "文档",
            "文件大小": ">10MB",
            "文件名正则表达式": "^report_\\d{4}\\.pdf$"
        }

        self.condition_help_label.setText(help_texts.get(condition_type, ""))
        self.condition_value_edit.setPlaceholderText(placeholders.get(condition_type, ""))

        # 如果是文件类型，提供下拉选择
        if condition_type == "文件类型":
            # 这里可以考虑将输入框替换为下拉框，但为了简化，暂时保持文本输入
            pass

    def on_select_tags(self):
        """选择标签"""
        try:
            # 获取所有标签
            all_tags = self.tag_service.get_all_tags()

            if not all_tags:
                QMessageBox.information(self, "提示", "当前没有可用的标签，请先创建一些标签")
                return

            # 创建标签选择对话框
            from smartvault.ui.dialogs.tag_selection_dialog import TagSelectionDialog

            # 获取当前已选择的标签
            current_tags = [tag.strip() for tag in self.tags_edit.text().split(",") if tag.strip()]

            dialog = TagSelectionDialog(self, selected_tags=current_tags)
            if dialog.exec() == QDialog.Accepted:
                selected_tags = dialog.get_selected_tags()
                tag_names = [tag['name'] for tag in selected_tags]
                self.tags_edit.setText(", ".join(tag_names))

        except Exception as e:
            QMessageBox.warning(self, "错误", f"选择标签失败: {e}")

    def update_preview(self):
        """更新规则预览"""
        name = self.name_edit.text().strip()
        condition_type = self.condition_type_combo.currentText()
        condition_value = self.condition_value_edit.text().strip()
        tags = self.tags_edit.text().strip()
        enabled = self.enabled_check.isChecked()
        priority = self.priority_spin.value()

        if not name:
            name = "未命名规则"

        preview_text = f"规则名称: {name}\n"
        preview_text += f"状态: {'启用' if enabled else '禁用'}\n"
        preview_text += f"优先级: {priority}\n"
        preview_text += f"条件: 当文件的{condition_type}匹配\"{condition_value or '(未设置)'}\"时\n"
        preview_text += f"操作: 自动添加标签\"{tags or '(未设置)'}\""

        self.preview_label.setText(preview_text)

    def load_rule_data(self):
        """加载规则数据（编辑模式）"""
        if not self.rule:
            return

        self.name_edit.setText(self.rule.name)
        self.enabled_check.setChecked(self.rule.enabled)
        self.priority_spin.setValue(self.rule.priority)

        # 设置条件类型
        condition_type_map = {
            ConditionType.FILE_EXTENSION: "文件扩展名",
            ConditionType.FILE_NAME_PATTERN: "文件名模式",
            ConditionType.FILE_PATH_PATTERN: "文件路径模式",
            ConditionType.FILE_SIZE_RANGE: "文件大小范围",
            ConditionType.FILE_TYPE: "文件类型"
        }

        condition_text = condition_type_map.get(self.rule.condition_type, "文件扩展名")
        index = self.condition_type_combo.findText(condition_text)
        if index >= 0:
            self.condition_type_combo.setCurrentIndex(index)

        self.condition_value_edit.setText(self.rule.condition_value)
        self.tags_edit.setText(", ".join(self.rule.tag_names))

    def accept_rule(self):
        """确认规则"""
        # 验证输入
        name = self.name_edit.text().strip()
        if not name:
            QMessageBox.warning(self, "错误", "请输入规则名称")
            return

        condition_value = self.condition_value_edit.text().strip()
        if not condition_value:
            QMessageBox.warning(self, "错误", "请输入条件值")
            return

        tags_text = self.tags_edit.text().strip()
        if not tags_text:
            QMessageBox.warning(self, "错误", "请输入要添加的标签")
            return

        # 解析标签
        tag_names = [tag.strip() for tag in tags_text.split(",") if tag.strip()]
        if not tag_names:
            QMessageBox.warning(self, "错误", "请输入有效的标签名称")
            return

        # 创建规则
        condition_type_map = {
            "文件扩展名": ConditionType.FILE_EXTENSION,
            "文件名模式": ConditionType.FILE_NAME_PATTERN,
            "文件路径模式": ConditionType.FILE_PATH_PATTERN,
            "文件大小范围": ConditionType.FILE_SIZE_RANGE,
            "文件类型": ConditionType.FILE_TYPE,
            "文件大小": ConditionType.FILE_SIZE,
            "文件名正则表达式": ConditionType.FILE_NAME_REGEX
        }

        condition_type = condition_type_map[self.condition_type_combo.currentText()]

        self.result_rule = AutoTagRule(
            id=self.rule.id if self.is_edit_mode else str(uuid.uuid4()),
            name=name,
            condition_type=condition_type,
            condition_value=condition_value,
            tag_names=tag_names,
            enabled=self.enabled_check.isChecked(),
            priority=self.priority_spin.value()
        )

        self.accept()

    def get_rule(self):
        """获取规则"""
        return getattr(self, 'result_rule', None)
