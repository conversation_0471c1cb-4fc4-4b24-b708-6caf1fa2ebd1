#!/usr/bin/env python3
"""
检查数据库中的时间戳问题
"""

import sys
import os
import sqlite3
from datetime import datetime

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_timestamps():
    """检查时间戳问题"""
    try:
        from smartvault.utils.config import load_config
        config = load_config()
        db_path = os.path.join(config['library_path'], 'data', 'smartvault.db')
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print('=== 检查未来时间戳详情 ===')
        
        # 检查files表
        cursor.execute("SELECT COUNT(*) FROM files WHERE added_at > datetime('now')")
        files_future = cursor.fetchone()[0]
        print(f'files表未来时间戳: {files_future}')
        
        # 检查tags表
        cursor.execute("SELECT COUNT(*) FROM tags WHERE created_at > datetime('now')")
        tags_future = cursor.fetchone()[0]
        print(f'tags表未来时间戳: {tags_future}')
        
        # 检查file_tags表
        cursor.execute("SELECT COUNT(*) FROM file_tags WHERE added_at > datetime('now')")
        file_tags_future = cursor.fetchone()[0]
        print(f'file_tags表未来时间戳: {file_tags_future}')
        
        # 检查具体的未来时间戳示例
        if files_future > 0:
            cursor.execute("SELECT added_at FROM files WHERE added_at > datetime('now') LIMIT 3")
            examples = cursor.fetchall()
            print(f'files表未来时间戳示例: {examples}')
        
        if file_tags_future > 0:
            cursor.execute("SELECT added_at FROM file_tags WHERE added_at > datetime('now') LIMIT 3")
            examples = cursor.fetchall()
            print(f'file_tags表未来时间戳示例: {examples}')
        
        # 检查当前时间
        cursor.execute("SELECT datetime('now')")
        current_db_time = cursor.fetchone()[0]
        print(f'数据库当前时间: {current_db_time}')
        print(f'系统当前时间: {datetime.now().isoformat()}')
        
        conn.close()
        
    except Exception as e:
        print(f'检查失败: {e}')

if __name__ == '__main__':
    check_timestamps()
