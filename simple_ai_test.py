#!/usr/bin/env python3
"""
简单的AI功能导入测试
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_imports():
    """测试基本导入"""
    print("🔍 测试基本导入...")
    
    try:
        # 测试AI管理器导入
        from smartvault.services.ai.ai_manager import AIManager
        print("✅ AI管理器导入成功")
        
        # 测试AI配置管理器导入
        from smartvault.ui.dialogs.settings.pages.ai.utils.ai_config_helper import AIConfigManager
        print("✅ AI配置管理器导入成功")
        
        # 测试基础AI组件导入
        from smartvault.ui.dialogs.settings.pages.ai.components.base_ai_widget import BaseAIWidget
        print("✅ 基础AI组件导入成功")
        
        # 测试AI状态组件导入
        from smartvault.ui.dialogs.settings.pages.ai.components.ai_status_widget import AIStatusWidget
        print("✅ AI状态组件导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_page_import():
    """测试AI页面导入"""
    print("\n🔍 测试AI页面导入...")
    
    try:
        from smartvault.ui.dialogs.settings.pages.ai.ai_main_page import AISettingsPage
        print("✅ AI设置页面导入成功")
        return True
        
    except Exception as e:
        print(f"❌ AI设置页面导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_settings_dialog_import():
    """测试设置对话框导入"""
    print("\n🔍 测试设置对话框导入...")
    
    try:
        from smartvault.ui.dialogs.settings_dialog import SettingsDialog
        print("✅ 设置对话框导入成功")
        return True
        
    except Exception as e:
        print(f"❌ 设置对话框导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 简单AI功能导入测试")
    print("=" * 50)
    
    # 测试基本导入
    test1 = test_basic_imports()
    
    # 测试AI页面导入
    test2 = test_ai_page_import()
    
    # 测试设置对话框导入
    test3 = test_settings_dialog_import()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"基本导入: {'✅' if test1 else '❌'}")
    print(f"AI页面导入: {'✅' if test2 else '❌'}")
    print(f"设置对话框导入: {'✅' if test3 else '❌'}")
    
    if all([test1, test2, test3]):
        print("\n🎉 所有导入测试通过！")
        return 0
    else:
        print("\n⚠️ 部分导入测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
