#!/usr/bin/env python3
"""
设备卷标读取工具

提供从用户选择的设备路径读取卷标的功能
"""

import os
import platform
from typing import Optional


class DeviceInfo:
    """设备信息类"""
    
    def __init__(self, path: str, volume_name: str, file_system: str = None):
        self.path = path
        self.volume_name = volume_name or f"设备_{os.path.splitdrive(path)[0].replace(':', '')}"
        self.file_system = file_system
    
    def __str__(self):
        return f"设备 {self.path}: {self.volume_name}"
    
    def __repr__(self):
        return f"DeviceInfo(path='{self.path}', volume_name='{self.volume_name}')"
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'path': self.path,
            'volume_name': self.volume_name,
            'file_system': self.file_system
        }


class DeviceVolumeReader:
    """设备卷标读取器"""
    
    def __init__(self):
        self.system = platform.system()
    
    def get_volume_info_from_path(self, device_path: str) -> Optional[DeviceInfo]:
        """从设备路径获取卷标信息
        
        Args:
            device_path: 设备路径（如 F:\ 或 /media/usb）
            
        Returns:
            Optional[DeviceInfo]: 设备信息，如果获取失败则返回None
        """
        if not device_path or not os.path.exists(device_path):
            return None
            
        if self.system == 'Windows':
            return self._get_windows_volume_info(device_path)
        elif self.system == 'Darwin':  # macOS
            return self._get_macos_volume_info(device_path)
        elif self.system == 'Linux':
            return self._get_linux_volume_info(device_path)
        else:
            return None
    
    def _get_windows_volume_info(self, device_path: str) -> Optional[DeviceInfo]:
        """获取Windows系统下设备的卷标信息"""
        try:
            import ctypes
            
            # 确保路径以反斜杠结尾
            if not device_path.endswith('\\'):
                device_path = device_path + '\\'
            
            # 获取卷标信息
            volume_name_buffer = ctypes.create_unicode_buffer(1024)
            file_system_buffer = ctypes.create_unicode_buffer(1024)
            
            result = ctypes.windll.kernel32.GetVolumeInformationW(
                device_path,
                volume_name_buffer,
                ctypes.sizeof(volume_name_buffer),
                None,
                None,
                None,
                file_system_buffer,
                ctypes.sizeof(file_system_buffer)
            )
            
            if result:
                volume_name = volume_name_buffer.value
                file_system = file_system_buffer.value
                
                # 如果没有卷标，使用驱动器号作为默认名称
                if not volume_name:
                    drive_letter = os.path.splitdrive(device_path)[0].replace(':', '')
                    volume_name = f"USB_{drive_letter}"
                
                return DeviceInfo(
                    path=device_path,
                    volume_name=volume_name,
                    file_system=file_system
                )
            else:
                # 获取失败，返回默认信息
                drive_letter = os.path.splitdrive(device_path)[0].replace(':', '')
                return DeviceInfo(
                    path=device_path,
                    volume_name=f"设备_{drive_letter}"
                )
                
        except Exception as e:
            print(f"获取Windows卷标失败: {e}")
            # 返回默认信息
            drive_letter = os.path.splitdrive(device_path)[0].replace(':', '')
            return DeviceInfo(
                path=device_path,
                volume_name=f"设备_{drive_letter}"
            )
    
    def _get_macos_volume_info(self, device_path: str) -> Optional[DeviceInfo]:
        """获取macOS系统下设备的卷标信息"""
        try:
            # 在macOS中，卷标通常是挂载点的最后一部分
            volume_name = os.path.basename(device_path.rstrip('/'))
            
            if not volume_name:
                volume_name = "未知设备"
            
            return DeviceInfo(
                path=device_path,
                volume_name=volume_name
            )
            
        except Exception as e:
            print(f"获取macOS卷标失败: {e}")
            return DeviceInfo(
                path=device_path,
                volume_name="未知设备"
            )
    
    def _get_linux_volume_info(self, device_path: str) -> Optional[DeviceInfo]:
        """获取Linux系统下设备的卷标信息"""
        try:
            # 在Linux中，卷标通常是挂载点的最后一部分
            volume_name = os.path.basename(device_path.rstrip('/'))
            
            if not volume_name:
                volume_name = "未知设备"
            
            return DeviceInfo(
                path=device_path,
                volume_name=volume_name
            )
            
        except Exception as e:
            print(f"获取Linux卷标失败: {e}")
            return DeviceInfo(
                path=device_path,
                volume_name="未知设备"
            )


# 全局读取器实例
_reader = None

def get_device_volume_reader() -> DeviceVolumeReader:
    """获取设备卷标读取器单例"""
    global _reader
    if _reader is None:
        _reader = DeviceVolumeReader()
    return _reader


def get_volume_info_from_path(device_path: str) -> Optional[DeviceInfo]:
    """快捷函数：从设备路径获取卷标信息"""
    return get_device_volume_reader().get_volume_info_from_path(device_path)


if __name__ == "__main__":
    # 测试代码
    reader = DeviceVolumeReader()
    
    # 测试当前系统的所有驱动器
    if platform.system() == 'Windows':
        import string
        for letter in string.ascii_uppercase:
            drive_path = f"{letter}:\\"
            if os.path.exists(drive_path):
                device_info = reader.get_volume_info_from_path(drive_path)
                if device_info:
                    print(f"驱动器 {letter}: - {device_info}")
    else:
        # 测试一个示例路径
        test_path = "/media"
        if os.path.exists(test_path):
            device_info = reader.get_volume_info_from_path(test_path)
            if device_info:
                print(f"测试路径: {device_info}")
