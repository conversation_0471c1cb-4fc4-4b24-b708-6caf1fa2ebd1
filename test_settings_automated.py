#!/usr/bin/env python3
"""
设置对话框自动化测试脚本
模拟用户操作，自动点击按钮和测试功能
"""

import sys
import os
import time
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMessageBox, QPushButton, QSpinBox, QComboBox, QCheckBox, QDialogButtonBox
from PySide6.QtCore import QTimer, Qt
from PySide6.QtTest import QTest
from smartvault.ui.main_window import MainWindow
from smartvault.ui.dialogs import SettingsDialog

class AutomatedSettingsTester:
    """自动化设置测试器"""

    def __init__(self):
        self.app = QApplication.instance()
        if self.app is None:
            self.app = QApplication(sys.argv)

        self.main_window = None
        self.settings_dialog = None
        self.current_test_step = 0
        self.test_steps = []

    def run_automated_test(self):
        """运行自动化测试"""
        print("🤖 开始设置对话框自动化测试...")

        try:
            # 初始化
            self.setup_test_environment()

            # 定义测试步骤
            self.define_test_steps()

            # 开始执行测试
            self.execute_next_test_step()

        except Exception as e:
            print(f"❌ 自动化测试失败: {e}")
            import traceback
            traceback.print_exc()

    def setup_test_environment(self):
        """设置测试环境"""
        print("\n🔧 设置测试环境...")

        self.main_window = MainWindow()
        self.settings_dialog = SettingsDialog(self.main_window)
        self.settings_dialog.set_monitor_service(self.main_window.monitor_service)
        self.settings_dialog.show()

        print("   ✅ 测试环境已准备就绪")

    def define_test_steps(self):
        """定义测试步骤"""
        self.test_steps = [
            ("切换到智能文件库设置", self.test_library_page),
            ("切换到监控设置", self.test_monitor_page),
            ("切换到界面设置", self.test_ui_page),
            ("切换到搜索设置", self.test_search_page),
            ("切换到自动标签", self.test_auto_tag_page),
            ("切换到高级设置", self.test_advanced_page),
            ("测试设置保存", self.test_settings_save),
            ("测试设置取消", self.test_settings_cancel),
        ]

    def execute_next_test_step(self):
        """执行下一个测试步骤"""
        if self.current_test_step >= len(self.test_steps):
            self.finish_test()
            return

        step_name, step_func = self.test_steps[self.current_test_step]
        print(f"\n🔄 步骤 {self.current_test_step + 1}: {step_name}")

        try:
            step_func()
            print(f"   ✅ {step_name} 完成")
        except Exception as e:
            print(f"   ❌ {step_name} 失败: {e}")

        self.current_test_step += 1

        # 延时执行下一步
        QTimer.singleShot(1000, self.execute_next_test_step)

    def test_library_page(self):
        """测试智能文件库设置页面"""
        self.switch_to_tab("智能文件库设置")

        # 获取页面
        page = self.settings_dialog.pages.get('library')
        if not page:
            raise Exception("智能文件库设置页面未找到")

        # 测试页面元素
        self.test_page_elements(page, "智能文件库设置")

    def test_monitor_page(self):
        """测试监控设置页面"""
        self.switch_to_tab("文件监控设置")

        page = self.settings_dialog.pages.get('monitor')
        if not page:
            raise Exception("监控设置页面未找到")

        # 检查监控表格
        table = page.monitor_table
        row_count = table.rowCount()
        print(f"      📋 监控配置数量: {row_count}")

        # 测试按钮
        buttons = page.findChildren(QPushButton)
        for button in buttons:
            if button.text() in ["添加监控文件夹", "编辑监控设置", "删除监控"]:
                print(f"      🔘 找到按钮: {button.text()}")
                # 注意：不实际点击，避免弹出对话框

        # 测试高级设置
        spin_box = page.event_interval_spin
        if spin_box:
            original_value = spin_box.value()
            spin_box.setValue(1000)
            print(f"      ⚙️ 事件间隔设置: {original_value} -> {spin_box.value()}")
            spin_box.setValue(original_value)  # 恢复原值

    def test_ui_page(self):
        """测试界面设置页面"""
        self.switch_to_tab("界面设置")

        page = self.settings_dialog.pages.get('ui')
        if not page:
            raise Exception("界面设置页面未找到")

        self.test_page_elements(page, "界面设置")

    def test_search_page(self):
        """测试搜索设置页面"""
        self.switch_to_tab("搜索设置")

        page = self.settings_dialog.pages.get('search')
        if not page:
            raise Exception("搜索设置页面未找到")

        self.test_page_elements(page, "搜索设置")

    def test_auto_tag_page(self):
        """测试自动标签页面"""
        self.switch_to_tab("自动标签")

        page = self.settings_dialog.pages.get('auto_tag')
        if not page:
            raise Exception("自动标签页面未找到")

        self.test_page_elements(page, "自动标签")

    def test_advanced_page(self):
        """测试高级设置页面"""
        self.switch_to_tab("高级设置")

        page = self.settings_dialog.pages.get('advanced')
        if not page:
            raise Exception("高级设置页面未找到")

        self.test_page_elements(page, "高级设置")

    def test_settings_save(self):
        """测试设置保存"""
        print("      💾 测试设置保存功能...")

        # 模拟点击确定按钮（但不实际执行）
        button_box = self.settings_dialog.findChild(QDialogButtonBox)
        if button_box:
            ok_button = button_box.button(QDialogButtonBox.Ok)
            if ok_button:
                print("      ✅ 找到确定按钮")
                # 注意：不实际点击，避免关闭对话框

    def test_settings_cancel(self):
        """测试设置取消"""
        print("      ❌ 测试设置取消功能...")

        button_box = self.settings_dialog.findChild(QDialogButtonBox)
        if button_box:
            cancel_button = button_box.button(QDialogButtonBox.Cancel)
            if cancel_button:
                print("      ✅ 找到取消按钮")

    def switch_to_tab(self, tab_name):
        """切换到指定选项卡"""
        tab_widget = self.settings_dialog.tab_widget

        for i in range(tab_widget.count()):
            if tab_widget.tabText(i) == tab_name:
                tab_widget.setCurrentIndex(i)
                print(f"      📑 已切换到 {tab_name}")

                # 等待UI更新
                self.app.processEvents()
                time.sleep(0.1)
                return

        raise Exception(f"未找到选项卡: {tab_name}")

    def test_page_elements(self, page, page_name):
        """测试页面元素"""
        # 测试基本方法
        try:
            title = page.get_page_title()
            print(f"      📋 页面标题: {title}")
        except Exception as e:
            print(f"      ⚠️ 获取页面标题失败: {e}")

        try:
            settings = page.save_settings()
            print(f"      💾 设置保存: {type(settings).__name__}")
        except Exception as e:
            print(f"      ⚠️ 设置保存失败: {e}")

        try:
            is_valid, error_msg = page.validate_settings()
            print(f"      ✅ 设置验证: {is_valid}")
        except Exception as e:
            print(f"      ⚠️ 设置验证失败: {e}")

        # 统计UI元素
        buttons = page.findChildren(QPushButton)
        spin_boxes = page.findChildren(QSpinBox)
        combo_boxes = page.findChildren(QComboBox)
        check_boxes = page.findChildren(QCheckBox)

        print(f"      🔘 按钮数量: {len(buttons)}")
        print(f"      🔢 数字输入框: {len(spin_boxes)}")
        print(f"      📋 下拉框: {len(combo_boxes)}")
        print(f"      ☑️ 复选框: {len(check_boxes)}")

    def finish_test(self):
        """完成测试"""
        print("\n🎉 自动化测试完成！")
        print("\n📊 测试总结:")
        print(f"   ✅ 总共执行了 {len(self.test_steps)} 个测试步骤")
        print("   📋 所有页面都已测试")
        print("   🔧 UI元素都已检查")

        print("\n💡 建议进行的手动测试:")
        print("   1. 实际修改设置项并保存")
        print("   2. 测试监控文件夹的添加/删除")
        print("   3. 验证设置在重启后是否保持")
        print("   4. 测试错误输入的处理")

        # 保持对话框打开供手动测试
        print("\n🖱️ 对话框将保持打开状态，您可以进行手动测试")

def main():
    """主函数"""
    tester = AutomatedSettingsTester()
    tester.run_automated_test()

    # 运行应用程序
    sys.exit(tester.app.exec())

if __name__ == "__main__":
    main()
