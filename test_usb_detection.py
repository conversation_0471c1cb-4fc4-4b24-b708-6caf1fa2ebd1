#!/usr/bin/env python3
"""
测试USB设备检测和卷标获取功能

这个脚本测试多种方法来检测USB设备并获取其卷标：
1. 使用 psutil 库
2. 使用 WMI (Windows Management Instrumentation)
3. 使用 Windows API (ctypes)
"""

import os
import sys
import platform

def test_psutil_method():
    """使用 psutil 库检测磁盘设备"""
    print("=== 方法1: 使用 psutil 库 ===")
    
    try:
        import psutil
        
        # 获取所有磁盘分区
        partitions = psutil.disk_partitions()
        
        removable_drives = []
        for partition in partitions:
            print(f"设备: {partition.device}")
            print(f"挂载点: {partition.mountpoint}")
            print(f"文件系统: {partition.fstype}")
            print(f"选项: {partition.opts}")
            
            # 检查是否是可移动设备
            if 'removable' in partition.opts:
                removable_drives.append({
                    'device': partition.device,
                    'mountpoint': partition.mountpoint,
                    'fstype': partition.fstype
                })
                print("  -> 这是可移动设备!")
            
            print("-" * 40)
        
        return removable_drives
        
    except ImportError:
        print("psutil 库未安装，请运行: pip install psutil")
        return []
    except Exception as e:
        print(f"psutil 方法出错: {e}")
        return []

def test_wmi_method():
    """使用 WMI 检测USB设备和卷标"""
    print("\n=== 方法2: 使用 WMI ===")
    
    if platform.system() != 'Windows':
        print("WMI 仅在 Windows 系统上可用")
        return []
    
    try:
        import wmi
        
        w = wmi.WMI()
        
        # 定义驱动器类型
        DRIVE_TYPES = {
            0: "Unknown",
            1: "No Root Directory", 
            2: "Removable Disk",
            3: "Local Disk",
            4: "Network Drive",
            5: "Compact Disc",
            6: "RAM Disk"
        }
        
        removable_drives = []
        
        # 查询逻辑磁盘
        for disk in w.Win32_LogicalDisk():
            drive_type = DRIVE_TYPES.get(disk.DriveType, "Unknown")
            
            print(f"驱动器: {disk.DeviceID}")
            print(f"卷标: {disk.VolumeName or '(无卷标)'}")
            print(f"类型: {drive_type}")
            print(f"文件系统: {disk.FileSystem or 'N/A'}")
            print(f"大小: {disk.Size or 'N/A'}")
            print(f"可用空间: {disk.FreeSpace or 'N/A'}")
            
            # 检查是否是可移动磁盘
            if disk.DriveType == 2:  # Removable Disk
                removable_drives.append({
                    'device': disk.DeviceID,
                    'volume_name': disk.VolumeName or f"USB_{disk.DeviceID.replace(':', '')}",
                    'file_system': disk.FileSystem,
                    'size': disk.Size
                })
                print("  -> 这是可移动设备!")
            
            print("-" * 40)
        
        return removable_drives
        
    except ImportError:
        print("WMI 库未安装，请运行: pip install WMI")
        return []
    except Exception as e:
        print(f"WMI 方法出错: {e}")
        return []

def test_windows_api_method():
    """使用 Windows API 检测驱动器"""
    print("\n=== 方法3: 使用 Windows API ===")
    
    if platform.system() != 'Windows':
        print("Windows API 仅在 Windows 系统上可用")
        return []
    
    try:
        import ctypes
        from ctypes import wintypes
        
        # 获取所有逻辑驱动器
        drives = []
        bitmask = ctypes.windll.kernel32.GetLogicalDrives()
        
        for letter in range(26):
            if bitmask & (1 << letter):
                drive_letter = chr(65 + letter) + ':'
                drives.append(drive_letter)
        
        removable_drives = []
        
        for drive in drives:
            drive_path = drive + '\\'
            
            # 获取驱动器类型
            drive_type = ctypes.windll.kernel32.GetDriveTypeW(drive_path)
            
            # 驱动器类型常量
            DRIVE_TYPES = {
                0: "DRIVE_UNKNOWN",
                1: "DRIVE_NO_ROOT_DIR", 
                2: "DRIVE_REMOVABLE",
                3: "DRIVE_FIXED",
                4: "DRIVE_REMOTE",
                5: "DRIVE_CDROM",
                6: "DRIVE_RAMDISK"
            }
            
            type_name = DRIVE_TYPES.get(drive_type, "UNKNOWN")
            
            print(f"驱动器: {drive}")
            print(f"类型: {type_name}")
            
            # 尝试获取卷标
            try:
                volume_name_buffer = ctypes.create_unicode_buffer(1024)
                file_system_buffer = ctypes.create_unicode_buffer(1024)
                
                result = ctypes.windll.kernel32.GetVolumeInformationW(
                    drive_path,
                    volume_name_buffer,
                    ctypes.sizeof(volume_name_buffer),
                    None,
                    None,
                    None,
                    file_system_buffer,
                    ctypes.sizeof(file_system_buffer)
                )
                
                if result:
                    volume_name = volume_name_buffer.value or f"USB_{drive.replace(':', '')}"
                    file_system = file_system_buffer.value
                    
                    print(f"卷标: {volume_name}")
                    print(f"文件系统: {file_system}")
                    
                    # 检查是否是可移动设备
                    if drive_type == 2:  # DRIVE_REMOVABLE
                        removable_drives.append({
                            'device': drive,
                            'volume_name': volume_name,
                            'file_system': file_system
                        })
                        print("  -> 这是可移动设备!")
                else:
                    print("无法获取卷信息")
                    
            except Exception as e:
                print(f"获取卷信息失败: {e}")
            
            print("-" * 40)
        
        return removable_drives
        
    except Exception as e:
        print(f"Windows API 方法出错: {e}")
        return []

def main():
    """主函数"""
    print("USB设备检测和卷标获取测试")
    print("=" * 50)
    
    # 测试所有方法
    psutil_results = test_psutil_method()
    wmi_results = test_wmi_method()
    api_results = test_windows_api_method()
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("检测结果汇总:")
    print("=" * 50)
    
    print(f"\npsutil 方法检测到 {len(psutil_results)} 个可移动设备:")
    for drive in psutil_results:
        print(f"  - {drive}")
    
    print(f"\nWMI 方法检测到 {len(wmi_results)} 个可移动设备:")
    for drive in wmi_results:
        print(f"  - {drive}")
    
    print(f"\nWindows API 方法检测到 {len(api_results)} 个可移动设备:")
    for drive in api_results:
        print(f"  - {drive}")
    
    # 推荐最佳方法
    print("\n" + "=" * 50)
    print("推荐方案:")
    print("=" * 50)
    
    if wmi_results:
        print("✅ 推荐使用 WMI 方法 - 功能最完整，信息最详细")
    elif api_results:
        print("✅ 推荐使用 Windows API 方法 - 原生支持，无需额外依赖")
    elif psutil_results:
        print("✅ 推荐使用 psutil 方法 - 跨平台支持")
    else:
        print("❌ 未检测到可移动设备或所有方法都失败")

if __name__ == "__main__":
    main()
