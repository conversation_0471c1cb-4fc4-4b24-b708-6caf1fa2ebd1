"""
高级搜索对话框
"""

import os
from datetime import datetime, timedelta
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGroupBox,
    QPushButton, QDialogButtonBox, QLabel, QLineEdit,
    QComboBox, QSpinBox, QDateEdit, QCheckBox,
    QTableWidget, QTableWidgetItem, QHeaderView,
    QTabWidget, QWidget, QTextEdit, QMessageBox,
    QSplitter, QFrame, QListWidget, QListWidgetItem,
    QMenu, QInputDialog
)
from PySide6.QtCore import Qt, QDate, Signal, QSettings
from PySide6.QtGui import QAction
from smartvault.services.search_service import SearchService
from smartvault.services.tag_service import TagService


class AdvancedSearchDialog(QDialog):
    """高级搜索对话框"""

    # 搜索完成信号
    search_completed = Signal(list)  # 搜索结果列表

    def __init__(self, parent=None):
        """初始化对话框

        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        self.setWindowTitle("高级搜索")
        self.resize(800, 600)

        # 初始化服务
        self.search_service = SearchService()
        self.tag_service = TagService()

        # 搜索条件
        self.search_conditions = []

        # 设置管理
        self.settings = QSettings("SmartVault", "AdvancedSearch")

        # 初始化UI
        self.init_ui()

        # 加载标签列表
        self.load_tags()

        # 加载保存的搜索
        self.load_saved_searches()

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)

        # 创建选项卡
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)

        # 基本搜索选项卡
        self.basic_tab = QWidget()
        self.init_basic_search_tab()
        self.tab_widget.addTab(self.basic_tab, "基本搜索")

        # 多条件搜索选项卡
        self.multi_tab = QWidget()
        self.init_multi_condition_tab()
        self.tab_widget.addTab(self.multi_tab, "多条件搜索")

        # 保存的搜索选项卡
        self.saved_tab = QWidget()
        self.init_saved_searches_tab()
        self.tab_widget.addTab(self.saved_tab, "保存的搜索")

        # 对话框按钮
        buttons = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        buttons.accepted.connect(self.accept_search)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)

    def init_basic_search_tab(self):
        """初始化基本搜索选项卡"""
        layout = QVBoxLayout(self.basic_tab)

        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)

        # 左侧：搜索条件
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)

        # 搜索条件组
        conditions_group = QGroupBox("搜索条件")
        conditions_layout = QVBoxLayout(conditions_group)

        # 基本搜索
        basic_group = QGroupBox("基本搜索")
        basic_layout = QVBoxLayout(basic_group)

        # 文件名
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("文件名:"))
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("支持通配符 * 和 ?")
        name_layout.addWidget(self.name_edit)
        basic_layout.addLayout(name_layout)

        # 文件类型
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("文件类型:"))
        self.type_combo = QComboBox()
        self.type_combo.addItems([
            "全部类型", "文档 (*.doc, *.pdf, *.txt)",
            "图片 (*.jpg, *.png, *.gif)", "视频 (*.mp4, *.avi, *.mkv)",
            "音频 (*.mp3, *.wav, *.flac)", "压缩包 (*.zip, *.rar, *.7z)",
            "自定义..."
        ])
        type_layout.addWidget(self.type_combo)
        self.custom_type_edit = QLineEdit()
        self.custom_type_edit.setPlaceholderText("如: *.txt,*.doc")
        self.custom_type_edit.setVisible(False)
        type_layout.addWidget(self.custom_type_edit)
        basic_layout.addLayout(type_layout)

        # 文件类型变化事件
        self.type_combo.currentTextChanged.connect(self.on_type_changed)

        conditions_layout.addWidget(basic_group)

        # 高级条件
        advanced_group = QGroupBox("高级条件")
        advanced_layout = QVBoxLayout(advanced_group)

        # 文件大小
        size_layout = QHBoxLayout()
        size_layout.addWidget(QLabel("文件大小:"))
        self.size_min_spin = QSpinBox()
        self.size_min_spin.setRange(0, 999999)
        self.size_min_spin.setSuffix(" KB")
        size_layout.addWidget(QLabel("从"))
        size_layout.addWidget(self.size_min_spin)
        size_layout.addWidget(QLabel("到"))
        self.size_max_spin = QSpinBox()
        self.size_max_spin.setRange(0, 999999)
        self.size_max_spin.setSuffix(" KB")
        self.size_max_spin.setValue(999999)
        size_layout.addWidget(self.size_max_spin)
        size_layout.addStretch()
        advanced_layout.addLayout(size_layout)

        # 添加日期
        date_layout = QHBoxLayout()
        date_layout.addWidget(QLabel("添加日期:"))
        self.date_from = QDateEdit()
        self.date_from.setDate(QDate.currentDate().addDays(-30))
        self.date_from.setCalendarPopup(True)
        date_layout.addWidget(QLabel("从"))
        date_layout.addWidget(self.date_from)
        date_layout.addWidget(QLabel("到"))
        self.date_to = QDateEdit()
        self.date_to.setDate(QDate.currentDate())
        self.date_to.setCalendarPopup(True)
        date_layout.addWidget(self.date_to)
        date_layout.addStretch()
        advanced_layout.addLayout(date_layout)

        # 入库方式
        entry_layout = QHBoxLayout()
        entry_layout.addWidget(QLabel("入库方式:"))
        self.entry_combo = QComboBox()
        self.entry_combo.addItems(["全部", "链接", "复制", "移动"])
        entry_layout.addWidget(self.entry_combo)
        entry_layout.addStretch()
        advanced_layout.addLayout(entry_layout)

        # 标签
        tag_layout = QHBoxLayout()
        tag_layout.addWidget(QLabel("标签:"))
        self.tag_combo = QComboBox()
        self.tag_combo.addItem("全部标签")
        tag_layout.addWidget(self.tag_combo)
        tag_layout.addStretch()
        advanced_layout.addLayout(tag_layout)

        conditions_layout.addWidget(advanced_group)

        # 搜索选项
        options_group = QGroupBox("搜索选项")
        options_layout = QVBoxLayout(options_group)

        self.case_sensitive_check = QCheckBox("区分大小写")
        options_layout.addWidget(self.case_sensitive_check)

        self.regex_check = QCheckBox("使用正则表达式")
        options_layout.addWidget(self.regex_check)

        conditions_layout.addWidget(options_group)

        # 搜索按钮
        search_buttons_layout = QHBoxLayout()
        self.search_button = QPushButton("搜索")
        self.search_button.clicked.connect(self.perform_search)
        search_buttons_layout.addWidget(self.search_button)

        self.preview_button = QPushButton("预览")
        self.preview_button.clicked.connect(self.preview_search)
        search_buttons_layout.addWidget(self.preview_button)

        self.clear_button = QPushButton("清除")
        self.clear_button.clicked.connect(self.clear_conditions)
        search_buttons_layout.addWidget(self.clear_button)

        search_buttons_layout.addStretch()
        conditions_layout.addLayout(search_buttons_layout)

        left_layout.addWidget(conditions_group)
        left_layout.addStretch()

        # 右侧：搜索结果预览
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)

        # 结果信息
        result_info_layout = QHBoxLayout()
        self.result_count_label = QLabel("搜索结果: 0 个文件")
        result_info_layout.addWidget(self.result_count_label)
        result_info_layout.addStretch()
        right_layout.addLayout(result_info_layout)

        # 结果表格
        self.result_table = QTableWidget()
        self.result_table.setColumnCount(4)
        self.result_table.setHorizontalHeaderLabels(["文件名", "类型", "大小", "添加日期"])

        # 设置表格属性
        header = self.result_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.resizeSection(0, 200)  # 文件名
        header.resizeSection(1, 80)   # 类型
        header.resizeSection(2, 80)   # 大小

        self.result_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.result_table.setAlternatingRowColors(True)
        right_layout.addWidget(self.result_table)

        # 添加到分割器
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 2)

    def init_multi_condition_tab(self):
        """初始化多条件搜索选项卡"""
        layout = QVBoxLayout(self.multi_tab)

        # 说明文本
        info_label = QLabel("多条件搜索允许您组合多个搜索条件，使用AND/OR逻辑操作符")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # 搜索表达式
        expr_group = QGroupBox("搜索表达式")
        expr_layout = QVBoxLayout(expr_group)

        # 表达式输入
        expr_input_layout = QHBoxLayout()
        expr_input_layout.addWidget(QLabel("表达式:"))
        self.expression_edit = QLineEdit()
        self.expression_edit.setPlaceholderText("例如: (name:*.txt AND size:>1MB) OR (type:image AND date:>2024-01-01)")
        expr_input_layout.addWidget(self.expression_edit)
        expr_layout.addLayout(expr_input_layout)

        # 表达式帮助
        help_text = QTextEdit()
        help_text.setMaximumHeight(100)
        help_text.setPlainText(
            "搜索表达式语法:\n"
            "• name:pattern - 文件名匹配\n"
            "• type:extension - 文件类型\n"
            "• size:>1MB, size:<100KB - 文件大小\n"
            "• date:>2024-01-01, date:<2024-12-31 - 日期范围\n"
            "• tag:标签名 - 标签匹配\n"
            "• entry:link/copy/move - 入库方式\n"
            "• 使用 AND, OR, NOT 组合条件\n"
            "• 使用括号分组: (条件1 AND 条件2) OR 条件3"
        )
        help_text.setReadOnly(True)
        expr_layout.addWidget(help_text)

        layout.addWidget(expr_group)

        # 条件构建器
        builder_group = QGroupBox("条件构建器")
        builder_layout = QVBoxLayout(builder_group)

        # 条件列表
        self.condition_list = QListWidget()
        self.condition_list.setMaximumHeight(150)
        builder_layout.addWidget(self.condition_list)

        # 条件操作按钮
        condition_buttons_layout = QHBoxLayout()

        add_condition_button = QPushButton("添加条件")
        add_condition_button.clicked.connect(self.add_search_condition)
        condition_buttons_layout.addWidget(add_condition_button)

        edit_condition_button = QPushButton("编辑条件")
        edit_condition_button.clicked.connect(self.edit_search_condition)
        condition_buttons_layout.addWidget(edit_condition_button)

        remove_condition_button = QPushButton("删除条件")
        remove_condition_button.clicked.connect(self.remove_search_condition)
        condition_buttons_layout.addWidget(remove_condition_button)

        condition_buttons_layout.addStretch()

        # 逻辑操作符
        logic_layout = QHBoxLayout()
        logic_layout.addWidget(QLabel("条件间关系:"))
        self.logic_combo = QComboBox()
        self.logic_combo.addItems(["AND (所有条件都满足)", "OR (任一条件满足)"])
        logic_layout.addWidget(self.logic_combo)
        logic_layout.addStretch()
        condition_buttons_layout.addLayout(logic_layout)

        builder_layout.addLayout(condition_buttons_layout)

        # 生成表达式按钮
        generate_button = QPushButton("生成搜索表达式")
        generate_button.clicked.connect(self.generate_expression)
        builder_layout.addWidget(generate_button)

        layout.addWidget(builder_group)

        # 搜索按钮
        multi_search_layout = QHBoxLayout()

        multi_search_button = QPushButton("执行多条件搜索")
        multi_search_button.clicked.connect(self.perform_multi_search)
        multi_search_layout.addWidget(multi_search_button)

        multi_preview_button = QPushButton("预览结果")
        multi_preview_button.clicked.connect(self.preview_multi_search)
        multi_search_layout.addWidget(multi_preview_button)

        multi_search_layout.addStretch()
        layout.addLayout(multi_search_layout)

        layout.addStretch()

    def init_saved_searches_tab(self):
        """初始化保存的搜索选项卡"""
        layout = QVBoxLayout(self.saved_tab)

        # 保存当前搜索
        save_group = QGroupBox("保存当前搜索")
        save_layout = QVBoxLayout(save_group)

        save_input_layout = QHBoxLayout()
        save_input_layout.addWidget(QLabel("搜索名称:"))
        self.save_name_edit = QLineEdit()
        self.save_name_edit.setPlaceholderText("为当前搜索条件命名")
        save_input_layout.addWidget(self.save_name_edit)

        save_button = QPushButton("保存搜索")
        save_button.clicked.connect(self.save_current_search)
        save_input_layout.addWidget(save_button)

        save_layout.addLayout(save_input_layout)
        layout.addWidget(save_group)

        # 已保存的搜索列表
        saved_group = QGroupBox("已保存的搜索")
        saved_layout = QVBoxLayout(saved_group)

        self.saved_list = QListWidget()
        self.saved_list.itemDoubleClicked.connect(self.load_saved_search)
        saved_layout.addWidget(self.saved_list)

        # 保存的搜索操作按钮
        saved_buttons_layout = QHBoxLayout()

        load_button = QPushButton("加载搜索")
        load_button.clicked.connect(self.load_selected_search)
        saved_buttons_layout.addWidget(load_button)

        rename_button = QPushButton("重命名")
        rename_button.clicked.connect(self.rename_saved_search)
        saved_buttons_layout.addWidget(rename_button)

        delete_button = QPushButton("删除")
        delete_button.clicked.connect(self.delete_saved_search)
        saved_buttons_layout.addWidget(delete_button)

        saved_buttons_layout.addStretch()
        saved_layout.addLayout(saved_buttons_layout)

        layout.addWidget(saved_group)

        # 搜索历史
        history_group = QGroupBox("搜索历史")
        history_layout = QVBoxLayout(history_group)

        self.history_list = QListWidget()
        self.history_list.itemDoubleClicked.connect(self.load_history_search)
        history_layout.addWidget(self.history_list)

        # 历史操作按钮
        history_buttons_layout = QHBoxLayout()

        load_history_button = QPushButton("加载历史搜索")
        load_history_button.clicked.connect(self.load_selected_history)
        history_buttons_layout.addWidget(load_history_button)

        clear_history_button = QPushButton("清除历史")
        clear_history_button.clicked.connect(self.clear_search_history)
        history_buttons_layout.addWidget(clear_history_button)

        history_buttons_layout.addStretch()
        history_layout.addLayout(history_buttons_layout)

        layout.addWidget(history_group)

        layout.addStretch()

    def on_type_changed(self, text):
        """文件类型改变事件"""
        self.custom_type_edit.setVisible(text == "自定义...")

    def load_tags(self):
        """加载标签列表"""
        try:
            tags = self.tag_service.get_all_tags()
            for tag in tags:
                self.tag_combo.addItem(tag["name"], tag["id"])
        except Exception as e:
            print(f"加载标签失败: {e}")

    def clear_conditions(self):
        """清除搜索条件"""
        self.name_edit.clear()
        self.type_combo.setCurrentIndex(0)
        self.custom_type_edit.clear()
        self.size_min_spin.setValue(0)
        self.size_max_spin.setValue(999999)
        self.date_from.setDate(QDate.currentDate().addDays(-30))
        self.date_to.setDate(QDate.currentDate())
        self.entry_combo.setCurrentIndex(0)
        self.tag_combo.setCurrentIndex(0)
        self.case_sensitive_check.setChecked(False)
        self.regex_check.setChecked(False)

        # 清除结果
        self.result_table.setRowCount(0)
        self.result_count_label.setText("搜索结果: 0 个文件")

    def build_search_filters(self):
        """构建搜索过滤条件"""
        filters = {}

        # 文件名
        if self.name_edit.text().strip():
            filters["name"] = self.name_edit.text().strip()
            filters["case_sensitive"] = self.case_sensitive_check.isChecked()
            filters["use_regex"] = self.regex_check.isChecked()

        # 文件类型
        type_text = self.type_combo.currentText()
        if type_text != "全部类型":
            if type_text == "自定义...":
                if self.custom_type_edit.text().strip():
                    filters["file_types"] = self.custom_type_edit.text().strip()
            else:
                # 预定义类型
                type_mapping = {
                    "文档 (*.doc, *.pdf, *.txt)": "*.doc,*.docx,*.pdf,*.txt,*.rtf",
                    "图片 (*.jpg, *.png, *.gif)": "*.jpg,*.jpeg,*.png,*.gif,*.bmp,*.tiff",
                    "视频 (*.mp4, *.avi, *.mkv)": "*.mp4,*.avi,*.mkv,*.mov,*.wmv,*.flv",
                    "音频 (*.mp3, *.wav, *.flac)": "*.mp3,*.wav,*.flac,*.aac,*.ogg,*.wma",
                    "压缩包 (*.zip, *.rar, *.7z)": "*.zip,*.rar,*.7z,*.tar,*.gz,*.bz2"
                }
                filters["file_types"] = type_mapping.get(type_text, "")

        # 文件大小 (转换为字节)
        if self.size_min_spin.value() > 0:
            filters["min_size"] = self.size_min_spin.value() * 1024
        if self.size_max_spin.value() < 999999:
            filters["max_size"] = self.size_max_spin.value() * 1024

        # 日期范围
        date_from = self.date_from.date().toPython()
        date_to = self.date_to.date().toPython()
        if date_from != QDate.currentDate().addDays(-30).toPython():
            filters["date_from"] = date_from.isoformat()
        if date_to != QDate.currentDate().toPython():
            filters["date_to"] = (date_to + timedelta(days=1)).isoformat()  # 包含当天

        # 入库方式
        entry_text = self.entry_combo.currentText()
        if entry_text != "全部":
            entry_mapping = {"链接": "link", "复制": "copy", "移动": "move"}
            filters["entry_type"] = entry_mapping.get(entry_text)

        # 标签
        if self.tag_combo.currentIndex() > 0:
            filters["tag_id"] = self.tag_combo.currentData()

        return filters

    def preview_search(self):
        """预览搜索结果"""
        try:
            filters = self.build_search_filters()

            # 执行搜索（限制结果数量）
            results = self.search_service.search_files("", filters)

            # 更新结果表格
            self.update_result_table(results[:100])  # 只显示前100个结果

            # 更新结果计数
            total_count = len(results)
            if total_count > 100:
                self.result_count_label.setText(f"搜索结果: {total_count} 个文件 (显示前100个)")
            else:
                self.result_count_label.setText(f"搜索结果: {total_count} 个文件")

        except Exception as e:
            QMessageBox.critical(self, "搜索失败", f"预览搜索失败: {e}")

    def perform_search(self):
        """执行完整搜索"""
        try:
            filters = self.build_search_filters()
            results = self.search_service.search_files("", filters)

            # 更新结果表格
            self.update_result_table(results)

            # 更新结果计数
            self.result_count_label.setText(f"搜索结果: {len(results)} 个文件")

        except Exception as e:
            QMessageBox.critical(self, "搜索失败", f"搜索失败: {e}")

    def update_result_table(self, results):
        """更新结果表格"""
        self.result_table.setRowCount(len(results))

        for row, file_info in enumerate(results):
            # 文件名
            self.result_table.setItem(row, 0, QTableWidgetItem(file_info["name"]))

            # 类型
            ext = os.path.splitext(file_info["name"])[1].upper()
            self.result_table.setItem(row, 1, QTableWidgetItem(ext))

            # 大小
            size_text = self.format_size(file_info["size"])
            self.result_table.setItem(row, 2, QTableWidgetItem(size_text))

            # 添加日期
            added_date = datetime.fromisoformat(file_info["added_at"]).strftime("%Y-%m-%d")
            self.result_table.setItem(row, 3, QTableWidgetItem(added_date))

    def format_size(self, size):
        """格式化文件大小"""
        if size < 1024:
            return f"{size} B"
        elif size < 1024 * 1024:
            return f"{size / 1024:.1f} KB"
        elif size < 1024 * 1024 * 1024:
            return f"{size / (1024 * 1024):.1f} MB"
        else:
            return f"{size / (1024 * 1024 * 1024):.1f} GB"

    def accept_search(self):
        """确认搜索并返回结果"""
        try:
            filters = self.build_search_filters()
            results = self.search_service.search_files("", filters)

            # 发送搜索完成信号
            self.search_completed.emit(results)

            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "搜索失败", f"搜索失败: {e}")

    def get_search_results(self):
        """获取当前搜索结果"""
        try:
            filters = self.build_search_filters()
            return self.search_service.search_files("", filters)
        except Exception as e:
            print(f"获取搜索结果失败: {e}")
            return []

    # ==================== 多条件搜索功能 ====================

    def add_search_condition(self):
        """添加搜索条件"""
        from smartvault.ui.dialogs.search_condition_dialog import SearchConditionDialog

        dialog = SearchConditionDialog(self)
        if dialog.exec() == QDialog.Accepted:
            condition = dialog.get_condition()
            self.search_conditions.append(condition)
            self.update_condition_list()

    def edit_search_condition(self):
        """编辑搜索条件"""
        current_row = self.condition_list.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请先选择要编辑的条件")
            return

        from smartvault.ui.dialogs.search_condition_dialog import SearchConditionDialog

        condition = self.search_conditions[current_row]
        dialog = SearchConditionDialog(self, condition)
        if dialog.exec() == QDialog.Accepted:
            new_condition = dialog.get_condition()
            self.search_conditions[current_row] = new_condition
            self.update_condition_list()

    def remove_search_condition(self):
        """删除搜索条件"""
        current_row = self.condition_list.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请先选择要删除的条件")
            return

        reply = QMessageBox.question(
            self, "确认删除",
            "确定要删除选中的搜索条件吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            del self.search_conditions[current_row]
            self.update_condition_list()

    def update_condition_list(self):
        """更新条件列表显示"""
        self.condition_list.clear()
        for i, condition in enumerate(self.search_conditions):
            display_text = self.format_condition_display(condition)
            item = QListWidgetItem(f"{i+1}. {display_text}")
            self.condition_list.addItem(item)

    def format_condition_display(self, condition):
        """格式化条件显示文本"""
        field = condition.get("field", "")
        operator = condition.get("operator", "")
        value = condition.get("value", "")

        field_names = {
            "name": "文件名",
            "type": "文件类型",
            "size": "文件大小",
            "date": "添加日期",
            "tag": "标签",
            "entry": "入库方式"
        }

        field_display = field_names.get(field, field)
        return f"{field_display} {operator} {value}"

    def generate_expression(self):
        """生成搜索表达式"""
        if not self.search_conditions:
            QMessageBox.warning(self, "警告", "请先添加搜索条件")
            return

        logic_op = "AND" if "AND" in self.logic_combo.currentText() else "OR"

        expressions = []
        for condition in self.search_conditions:
            expr = self.condition_to_expression(condition)
            expressions.append(expr)

        full_expression = f" {logic_op} ".join(expressions)
        self.expression_edit.setText(full_expression)

    def condition_to_expression(self, condition):
        """将条件转换为表达式"""
        field = condition.get("field", "")
        operator = condition.get("operator", "")
        value = condition.get("value", "")

        if field == "size":
            # 处理文件大小
            if operator == ">":
                return f"size:>{value}"
            elif operator == "<":
                return f"size:<{value}"
            elif operator == "=":
                return f"size:{value}"
        elif field == "date":
            # 处理日期
            if operator == ">":
                return f"date:>{value}"
            elif operator == "<":
                return f"date:<{value}"
            elif operator == "=":
                return f"date:{value}"
        else:
            # 其他字段
            return f"{field}:{value}"

    def perform_multi_search(self):
        """执行多条件搜索"""
        try:
            expression = self.expression_edit.text().strip()
            if not expression:
                QMessageBox.warning(self, "警告", "请输入搜索表达式或使用条件构建器")
                return

            # 解析表达式并执行搜索
            results = self.parse_and_search(expression)

            # 保存到搜索历史
            self.add_to_history(expression)

            # 发送搜索完成信号
            self.search_completed.emit(results)

            QMessageBox.information(self, "搜索完成", f"找到 {len(results)} 个匹配的文件")

        except Exception as e:
            QMessageBox.critical(self, "搜索失败", f"多条件搜索失败: {e}")

    def preview_multi_search(self):
        """预览多条件搜索结果"""
        try:
            expression = self.expression_edit.text().strip()
            if not expression:
                QMessageBox.warning(self, "警告", "请输入搜索表达式")
                return

            # 解析表达式并执行搜索（限制结果数量）
            results = self.parse_and_search(expression, limit=50)

            # 显示预览结果
            preview_text = f"预览结果: 找到 {len(results)} 个文件\n\n"
            for i, file_info in enumerate(results[:10]):
                preview_text += f"{i+1}. {file_info['name']} ({self.format_size(file_info['size'])})\n"

            if len(results) > 10:
                preview_text += f"... 还有 {len(results) - 10} 个文件"

            QMessageBox.information(self, "搜索预览", preview_text)

        except Exception as e:
            QMessageBox.critical(self, "预览失败", f"搜索预览失败: {e}")

    def parse_and_search(self, expression, limit=None):
        """解析表达式并执行搜索"""
        # 简化的表达式解析器
        # 这里实现基本的表达式解析逻辑

        # 暂时使用简单的解析方式
        filters = self.parse_simple_expression(expression)

        results = self.search_service.search_files("", filters)

        if limit:
            return results[:limit]
        return results

    def parse_simple_expression(self, expression):
        """简单的表达式解析"""
        filters = {}

        # 分割条件（简化处理，不考虑括号）
        if " AND " in expression:
            conditions = expression.split(" AND ")
        elif " OR " in expression:
            conditions = expression.split(" OR ")
        else:
            conditions = [expression]

        for condition in conditions:
            condition = condition.strip()
            if ":" in condition:
                field, value = condition.split(":", 1)
                field = field.strip()
                value = value.strip()

                if field == "name":
                    filters["name"] = value
                elif field == "type":
                    filters["file_types"] = f"*.{value}"
                elif field == "size":
                    if value.startswith(">"):
                        size_str = value[1:]
                        filters["min_size"] = self.parse_size(size_str)
                    elif value.startswith("<"):
                        size_str = value[1:]
                        filters["max_size"] = self.parse_size(size_str)
                elif field == "date":
                    if value.startswith(">"):
                        filters["date_from"] = value[1:]
                    elif value.startswith("<"):
                        filters["date_to"] = value[1:]
                elif field == "entry":
                    filters["entry_type"] = value

        return filters

    def parse_size(self, size_str):
        """解析文件大小字符串"""
        size_str = size_str.upper()
        if size_str.endswith("KB"):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith("MB"):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith("GB"):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)

    # ==================== 保存的搜索功能 ====================

    def save_current_search(self):
        """保存当前搜索"""
        name = self.save_name_edit.text().strip()
        if not name:
            QMessageBox.warning(self, "警告", "请输入搜索名称")
            return

        # 获取当前搜索条件
        current_tab = self.tab_widget.currentIndex()
        if current_tab == 0:  # 基本搜索
            search_data = {
                "type": "basic",
                "filters": self.build_search_filters()
            }
        elif current_tab == 1:  # 多条件搜索
            search_data = {
                "type": "multi",
                "expression": self.expression_edit.text(),
                "conditions": self.search_conditions
            }
        else:
            QMessageBox.warning(self, "警告", "请在基本搜索或多条件搜索选项卡中保存")
            return

        # 保存到设置
        saved_searches = self.settings.value("saved_searches", {})
        saved_searches[name] = search_data
        self.settings.setValue("saved_searches", saved_searches)

        # 更新列表
        self.load_saved_searches()

        # 清空输入框
        self.save_name_edit.clear()

        QMessageBox.information(self, "保存成功", f"搜索 '{name}' 已保存")

    def load_saved_searches(self):
        """加载保存的搜索"""
        if not hasattr(self, 'saved_list'):
            return

        self.saved_list.clear()
        saved_searches = self.settings.value("saved_searches", {})

        for name, data in saved_searches.items():
            item = QListWidgetItem(name)
            item.setData(Qt.UserRole, data)
            self.saved_list.addItem(item)

    def load_selected_search(self):
        """加载选中的保存搜索"""
        current_item = self.saved_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "警告", "请先选择要加载的搜索")
            return

        self.load_saved_search(current_item)

    def load_saved_search(self, item):
        """加载保存的搜索"""
        search_data = item.data(Qt.UserRole)

        if search_data["type"] == "basic":
            # 切换到基本搜索选项卡
            self.tab_widget.setCurrentIndex(0)
            # 加载基本搜索条件
            self.load_basic_filters(search_data["filters"])
        elif search_data["type"] == "multi":
            # 切换到多条件搜索选项卡
            self.tab_widget.setCurrentIndex(1)
            # 加载多条件搜索
            self.expression_edit.setText(search_data["expression"])
            self.search_conditions = search_data.get("conditions", [])
            self.update_condition_list()

    def load_basic_filters(self, filters):
        """加载基本搜索过滤条件"""
        # 实现基本搜索条件的加载
        if "name" in filters:
            self.name_edit.setText(filters["name"])

        if "file_types" in filters:
            # 设置文件类型
            self.custom_type_edit.setText(filters["file_types"])
            self.type_combo.setCurrentText("自定义...")

        # 其他条件的加载...

    def rename_saved_search(self):
        """重命名保存的搜索"""
        current_item = self.saved_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "警告", "请先选择要重命名的搜索")
            return

        old_name = current_item.text()
        new_name, ok = QInputDialog.getText(self, "重命名搜索", "新名称:", text=old_name)

        if ok and new_name.strip():
            saved_searches = self.settings.value("saved_searches", {})
            search_data = saved_searches.pop(old_name)
            saved_searches[new_name.strip()] = search_data
            self.settings.setValue("saved_searches", saved_searches)

            self.load_saved_searches()

    def delete_saved_search(self):
        """删除保存的搜索"""
        current_item = self.saved_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "警告", "请先选择要删除的搜索")
            return

        name = current_item.text()
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除搜索 '{name}' 吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            saved_searches = self.settings.value("saved_searches", {})
            saved_searches.pop(name, None)
            self.settings.setValue("saved_searches", saved_searches)

            self.load_saved_searches()

    def add_to_history(self, expression):
        """添加到搜索历史"""
        history = self.settings.value("search_history", [])

        # 避免重复
        if expression in history:
            history.remove(expression)

        # 添加到开头
        history.insert(0, expression)

        # 限制历史记录数量
        if len(history) > 20:
            history = history[:20]

        self.settings.setValue("search_history", history)
        self.load_search_history()

    def load_search_history(self):
        """加载搜索历史"""
        if not hasattr(self, 'history_list'):
            return

        self.history_list.clear()
        history = self.settings.value("search_history", [])

        for expression in history:
            item = QListWidgetItem(expression)
            self.history_list.addItem(item)

    def load_selected_history(self):
        """加载选中的历史搜索"""
        current_item = self.history_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "警告", "请先选择要加载的历史搜索")
            return

        self.load_history_search(current_item)

    def load_history_search(self, item):
        """加载历史搜索"""
        expression = item.text()

        # 切换到多条件搜索选项卡
        self.tab_widget.setCurrentIndex(1)
        self.expression_edit.setText(expression)

    def clear_search_history(self):
        """清除搜索历史"""
        reply = QMessageBox.question(
            self, "确认清除",
            "确定要清除所有搜索历史吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.settings.setValue("search_history", [])
            self.load_search_history()
