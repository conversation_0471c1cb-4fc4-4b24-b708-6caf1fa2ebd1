#!/usr/bin/env python3
"""
测试多条件自动标签功能
"""

import unittest
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from smartvault.services.auto_tag_service import (
    AutoTagRule, AutoTagService, Condition, ConditionGroup, 
    ConditionType, LogicOperator
)


class TestMultiConditionAutoTag(unittest.TestCase):
    """测试多条件自动标签功能"""
    
    def setUp(self):
        """设置测试"""
        self.service = AutoTagService()
    
    def test_single_condition_compatibility(self):
        """测试单条件兼容性"""
        # 创建旧版本格式的规则
        rule = AutoTagRule(
            id="test_rule",
            name="PDF文档规则",
            tag_names=["文档", "PDF"],
            condition_type=ConditionType.FILE_EXTENSION,
            condition_value="pdf"
        )
        
        # 测试匹配
        file_info = {"name": "test.pdf", "size": 1024}
        self.assertTrue(rule.matches(file_info))
        
        file_info = {"name": "test.txt", "size": 1024}
        self.assertFalse(rule.matches(file_info))
    
    def test_multi_condition_and(self):
        """测试多条件AND组合"""
        # 创建条件组：文件扩展名是pdf AND 文件大小>1MB
        condition_group = ConditionGroup(
            operator=LogicOperator.AND,
            conditions=[
                Condition(type=ConditionType.FILE_EXTENSION, value="pdf"),
                Condition(type=ConditionType.FILE_SIZE, value=">1MB")
            ]
        )
        
        rule = AutoTagRule(
            id="test_rule",
            name="大PDF文档规则",
            tag_names=["大文档", "PDF"],
            condition_group=condition_group
        )
        
        # 测试匹配：PDF且大于1MB
        file_info = {"name": "test.pdf", "size": 2 * 1024 * 1024}  # 2MB
        self.assertTrue(rule.matches(file_info))
        
        # 测试不匹配：PDF但小于1MB
        file_info = {"name": "test.pdf", "size": 512 * 1024}  # 512KB
        self.assertFalse(rule.matches(file_info))
        
        # 测试不匹配：大于1MB但不是PDF
        file_info = {"name": "test.txt", "size": 2 * 1024 * 1024}  # 2MB
        self.assertFalse(rule.matches(file_info))
    
    def test_multi_condition_or(self):
        """测试多条件OR组合"""
        # 创建条件组：文件扩展名是pdf OR 文件扩展名是doc
        condition_group = ConditionGroup(
            operator=LogicOperator.OR,
            conditions=[
                Condition(type=ConditionType.FILE_EXTENSION, value="pdf"),
                Condition(type=ConditionType.FILE_EXTENSION, value="doc,docx")
            ]
        )
        
        rule = AutoTagRule(
            id="test_rule",
            name="文档规则",
            tag_names=["文档"],
            condition_group=condition_group
        )
        
        # 测试匹配：PDF
        file_info = {"name": "test.pdf", "size": 1024}
        self.assertTrue(rule.matches(file_info))
        
        # 测试匹配：DOC
        file_info = {"name": "test.doc", "size": 1024}
        self.assertTrue(rule.matches(file_info))
        
        # 测试匹配：DOCX
        file_info = {"name": "test.docx", "size": 1024}
        self.assertTrue(rule.matches(file_info))
        
        # 测试不匹配：TXT
        file_info = {"name": "test.txt", "size": 1024}
        self.assertFalse(rule.matches(file_info))
    
    def test_complex_nested_conditions(self):
        """测试复杂嵌套条件"""
        # 创建复杂条件：(扩展名是pdf OR 扩展名是doc) AND (大小>1MB OR 文件名包含"重要")
        inner_group1 = ConditionGroup(
            operator=LogicOperator.OR,
            conditions=[
                Condition(type=ConditionType.FILE_EXTENSION, value="pdf"),
                Condition(type=ConditionType.FILE_EXTENSION, value="doc,docx")
            ]
        )
        
        inner_group2 = ConditionGroup(
            operator=LogicOperator.OR,
            conditions=[
                Condition(type=ConditionType.FILE_SIZE, value=">1MB"),
                Condition(type=ConditionType.FILE_NAME_PATTERN, value="重要")
            ]
        )
        
        main_group = ConditionGroup(
            operator=LogicOperator.AND,
            conditions=[inner_group1, inner_group2]
        )
        
        rule = AutoTagRule(
            id="test_rule",
            name="重要文档规则",
            tag_names=["重要文档"],
            condition_group=main_group
        )
        
        # 测试匹配：PDF + 大文件
        file_info = {"name": "test.pdf", "size": 2 * 1024 * 1024}
        self.assertTrue(rule.matches(file_info))
        
        # 测试匹配：DOC + 文件名包含"重要"
        file_info = {"name": "重要报告.doc", "size": 512 * 1024}
        self.assertTrue(rule.matches(file_info))
        
        # 测试不匹配：PDF但小文件且文件名不包含"重要"
        file_info = {"name": "test.pdf", "size": 512 * 1024}
        self.assertFalse(rule.matches(file_info))
        
        # 测试不匹配：TXT文件（即使满足其他条件）
        file_info = {"name": "重要文档.txt", "size": 2 * 1024 * 1024}
        self.assertFalse(rule.matches(file_info))
    
    def test_rule_description(self):
        """测试规则描述功能"""
        # 测试单条件描述
        rule = AutoTagRule(
            id="test_rule",
            name="PDF规则",
            tag_names=["PDF"],
            condition_type=ConditionType.FILE_EXTENSION,
            condition_value="pdf"
        )
        
        description = rule.get_description()
        self.assertIn("文件扩展名", description)
        self.assertIn("pdf", description)
        
        # 测试多条件描述
        condition_group = ConditionGroup(
            operator=LogicOperator.AND,
            conditions=[
                Condition(type=ConditionType.FILE_EXTENSION, value="pdf"),
                Condition(type=ConditionType.FILE_SIZE, value=">1MB")
            ]
        )
        
        rule = AutoTagRule(
            id="test_rule",
            name="大PDF规则",
            tag_names=["大PDF"],
            condition_group=condition_group
        )
        
        description = rule.get_description()
        self.assertIn("文件扩展名", description)
        self.assertIn("文件大小", description)
        self.assertIn("且", description)
    
    def test_service_integration(self):
        """测试服务集成"""
        # 创建多条件规则
        condition_group = ConditionGroup(
            operator=LogicOperator.AND,
            conditions=[
                Condition(type=ConditionType.FILE_TYPE, value="图片"),
                Condition(type=ConditionType.FILE_SIZE, value=">500KB")
            ]
        )
        
        rule = AutoTagRule(
            id="test_rule",
            name="大图片规则",
            tag_names=["大图片", "媒体"],
            condition_group=condition_group
        )
        
        self.service.add_rule(rule)
        
        # 测试匹配的文件
        file_info = {"name": "photo.jpg", "size": 1024 * 1024}  # 1MB
        auto_tags = self.service.get_auto_tags_for_file(file_info)
        self.assertIn("大图片", auto_tags)
        self.assertIn("媒体", auto_tags)
        
        # 测试不匹配的文件（小图片）
        file_info = {"name": "icon.png", "size": 100 * 1024}  # 100KB
        auto_tags = self.service.get_auto_tags_for_file(file_info)
        self.assertEqual(len(auto_tags), 0)


def run_tests():
    """运行测试"""
    print("开始多条件自动标签功能测试...")
    
    # 创建测试套件
    suite = unittest.TestSuite()
    suite.addTest(unittest.makeSuite(TestMultiConditionAutoTag))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    if result.wasSuccessful():
        print("\n✅ 所有多条件自动标签测试通过！")
        return True
    else:
        print(f"\n❌ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        for failure in result.failures:
            print(f"失败: {failure[0]}")
            print(f"详情: {failure[1]}")
        for error in result.errors:
            print(f"错误: {error[0]}")
            print(f"详情: {error[1]}")
        return False


if __name__ == "__main__":
    run_tests()
