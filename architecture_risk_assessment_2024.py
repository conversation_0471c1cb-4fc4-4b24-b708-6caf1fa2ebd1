#!/usr/bin/env python3
"""
SmartVault 架构风险评估工具 (2024年12月版)
基于当前代码状态的全面架构风险分析
"""

import os
import ast
import json
from pathlib import Path
from typing import Dict, List, Tuple
from dataclasses import dataclass
from datetime import datetime

@dataclass
class FileMetrics:
    """文件度量指标"""
    path: str
    lines: int
    classes: int
    methods: int
    complexity_score: float
    last_modified: str
    risk_level: str

@dataclass
class ArchitectureRisk:
    """架构风险评估"""
    risk_id: str
    component: str
    description: str
    current_status: str
    impact_level: str
    urgency: str
    recommendation: str

class ArchitectureAnalyzer:
    """架构分析器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.metrics = []
        self.risks = []
        
        # 风险阈值定义
        self.thresholds = {
            'file_lines_warning': 500,
            'file_lines_critical': 800,
            'class_methods_warning': 15,
            'class_methods_critical': 25,
            'complexity_warning': 10,
            'complexity_critical': 20
        }
    
    def analyze_project(self) -> Dict:
        """分析整个项目"""
        print("🔍 开始架构风险评估...")
        
        # 1. 扫描所有Python文件
        self._scan_python_files()
        
        # 2. 分析文件复杂度
        self._analyze_complexity()
        
        # 3. 识别架构风险
        self._identify_risks()
        
        # 4. 生成评估报告
        return self._generate_report()
    
    def _scan_python_files(self):
        """扫描Python文件"""
        python_files = list(self.project_root.rglob("*.py"))
        
        for file_path in python_files:
            # 跳过测试文件和临时文件
            if any(skip in str(file_path) for skip in ['test_', '__pycache__', '.git', 'venv']):
                continue
                
            try:
                metrics = self._analyze_file(file_path)
                if metrics:
                    self.metrics.append(metrics)
            except Exception as e:
                print(f"⚠️ 分析文件失败: {file_path} - {e}")
    
    def _analyze_file(self, file_path: Path) -> FileMetrics:
        """分析单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = len(content.splitlines())
            
            # 解析AST
            tree = ast.parse(content)
            
            classes = len([node for node in ast.walk(tree) if isinstance(node, ast.ClassDef)])
            methods = len([node for node in ast.walk(tree) if isinstance(node, ast.FunctionDef)])
            
            # 计算复杂度分数
            complexity_score = self._calculate_complexity(tree, lines, classes, methods)
            
            # 确定风险等级
            risk_level = self._determine_risk_level(lines, classes, methods, complexity_score)
            
            # 获取修改时间
            last_modified = datetime.fromtimestamp(file_path.stat().st_mtime).strftime('%Y-%m-%d')
            
            return FileMetrics(
                path=str(file_path.relative_to(self.project_root)),
                lines=lines,
                classes=classes,
                methods=methods,
                complexity_score=complexity_score,
                last_modified=last_modified,
                risk_level=risk_level
            )
        except Exception as e:
            print(f"⚠️ 文件分析错误: {file_path} - {e}")
            return None
    
    def _calculate_complexity(self, tree, lines, classes, methods) -> float:
        """计算复杂度分数"""
        # 基础复杂度：文件行数权重
        base_score = lines / 100
        
        # 类复杂度：类数量和平均方法数
        if classes > 0:
            avg_methods_per_class = methods / classes
            class_complexity = classes * (1 + avg_methods_per_class / 10)
        else:
            class_complexity = 0
        
        # 嵌套复杂度：检查嵌套结构
        nested_complexity = 0
        for node in ast.walk(tree):
            if isinstance(node, (ast.If, ast.For, ast.While, ast.Try)):
                nested_complexity += 1
        
        return base_score + class_complexity + (nested_complexity / 10)
    
    def _determine_risk_level(self, lines, classes, methods, complexity_score) -> str:
        """确定风险等级"""
        if (lines >= self.thresholds['file_lines_critical'] or 
            complexity_score >= self.thresholds['complexity_critical']):
            return "🔴 高风险"
        elif (lines >= self.thresholds['file_lines_warning'] or 
              complexity_score >= self.thresholds['complexity_warning']):
            return "🟡 中风险"
        else:
            return "🟢 低风险"
    
    def _analyze_complexity(self):
        """分析复杂度分布"""
        # 按风险等级分组
        risk_distribution = {"🔴 高风险": 0, "🟡 中风险": 0, "🟢 低风险": 0}
        
        for metric in self.metrics:
            risk_distribution[metric.risk_level] += 1
        
        print(f"\n📊 风险分布:")
        for risk, count in risk_distribution.items():
            print(f"   {risk}: {count} 个文件")
    
    def _identify_risks(self):
        """识别架构风险"""
        # 检查超长文件
        for metric in self.metrics:
            if metric.lines >= self.thresholds['file_lines_critical']:
                self.risks.append(ArchitectureRisk(
                    risk_id=f"AR001_{metric.path.replace('/', '_')}",
                    component=metric.path,
                    description=f"文件过长 ({metric.lines} 行)，超过临界阈值",
                    current_status="需要立即重构",
                    impact_level="高",
                    urgency="立即",
                    recommendation="模块化重构，拆分为多个文件"
                ))
            elif metric.lines >= self.thresholds['file_lines_warning']:
                self.risks.append(ArchitectureRisk(
                    risk_id=f"AR002_{metric.path.replace('/', '_')}",
                    component=metric.path,
                    description=f"文件较长 ({metric.lines} 行)，接近警告阈值",
                    current_status="需要监控",
                    impact_level="中",
                    urgency="计划中",
                    recommendation="评估重构可行性，预防性优化"
                ))
        
        # 检查复杂度过高的文件
        high_complexity_files = [m for m in self.metrics if m.complexity_score >= self.thresholds['complexity_critical']]
        if high_complexity_files:
            for metric in high_complexity_files:
                self.risks.append(ArchitectureRisk(
                    risk_id=f"AR003_{metric.path.replace('/', '_')}",
                    component=metric.path,
                    description=f"复杂度过高 (分数: {metric.complexity_score:.1f})",
                    current_status="需要简化",
                    impact_level="中",
                    urgency="计划中",
                    recommendation="简化逻辑，提取公共方法"
                ))
    
    def _generate_report(self) -> Dict:
        """生成评估报告"""
        # 排序：按行数降序
        sorted_metrics = sorted(self.metrics, key=lambda x: x.lines, reverse=True)
        
        # 统计信息
        total_files = len(self.metrics)
        total_lines = sum(m.lines for m in self.metrics)
        high_risk_files = len([m for m in self.metrics if m.risk_level == "🔴 高风险"])
        medium_risk_files = len([m for m in self.metrics if m.risk_level == "🟡 中风险"])
        
        report = {
            'assessment_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'summary': {
                'total_files': total_files,
                'total_lines': total_lines,
                'high_risk_files': high_risk_files,
                'medium_risk_files': medium_risk_files,
                'architecture_health': self._calculate_health_score()
            },
            'top_risk_files': sorted_metrics[:10],
            'identified_risks': self.risks,
            'recommendations': self._generate_recommendations()
        }
        
        return report
    
    def _calculate_health_score(self) -> str:
        """计算架构健康度"""
        total_files = len(self.metrics)
        if total_files == 0:
            return "无法评估"
        
        high_risk = len([m for m in self.metrics if m.risk_level == "🔴 高风险"])
        medium_risk = len([m for m in self.metrics if m.risk_level == "🟡 中风险"])
        
        risk_ratio = (high_risk * 2 + medium_risk) / total_files
        
        if risk_ratio <= 0.1:
            return "A+ (优秀)"
        elif risk_ratio <= 0.2:
            return "A (良好)"
        elif risk_ratio <= 0.3:
            return "B (一般)"
        elif risk_ratio <= 0.5:
            return "C (需要改进)"
        else:
            return "D (需要重构)"
    
    def _generate_recommendations(self) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        high_risk_count = len([r for r in self.risks if r.impact_level == "高"])
        if high_risk_count > 0:
            recommendations.append(f"立即处理 {high_risk_count} 个高风险项目")
        
        medium_risk_count = len([r for r in self.risks if r.impact_level == "中"])
        if medium_risk_count > 0:
            recommendations.append(f"计划处理 {medium_risk_count} 个中风险项目")
        
        # 检查是否有超长文件
        long_files = [m for m in self.metrics if m.lines >= 800]
        if long_files:
            recommendations.append("优先重构超长文件，采用模块化设计")
        
        return recommendations

def main():
    """主函数"""
    analyzer = ArchitectureAnalyzer(".")
    report = analyzer.analyze_project()
    
    # 输出报告
    print(f"\n📋 架构风险评估报告")
    print(f"评估时间: {report['assessment_date']}")
    print(f"架构健康度: {report['summary']['architecture_health']}")
    print(f"总文件数: {report['summary']['total_files']}")
    print(f"总代码行数: {report['summary']['total_lines']}")
    print(f"高风险文件: {report['summary']['high_risk_files']}")
    print(f"中风险文件: {report['summary']['medium_risk_files']}")
    
    print(f"\n🔝 风险最高的文件:")
    for i, metric in enumerate(report['top_risk_files'][:5], 1):
        print(f"   {i}. {metric.path} - {metric.lines}行 {metric.risk_level}")
    
    print(f"\n⚠️ 识别的风险:")
    for risk in report['identified_risks'][:5]:
        print(f"   • {risk.component}: {risk.description}")
    
    print(f"\n💡 改进建议:")
    for rec in report['recommendations']:
        print(f"   • {rec}")
    
    # 保存详细报告
    with open('architecture_assessment_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n📄 详细报告已保存到: architecture_assessment_report.json")

if __name__ == "__main__":
    main()
