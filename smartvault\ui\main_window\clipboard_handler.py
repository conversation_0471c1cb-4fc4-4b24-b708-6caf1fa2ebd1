"""
剪贴板处理器 - 从core.py拆分出的剪贴板相关功能
负责处理剪贴板监控的启动、停止、状态管理和浮动窗口控制等功能
"""

from PySide6.QtCore import Qt


class ClipboardHandler:
    """剪贴板处理器类 - 处理所有剪贴板相关的UI交互"""

    def __init__(self, main_window):
        """初始化剪贴板处理器

        Args:
            main_window: 主窗口实例
        """
        self.main_window = main_window

    def start_clipboard_monitor_if_enabled(self):
        """根据配置启动剪贴板监控"""
        try:
            from smartvault.utils.config import get_clipboard_status

            # 检查剪贴板监控状态（由UI工具栏控制）
            enabled = get_clipboard_status()

            print(f"剪贴板监控状态: enabled={enabled}")

            if enabled:
                # 启动剪贴板监控
                self.main_window.clipboard_service.start_monitoring()
                self.main_window.show_status_message("剪贴板查重已自动启动", True)

                # 更新工具栏状态
                if hasattr(self.main_window, 'toolbar_manager'):
                    self.main_window.toolbar_manager.update_clipboard_status(True)

                # 更新浮动窗口状态
                self.main_window.clipboard_floating_widget.update_monitoring_status("监控中")

                # 设置监控启用状态
                self.main_window.clipboard_floating_widget.set_monitoring_enabled(True)

                print("剪贴板监控已自动启动")
            else:
                # 更新浮动窗口状态为未启动
                self.main_window.clipboard_floating_widget.update_monitoring_status("未启动")

                # 设置监控禁用状态
                self.main_window.clipboard_floating_widget.set_monitoring_enabled(False)

                print("剪贴板监控已禁用")

        except Exception as e:
            print(f"启动剪贴板监控失败: {e}")
            import traceback
            traceback.print_exc()

    def setup_clipboard_float_mode(self):
        """设置剪贴板浮动窗口模式"""
        try:
            from smartvault.utils.config import load_config
            config = load_config()

            clipboard_config = config.get("clipboard", {})
            float_mode = clipboard_config.get("float_mode", "on_demand")

            # 设置浮动窗口模式
            is_persistent = (float_mode == "persistent")
            self.main_window.clipboard_floating_widget.set_persistent_mode(is_persistent)

            if is_persistent:
                print("剪贴板浮动窗口设置为持续显示模式")
            else:
                print("剪贴板浮动窗口设置为按需弹出模式")

        except Exception as e:
            print(f"设置浮动窗口模式失败: {e}")

    def toggle_clipboard_monitor(self):
        """切换剪贴板监控状态"""
        try:
            from smartvault.utils.config import save_clipboard_status

            if self.main_window.clipboard_service.is_monitoring:
                # 停止监控
                self.main_window.clipboard_service.stop_monitoring()
                self.main_window.show_status_message("剪贴板查重已停止", True)

                # 保存状态到配置文件
                save_clipboard_status(False)

                # 更新工具栏状态
                if hasattr(self.main_window, 'toolbar_manager'):
                    self.main_window.toolbar_manager.update_clipboard_status(False)

                # 更新浮动窗口状态
                self.main_window.clipboard_floating_widget.update_monitoring_status("已停止")

                # 设置监控禁用状态（这会隐藏浮动窗口）
                self.main_window.clipboard_floating_widget.set_monitoring_enabled(False)

            else:
                # 启动监控
                self.main_window.clipboard_service.start_monitoring()
                self.main_window.show_status_message("剪贴板查重已启动", True)

                # 保存状态到配置文件
                save_clipboard_status(True)

                # 更新工具栏状态
                if hasattr(self.main_window, 'toolbar_manager'):
                    self.main_window.toolbar_manager.update_clipboard_status(True)

                # 更新浮动窗口状态
                self.main_window.clipboard_floating_widget.update_monitoring_status("监控中")

                # 设置监控启用状态（这会根据模式决定是否显示浮动窗口）
                self.main_window.clipboard_floating_widget.set_monitoring_enabled(True)

        except Exception as e:
            print(f"切换剪贴板监控状态失败: {e}")

    def show_clipboard_demo(self):
        """显示剪贴板功能演示"""
        try:
            # 创建演示数据
            demo_info = {
                'type': 'demo',
                'source_text': '演示：剪贴板查重功能已启动',
                'cleaned_name': '演示文件.pdf',
                'duplicates': [
                    {
                        'id': 'demo',
                        'name': '演示文件.pdf',
                        'original_path': '演示路径',
                        'library_path': '演示/演示文件.pdf',
                        'entry_type': 'file'
                    }
                ]
            }

            # 显示演示浮动窗口
            self.main_window.clipboard_floating_widget.show_duplicate(demo_info)

        except Exception as e:
            print(f"显示剪贴板演示失败: {e}")

    def on_clipboard_duplicate_found(self, duplicate_info):
        """处理剪贴板重复文件发现事件"""
        try:
            self.main_window.clipboard_floating_widget.show_duplicate(duplicate_info)
        except Exception as e:
            print(f"处理剪贴板重复文件事件失败: {e}")

    def on_clipboard_open_file(self, file_id):
        """处理剪贴板浮动窗的打开文件请求"""
        try:
            # 在文件视图中定位并选中文件
            self._locate_and_select_file(file_id)
            self.main_window.show_status_message(f"已定位到重复文件", True)
        except Exception as e:
            print(f"定位剪贴板文件失败: {e}")
            self.main_window.show_status_message("定位文件失败", False)

    def _locate_and_select_file(self, file_id):
        """在文件视图中定位并选中指定文件（支持跨页面定位）"""
        try:
            print(f"🎯 开始定位文件: {file_id}")

            # 确保主窗口获得焦点
            if hasattr(self.main_window, 'raise_'):
                self.main_window.raise_()
            if hasattr(self.main_window, 'activateWindow'):
                self.main_window.activateWindow()

            # 在当前文件视图中定位文件
            if hasattr(self.main_window, 'file_view') and self.main_window.file_view:
                print("📁 文件视图存在，开始定位...")

                # 获取当前活动的模型和视图
                model = self.main_window.file_view.get_current_model()
                current_view = self.main_window.file_view.get_current_view()

                if model and current_view:
                    print(f"📊 当前模型: {type(model).__name__}, 视图: {type(current_view).__name__}")

                    # 首先尝试在当前页面查找
                    found = self._select_file_in_current_page(file_id, model, current_view)

                    if not found:
                        print("🔍 当前页面未找到，尝试跨页面定位...")
                        # 如果当前页面没找到，尝试跨页面定位
                        self._locate_file_across_pages(file_id, model, current_view)
                else:
                    print("❌ 模型或视图为空")
                    self.main_window.show_status_message("文件视图未就绪", False)
            else:
                print("❌ 文件视图不存在")
                self.main_window.show_status_message("文件视图不存在", False)

        except Exception as e:
            print(f"❌ 定位文件失败: {e}")
            import traceback
            traceback.print_exc()
            self.main_window.show_status_message("定位文件失败", False)

    def _select_file_in_current_page(self, file_id, model, current_view):
        """在当前页面中选中指定文件"""
        try:
            from PySide6.QtWidgets import QApplication
            from PySide6.QtCore import Qt

            QApplication.processEvents()

            # 在当前页面的数据中查找文件
            for row in range(model.rowCount()):
                index = model.index(row, 0)  # 第一列

                # 获取文件ID - 使用正确的角色
                current_file_id = None

                # 优先使用FileIdRole（如果模型定义了）
                if hasattr(model, 'FileIdRole'):
                    current_file_id = model.data(index, model.FileIdRole)
                    print(f"🔍 行{row}: 使用FileIdRole获取ID = {current_file_id}")

                # 如果没有FileIdRole，尝试UserRole作为备用方案（向后兼容）
                if not current_file_id:
                    current_file_id = model.data(index, Qt.UserRole)
                    print(f"🔍 行{row}: 使用UserRole获取ID = {current_file_id} (备用方案)")

                if current_file_id and str(current_file_id) == str(file_id):
                    print(f"🎯 找到目标文件在第 {row+1} 行")

                    # 选中文件的方法取决于视图类型
                    if hasattr(current_view, 'selectRow'):
                        # 表格视图
                        current_view.selectRow(row)
                        print("✅ 使用selectRow选中文件")
                    elif hasattr(current_view, 'setCurrentIndex'):
                        # 其他视图类型
                        current_view.setCurrentIndex(index)
                        print("✅ 使用setCurrentIndex选中文件")

                    # 滚动到选中项
                    if hasattr(current_view, 'scrollTo'):
                        current_view.scrollTo(index)
                        print("✅ 滚动到选中文件")

                    # 确保视图获得焦点
                    if hasattr(current_view, 'setFocus'):
                        current_view.setFocus()

                    print(f"✅ 成功定位并选中文件: {file_id} (第{row+1}行)")
                    self.main_window.show_status_message("已定位到重复文件", True)
                    return True

            print(f"❌ 在当前页面未找到文件: {file_id}")
            return False

        except Exception as e:
            print(f"在当前页面选中文件失败: {e}")
            return False

    def _locate_file_across_pages(self, file_id, model, current_view):
        """跨页面定位文件"""
        try:
            from PySide6.QtCore import QTimer

            # 使用文件服务查询文件所在的页面
            file_service = self.main_window.file_service

            # 获取文件信息
            file_info = file_service.get_file_by_id(file_id)
            if not file_info:
                print(f"❌ 文件不存在: {file_id}")
                self.main_window.show_status_message("文件不存在", False)
                return

            print(f"🔍 跨页面定位文件: {file_id}")
            print(f"📄 文件信息: {file_info['name']}")

            # 简化实现：跳转到第一页并重新搜索
            print("🔄 跳转到第一页重新搜索...")
            if hasattr(model, 'goToPage'):
                model.goToPage(0)  # 跳转到第一页

                # 等待数据加载后再次尝试选中
                QTimer.singleShot(500, lambda: self._select_file_in_current_page(file_id, model, current_view))
            elif hasattr(model, 'load_page'):
                model.load_page(1)

                # 等待数据加载
                QTimer.singleShot(500, lambda: self._select_file_in_current_page(file_id, model, current_view))
            else:
                print("❌ 模型不支持分页跳转")
                self.main_window.show_status_message("无法跨页面定位文件", False)

        except Exception as e:
            print(f"跨页面定位失败: {e}")
            self.main_window.show_status_message("跨页面定位失败", False)

    def cleanup(self):
        """清理资源"""
        try:
            # 剪贴板处理器通常不需要特殊清理
            # 因为剪贴板服务由主窗口管理
            pass
        except Exception as e:
            print(f"清理剪贴板处理器资源失败: {e}")
