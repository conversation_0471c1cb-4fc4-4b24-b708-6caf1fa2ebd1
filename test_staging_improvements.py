#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试中转文件夹用户体验改进

验证阶段1的改进效果：
1. "移出中转文件夹" → "移至智能文件库"
2. 移动后状态提示改进
3. "移动到文件夹"功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PySide6.QtCore import Qt

from smartvault.ui.views.file_grid_view import FileGridView
from smartvault.ui.models.file_grid_model import FileGridModel
from smartvault.services.file import FileService
from smartvault.services.tag_service import TagService


class StagingImprovementsDemo(QMainWindow):
    """中转文件夹改进演示窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("SmartVault - 中转文件夹用户体验改进演示")
        self.setGeometry(100, 100, 1000, 700)
        
        # 初始化服务
        self.file_service = FileService()
        self.tag_service = TagService()
        
        # 创建中央窗口
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 添加说明标签
        info_label = QLabel("""
🎯 中转文件夹用户体验改进演示

本演示展示了阶段1的改进效果：

✅ 改进1：菜单文本优化
• "移出中转文件夹" → "移至智能文件库"
• "批量移出中转文件夹" → "批量移至智能文件库"

✅ 改进2：状态提示明确化
• 移动后显示："文件已移至智能文件库，当前显示：全部文件"
• 批量操作显示："成功移至智能文件库 X 个文件，当前显示：全部文件"

✅ 改进3：移动到文件夹功能
• 文件右键菜单新增"移动到文件夹"子菜单
• 支持移动到现有自定义文件夹
• 支持创建新文件夹并移动文件
• 自动移出中转状态

🔧 操作方法：
1. 右键点击文件查看新的菜单选项
2. 测试"移至智能文件库"功能和状态提示
3. 测试"移动到文件夹"功能
        """)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #f0f0f0;
                border: 1px solid #ccc;
                border-radius: 5px;
                padding: 10px;
                font-size: 12px;
                line-height: 1.4;
            }
        """)
        layout.addWidget(info_label)
        
        # 创建文件网格视图
        self.file_view = FileGridView()
        layout.addWidget(self.file_view)
        
        # 添加操作按钮
        button_layout = QVBoxLayout()
        
        # 加载测试数据按钮
        load_data_btn = QPushButton("🔄 加载测试数据")
        load_data_btn.clicked.connect(self.load_test_data)
        button_layout.addWidget(load_data_btn)
        
        # 创建测试文件夹按钮
        create_folders_btn = QPushButton("📁 创建测试文件夹")
        create_folders_btn.clicked.connect(self.create_test_folders)
        button_layout.addWidget(create_folders_btn)
        
        layout.addLayout(button_layout)
        
        # 状态标签
        self.status_label = QLabel("准备就绪")
        self.status_label.setStyleSheet("color: green; font-weight: bold;")
        layout.addWidget(self.status_label)
        
        # 加载初始数据
        self.load_test_data()
    
    def load_test_data(self):
        """加载测试数据"""
        try:
            # 获取一些文件用于测试
            files = self.file_service.get_files(limit=20, offset=0)
            
            if not files:
                self.status_label.setText("⚠️ 数据库中没有文件，请先添加一些文件")
                return
            
            # 设置文件到视图
            self.file_view.set_files(files)
            
            # 统计中转文件夹文件数量
            staging_count = sum(1 for f in files if f.get('staging_status') == 'staging')
            
            self.status_label.setText(
                f"✅ 已加载 {len(files)} 个文件，其中 {staging_count} 个在中转文件夹"
            )
            
        except Exception as e:
            self.status_label.setText(f"❌ 加载数据失败: {e}")
    
    def create_test_folders(self):
        """创建测试文件夹"""
        try:
            # 创建一些测试文件夹
            test_folders = ["测试文档", "临时文件", "重要资料"]
            
            created_count = 0
            for folder_name in test_folders:
                try:
                    self.tag_service.create_folder_tag(folder_name)
                    created_count += 1
                except Exception as e:
                    if "UNIQUE constraint failed" not in str(e):
                        raise e
            
            if created_count > 0:
                self.status_label.setText(f"✅ 已创建 {created_count} 个测试文件夹")
            else:
                self.status_label.setText("ℹ️ 测试文件夹已存在")
                
        except Exception as e:
            self.status_label.setText(f"❌ 创建测试文件夹失败: {e}")
    
    def show_status_message(self, message, is_success=True):
        """显示状态消息（模拟主窗口方法）"""
        color = "green" if is_success else "red"
        self.status_label.setStyleSheet(f"color: {color}; font-weight: bold;")
        self.status_label.setText(message)
        print(f"状态消息: {message}")
    
    def _load_files_silently(self):
        """静默加载文件（模拟主窗口方法）"""
        self.load_test_data()


def main():
    """主函数"""
    print("🚀 启动中转文件夹用户体验改进演示")
    
    # 创建应用
    app = QApplication(sys.argv)
    
    # 创建演示窗口
    demo = StagingImprovementsDemo()
    demo.show()
    
    print("✅ 演示窗口已启动")
    print("\n📋 测试指南:")
    print("1. 右键点击文件查看新的菜单选项")
    print("2. 测试'移至智能文件库'功能和状态提示")
    print("3. 测试'移动到文件夹'功能")
    print("4. 观察状态栏的提示信息变化")
    
    # 运行应用
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
