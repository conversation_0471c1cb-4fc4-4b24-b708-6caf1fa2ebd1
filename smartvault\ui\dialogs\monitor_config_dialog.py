"""
监控配置对话框
用于添加和编辑监控文件夹配置
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QGroupBox,
    QLabel, QLineEdit, QPushButton, QComboBox, QCheckBox, QTextEdit,
    QDialogButtonBox, QFileDialog, QMessageBox
)
from PySide6.QtCore import Qt
import os
from typing import Dict, List, Optional, Tuple


class MonitorConfigDialog(QDialog):
    """监控配置对话框"""

    def __init__(self, config: Optional[Dict] = None, parent=None):
        """初始化对话框

        Args:
            config: 现有配置（编辑模式），None表示添加模式
            parent: 父窗口
        """
        super().__init__(parent)
        self.config = config
        self.is_edit_mode = config is not None

        self.init_ui()

        if self.is_edit_mode:
            self.load_config()

    def init_ui(self):
        """初始化UI界面"""
        title = "编辑监控配置" if self.is_edit_mode else "添加监控文件夹"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(500, 400)

        layout = QVBoxLayout(self)

        # 基本设置组
        basic_group = QGroupBox("基本设置")
        basic_layout = QFormLayout(basic_group)

        # 文件夹路径
        path_layout = QHBoxLayout()
        self.path_edit = QLineEdit()
        self.path_edit.setPlaceholderText("选择要监控的文件夹路径")
        path_layout.addWidget(self.path_edit)

        browse_button = QPushButton("浏览...")
        browse_button.clicked.connect(self.on_browse_folder)
        path_layout.addWidget(browse_button)

        basic_layout.addRow("文件夹路径:", path_layout)

        # 入库模式
        self.mode_combo = QComboBox()
        self.mode_combo.addItems(["链接", "复制", "移动"])
        self.mode_combo.setCurrentText("链接")
        basic_layout.addRow("入库模式:", self.mode_combo)

        layout.addWidget(basic_group)

        # 文件过滤组
        filter_group = QGroupBox("文件过滤")
        filter_layout = QVBoxLayout(filter_group)

        filter_layout.addWidget(QLabel("文件类型模式 (每行一个，如 *.txt):"))
        self.patterns_edit = QTextEdit()
        self.patterns_edit.setMaximumHeight(100)
        self.patterns_edit.setPlaceholderText("*.txt\n*.pdf\n*.doc\n*.docx")
        filter_layout.addWidget(self.patterns_edit)

        layout.addWidget(filter_group)

        # 选项组
        options_group = QGroupBox("监控选项")
        options_layout = QVBoxLayout(options_group)

        self.auto_add_check = QCheckBox("自动添加到文件库")
        self.auto_add_check.setChecked(True)
        self.auto_add_check.setToolTip("检测到新文件时自动添加到文件库")
        options_layout.addWidget(self.auto_add_check)

        self.recursive_check = QCheckBox("递归监控子文件夹")
        self.recursive_check.setChecked(True)
        self.recursive_check.setToolTip("监控指定文件夹及其所有子文件夹")
        options_layout.addWidget(self.recursive_check)

        # 查重选项（预留功能）
        self.auto_dedup_check = QCheckBox("启用自动查重 (功能开发中)")
        self.auto_dedup_check.setChecked(False)
        self.auto_dedup_check.setEnabled(False)
        self.auto_dedup_check.setToolTip("自动检测重复文件，避免重复添加（第三阶段开发）")
        options_layout.addWidget(self.auto_dedup_check)

        layout.addWidget(options_group)

        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel,
            Qt.Horizontal, self
        )
        button_box.accepted.connect(self.on_accept)
        button_box.rejected.connect(self.reject)

        layout.addWidget(button_box)

    def on_browse_folder(self):
        """浏览文件夹"""
        folder = QFileDialog.getExistingDirectory(
            self, "选择要监控的文件夹", self.path_edit.text()
        )
        if folder:
            self.path_edit.setText(folder)

    def load_config(self):
        """加载现有配置到UI控件"""
        if not self.config:
            return

        # 文件夹路径
        self.path_edit.setText(self.config.get("folder_path", ""))

        # 入库模式
        entry_mode = self.config.get("entry_mode", "link")
        mode_map = {"link": "链接", "copy": "复制", "move": "移动"}
        mode_text = mode_map.get(entry_mode, "链接")
        self.mode_combo.setCurrentText(mode_text)

        # 文件模式
        patterns = self.config.get("file_patterns", [])
        if patterns:
            self.patterns_edit.setPlainText("\n".join(patterns))

        # 选项
        self.auto_add_check.setChecked(self.config.get("auto_add", True))
        self.recursive_check.setChecked(self.config.get("recursive", True))
        self.auto_dedup_check.setChecked(self.config.get("auto_dedup", False))

    def get_config(self) -> Dict:
        """获取配置

        Returns:
            dict: 监控配置字典
        """
        # 入库模式映射
        mode_map = {"链接": "link", "复制": "copy", "移动": "move"}
        entry_mode = mode_map.get(self.mode_combo.currentText(), "link")

        # 文件模式
        patterns_text = self.patterns_edit.toPlainText().strip()
        file_patterns = []
        if patterns_text:
            file_patterns = [
                pattern.strip() for pattern in patterns_text.split('\n')
                if pattern.strip()
            ]

        return {
            "folder_path": self.path_edit.text().strip(),
            "entry_mode": entry_mode,
            "file_patterns": file_patterns,
            "auto_add": self.auto_add_check.isChecked(),
            "recursive": self.recursive_check.isChecked(),
            "auto_dedup": self.auto_dedup_check.isChecked()
        }

    def validate_config(self) -> Tuple[bool, str]:
        """验证配置

        Returns:
            tuple: (是否有效, 错误信息)
        """
        folder_path = self.path_edit.text().strip()

        # 检查路径是否为空
        if not folder_path:
            return False, "请选择要监控的文件夹路径"

        # 检查路径是否存在
        if not os.path.exists(folder_path):
            return False, f"文件夹不存在: {folder_path}"

        # 检查是否为文件夹
        if not os.path.isdir(folder_path):
            return False, f"路径不是文件夹: {folder_path}"

        # 检查文件模式格式
        patterns_text = self.patterns_edit.toPlainText().strip()
        if patterns_text:
            patterns = [p.strip() for p in patterns_text.split('\n') if p.strip()]
            for pattern in patterns:
                if not pattern:
                    continue
                # 简单验证：应该包含文件扩展名模式
                if not ('*' in pattern or '?' in pattern or pattern.startswith('.')):
                    return False, f"文件模式格式可能不正确: {pattern}\n建议使用如 *.txt 的格式"

        return True, ""

    def on_accept(self):
        """确定按钮点击事件"""
        # 验证配置
        is_valid, error_msg = self.validate_config()
        if not is_valid:
            QMessageBox.warning(self, "配置错误", error_msg)
            return

        self.accept()


# 为了兼容性，在settings_dialog模块中也导出这个类
__all__ = ['MonitorConfigDialog']
