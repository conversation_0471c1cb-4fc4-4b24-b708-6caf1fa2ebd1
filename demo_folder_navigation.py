#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
文件夹导航功能演示脚本

展示如何使用新实现的自定义文件夹和移动设备文件夹功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PySide6.QtCore import Qt

from smartvault.ui.widgets.navigation_panel import NavigationPanel


class FolderNavigationDemo(QMainWindow):
    """文件夹导航功能演示窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("SmartVault - 文件夹导航功能演示")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央窗口
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 添加说明标签
        info_label = QLabel("""
🎯 文件夹导航功能演示

本演示展示了"文件夹导航功能简化方案"第二、第三章节的实现：

📁 自定义文件夹功能：
• 基于现有标签系统实现
• 右键点击"📁 自定义文件夹" → "添加文件夹"
• 创建的文件夹会显示为标签，可用于文件分类

💾 移动设备文件夹功能：
• 基于现有标签系统实现  
• 右键点击"💾 移动设备" → "添加设备文件夹"
• 可为不同设备创建专门的文件夹

🔧 操作方法：
1. 在左侧导航面板的"文件夹"标签页中
2. 右键点击相应分类进行添加
3. 点击创建的文件夹可筛选相关文件
4. 右键点击文件夹标签可删除

✨ 特点：
• 复用现有标签系统，无需额外数据结构
• 支持层级显示和文件筛选
• 与现有功能完美集成
        """)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #f0f0f0;
                border: 1px solid #ccc;
                border-radius: 5px;
                padding: 10px;
                font-size: 12px;
                line-height: 1.4;
            }
        """)
        layout.addWidget(info_label)
        
        # 创建导航面板
        self.navigation_panel = NavigationPanel()
        layout.addWidget(self.navigation_panel)
        
        # 添加操作按钮
        button_layout = QVBoxLayout()
        
        # 快速添加示例文件夹按钮
        add_demo_folders_btn = QPushButton("🚀 添加演示文件夹")
        add_demo_folders_btn.clicked.connect(self.add_demo_folders)
        button_layout.addWidget(add_demo_folders_btn)
        
        # 刷新导航面板按钮
        refresh_btn = QPushButton("🔄 刷新导航面板")
        refresh_btn.clicked.connect(self.refresh_navigation)
        button_layout.addWidget(refresh_btn)
        
        layout.addLayout(button_layout)
        
        # 状态标签
        self.status_label = QLabel("准备就绪")
        self.status_label.setStyleSheet("color: green; font-weight: bold;")
        layout.addWidget(self.status_label)
    
    def add_demo_folders(self):
        """添加演示文件夹"""
        try:
            from smartvault.services.tag_service import TagService
            tag_service = TagService()
            
            # 添加自定义文件夹示例
            custom_folders = ["项目文档", "设计素材", "临时文件"]
            for folder_name in custom_folders:
                try:
                    tag_service.create_folder_tag(folder_name)
                    self.status_label.setText(f"✅ 已添加自定义文件夹: {folder_name}")
                    QApplication.processEvents()
                except Exception as e:
                    if "UNIQUE constraint failed" in str(e):
                        self.status_label.setText(f"⚠️ 文件夹已存在: {folder_name}")
                    else:
                        raise e
            
            # 添加设备文件夹示例
            device_folders = ["USB_工作盘", "手机_备份", "云盘_同步"]
            for device_name in device_folders:
                try:
                    tag_service.create_device_folder_tag(device_name)
                    self.status_label.setText(f"✅ 已添加设备文件夹: {device_name}")
                    QApplication.processEvents()
                except Exception as e:
                    if "UNIQUE constraint failed" in str(e):
                        self.status_label.setText(f"⚠️ 设备文件夹已存在: {device_name}")
                    else:
                        raise e
            
            # 刷新导航面板
            self.refresh_navigation()
            self.status_label.setText("🎉 演示文件夹添加完成！")
            
        except Exception as e:
            self.status_label.setText(f"❌ 添加演示文件夹失败: {e}")
    
    def refresh_navigation(self):
        """刷新导航面板"""
        try:
            self.navigation_panel.refresh_folder_tree()
            self.status_label.setText("🔄 导航面板已刷新")
        except Exception as e:
            self.status_label.setText(f"❌ 刷新失败: {e}")


def main():
    """主函数"""
    print("🚀 启动文件夹导航功能演示")
    
    # 创建应用
    app = QApplication(sys.argv)
    
    # 创建演示窗口
    demo = FolderNavigationDemo()
    demo.show()
    
    print("✅ 演示窗口已启动")
    print("\n📋 功能说明:")
    print("1. 窗口左侧显示导航面板")
    print("2. 点击'🚀 添加演示文件夹'可快速创建示例")
    print("3. 在导航面板中右键点击分类可添加自定义文件夹")
    print("4. 点击文件夹可筛选相关文件（需要先为文件添加标签）")
    
    # 运行应用
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
