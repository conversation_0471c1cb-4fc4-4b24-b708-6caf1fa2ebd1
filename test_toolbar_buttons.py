#!/usr/bin/env python3
"""
测试工具栏按钮美化效果
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QHBoxLayout, QPushButton, QLabel
from PySide6.QtCore import Qt

from smartvault.ui.themes import theme_manager


class TestToolbarWindow(QMainWindow):
    """测试工具栏按钮窗口"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("工具栏按钮美化测试")
        self.setGeometry(100, 100, 800, 400)

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建布局
        layout = QVBoxLayout(central_widget)

        # 添加说明标签
        info_label = QLabel("工具栏按钮美化效果测试")
        info_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(info_label)

        # 创建主题切换按钮
        theme_layout = QHBoxLayout()

        light_btn = QPushButton("浅色主题")
        light_btn.clicked.connect(lambda: self.switch_theme("light"))
        theme_layout.addWidget(light_btn)

        dark_btn = QPushButton("深色主题")
        dark_btn.clicked.connect(lambda: self.switch_theme("dark"))
        theme_layout.addWidget(dark_btn)

        blue_btn = QPushButton("蓝色主题")
        blue_btn.clicked.connect(lambda: self.switch_theme("blue"))
        theme_layout.addWidget(blue_btn)

        green_btn = QPushButton("绿色主题")
        green_btn.clicked.connect(lambda: self.switch_theme("green"))
        theme_layout.addWidget(green_btn)

        theme_layout.addStretch()
        layout.addLayout(theme_layout)

        # 创建按钮测试区域
        button_layout = QHBoxLayout()

        # 文件夹监控按钮（参考样式）
        monitor_btn = QPushButton("文件夹监控")
        monitor_btn.setMaximumWidth(100)
        monitor_btn.setMinimumWidth(80)
        monitor_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: 1px solid #45a049;
                border-radius: 3px;
                padding: 4px 8px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        button_layout.addWidget(monitor_btn)

        # 剪贴板查重按钮（参考样式）
        clipboard_btn = QPushButton("剪贴板查重")
        clipboard_btn.setMaximumWidth(100)
        clipboard_btn.setMinimumWidth(80)
        clipboard_btn.setStyleSheet("""
            QPushButton {
                background-color: #9e9e9e !important;
                color: white !important;
                border: 1px solid #757575 !important;
                border-radius: 3px !important;
                padding: 4px 8px !important;
                font-size: 12px !important;
                font-weight: normal !important;
            }
            QPushButton:hover {
                background-color: #757575 !important;
                border-color: #616161 !important;
            }
            QPushButton:pressed {
                background-color: #616161 !important;
            }
        """)
        button_layout.addWidget(clipboard_btn)

        # 添加文件按钮（新样式）
        add_file_btn = QPushButton("添加文件")
        add_file_btn.setMaximumWidth(100)
        add_file_btn.setMinimumWidth(80)
        add_file_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196f3 !important;
                color: white !important;
                border: 1px solid #1976d2 !important;
                border-radius: 3px !important;
                padding: 4px 8px !important;
                font-size: 12px !important;
                font-weight: normal !important;
            }
            QPushButton:hover {
                background-color: #1976d2 !important;
                border-color: #1565c0 !important;
            }
            QPushButton:pressed {
                background-color: #1565c0 !important;
            }
        """)
        button_layout.addWidget(add_file_btn)

        # 添加文件夹按钮（新样式）
        add_folder_btn = QPushButton("添加文件夹")
        add_folder_btn.setMaximumWidth(100)
        add_folder_btn.setMinimumWidth(80)
        add_folder_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff9800 !important;
                color: white !important;
                border: 1px solid #f57c00 !important;
                border-radius: 3px !important;
                padding: 4px 8px !important;
                font-size: 12px !important;
                font-weight: normal !important;
            }
            QPushButton:hover {
                background-color: #f57c00 !important;
                border-color: #ef6c00 !important;
            }
            QPushButton:pressed {
                background-color: #ef6c00 !important;
            }
        """)
        button_layout.addWidget(add_folder_btn)

        button_layout.addStretch()
        layout.addLayout(button_layout)

        # 添加说明文本
        desc_label = QLabel("""
按钮设计说明：
• 文件夹监控：绿色（#4CAF50）- 表示监控功能
• 剪贴板查重：灰色（#9e9e9e）- 表示未启动状态
• 添加文件：蓝色（#2196f3）- 表示文件操作
• 添加文件夹：橙色（#ff9800）- 表示文件夹操作

所有按钮都有：
• 统一的尺寸：80-100px宽度
• 统一的样式：圆角、边框、悬停效果
• 统一的字体和内边距
        """)
        desc_label.setStyleSheet("margin: 20px; padding: 10px; background-color: #f5f5f5; border-radius: 5px;")
        layout.addWidget(desc_label)

        layout.addStretch()

        # 应用默认主题
        theme_manager.apply_theme("light")

    def switch_theme(self, theme_name):
        """切换主题"""
        theme_manager.set_theme(theme_name)
        print(f"切换到{theme_name}主题")


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("SmartVault Toolbar Test")
    app.setApplicationVersion("1.0")

    # 创建测试窗口
    window = TestToolbarWindow()
    window.show()

    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
