#!/usr/bin/env python3
"""
修复schedule模块导入问题的脚本
"""

import subprocess
import sys

def main():
    print("SmartVault Schedule模块修复工具")
    print("=" * 40)

    # 显示当前Python环境信息
    print(f"当前Python路径: {sys.executable}")
    print(f"Python版本: {sys.version}")
    print()

    # 检查schedule模块
    print("检查schedule模块状态...")
    try:
        import schedule  # type: ignore
        print("[OK] schedule模块可以正常导入")
        print(f"模块路径: {schedule.__file__}")
    except ImportError:
        print("[ERROR] schedule模块无法导入")

        # 尝试安装
        print("正在安装schedule模块...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "schedule"],
                         check=True, capture_output=True, text=True)
            print("[OK] schedule模块安装成功")
        except subprocess.CalledProcessError as e:
            print(f"[ERROR] 安装失败: {e}")
            return False

    print()

    # 测试SmartVault备份服务导入
    print("测试SmartVault备份服务...")
    try:
        from smartvault.services.backup_service import get_backup_service
        backup_service = get_backup_service()
        status = backup_service.get_backup_status()
        print("[OK] 备份服务导入成功")
        print(f"Schedule可用: {status.get('schedule_available', False)}")
    except Exception as e:
        print(f"[ERROR] 备份服务导入失败: {e}")
        return False

    print()

    # 测试SmartVault主程序导入
    print("测试SmartVault主程序导入...")
    try:
        from smartvault.ui.main_window import MainWindow  # type: ignore # noqa: F401
        print("[OK] 主程序导入成功")
    except Exception as e:
        print(f"[ERROR] 主程序导入失败: {e}")
        return False

    print()
    print("[SUCCESS] 所有测试通过！SmartVault应该可以正常启动了。")
    print()
    print("现在可以运行:")
    print("  python -m smartvault.main")
    print("或者:")
    print("  python run_smartvault.py")

    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            input("\n按回车键退出...")
            sys.exit(1)
    except Exception as e:
        print(f"\n[ERROR] 修复过程中发生错误: {e}")
        input("\n按回车键退出...")
        sys.exit(1)
