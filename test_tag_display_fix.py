#!/usr/bin/env python3
"""
测试标签显示修复
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PySide6.QtCore import Qt

from smartvault.ui.dialogs.tag_management_dialog import TagManagementDialog


class TestWindow(QMainWindow):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("标签显示修复测试")
        self.setGeometry(100, 100, 300, 200)
        
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("标签显示修复测试")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # 测试按钮
        test_btn = QPushButton("打开标签管理对话框")
        test_btn.clicked.connect(self.open_tag_management)
        layout.addWidget(test_btn)
        
        # 说明
        info = QLabel("点击按钮打开标签管理对话框，\n检查左侧标签树的文字是否清晰可见")
        info.setAlignment(Qt.AlignmentFlag.AlignCenter)
        info.setStyleSheet("color: #666; margin: 10px;")
        layout.addWidget(info)
        
    def open_tag_management(self):
        """打开标签管理对话框"""
        try:
            dialog = TagManagementDialog(self)
            dialog.exec()
        except Exception as e:
            print(f"打开标签管理对话框失败: {e}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
