"""
颜色工具函数
提供颜色处理相关的实用函数
"""

from PySide6.QtGui import QColor
from typing import Union


def lighten_color(color: Union[str, QColor], factor: float = 0.3) -> QColor:
    """使颜色变浅

    Args:
        color: 颜色值（字符串或QColor对象）
        factor: 变浅因子（0.0-1.0，值越大越浅）

    Returns:
        QColor: 变浅后的颜色
    """
    if isinstance(color, str):
        color = QColor(color)

    if not color.isValid():
        return QColor("#CCCCCC")  # 默认浅灰色

    # 获取HSL值
    h, s, l, a = color.getHslF()

    # 增加亮度
    new_l = min(1.0, l + (1.0 - l) * factor)

    # 创建新颜色
    new_color = QColor()
    new_color.setHslF(h, s, new_l, a)

    return new_color


def darken_color(color: Union[str, QColor], factor: float = 0.3) -> QColor:
    """使颜色变深

    Args:
        color: 颜色值（字符串或QColor对象）
        factor: 变深因子（0.0-1.0，值越大越深）

    Returns:
        QColor: 变深后的颜色
    """
    if isinstance(color, str):
        color = QColor(color)

    if not color.isValid():
        return QColor("#333333")  # 默认深灰色

    # 获取HSL值
    h, s, l, a = color.getHslF()

    # 减少亮度
    new_l = max(0.0, l * (1.0 - factor))

    # 创建新颜色
    new_color = QColor()
    new_color.setHslF(h, s, new_l, a)

    return new_color


def get_inherited_color(base_color: Union[str, QColor], depth: int) -> QColor:
    """获取继承的颜色（根据层级深度递减，保证可读性）

    Args:
        base_color: 基础颜色
        depth: 层级深度（0为根级，1为第一级子标签，以此类推）

    Returns:
        QColor: 继承的颜色
    """
    if isinstance(base_color, str):
        base_color = QColor(base_color)

    if not base_color.isValid() or depth == 0:
        return base_color

    # 优化的颜色递减算法，确保可读性
    if depth == 1:
        # 第一级子标签：变浅15%
        inherited_color = lighten_color(base_color, 0.15)
    elif depth == 2:
        # 第二级子标签：变浅25%
        inherited_color = lighten_color(base_color, 0.25)
    else:
        # 第三级及以上：限制最大变浅程度为35%，避免过浅
        inherited_color = lighten_color(base_color, 0.35)

    # 确保继承的颜色具有足够的可读性
    return ensure_readable_color(inherited_color, min_lightness=50, max_lightness=180)


def is_light_color(color: Union[str, QColor]) -> bool:
    """判断颜色是否为浅色

    Args:
        color: 颜色值

    Returns:
        bool: True表示浅色，False表示深色
    """
    if isinstance(color, str):
        color = QColor(color)

    if not color.isValid():
        return True

    # 使用亮度判断
    return color.lightness() > 128


def get_contrast_color(background_color: Union[str, QColor]) -> QColor:
    """获取与背景色对比度高的前景色

    Args:
        background_color: 背景颜色

    Returns:
        QColor: 对比色（黑色或白色）
    """
    if is_light_color(background_color):
        return QColor("#000000")  # 黑色
    else:
        return QColor("#FFFFFF")  # 白色


def create_color_block_style(color: Union[str, QColor], size: int = 16) -> str:
    """创建颜色块的样式字符串

    Args:
        color: 颜色值
        size: 色块大小（像素）

    Returns:
        str: CSS样式字符串
    """
    if isinstance(color, QColor):
        color = color.name()

    return f"""
        QLabel {{
            background-color: {color};
            border: 1px solid #ccc;
            border-radius: 3px;
            min-width: {size}px;
            max-width: {size}px;
            min-height: {size}px;
            max-height: {size}px;
        }}
    """


def ensure_readable_color(color: Union[str, QColor], min_lightness: int = 40, max_lightness: int = 200) -> QColor:
    """确保颜色具有足够的可读性

    Args:
        color: 原始颜色
        min_lightness: 最小亮度值（0-255）
        max_lightness: 最大亮度值（0-255）

    Returns:
        QColor: 调整后的可读颜色
    """
    if isinstance(color, str):
        color = QColor(color)

    if not color.isValid():
        return QColor("#666666")  # 默认深灰色

    lightness = color.lightness()

    # 如果颜色太浅，使其变深
    if lightness > max_lightness:
        # 计算需要变深的程度
        factor = (lightness - max_lightness) / (255 - max_lightness)
        return darken_color(color, factor * 0.6)  # 最多变深60%

    # 如果颜色太深，使其变浅
    elif lightness < min_lightness:
        # 计算需要变浅的程度
        factor = (min_lightness - lightness) / min_lightness
        return lighten_color(color, factor * 0.4)  # 最多变浅40%

    # 颜色在合适范围内，直接返回
    return color


def blend_colors(color1: Union[str, QColor], color2: Union[str, QColor], ratio: float = 0.5) -> QColor:
    """混合两种颜色

    Args:
        color1: 第一种颜色
        color2: 第二种颜色
        ratio: 混合比例（0.0-1.0，0.0表示完全是color1，1.0表示完全是color2）

    Returns:
        QColor: 混合后的颜色
    """
    if isinstance(color1, str):
        color1 = QColor(color1)
    if isinstance(color2, str):
        color2 = QColor(color2)

    if not color1.isValid():
        return color2 if color2.isValid() else QColor("#CCCCCC")
    if not color2.isValid():
        return color1

    # 获取RGB值
    r1, g1, b1, a1 = color1.getRgb()
    r2, g2, b2, a2 = color2.getRgb()

    # 计算混合后的RGB值
    r = int(r1 * (1 - ratio) + r2 * ratio)
    g = int(g1 * (1 - ratio) + g2 * ratio)
    b = int(b1 * (1 - ratio) + b2 * ratio)
    a = int(a1 * (1 - ratio) + a2 * ratio)

    return QColor(r, g, b, a)
