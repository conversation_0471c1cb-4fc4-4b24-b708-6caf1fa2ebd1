"""
SmartVault 自动备份服务
提供数据库的自动备份、恢复和管理功能
"""

import os
import shutil
import time
import threading
from datetime import datetime, timedelta
from smartvault.utils.config import load_config, save_config

# 尝试导入schedule模块，如果失败则使用fallback
try:
    import schedule
    SCHEDULE_AVAILABLE = True
except ImportError:
    print("警告: schedule模块未安装，自动备份功能将被禁用")
    print("   请运行: pip install schedule")
    SCHEDULE_AVAILABLE = False

    # 创建一个简单的fallback类
    class MockSchedule:
        def every(self, interval):
            return self
        def hours(self):
            return self
        def do(self, func):
            return self
        def clear(self):
            pass
        def run_pending(self):
            pass

    schedule = MockSchedule()


class BackupService:
    """自动备份服务类"""

    def __init__(self):
        self.backup_thread = None
        self.is_running = False
        self.config = load_config()

    def get_backup_config(self):
        """获取备份配置"""
        return self.config.get("backup", {
            "enabled": True,
            "interval_hours": 24,  # 每24小时备份一次
            "max_backups": 7,      # 保留最近7个备份
            "backup_on_startup": True,  # 启动时备份
            "backup_on_shutdown": False, # 关闭时备份（默认禁用，提高用户体验）
            "compress": False,     # 是否压缩备份
            "backup_location": "auto"  # auto表示在文件库内创建backups目录
        })

    def save_backup_config(self, backup_config):
        """保存备份配置"""
        self.config["backup"] = backup_config
        save_config(self.config)

    def get_backup_directory(self):
        """获取备份目录路径"""
        backup_config = self.get_backup_config()
        library_path = self.config.get("library_path", "")

        if backup_config["backup_location"] == "auto":
            # 在文件库内创建backups目录
            backup_dir = os.path.join(library_path, "backups")
        else:
            # 使用用户指定的备份目录
            backup_dir = backup_config["backup_location"]

        os.makedirs(backup_dir, exist_ok=True)
        return backup_dir

    def get_database_path(self):
        """获取数据库文件路径"""
        library_path = self.config.get("library_path", "")
        return os.path.join(library_path, "data", "smartvault.db")

    def create_backup(self, backup_type="manual"):
        """创建数据库备份

        Args:
            backup_type: 备份类型 (manual, auto, startup, shutdown)

        Returns:
            tuple: (success, backup_path, message)
        """
        try:
            db_path = self.get_database_path()
            if not os.path.exists(db_path):
                return False, None, "数据库文件不存在"

            backup_dir = self.get_backup_directory()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"smartvault_backup_{backup_type}_{timestamp}.db"
            backup_path = os.path.join(backup_dir, backup_filename)

            # 创建备份
            shutil.copy2(db_path, backup_path)

            # 验证备份完整性
            if os.path.getsize(backup_path) != os.path.getsize(db_path):
                os.remove(backup_path)
                return False, None, "备份文件大小不匹配"

            # 清理旧备份
            self._cleanup_old_backups()

            backup_size = os.path.getsize(backup_path) / 1024 / 1024  # MB
            message = f"备份创建成功 ({backup_size:.2f}MB)"

            print(f"{message}: {backup_path}")
            return True, backup_path, message

        except Exception as e:
            error_msg = f"创建备份失败: {e}"
            print(f"{error_msg}")
            return False, None, error_msg

    def _cleanup_old_backups(self):
        """清理旧的备份文件"""
        try:
            backup_config = self.get_backup_config()
            max_backups = backup_config["max_backups"]
            backup_dir = self.get_backup_directory()

            # 获取所有备份文件
            backup_files = []
            for filename in os.listdir(backup_dir):
                if filename.startswith("smartvault_backup_") and filename.endswith(".db"):
                    filepath = os.path.join(backup_dir, filename)
                    backup_files.append((filepath, os.path.getmtime(filepath)))

            # 按修改时间排序，保留最新的
            backup_files.sort(key=lambda x: x[1], reverse=True)

            # 删除超出数量限制的备份
            for filepath, _ in backup_files[max_backups:]:
                try:
                    os.remove(filepath)
                    print(f"删除旧备份: {os.path.basename(filepath)}")
                except Exception as e:
                    print(f"删除旧备份失败: {e}")

        except Exception as e:
            print(f"清理旧备份失败: {e}")

    def restore_backup(self, backup_path):
        """从备份恢复数据库

        Args:
            backup_path: 备份文件路径

        Returns:
            tuple: (success, message)
        """
        try:
            if not os.path.exists(backup_path):
                return False, "备份文件不存在"

            db_path = self.get_database_path()

            # 创建当前数据库的紧急备份
            emergency_backup = db_path + f".emergency_{int(time.time())}"
            if os.path.exists(db_path):
                shutil.copy2(db_path, emergency_backup)

            # 恢复备份
            shutil.copy2(backup_path, db_path)

            message = f"数据库恢复成功，紧急备份: {emergency_backup}"
            print(f"{message}")
            return True, message

        except Exception as e:
            error_msg = f"恢复备份失败: {e}"
            print(f"{error_msg}")
            return False, error_msg

    def list_backups(self):
        """列出所有可用的备份

        Returns:
            list: 备份信息列表 [(path, size, date, type), ...]
        """
        try:
            backup_dir = self.get_backup_directory()
            backups = []

            for filename in os.listdir(backup_dir):
                if filename.startswith("smartvault_backup_") and filename.endswith(".db"):
                    filepath = os.path.join(backup_dir, filename)
                    size = os.path.getsize(filepath) / 1024 / 1024  # MB
                    mtime = os.path.getmtime(filepath)
                    date = datetime.fromtimestamp(mtime)

                    # 从文件名提取备份类型
                    parts = filename.replace("smartvault_backup_", "").replace(".db", "").split("_")
                    backup_type = parts[0] if parts else "unknown"

                    backups.append((filepath, size, date, backup_type))

            # 按日期排序，最新的在前
            backups.sort(key=lambda x: x[2], reverse=True)
            return backups

        except Exception as e:
            print(f"⚠️ 列出备份失败: {e}")
            return []

    def start_auto_backup(self):
        """启动自动备份服务"""
        backup_config = self.get_backup_config()

        if not backup_config["enabled"]:
            print("自动备份已禁用")
            return

        if self.is_running:
            print("自动备份服务已在运行")
            return

        # 检查schedule模块是否可用
        if not SCHEDULE_AVAILABLE:
            print("schedule模块不可用，仅执行启动备份")
            # 仍然执行启动备份
            if backup_config["backup_on_startup"]:
                self.create_backup("startup")
            return

        # 设置定时任务
        interval_hours = backup_config["interval_hours"]
        schedule.every(interval_hours).hours.do(self._auto_backup_job)

        # 启动时备份
        if backup_config["backup_on_startup"]:
            self.create_backup("startup")

        # 启动后台线程
        self.is_running = True
        self.backup_thread = threading.Thread(target=self._backup_scheduler, daemon=True)
        self.backup_thread.start()

        print(f"自动备份服务已启动 (每{interval_hours}小时备份一次)")

    def stop_auto_backup(self):
        """停止自动备份服务（快速版本）"""
        # 重新加载配置以确保获取最新设置
        self.config = load_config()
        backup_config = self.get_backup_config()

        # 关闭时备份（只有在明确启用时才执行）
        if backup_config.get("backup_on_shutdown", False):
            print("执行关闭时备份...")
            self.create_backup("shutdown")
        else:
            print("跳过关闭时备份（已在设置中禁用）")

        self.is_running = False
        schedule.clear()

        # 快速停止：不等待daemon线程，让它自然结束
        if self.backup_thread and self.backup_thread.is_alive():
            # daemon线程会在主程序退出时自动结束，无需等待
            print("备份线程将自动结束...")

        print("自动备份服务已停止")

    def _auto_backup_job(self):
        """自动备份任务"""
        self.create_backup("auto")

    def _backup_scheduler(self):
        """备份调度器线程"""
        while self.is_running:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
            except Exception as e:
                print(f"备份调度器错误: {e}")
                time.sleep(60)

    def get_backup_status(self):
        """获取备份状态信息

        Returns:
            dict: 备份状态信息
        """
        backup_config = self.get_backup_config()
        backups = self.list_backups()

        latest_backup = None
        if backups:
            latest_backup = {
                'path': backups[0][0],
                'size_mb': backups[0][1],
                'date': backups[0][2],
                'type': backups[0][3]
            }

        return {
            'enabled': backup_config["enabled"] and SCHEDULE_AVAILABLE,
            'is_running': self.is_running and SCHEDULE_AVAILABLE,
            'interval_hours': backup_config["interval_hours"],
            'max_backups': backup_config["max_backups"],
            'total_backups': len(backups),
            'latest_backup': latest_backup,
            'backup_directory': self.get_backup_directory(),
            'schedule_available': SCHEDULE_AVAILABLE
        }


# 全局备份服务实例
_backup_service = None

def get_backup_service():
    """获取备份服务实例"""
    global _backup_service
    if _backup_service is None:
        _backup_service = BackupService()
    return _backup_service
