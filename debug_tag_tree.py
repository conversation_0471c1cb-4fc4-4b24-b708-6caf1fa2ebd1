#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试标签树结构
"""

import os
import sys
import tempfile
import shutil

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from smartvault.services.tag_service import TagService
from smartvault.services.file import FileService
from smartvault.data.database import Database


def debug_tag_tree():
    """调试标签树结构"""
    # 创建临时数据库
    temp_dir = tempfile.mkdtemp()
    db_path = os.path.join(temp_dir, "test.db")
    db = Database(db_path)
    
    try:
        # 创建服务实例
        tag_service = TagService()
        tag_service._db = db
        
        file_service = FileService()
        file_service._db = db
        
        # 创建测试文件
        test_files = []
        for i in range(3):
            test_file = os.path.join(temp_dir, f"test{i+1}.txt")
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(f"测试文件{i+1}内容")
            test_files.append(test_file)
        
        # 创建层级标签
        work_tag_id = tag_service.create_tag("工作", "#FF9800")
        project_a_id = tag_service.create_tag("项目A", "#2196F3", work_tag_id)
        project_b_id = tag_service.create_tag("项目B", "#4CAF50", work_tag_id)
        personal_tag_id = tag_service.create_tag("个人", "#9C27B0")
        
        print("创建的标签:")
        print(f"  工作: {work_tag_id}")
        print(f"  项目A: {project_a_id} (父: {work_tag_id})")
        print(f"  项目B: {project_b_id} (父: {work_tag_id})")
        print(f"  个人: {personal_tag_id}")
        
        # 添加文件并关联标签
        file1_id = file_service.add_file(test_files[0], "link")
        file2_id = file_service.add_file(test_files[1], "link")
        file3_id = file_service.add_file(test_files[2], "link")
        
        print(f"\n创建的文件:")
        print(f"  文件1: {file1_id}")
        print(f"  文件2: {file2_id}")
        print(f"  文件3: {file3_id}")
        
        # 关联标签
        tag_service.add_tag_to_file(file1_id, work_tag_id)
        tag_service.add_tag_to_file(file1_id, project_a_id)
        tag_service.add_tag_to_file(file2_id, project_b_id)
        tag_service.add_tag_to_file(file3_id, personal_tag_id)
        
        print(f"\n标签关联:")
        print(f"  文件1 -> 工作, 项目A")
        print(f"  文件2 -> 项目B")
        print(f"  文件3 -> 个人")
        
        # 获取标签统计
        stats = tag_service.get_tag_statistics()
        print(f"\n标签统计:")
        for stat in stats:
            print(f"  {stat['name']}: {stat['file_count']} 个文件")
        
        # 获取标签树结构
        tag_tree = tag_service.get_tag_tree()
        
        print(f"\n标签树结构:")
        def print_tree(nodes, indent=0):
            for node in nodes:
                prefix = "  " * indent
                print(f"{prefix}{node['name']}: 直接={node['file_count']}, 总计={node['total_file_count']}")
                if node['children']:
                    print_tree(node['children'], indent + 1)
        
        print_tree(tag_tree)
        
        # 验证工作标签的文件
        work_files = tag_service.get_files_by_tag(work_tag_id)
        print(f"\n工作标签的直接文件: {len(work_files)} 个")
        for f in work_files:
            print(f"  {f['name']}")
        
        # 验证项目A标签的文件
        project_a_files = tag_service.get_files_by_tag(project_a_id)
        print(f"\n项目A标签的文件: {len(project_a_files)} 个")
        for f in project_a_files:
            print(f"  {f['name']}")
        
        # 验证项目B标签的文件
        project_b_files = tag_service.get_files_by_tag(project_b_id)
        print(f"\n项目B标签的文件: {len(project_b_files)} 个")
        for f in project_b_files:
            print(f"  {f['name']}")
        
    finally:
        db.close()
        shutil.rmtree(temp_dir)


if __name__ == "__main__":
    debug_tag_tree()
