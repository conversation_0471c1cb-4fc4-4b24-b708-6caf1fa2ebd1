"""
备份设置页面
实现数据库自动备份的配置管理
"""

from PySide6.QtWidgets import (
    QVBoxLayout, QHBoxLayout, QGroupBox, QLabel, QCheckBox,
    QSpinBox, QPushButton, QListWidget, QListWidgetItem,
    QMessageBox, QFileDialog, QProgressBar
)
from PySide6.QtCore import Qt, QThread, QTimer
from typing import Tuple
from ..base.base_page import BaseSettingsPage


class BackupSettingsPage(BaseSettingsPage):
    """备份设置页面"""

    def setup_ui(self):
        """设置UI界面"""
        layout = QVBoxLayout(self)

        # 自动备份设置组
        auto_backup_group = QGroupBox("自动备份设置")
        auto_backup_layout = QVBoxLayout(auto_backup_group)

        # 启用自动备份
        self.enable_backup_check = QCheckBox("启用自动备份")
        auto_backup_layout.addWidget(self.enable_backup_check)

        # 备份间隔设置
        interval_layout = QHBoxLayout()
        interval_layout.addWidget(QLabel("备份间隔:"))

        self.interval_spin = QSpinBox()
        self.interval_spin.setRange(1, 168)  # 1小时到7天
        self.interval_spin.setSuffix(" 小时")
        interval_layout.addWidget(self.interval_spin)
        interval_layout.addStretch()

        auto_backup_layout.addLayout(interval_layout)

        # 保留备份数量
        max_backups_layout = QHBoxLayout()
        max_backups_layout.addWidget(QLabel("保留备份数量:"))

        self.max_backups_spin = QSpinBox()
        self.max_backups_spin.setRange(1, 30)
        self.max_backups_spin.setSuffix(" 个")
        max_backups_layout.addWidget(self.max_backups_spin)
        max_backups_layout.addStretch()

        auto_backup_layout.addLayout(max_backups_layout)

        # 备份时机设置
        timing_layout = QVBoxLayout()
        self.backup_on_startup_check = QCheckBox("程序启动时自动备份")
        timing_layout.addWidget(self.backup_on_startup_check)

        # 移除"程序关闭时自动备份"选项，提高用户体验
        # 关闭时备份会让程序退出变慢，容易让用户困惑

        auto_backup_layout.addLayout(timing_layout)

        layout.addWidget(auto_backup_group)

        # 手动备份操作组
        manual_backup_group = QGroupBox("手动备份操作")
        manual_backup_layout = QVBoxLayout(manual_backup_group)

        # 手动备份按钮
        backup_button_layout = QHBoxLayout()
        self.create_backup_btn = QPushButton("立即创建备份")
        self.create_backup_btn.clicked.connect(self.create_manual_backup)
        backup_button_layout.addWidget(self.create_backup_btn)
        backup_button_layout.addStretch()

        manual_backup_layout.addLayout(backup_button_layout)

        # 备份状态显示
        self.backup_status_label = QLabel("备份状态: 未知")
        manual_backup_layout.addWidget(self.backup_status_label)

        # 进度条
        self.backup_progress = QProgressBar()
        self.backup_progress.setVisible(False)
        manual_backup_layout.addWidget(self.backup_progress)

        layout.addWidget(manual_backup_group)

        # 备份历史组
        backup_history_group = QGroupBox("备份历史")
        backup_history_layout = QVBoxLayout(backup_history_group)

        # 备份列表
        self.backup_list = QListWidget()
        backup_history_layout.addWidget(self.backup_list)

        # 备份操作按钮
        backup_ops_layout = QHBoxLayout()
        self.refresh_backups_btn = QPushButton("刷新列表")
        self.refresh_backups_btn.clicked.connect(self.refresh_backup_list)
        self.restore_backup_btn = QPushButton("恢复备份")
        self.restore_backup_btn.clicked.connect(self.restore_selected_backup)
        self.delete_backup_btn = QPushButton("删除备份")
        self.delete_backup_btn.clicked.connect(self.delete_selected_backup)

        backup_ops_layout.addWidget(self.refresh_backups_btn)
        backup_ops_layout.addWidget(self.restore_backup_btn)
        backup_ops_layout.addWidget(self.delete_backup_btn)
        backup_ops_layout.addStretch()

        backup_history_layout.addLayout(backup_ops_layout)

        layout.addWidget(backup_history_group)
        layout.addStretch()

        # 初始化备份服务
        self.backup_service = None
        self._init_backup_service()

        # 定时更新状态
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_backup_status)
        self.status_timer.start(5000)  # 每5秒更新一次

    def _init_backup_service(self):
        """初始化备份服务"""
        try:
            from smartvault.services.backup_service import get_backup_service
            self.backup_service = get_backup_service()
        except Exception as e:
            print(f"初始化备份服务失败: {e}")

    def load_settings(self, config: dict):
        """从配置加载设置到UI控件"""
        self.config = config
        backup_config = config.get("backup", {})

        # 自动备份设置
        self.enable_backup_check.setChecked(backup_config.get("enabled", True))
        self.interval_spin.setValue(backup_config.get("interval_hours", 24))
        self.max_backups_spin.setValue(backup_config.get("max_backups", 7))
        self.backup_on_startup_check.setChecked(backup_config.get("backup_on_startup", True))
        # 移除了backup_on_shutdown_check的设置

        # 刷新备份列表和状态
        self.refresh_backup_list()
        self.update_backup_status()

    def save_settings(self) -> dict:
        """从UI控件保存设置到配置"""
        return {
            "enabled": self.enable_backup_check.isChecked(),
            "interval_hours": self.interval_spin.value(),
            "max_backups": self.max_backups_spin.value(),
            "backup_on_startup": self.backup_on_startup_check.isChecked(),
            "backup_on_shutdown": False,  # 固定为False，移除此选项
            "compress": False,  # 暂时不支持压缩
            "backup_location": "auto"  # 自动在文件库内创建备份目录
        }

    def validate_settings(self) -> Tuple[bool, str]:
        """验证设置有效性"""
        # 验证备份间隔
        if self.interval_spin.value() < 1:
            return False, "备份间隔不能少于1小时"

        # 验证保留备份数量
        if self.max_backups_spin.value() < 1:
            return False, "保留备份数量不能少于1个"

        return True, ""

    def reset_to_defaults(self):
        """重置为默认设置"""
        self.enable_backup_check.setChecked(True)
        self.interval_spin.setValue(24)
        self.max_backups_spin.setValue(7)
        self.backup_on_startup_check.setChecked(True)
        # 移除了backup_on_shutdown_check的重置

    def get_page_title(self) -> str:
        """获取页面标题"""
        return "备份设置"

    def create_manual_backup(self):
        """创建手动备份"""
        if not self.backup_service:
            QMessageBox.warning(self, "错误", "备份服务未初始化")
            return

        try:
            # 显示进度条
            self.backup_progress.setVisible(True)
            self.backup_progress.setRange(0, 0)  # 不确定进度
            self.create_backup_btn.setEnabled(False)

            # 创建备份
            success, backup_path, message = self.backup_service.create_backup("manual")

            # 隐藏进度条
            self.backup_progress.setVisible(False)
            self.create_backup_btn.setEnabled(True)

            if success:
                QMessageBox.information(self, "备份成功", f"备份创建成功！\n\n{message}")
                self.refresh_backup_list()
            else:
                QMessageBox.warning(self, "备份失败", f"备份创建失败：\n{message}")

        except Exception as e:
            self.backup_progress.setVisible(False)
            self.create_backup_btn.setEnabled(True)
            QMessageBox.critical(self, "错误", f"创建备份时发生错误：\n{str(e)}")

    def refresh_backup_list(self):
        """刷新备份列表"""
        if not self.backup_service:
            return

        try:
            self.backup_list.clear()
            backups = self.backup_service.list_backups()

            for backup_path, size_mb, date_str, backup_type in backups:
                item_text = f"{date_str} - {backup_type} ({size_mb:.2f}MB)"
                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, backup_path)  # 存储备份文件路径
                self.backup_list.addItem(item)

            if not backups:
                item = QListWidgetItem("暂无备份文件")
                item.setFlags(Qt.NoItemFlags)  # 禁用选择
                self.backup_list.addItem(item)

        except Exception as e:
            print(f"刷新备份列表失败: {e}")

    def update_backup_status(self):
        """更新备份状态"""
        if not self.backup_service:
            self.backup_status_label.setText("备份状态: 服务未初始化")
            return

        try:
            status = self.backup_service.get_backup_status()

            if status['enabled']:
                if status['is_running']:
                    status_text = f"备份状态: 运行中 (每{status['interval_hours']}小时)"
                else:
                    status_text = "备份状态: 已启用但未运行"
            else:
                status_text = "备份状态: 已禁用"

            if status['latest_backup']:
                latest = status['latest_backup']
                status_text += f"\n最新备份: {latest['date']} ({latest['size_mb']:.2f}MB)"

            self.backup_status_label.setText(status_text)

        except Exception as e:
            self.backup_status_label.setText(f"备份状态: 获取状态失败 - {str(e)}")

    def restore_selected_backup(self):
        """恢复选中的备份"""
        current_item = self.backup_list.currentItem()
        if not current_item or not current_item.data(Qt.UserRole):
            QMessageBox.information(self, "提示", "请选择要恢复的备份")
            return

        backup_path = current_item.data(Qt.UserRole)

        # 确认恢复
        reply = QMessageBox.question(
            self, "确认恢复",
            f"确定要恢复此备份吗？\n\n{current_item.text()}\n\n"
            "注意：这将替换当前数据库，建议先创建当前数据库的备份。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        try:
            success, message = self.backup_service.restore_backup(backup_path)

            if success:
                QMessageBox.information(self, "恢复成功", f"数据库恢复成功！\n\n{message}")
            else:
                QMessageBox.warning(self, "恢复失败", f"数据库恢复失败：\n{message}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"恢复备份时发生错误：\n{str(e)}")

    def delete_selected_backup(self):
        """删除选中的备份"""
        current_item = self.backup_list.currentItem()
        if not current_item or not current_item.data(Qt.UserRole):
            QMessageBox.information(self, "提示", "请选择要删除的备份")
            return

        backup_path = current_item.data(Qt.UserRole)

        # 确认删除
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除此备份吗？\n\n{current_item.text()}\n\n"
            "注意：删除后无法恢复。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        try:
            import os
            os.remove(backup_path)
            QMessageBox.information(self, "删除成功", "备份文件已删除")
            self.refresh_backup_list()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"删除备份时发生错误：\n{str(e)}")
