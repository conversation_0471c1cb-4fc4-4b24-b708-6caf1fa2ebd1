"""
搜索条件对话框
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGroupBox,
    QPushButton, QDialogButtonBox, QLabel, QLineEdit,
    QComboBox, QSpinBox, QDateEdit, QCheckBox, QWidget
)
from PySide6.QtCore import Qt, QDate


class SearchConditionDialog(QDialog):
    """搜索条件对话框"""

    def __init__(self, parent=None, condition=None):
        """初始化对话框

        Args:
            parent: 父窗口
            condition: 现有条件（编辑模式）
        """
        super().__init__(parent)
        self.setWindowTitle("搜索条件" if condition else "添加搜索条件")
        self.resize(400, 300)

        self.condition = condition or {}
        self.init_ui()

        # 如果是编辑模式，加载现有条件
        if condition:
            self.load_condition(condition)

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)

        # 字段选择
        field_group = QGroupBox("搜索字段")
        field_layout = QVBoxLayout(field_group)

        field_layout.addWidget(QLabel("选择要搜索的字段:"))
        self.field_combo = QComboBox()
        self.field_combo.addItems([
            "name - 文件名",
            "type - 文件类型",
            "size - 文件大小",
            "date - 添加日期",
            "tag - 标签",
            "entry - 入库方式"
        ])
        self.field_combo.currentTextChanged.connect(self.on_field_changed)
        field_layout.addWidget(self.field_combo)

        layout.addWidget(field_group)

        # 操作符选择
        operator_group = QGroupBox("操作符")
        operator_layout = QVBoxLayout(operator_group)

        operator_layout.addWidget(QLabel("选择比较操作符:"))
        self.operator_combo = QComboBox()
        operator_layout.addWidget(self.operator_combo)

        layout.addWidget(operator_group)

        # 值输入
        value_group = QGroupBox("值")
        value_layout = QVBoxLayout(value_group)

        # 文本输入
        self.text_input = QLineEdit()
        self.text_input.setPlaceholderText("输入搜索值")
        value_layout.addWidget(self.text_input)

        # 数字输入（用于文件大小）
        size_layout = QHBoxLayout()
        self.size_input = QSpinBox()
        self.size_input.setRange(0, 999999)
        self.size_input.setSuffix(" KB")
        size_layout.addWidget(self.size_input)
        self.size_unit_combo = QComboBox()
        self.size_unit_combo.addItems(["KB", "MB", "GB"])
        size_layout.addWidget(self.size_unit_combo)
        size_layout.addStretch()

        self.size_widget = QWidget()
        self.size_widget.setLayout(size_layout)
        self.size_widget.setVisible(False)
        value_layout.addWidget(self.size_widget)

        # 日期输入
        self.date_input = QDateEdit()
        self.date_input.setDate(QDate.currentDate())
        self.date_input.setCalendarPopup(True)
        self.date_input.setVisible(False)
        value_layout.addWidget(self.date_input)

        # 预定义值选择（用于入库方式等）
        self.predefined_combo = QComboBox()
        self.predefined_combo.setVisible(False)
        value_layout.addWidget(self.predefined_combo)

        layout.addWidget(value_group)

        # 对话框按钮
        buttons = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)

        # 初始化字段
        self.on_field_changed(self.field_combo.currentText())

    def on_field_changed(self, field_text):
        """字段改变事件"""
        field = field_text.split(" - ")[0]

        # 清空操作符
        self.operator_combo.clear()

        # 隐藏所有输入控件
        self.text_input.setVisible(False)
        self.size_widget.setVisible(False)
        self.date_input.setVisible(False)
        self.predefined_combo.setVisible(False)

        if field in ["name", "type", "tag"]:
            # 文本字段
            self.operator_combo.addItems(["包含", "等于", "开始于", "结束于", "匹配模式"])
            self.text_input.setVisible(True)

            if field == "name":
                self.text_input.setPlaceholderText("文件名或模式，如 *.txt")
            elif field == "type":
                self.text_input.setPlaceholderText("文件扩展名，如 txt, pdf")
            elif field == "tag":
                self.text_input.setPlaceholderText("标签名称")

        elif field == "size":
            # 文件大小字段
            self.operator_combo.addItems(["大于", "小于", "等于", "介于"])
            self.size_widget.setVisible(True)

        elif field == "date":
            # 日期字段
            self.operator_combo.addItems(["晚于", "早于", "等于", "介于"])
            self.date_input.setVisible(True)

        elif field == "entry":
            # 入库方式字段
            self.operator_combo.addItems(["等于"])
            self.predefined_combo.clear()
            self.predefined_combo.addItems(["link", "copy", "move"])
            self.predefined_combo.setVisible(True)

    def load_condition(self, condition):
        """加载现有条件"""
        field = condition.get("field", "")
        operator = condition.get("operator", "")
        value = condition.get("value", "")

        # 设置字段
        for i in range(self.field_combo.count()):
            if self.field_combo.itemText(i).startswith(field):
                self.field_combo.setCurrentIndex(i)
                break

        # 设置操作符
        operator_map = {
            "包含": "包含", "=": "等于", ">": "大于", "<": "小于",
            "开始于": "开始于", "结束于": "结束于", "匹配模式": "匹配模式",
            "晚于": "晚于", "早于": "早于", "介于": "介于"
        }

        operator_display = operator_map.get(operator, operator)
        for i in range(self.operator_combo.count()):
            if self.operator_combo.itemText(i) == operator_display:
                self.operator_combo.setCurrentIndex(i)
                break

        # 设置值
        if field == "size":
            # 解析文件大小
            if value.endswith("KB"):
                self.size_input.setValue(int(value[:-2]))
                self.size_unit_combo.setCurrentText("KB")
            elif value.endswith("MB"):
                self.size_input.setValue(int(value[:-2]))
                self.size_unit_combo.setCurrentText("MB")
            elif value.endswith("GB"):
                self.size_input.setValue(int(value[:-2]))
                self.size_unit_combo.setCurrentText("GB")
        elif field == "date":
            # 解析日期
            try:
                from datetime import datetime
                date_obj = datetime.fromisoformat(value).date()
                self.date_input.setDate(QDate.fromString(date_obj.isoformat(), Qt.ISODate))
            except:
                pass
        elif field == "entry":
            # 设置入库方式
            for i in range(self.predefined_combo.count()):
                if self.predefined_combo.itemText(i) == value:
                    self.predefined_combo.setCurrentIndex(i)
                    break
        else:
            # 文本值
            self.text_input.setText(value)

    def get_condition(self):
        """获取条件"""
        field_text = self.field_combo.currentText()
        field = field_text.split(" - ")[0]

        operator_text = self.operator_combo.currentText()

        # 操作符映射
        operator_map = {
            "包含": "LIKE", "等于": "=", "大于": ">", "小于": "<",
            "开始于": "STARTS", "结束于": "ENDS", "匹配模式": "MATCH",
            "晚于": ">", "早于": "<", "介于": "BETWEEN"
        }

        operator = operator_map.get(operator_text, "=")

        # 获取值
        if field == "size":
            size_value = self.size_input.value()
            size_unit = self.size_unit_combo.currentText()
            value = f"{size_value}{size_unit}"
        elif field == "date":
            date_value = self.date_input.date().toPython()
            value = date_value.isoformat()
        elif field == "entry":
            value = self.predefined_combo.currentText()
        else:
            value = self.text_input.text().strip()

        return {
            "field": field,
            "operator": operator,
            "value": value,
            "display_operator": operator_text
        }
