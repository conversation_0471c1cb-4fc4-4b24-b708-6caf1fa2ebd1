"""
本地化和中文化工具
"""

from PySide6.QtWidgets import QDialogButtonBox, QMessageBox
from PySide6.QtCore import QCoreApplication


def setup_chinese_ui():
    """设置中文界面"""
    # 设置Qt标准按钮的中文文本
    QCoreApplication.instance().installTranslator(ChineseTranslator())


class ChineseTranslator:
    """中文翻译器"""
    
    def __init__(self):
        self.translations = {
            # 标准按钮文本
            "OK": "确定",
            "Cancel": "取消", 
            "Yes": "是",
            "No": "否",
            "Apply": "应用",
            "Close": "关闭",
            "Save": "保存",
            "Open": "打开",
            "Reset": "重置",
            "Help": "帮助",
            "Abort": "中止",
            "Retry": "重试",
            "Ignore": "忽略",
            
            # 对话框标题
            "Information": "信息",
            "Warning": "警告", 
            "Error": "错误",
            "Question": "询问",
            "Critical": "严重错误",
        }
    
    def translate(self, context, source_text, disambiguation=None, n=-1):
        """翻译文本"""
        return self.translations.get(source_text, source_text)


def localize_dialog_buttons(dialog):
    """本地化对话框按钮
    
    Args:
        dialog: 要本地化的对话框
    """
    # 查找对话框中的按钮框
    button_box = dialog.findChild(QDialogButtonBox)
    if button_box:
        localize_button_box(button_box)


def localize_button_box(button_box):
    """本地化按钮框
    
    Args:
        button_box: QDialogButtonBox实例
    """
    # 中文按钮文本映射
    button_texts = {
        QDialogButtonBox.StandardButton.Ok: "确定",
        QDialogButtonBox.StandardButton.Cancel: "取消",
        QDialogButtonBox.StandardButton.Yes: "是", 
        QDialogButtonBox.StandardButton.No: "否",
        QDialogButtonBox.StandardButton.Apply: "应用",
        QDialogButtonBox.StandardButton.Close: "关闭",
        QDialogButtonBox.StandardButton.Save: "保存",
        QDialogButtonBox.StandardButton.Open: "打开",
        QDialogButtonBox.StandardButton.Reset: "重置",
        QDialogButtonBox.StandardButton.Help: "帮助",
        QDialogButtonBox.StandardButton.Abort: "中止",
        QDialogButtonBox.StandardButton.Retry: "重试",
        QDialogButtonBox.StandardButton.Ignore: "忽略",
    }
    
    # 设置按钮文本
    for standard_button, chinese_text in button_texts.items():
        button = button_box.button(standard_button)
        if button:
            button.setText(chinese_text)


def localize_message_box(message_box):
    """本地化消息框
    
    Args:
        message_box: QMessageBox实例
    """
    # 本地化标准按钮
    localize_button_box(message_box)
    
    # 本地化窗口标题（如果是默认的英文标题）
    title_translations = {
        "Information": "信息",
        "Warning": "警告",
        "Error": "错误", 
        "Question": "询问",
        "Critical": "严重错误",
    }
    
    current_title = message_box.windowTitle()
    if current_title in title_translations:
        message_box.setWindowTitle(title_translations[current_title])


def create_localized_message_box(parent, title, text, icon=QMessageBox.Icon.Information, 
                                buttons=QMessageBox.StandardButton.Ok):
    """创建本地化的消息框
    
    Args:
        parent: 父窗口
        title: 标题
        text: 消息文本
        icon: 图标类型
        buttons: 按钮类型
        
    Returns:
        QMessageBox: 本地化的消息框
    """
    msg_box = QMessageBox(parent)
    msg_box.setWindowTitle(title)
    msg_box.setText(text)
    msg_box.setIcon(icon)
    msg_box.setStandardButtons(buttons)
    
    # 本地化按钮
    localize_message_box(msg_box)
    
    return msg_box


def show_info_message(parent, title, text):
    """显示信息消息
    
    Args:
        parent: 父窗口
        title: 标题
        text: 消息文本
        
    Returns:
        int: 用户选择的结果
    """
    msg_box = create_localized_message_box(
        parent, title, text, 
        QMessageBox.Icon.Information,
        QMessageBox.StandardButton.Ok
    )
    return msg_box.exec()


def show_warning_message(parent, title, text):
    """显示警告消息
    
    Args:
        parent: 父窗口
        title: 标题
        text: 消息文本
        
    Returns:
        int: 用户选择的结果
    """
    msg_box = create_localized_message_box(
        parent, title, text,
        QMessageBox.Icon.Warning,
        QMessageBox.StandardButton.Ok
    )
    return msg_box.exec()


def show_error_message(parent, title, text):
    """显示错误消息
    
    Args:
        parent: 父窗口
        title: 标题
        text: 消息文本
        
    Returns:
        int: 用户选择的结果
    """
    msg_box = create_localized_message_box(
        parent, title, text,
        QMessageBox.Icon.Critical,
        QMessageBox.StandardButton.Ok
    )
    return msg_box.exec()


def show_question_message(parent, title, text, 
                         buttons=QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No):
    """显示询问消息
    
    Args:
        parent: 父窗口
        title: 标题
        text: 消息文本
        buttons: 按钮类型
        
    Returns:
        int: 用户选择的结果
    """
    msg_box = create_localized_message_box(
        parent, title, text,
        QMessageBox.Icon.Question,
        buttons
    )
    return msg_box.exec()


def apply_chinese_style(app):
    """应用中文样式
    
    Args:
        app: QApplication实例
    """
    # 设置中文字体
    from PySide6.QtGui import QFont
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    
    # 设置样式表以确保中文显示正确
    app.setStyleSheet("""
        QWidget {
            font-family: "Microsoft YaHei", "SimHei", "Arial Unicode MS", sans-serif;
        }
        
        QMessageBox {
            font-family: "Microsoft YaHei", "SimHei", "Arial Unicode MS", sans-serif;
        }
        
        QDialog {
            font-family: "Microsoft YaHei", "SimHei", "Arial Unicode MS", sans-serif;
        }
        
        QPushButton {
            font-family: "Microsoft YaHei", "SimHei", "Arial Unicode MS", sans-serif;
            min-width: 60px;
            padding: 4px 8px;
        }
    """)


# 便捷函数，用于替换标准的QMessageBox调用
def information(parent, title, text):
    """信息对话框（中文化版本）"""
    return show_info_message(parent, title, text)


def warning(parent, title, text):
    """警告对话框（中文化版本）"""
    return show_warning_message(parent, title, text)


def critical(parent, title, text):
    """错误对话框（中文化版本）"""
    return show_error_message(parent, title, text)


def question(parent, title, text, buttons=QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No):
    """询问对话框（中文化版本）"""
    return show_question_message(parent, title, text, buttons)
