"""
智能文件库问题修复工具
"""

import os
import sys
import shutil
import sqlite3
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from smartvault.utils.config import load_config, save_config, normalize_path
from smartvault.data.database import Database
from smartvault.data.file_system import FileSystem


class LibraryFixer:
    """智能文件库修复器"""

    def __init__(self):
        """初始化修复器"""
        self.issues_found = []
        self.fixes_applied = []

    def diagnose_library(self):
        """诊断文件库问题"""
        print("🔍 开始诊断智能文件库...")

        # 1. 检查配置
        self._check_config()

        # 2. 检查文件库结构
        self._check_library_structure()

        # 3. 检查数据库
        self._check_database()

        # 4. 检查文件路径一致性
        self._check_path_consistency()

        # 5. 检查文件可用性
        self._check_file_availability()

        return self.issues_found

    def _check_config(self):
        """检查配置"""
        try:
            config = load_config()
            library_path = config.get("library_path")

            if not library_path:
                self.issues_found.append("配置中缺少library_path")
                return

            if not os.path.exists(library_path):
                self.issues_found.append(f"配置的文件库路径不存在: {library_path}")
                return

            print(f"✅ 配置检查通过: {library_path}")

        except Exception as e:
            self.issues_found.append(f"配置加载失败: {e}")

    def _check_library_structure(self):
        """检查文件库结构"""
        try:
            config = load_config()
            library_path = config["library_path"]

            # 检查data目录
            data_dir = os.path.join(library_path, "data")
            if not os.path.exists(data_dir):
                self.issues_found.append(f"缺少data目录: {data_dir}")
                return

            # 检查数据库文件
            db_file = os.path.join(data_dir, "smartvault.db")
            if not os.path.exists(db_file):
                self.issues_found.append(f"缺少数据库文件: {db_file}")
                return

            print(f"✅ 文件库结构检查通过")

        except Exception as e:
            self.issues_found.append(f"文件库结构检查失败: {e}")

    def _check_database(self):
        """检查数据库"""
        try:
            db = Database.create_from_config()

            # 检查表是否存在
            cursor = db.conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]

            required_tables = ['files', 'tags', 'file_tags']
            missing_tables = [table for table in required_tables if table not in tables]

            if missing_tables:
                self.issues_found.append(f"数据库缺少表: {missing_tables}")
            else:
                print(f"✅ 数据库检查通过，包含表: {tables}")

            db.close()

        except Exception as e:
            self.issues_found.append(f"数据库检查失败: {e}")

    def _check_path_consistency(self):
        """检查路径一致性"""
        try:
            config = load_config()
            config_path = config["library_path"]

            # 检查FileSystem路径
            fs = FileSystem()
            fs_path = fs.library_path

            # 检查Database路径
            db = Database()
            db_data_dir = os.path.dirname(db.db_path)
            expected_data_dir = os.path.join(config_path, "data")

            # 标准化路径进行比较
            config_path_norm = normalize_path(config_path)
            fs_path_norm = normalize_path(fs_path)
            db_data_dir_norm = normalize_path(db_data_dir)
            expected_data_dir_norm = normalize_path(expected_data_dir)

            if fs_path_norm != config_path_norm:
                self.issues_found.append(f"FileSystem路径不一致: {fs_path_norm} != {config_path_norm}")

            if db_data_dir_norm != expected_data_dir_norm:
                self.issues_found.append(f"Database路径不一致: {db_data_dir_norm} != {expected_data_dir_norm}")

            if not self.issues_found or len([i for i in self.issues_found if "路径不一致" in i]) == 0:
                print(f"✅ 路径一致性检查通过")

            db.close()

        except Exception as e:
            self.issues_found.append(f"路径一致性检查失败: {e}")

    def _check_file_availability(self):
        """检查文件可用性"""
        try:
            db = Database()
            cursor = db.conn.cursor()

            # 获取所有文件记录
            cursor.execute("SELECT id, name, original_path, library_path, entry_type FROM files")
            files = cursor.fetchall()

            unavailable_files = []

            for file_record in files:
                file_id, name, original_path, library_path, entry_type = file_record

                # 确定实际文件路径
                if entry_type == "link":
                    actual_path = original_path
                else:
                    actual_path = library_path

                # 检查文件是否存在
                if actual_path and not os.path.exists(actual_path):
                    unavailable_files.append((file_id, name, actual_path, entry_type))

            if unavailable_files:
                self.issues_found.append(f"发现 {len(unavailable_files)} 个不可用文件")
                for file_id, name, path, entry_type in unavailable_files[:5]:  # 只显示前5个
                    print(f"  ❌ {name} ({entry_type}): {path}")
                if len(unavailable_files) > 5:
                    print(f"  ... 还有 {len(unavailable_files) - 5} 个文件")
            else:
                print(f"✅ 文件可用性检查通过，共检查 {len(files)} 个文件")

            db.close()

        except Exception as e:
            self.issues_found.append(f"文件可用性检查失败: {e}")

    def fix_issues(self):
        """修复发现的问题"""
        if not self.issues_found:
            print("🎉 没有发现问题，无需修复！")
            return

        print(f"\n🔧 开始修复 {len(self.issues_found)} 个问题...")

        for issue in self.issues_found:
            try:
                if "缺少data目录" in issue:
                    self._fix_missing_data_dir(issue)
                elif "缺少数据库文件" in issue:
                    self._fix_missing_database(issue)
                elif "数据库缺少表" in issue:
                    self._fix_missing_tables(issue)
                elif "路径不一致" in issue:
                    self._fix_path_inconsistency(issue)
                else:
                    print(f"⚠️  暂不支持自动修复: {issue}")
            except Exception as e:
                print(f"❌ 修复失败 '{issue}': {e}")

    def _fix_missing_data_dir(self, issue):
        """修复缺少data目录"""
        config = load_config()
        library_path = config["library_path"]
        data_dir = os.path.join(library_path, "data")

        os.makedirs(data_dir, exist_ok=True)
        self.fixes_applied.append(f"创建data目录: {data_dir}")
        print(f"✅ 已创建data目录: {data_dir}")

    def _fix_missing_database(self, issue):
        """修复缺少数据库文件"""
        # 重新初始化数据库
        db = Database.create_from_config()
        db.close()
        self.fixes_applied.append("重新创建数据库文件")
        print(f"✅ 已重新创建数据库文件")

    def _fix_missing_tables(self, issue):
        """修复缺少的数据库表"""
        db = Database.create_from_config()
        db._create_tables()  # 重新创建表
        db.close()
        self.fixes_applied.append("重新创建数据库表")
        print(f"✅ 已重新创建数据库表")

    def _fix_path_inconsistency(self, issue):
        """修复路径不一致问题"""
        # 这个问题通常需要重启应用程序来解决
        print(f"⚠️  路径不一致问题需要重启应用程序来解决")

    def generate_report(self):
        """生成修复报告"""
        print("\n" + "="*60)
        print("📊 智能文件库诊断和修复报告")
        print("="*60)

        print(f"发现问题数: {len(self.issues_found)}")
        print(f"已修复数: {len(self.fixes_applied)}")

        if self.issues_found:
            print("\n🔍 发现的问题:")
            for i, issue in enumerate(self.issues_found, 1):
                print(f"  {i}. {issue}")

        if self.fixes_applied:
            print("\n🔧 已应用的修复:")
            for i, fix in enumerate(self.fixes_applied, 1):
                print(f"  {i}. {fix}")

        if len(self.fixes_applied) == len(self.issues_found):
            print("\n🎉 所有问题已修复！")
        elif self.fixes_applied:
            print(f"\n⚠️  还有 {len(self.issues_found) - len(self.fixes_applied)} 个问题需要手动处理")
        else:
            print("\n❌ 没有问题被自动修复，可能需要手动处理")


def main():
    """主函数"""
    print("🔧 智能文件库修复工具")
    print("="*40)

    fixer = LibraryFixer()

    # 诊断问题
    issues = fixer.diagnose_library()

    if issues:
        print(f"\n⚠️  发现 {len(issues)} 个问题")

        # 询问是否修复
        response = input("\n是否尝试自动修复这些问题？(y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            fixer.fix_issues()

    # 生成报告
    fixer.generate_report()


if __name__ == "__main__":
    main()
