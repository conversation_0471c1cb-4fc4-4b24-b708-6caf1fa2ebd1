"""
文件监控服务 - 基于 Qt QFileSystemWatcher 的实现
"""

import os
import uuid
import fnmatch
import time
from datetime import datetime
from typing import List, Dict, Optional, Callable

from PySide6.QtCore import QObject, QFileSystemWatcher, Signal, QTimer
from smartvault.data.database import Database


class FileMonitorService(QObject):
    """文件监控服务类 - 基于 Qt 的线程安全实现"""

    # 定义信号
    file_created = Signal(str, str)  # file_path, monitor_id
    file_modified = Signal(str, str)  # file_path, monitor_id
    file_moved = Signal(str, str)    # file_path, monitor_id

    def __init__(self):
        """初始化文件监控服务"""
        super().__init__()
        self._db = None
        self.watchers = {}  # monitor_id -> QFileSystemWatcher
        self.monitor_configs = {}  # monitor_id -> config
        self.event_callback = None
        self.error_logs = []
        self._tables_initialized = False

        # 防重复处理机制
        self.processed_files = set()  # 已处理的文件路径
        self.processing_files = set()  # 正在处理的文件路径

        # 事件去重机制 (死循环修复)
        self.event_timestamps = {}  # file_path -> last_event_time
        self.event_debounce_ms = 1000  # 1秒内的重复事件忽略
        self.directory_snapshots = {}  # monitor_id -> {file_path: mtime}
        self.self_created_files = set()  # 自己创建的文件，避免重复处理
        self.pending_scans = {}  # monitor_id -> QTimer，用于延迟扫描

        # 批量处理统计机制
        self.batch_processing = {}  # monitor_id -> {'files': [], 'success': [], 'errors': [], 'timer': QTimer}
        self.batch_timeout_ms = 2000  # 2秒内的文件处理归为一批

        # 连接信号到处理函数
        self.file_created.connect(self._handle_file_created)
        self.file_modified.connect(self._handle_file_modified)
        self.file_moved.connect(self._handle_file_moved)

        # 依赖注入的文件服务实例（由主窗口设置）
        self._file_service = None

    def set_file_service(self, file_service):
        """设置文件服务实例（依赖注入）"""
        self._file_service = file_service

    @property
    def db(self):
        """获取数据库实例，支持延迟初始化和测试注入"""
        if self._db is None:
            self._db = Database.create_from_config()

        # 确保数据库表已初始化
        if not self._tables_initialized:
            self._init_database_tables()
            self._tables_initialized = True

        return self._db

    def _init_database_tables(self):
        """初始化数据库表"""
        try:
            cursor = self._db.conn.cursor()

            # 创建监控配置表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS monitor_configs (
                    id TEXT PRIMARY KEY,
                    folder_path TEXT NOT NULL,
                    entry_mode TEXT NOT NULL DEFAULT 'link',
                    file_patterns TEXT,
                    auto_add INTEGER DEFAULT 1,
                    recursive INTEGER DEFAULT 1,
                    auto_dedup INTEGER DEFAULT 0,
                    is_active INTEGER DEFAULT 0,
                    created_at TIMESTAMP NOT NULL,
                    updated_at TIMESTAMP NOT NULL
                )
            ''')

            # 检查并添加auto_dedup字段（兼容现有数据库）
            try:
                cursor.execute("ALTER TABLE monitor_configs ADD COLUMN auto_dedup INTEGER DEFAULT 0")
                self._db.conn.commit()
            except Exception:
                # 字段已存在，忽略错误
                pass

            # 创建监控事件表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS monitor_events (
                    id TEXT PRIMARY KEY,
                    monitor_id TEXT NOT NULL,
                    event_type TEXT NOT NULL,
                    file_path TEXT NOT NULL,
                    file_size INTEGER,
                    processed INTEGER DEFAULT 0,
                    error_message TEXT,
                    created_at TIMESTAMP NOT NULL,
                    FOREIGN KEY (monitor_id) REFERENCES monitor_configs (id)
                )
            ''')

            self._db.conn.commit()

        except Exception as e:
            print(f"初始化监控数据库表失败: {e}")

    def add_monitor_folder(self, folder_path: str, entry_mode: str = "link",
                          file_patterns: List[str] = None, auto_add: bool = True,
                          recursive: bool = True, auto_dedup: bool = False) -> str:
        """添加监控文件夹

        Args:
            folder_path: 文件夹路径
            entry_mode: 入库模式 (link/copy/move)
            file_patterns: 文件模式列表，如 ["*.txt", "*.pdf"]
            auto_add: 是否自动添加到库
            recursive: 是否递归监控子文件夹
            auto_dedup: 是否启用自动查重（预留功能，第三阶段实现）

        Returns:
            str: 监控ID
        """
        # 验证文件夹路径
        if not os.path.exists(folder_path):
            raise ValueError(f"文件夹不存在: {folder_path}")

        if not os.path.isdir(folder_path):
            raise ValueError(f"路径不是文件夹: {folder_path}")

        # 生成监控ID
        monitor_id = str(uuid.uuid4())

        # 处理文件模式
        patterns_str = ""
        if file_patterns:
            patterns_str = ",".join(file_patterns)

        # 保存监控配置
        cursor = self.db.conn.cursor()
        cursor.execute(
            """
            INSERT INTO monitor_configs (
                id, folder_path, entry_mode, file_patterns,
                auto_add, recursive, auto_dedup, is_active, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """,
            (
                monitor_id,
                os.path.abspath(folder_path),
                entry_mode,
                patterns_str,
                1 if auto_add else 0,
                1 if recursive else 0,
                1 if auto_dedup else 0,
                0,  # 初始状态为非活动
                datetime.now().isoformat(),
                datetime.now().isoformat()
            )
        )

        self.db.conn.commit()
        return monitor_id

    def start_monitoring(self, monitor_id: str) -> bool:
        """启动监控

        Args:
            monitor_id: 监控ID

        Returns:
            bool: 是否成功启动
        """
        try:
            # 获取监控配置
            config = self.get_monitor_config(monitor_id)
            if not config:
                return False

            # 检查文件夹是否存在
            if not os.path.exists(config["folder_path"]):
                self._log_error(f"监控文件夹不存在: {config['folder_path']}")
                return False

            # 如果已经在监控，先停止
            if monitor_id in self.watchers:
                self.stop_monitoring(monitor_id)

            # 创建 Qt 文件系统监控器
            watcher = QFileSystemWatcher()
            watcher.addPath(config["folder_path"])

            # 连接信号
            watcher.directoryChanged.connect(
                lambda path: self._on_directory_changed(path, monitor_id)
            )

            # 保存监控器和配置
            self.watchers[monitor_id] = watcher
            self.monitor_configs[monitor_id] = config

            # 更新数据库状态
            cursor = self.db.conn.cursor()
            cursor.execute(
                "UPDATE monitor_configs SET is_active = 1, updated_at = ? WHERE id = ?",
                (datetime.now().isoformat(), monitor_id)
            )
            self.db.conn.commit()

            print(f"开始监控文件夹: {config['folder_path']}")
            return True

        except Exception as e:
            self._log_error(f"启动监控失败: {e}")
            return False

    def stop_monitoring(self, monitor_id: str) -> bool:
        """停止监控

        Args:
            monitor_id: 监控ID

        Returns:
            bool: 是否成功停止
        """
        try:
            if monitor_id in self.watchers:
                watcher = self.watchers[monitor_id]
                # Qt 对象会自动清理，只需要移除引用
                del self.watchers[monitor_id]

            if monitor_id in self.monitor_configs:
                del self.monitor_configs[monitor_id]

            # 更新数据库状态
            cursor = self.db.conn.cursor()
            cursor.execute(
                "UPDATE monitor_configs SET is_active = 0, updated_at = ? WHERE id = ?",
                (datetime.now().isoformat(), monitor_id)
            )
            self.db.conn.commit()

            return True

        except Exception as e:
            self._log_error(f"停止监控失败: {e}")
            return False

    def stop_all_monitoring(self):
        """停止所有监控"""
        monitor_ids = list(self.watchers.keys())
        for monitor_id in monitor_ids:
            self.stop_monitoring(monitor_id)

    def _on_directory_changed(self, path: str, monitor_id: str):
        """目录变化事件处理（改进版，添加事件去重）"""
        try:
            config = self.monitor_configs.get(monitor_id)
            if not config:
                return

            # 事件去重：检查是否为重复事件
            current_time = time.time() * 1000
            event_key = f"{path}:{monitor_id}"
            last_time = self.event_timestamps.get(event_key, 0)

            if current_time - last_time < self.event_debounce_ms:
                print(f"⏭️ 忽略重复目录变化事件: {os.path.basename(path)}")
                return

            self.event_timestamps[event_key] = current_time

            # 取消之前的延迟扫描
            if monitor_id in self.pending_scans:
                self.pending_scans[monitor_id].stop()
                del self.pending_scans[monitor_id]

            # 延迟扫描，合并短时间内的多个事件
            timer = QTimer()
            timer.setSingleShot(True)
            timer.timeout.connect(lambda: self._delayed_scan_directory(path, monitor_id, config))
            timer.start(200)  # 200ms延迟

            self.pending_scans[monitor_id] = timer

            print(f"📁 目录变化事件已排队: {os.path.basename(path)}")

        except Exception as e:
            self._log_error(f"处理目录变化事件失败: {e}")

    def _delayed_scan_directory(self, path: str, monitor_id: str, config: Dict):
        """延迟扫描目录（在定时器中调用）"""
        try:
            # 清理定时器引用
            if monitor_id in self.pending_scans:
                del self.pending_scans[monitor_id]

            # 执行改进的目录扫描
            self._scan_directory_for_new_files_improved(path, monitor_id, config)

        except Exception as e:
            self._log_error(f"延迟扫描目录失败: {e}")


    def _scan_directory_for_new_files_improved(self, directory: str, monitor_id: str, config: Dict):
        """改进的目录扫描（使用快照，只处理新文件）"""
        try:
            if not os.path.exists(directory):
                return

            # 获取当前目录快照
            current_snapshot = {}
            last_snapshot = self.directory_snapshots.get(monitor_id, {})

            for filename in os.listdir(directory):
                file_path = os.path.join(directory, filename)

                # 跳过目录
                if os.path.isdir(file_path):
                    continue

                # 跳过自己创建的文件
                if file_path in self.self_created_files:
                    print(f"⏭️ 跳过自创建文件: {os.path.basename(file_path)}")
                    self.self_created_files.discard(file_path)  # 清理标记
                    continue

                # 检查文件模式匹配
                if not self._matches_patterns(file_path, config["file_patterns"]):
                    continue

                # 获取文件修改时间
                try:
                    mtime = os.path.getmtime(file_path)
                    current_snapshot[file_path] = mtime

                    # 只处理新文件或修改的文件
                    if file_path not in last_snapshot or last_snapshot[file_path] != mtime:
                        print(f"📄 检测到新/修改文件: {os.path.basename(file_path)}")
                        self.file_created.emit(file_path, monitor_id)

                except OSError:
                    # 文件可能已被删除或无法访问
                    continue

            # 更新目录快照
            self.directory_snapshots[monitor_id] = current_snapshot

            print(f"📊 目录扫描完成: {len(current_snapshot)} 个文件，{len([f for f in current_snapshot if f not in last_snapshot])} 个新文件")

        except Exception as e:
            self._log_error(f"改进目录扫描失败: {e}")

    def _handle_file_created(self, file_path: str, monitor_id: str):
        """处理文件创建信号"""
        try:
            # 防重复处理检查
            if file_path in self.processed_files or file_path in self.processing_files:
                print(f"⏭️ 跳过已处理的文件: {os.path.basename(file_path)}")
                return

            config = self.monitor_configs.get(monitor_id)
            if not config:
                return

            print(f"📁 监控事件: created - {os.path.basename(file_path)}")

            # 标记为正在处理
            self.processing_files.add(file_path)

            try:
                # 初始化批量处理统计（如果需要）
                self._init_batch_processing(monitor_id)

                # 添加到批量处理队列
                self.batch_processing[monitor_id]['files'].append(file_path)

                # 通知UI开始批量处理（仅第一个文件时）
                if len(self.batch_processing[monitor_id]['files']) == 1:
                    if self.event_callback:
                        self.event_callback("batch_start", f"开始批量处理文件...", monitor_id)

                # 如果启用自动添加，处理文件
                if config["auto_add"]:
                    success = self._auto_add_file_with_feedback_batch(file_path, config, monitor_id)
                    if success:
                        self.batch_processing[monitor_id]['success'].append(file_path)
                    else:
                        self.batch_processing[monitor_id]['errors'].append(file_path)

                # 重置批量处理定时器
                self._reset_batch_timer(monitor_id)

                # 标记为已处理
                self.processed_files.add(file_path)

            finally:
                # 移除正在处理标记
                self.processing_files.discard(file_path)

        except Exception as e:
            error_msg = f"处理文件创建事件失败: {e}"
            self._log_error(error_msg)
            if self.event_callback:
                self.event_callback("error", error_msg, monitor_id)
            # 确保移除处理标记
            self.processing_files.discard(file_path)
            # 添加到错误列表
            if monitor_id in self.batch_processing:
                self.batch_processing[monitor_id]['errors'].append(file_path)

    def _handle_file_modified(self, file_path: str, monitor_id: str):
        """处理文件修改信号"""
        # 目前不处理修改事件，预留接口
        pass

    def _handle_file_moved(self, file_path: str, monitor_id: str):
        """处理文件移动信号"""
        # 目前不处理移动事件，预留接口
        pass

    def get_monitor_config(self, monitor_id: str) -> Optional[Dict]:
        """获取监控配置

        Args:
            monitor_id: 监控ID

        Returns:
            dict: 监控配置信息
        """
        cursor = self.db.conn.cursor()
        cursor.execute(
            "SELECT * FROM monitor_configs WHERE id = ?",
            (monitor_id,)
        )

        row = cursor.fetchone()
        if not row:
            return None

        config = dict(row)

        # 处理文件模式
        if config["file_patterns"]:
            config["file_patterns"] = config["file_patterns"].split(",")
        else:
            config["file_patterns"] = []

        # 转换布尔值
        config["auto_add"] = bool(config["auto_add"])
        config["recursive"] = bool(config["recursive"])
        config["auto_dedup"] = bool(config.get("auto_dedup", 0))
        config["is_active"] = bool(config["is_active"])

        return config

    def get_monitor_status(self, monitor_id: str) -> Optional[Dict]:
        """获取监控状态

        Args:
            monitor_id: 监控ID

        Returns:
            dict: 监控状态信息
        """
        config = self.get_monitor_config(monitor_id)
        if not config:
            return None

        is_running = monitor_id in self.watchers

        return {
            "monitor_id": monitor_id,
            "folder_path": config["folder_path"],
            "is_active": is_running,
            "is_configured": config["is_active"],
            "entry_mode": config["entry_mode"],
            "auto_add": config["auto_add"],
            "recursive": config["recursive"]
        }

    def get_all_monitors(self) -> List[Dict]:
        """获取所有监控配置

        Returns:
            list: 监控配置列表
        """
        cursor = self.db.conn.cursor()
        cursor.execute("SELECT * FROM monitor_configs ORDER BY created_at DESC")

        monitors = []
        for row in cursor.fetchall():
            config = dict(row)

            # 处理文件模式
            if config["file_patterns"]:
                config["file_patterns"] = config["file_patterns"].split(",")
            else:
                config["file_patterns"] = []

            # 转换布尔值
            config["auto_add"] = bool(config["auto_add"])
            config["recursive"] = bool(config["recursive"])
            config["auto_dedup"] = bool(config.get("auto_dedup", 0))
            config["is_active"] = bool(config["is_active"])

            monitors.append(config)

        return monitors

    def get_all_monitor_configs(self) -> List[Dict]:
        """获取所有监控配置（别名方法，兼容设置页面调用）

        Returns:
            list: 监控配置列表
        """
        return self.get_all_monitors()

    def set_event_callback(self, callback: Callable):
        """设置事件回调函数

        Args:
            callback: 回调函数，签名为 (event_type, file_path, monitor_id)
        """
        self.event_callback = callback



    def _auto_add_file_with_feedback(self, file_path: str, config: Dict, monitor_id: str):
        """自动添加文件到库（旧版本，已弃用，保留兼容性）"""
        # 这个方法已被批量处理机制替代，但保留以防有其他地方调用
        return self._auto_add_file_with_feedback_batch(file_path, config, monitor_id)



    def _matches_patterns(self, file_path: str, patterns: List[str]) -> bool:
        """检查文件是否匹配模式

        Args:
            file_path: 文件路径
            patterns: 文件模式列表

        Returns:
            bool: 是否匹配
        """
        if not patterns:
            return True  # 没有模式限制，匹配所有文件

        filename = os.path.basename(file_path)

        for pattern in patterns:
            if fnmatch.fnmatch(filename, pattern):
                return True

        return False



    def update_monitor_config(self, monitor_id: str, **kwargs) -> bool:
        """更新监控配置

        Args:
            monitor_id: 监控ID
            **kwargs: 要更新的配置项

        Returns:
            bool: 是否成功更新
        """
        try:
            # 构建更新SQL
            update_fields = []
            params = []

            for key, value in kwargs.items():
                if key in ["entry_mode", "auto_add", "recursive", "file_patterns"]:
                    if key == "file_patterns" and isinstance(value, list):
                        value = ",".join(value)
                    elif key in ["auto_add", "recursive"]:
                        value = 1 if value else 0

                    update_fields.append(f"{key} = ?")
                    params.append(value)

            if not update_fields:
                return False

            # 添加更新时间
            update_fields.append("updated_at = ?")
            params.append(datetime.now().isoformat())
            params.append(monitor_id)

            cursor = self.db.conn.cursor()
            cursor.execute(
                f"UPDATE monitor_configs SET {', '.join(update_fields)} WHERE id = ?",
                params
            )

            self.db.conn.commit()
            return cursor.rowcount > 0

        except Exception as e:
            self._log_error(f"更新监控配置失败: {e}")
            return False

    def remove_monitor_folder(self, monitor_id: str) -> bool:
        """删除监控文件夹

        Args:
            monitor_id: 监控ID

        Returns:
            bool: 是否成功删除
        """
        try:
            # 先停止监控
            self.stop_monitoring(monitor_id)

            # 删除监控配置
            cursor = self.db.conn.cursor()
            cursor.execute("DELETE FROM monitor_configs WHERE id = ?", (monitor_id,))
            config_deleted = cursor.rowcount > 0

            # 删除相关事件记录
            cursor.execute("DELETE FROM monitor_events WHERE monitor_id = ?", (monitor_id,))

            self.db.conn.commit()
            return config_deleted  # 只要配置删除成功就算成功

        except Exception as e:
            self._log_error(f"删除监控配置失败: {e}")
            return False

    def get_monitor_statistics(self) -> Dict:
        """获取监控统计信息

        Returns:
            dict: 统计信息
        """
        cursor = self.db.conn.cursor()

        # 总监控数
        cursor.execute("SELECT COUNT(*) FROM monitor_configs")
        total_monitors = cursor.fetchone()[0]

        # 活动监控数
        cursor.execute("SELECT COUNT(*) FROM monitor_configs WHERE is_active = 1")
        active_monitors = cursor.fetchone()[0]

        # 总处理文件数
        cursor.execute("SELECT COUNT(*) FROM monitor_events WHERE processed = 1")
        total_files_added = cursor.fetchone()[0]

        # 错误数
        cursor.execute("SELECT COUNT(*) FROM monitor_events WHERE error_message IS NOT NULL")
        error_count = cursor.fetchone()[0]

        return {
            "total_monitors": total_monitors,
            "active_monitors": active_monitors,
            "running_monitors": len(self.watchers),
            "total_files_added": total_files_added,
            "error_count": error_count
        }

    def get_recent_errors(self, limit: int = 10) -> List[Dict]:
        """获取最近的错误日志

        Args:
            limit: 返回数量限制

        Returns:
            list: 错误日志列表
        """
        # 返回内存中的错误日志
        return self.error_logs[-limit:] if self.error_logs else []

    def _log_error(self, message: str):
        """记录错误日志

        Args:
            message: 错误消息
        """
        error_entry = {
            "timestamp": datetime.now().isoformat(),
            "message": message
        }

        self.error_logs.append(error_entry)

        # 保持错误日志数量在合理范围内
        if len(self.error_logs) > 100:
            self.error_logs = self.error_logs[-50:]

        print(f"监控服务错误: {message}")

    def _init_batch_processing(self, monitor_id: str):
        """初始化批量处理统计"""
        if monitor_id not in self.batch_processing:
            from PySide6.QtCore import QTimer
            self.batch_processing[monitor_id] = {
                'files': [],
                'success': [],
                'errors': [],
                'timer': QTimer()
            }
            # 设置定时器回调 - 使用functools.partial避免lambda作用域问题
            import functools
            self.batch_processing[monitor_id]['timer'].setSingleShot(True)
            callback = functools.partial(self._finish_batch_processing, monitor_id)
            self.batch_processing[monitor_id]['timer'].timeout.connect(callback)

    def _reset_batch_timer(self, monitor_id: str):
        """重置批量处理定时器"""
        if monitor_id in self.batch_processing:
            timer = self.batch_processing[monitor_id]['timer']
            timer.stop()
            timer.start(self.batch_timeout_ms)

    def _finish_batch_processing(self, monitor_id: str):
        """完成批量处理并发送统计信息"""
        try:
            print(f"🔔 批量处理定时器触发: monitor_id={monitor_id}")

            if monitor_id not in self.batch_processing:
                print(f"⚠️ 批量处理数据不存在: {monitor_id}")
                return

            batch_data = self.batch_processing[monitor_id]
            total_files = len(batch_data['files'])
            success_count = len(batch_data['success'])
            error_count = len(batch_data['errors'])

            print(f"📊 批量处理数据: 总计 {total_files} 个文件，成功 {success_count} 个，失败 {error_count} 个")

            if total_files > 0:
                # 发送批量处理完成事件
                if self.event_callback:
                    stats_msg = f"批量处理完成: 成功 {success_count} 个，失败 {error_count} 个文件"
                    print(f"🔔 发送UI回调: {stats_msg}")
                    self.event_callback("batch_complete", stats_msg, monitor_id, {
                        'total': total_files,
                        'success': success_count,
                        'errors': error_count,
                        'success_files': batch_data['success'],
                        'error_files': batch_data['errors']
                    })
                else:
                    print(f"⚠️ event_callback 为空，无法发送UI回调")

                print(f"📊 批量处理统计: 总计 {total_files} 个文件，成功 {success_count} 个，失败 {error_count} 个")

            # 清理批量处理数据
            del self.batch_processing[monitor_id]
            print(f"🧹 已清理批量处理数据: {monitor_id}")

        except Exception as e:
            self._log_error(f"完成批量处理失败: {e}")
            import traceback
            traceback.print_exc()

    def _auto_add_file_with_feedback_batch(self, file_path: str, config: Dict, monitor_id: str) -> bool:
        """批量模式的自动添加文件到库（不发送单个文件的UI回调）"""
        try:
            # 优先使用注入的文件服务实例
            if self._file_service:
                file_service = self._file_service
            else:
                # 回退到创建新实例（向后兼容）
                from smartvault.services.file import FileService
                file_service = FileService()
                if hasattr(self, '_db') and self._db:
                    file_service._db = self._db

            # 使用统一的文件添加入口（自动启用智能重复处理）
            file_id = file_service.add_file(file_path, config["entry_mode"], smart_duplicate_handling=True)

            if file_id:
                # 成功添加，只打印日志，不发送UI回调
                success_msg = f"文件已添加到库: {os.path.basename(file_path)}"
                print(f"✅ {success_msg}")
                return True
            else:
                print(f"❌ 文件添加失败: {os.path.basename(file_path)}")
                return False

        except Exception as e:
            print(f"❌ 自动添加文件失败: {os.path.basename(file_path)} - {e}")
            return False
