"""
文件工作流集成测试
"""

import os
import unittest
import tempfile
from smartvault.data.database import Database
from smartvault.data.file_system import FileSystem
from smartvault.services.file import FileService


class TestFileWorkflow(unittest.TestCase):
    """文件工作流集成测试类"""

    def setUp(self):
        """测试前准备"""
        # 创建临时目录
        self.temp_dir = tempfile.TemporaryDirectory()
        self.app_data_dir = os.path.join(self.temp_dir.name, "app_data")
        self.library_path = os.path.join(self.app_data_dir, "library")
        os.makedirs(self.app_data_dir, exist_ok=True)

        # 创建测试文件
        self.test_file_path = os.path.join(self.temp_dir.name, "test_file.txt")
        with open(self.test_file_path, "w", encoding="utf-8") as f:
            f.write("This is a test file.")

        # 创建数据库和文件系统
        self.db_path = os.path.join(self.app_data_dir, "test.db")
        self.db = Database(self.db_path)
        self.fs = FileSystem(self.library_path)

        # 创建文件服务
        self.file_service = FileService()
        self.file_service.db = self.db
        self.file_service.file_system = self.fs

    def tearDown(self):
        """测试后清理"""
        # 关闭数据库连接
        if self.db.conn:
            self.db.close()

        # 删除临时目录
        self.temp_dir.cleanup()

    def test_add_and_get_file_link_mode(self):
        """测试添加和获取文件（链接模式）"""
        # 添加文件
        file_id = self.file_service.add_file(self.test_file_path, mode="link")

        # 验证文件ID
        self.assertIsNotNone(file_id)

        # 获取文件
        file = self.file_service.get_file_by_id(file_id)

        # 验证文件信息
        self.assertIsNotNone(file)
        self.assertEqual(file["id"], file_id)
        self.assertEqual(file["name"], os.path.basename(self.test_file_path))
        self.assertEqual(file["original_path"], self.test_file_path)
        self.assertIsNone(file["library_path"])
        self.assertEqual(file["entry_type"], "link")
        self.assertEqual(file["is_available"], 1)

    def test_add_and_get_file_copy_mode(self):
        """测试添加和获取文件（复制模式）"""
        # 添加文件
        file_id = self.file_service.add_file(self.test_file_path, mode="copy")

        # 验证文件ID
        self.assertIsNotNone(file_id)

        # 获取文件
        file = self.file_service.get_file_by_id(file_id)

        # 验证文件信息
        self.assertIsNotNone(file)
        self.assertEqual(file["id"], file_id)
        self.assertEqual(file["name"], os.path.basename(self.test_file_path))
        self.assertEqual(file["original_path"], self.test_file_path)
        self.assertIsNotNone(file["library_path"])
        self.assertEqual(file["entry_type"], "copy")
        self.assertEqual(file["is_available"], 1)

        # 验证文件是否已复制
        self.assertTrue(os.path.exists(file["library_path"]))

        # 验证文件内容
        with open(file["library_path"], "r", encoding="utf-8") as f:
            content = f.read()
        self.assertEqual(content, "This is a test file.")

    def test_add_and_remove_file(self):
        """测试添加和移除文件"""
        # 添加文件（使用链接模式，避免文件复制问题）
        file_id = self.file_service.add_file(self.test_file_path, mode="link")

        # 获取文件
        file = self.file_service.get_file_by_id(file_id)
        original_path = file["original_path"]

        # 验证文件信息
        self.assertEqual(original_path, self.test_file_path)
        self.assertEqual(file["entry_type"], "link")

        # 移除文件
        result = self.file_service.remove_file(file_id, delete_physical=False)
        self.assertTrue(result)

        # 验证文件记录是否已删除
        file = self.file_service.get_file_by_id(file_id)
        self.assertIsNone(file)

        # 验证原始文件是否仍然存在
        self.assertTrue(os.path.exists(self.test_file_path))


if __name__ == "__main__":
    unittest.main()
