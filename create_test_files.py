#!/usr/bin/env python3
"""
创建测试文件用于验证批量处理功能
"""

import os
import time

def create_test_files():
    """在监控目录中创建测试文件"""
    test_dir = r"D:\temp3"  # 使用已配置的监控目录
    
    if not os.path.exists(test_dir):
        print(f"监控目录不存在: {test_dir}")
        return
    
    print(f"在监控目录中创建测试文件: {test_dir}")
    
    # 创建5个测试文件
    for i in range(5):
        file_path = os.path.join(test_dir, f"batch_test_{i+1}_{int(time.time())}.txt")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(f"批量测试文件 {i+1}\n创建时间: {time.time()}\n内容: 这是用于测试批量处理功能的文件")
        print(f"创建文件: {os.path.basename(file_path)}")
        time.sleep(0.1)  # 短暂延迟，模拟文件逐个添加
    
    print("测试文件创建完成！请观察SmartVault的批量处理反应。")

if __name__ == "__main__":
    create_test_files()
