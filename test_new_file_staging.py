#!/usr/bin/env python3
"""
测试新文件是否默认进入中转文件夹
"""

import sys
import os
import tempfile
import shutil
sys.path.insert(0, os.path.abspath('.'))

from smartvault.services.file import FileService

def test_new_file_staging():
    """测试新文件是否默认进入中转文件夹"""
    print("🧪 测试新文件是否默认进入中转文件夹...")
    
    try:
        # 初始化文件服务
        file_service = FileService()
        
        # 创建临时测试文件
        temp_dir = tempfile.mkdtemp(prefix="smartvault_test_")
        test_file_path = os.path.join(temp_dir, "test_staging_new_file.txt")
        
        with open(test_file_path, 'w', encoding='utf-8') as f:
            f.write("这是一个测试文件，用于验证新文件是否默认进入中转文件夹")
        
        print(f"📁 创建测试文件: {test_file_path}")
        
        # 添加文件到文件库
        print("📥 添加文件到文件库...")
        file_id = file_service.add_file(test_file_path, mode="link")
        
        # 检查文件状态
        file_info = file_service.get_file_by_id(file_id)
        if file_info:
            staging_status = file_info.get('staging_status', 'normal')
            print(f"🔍 文件状态: {staging_status}")
            
            if staging_status == 'staging':
                print("✅ 成功！新文件默认进入中转文件夹")
                
                # 验证中转文件夹筛选
                staging_files = file_service.get_files(folder_filter_type="staging")
                staging_file_ids = [f['id'] for f in staging_files]
                
                if file_id in staging_file_ids:
                    print("✅ 中转文件夹筛选验证成功")
                else:
                    print("❌ 中转文件夹筛选验证失败")
                    return False
                
                return True
            else:
                print(f"❌ 失败！新文件状态为: {staging_status}，应该是 'staging'")
                return False
        else:
            print("❌ 无法获取文件信息")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理临时文件
        try:
            if 'temp_dir' in locals():
                shutil.rmtree(temp_dir, ignore_errors=True)
        except:
            pass

def test_folder_staging():
    """测试文件夹添加时的中转状态"""
    print("\n🧪 测试文件夹添加时的中转状态...")
    
    try:
        # 初始化文件服务
        file_service = FileService()
        
        # 创建临时测试文件夹
        temp_dir = tempfile.mkdtemp(prefix="smartvault_test_folder_")
        
        # 在文件夹中创建几个测试文件
        test_files = []
        for i in range(3):
            test_file_path = os.path.join(temp_dir, f"test_file_{i}.txt")
            with open(test_file_path, 'w', encoding='utf-8') as f:
                f.write(f"这是测试文件 {i}")
            test_files.append(test_file_path)
        
        print(f"📁 创建测试文件夹: {temp_dir}")
        print(f"📄 包含 {len(test_files)} 个文件")
        
        # 获取添加前的中转文件数量
        before_staging_files = file_service.get_files(folder_filter_type="staging")
        before_count = len(before_staging_files)
        
        # 添加文件夹到文件库
        print("📥 添加文件夹到文件库...")
        file_id = file_service.add_file(temp_dir, mode="link")
        
        # 获取添加后的中转文件数量
        after_staging_files = file_service.get_files(folder_filter_type="staging")
        after_count = len(after_staging_files)
        
        added_count = after_count - before_count
        print(f"🔍 中转文件夹中新增文件数量: {added_count}")
        
        if added_count == len(test_files):
            print("✅ 成功！文件夹中的所有文件都进入了中转文件夹")
            
            # 验证每个文件的状态
            all_staging = True
            for staging_file in after_staging_files[-added_count:]:
                if staging_file.get('staging_status') != 'staging':
                    all_staging = False
                    break
            
            if all_staging:
                print("✅ 所有新文件的状态都是 'staging'")
                return True
            else:
                print("❌ 部分文件状态不正确")
                return False
        else:
            print(f"❌ 失败！期望添加 {len(test_files)} 个文件，实际添加 {added_count} 个")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理临时文件
        try:
            if 'temp_dir' in locals():
                shutil.rmtree(temp_dir, ignore_errors=True)
        except:
            pass

if __name__ == "__main__":
    success1 = test_new_file_staging()
    success2 = test_folder_staging()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！新文件默认进入中转文件夹功能正常工作")
    else:
        print("\n❌ 部分测试失败")
    
    sys.exit(0 if (success1 and success2) else 1)
