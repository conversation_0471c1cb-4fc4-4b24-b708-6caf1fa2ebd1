"""
日志管理模块
"""

import os
import logging
from datetime import datetime
from smartvault.utils.config import get_app_data_dir


def setup_logging(level=logging.INFO):
    """设置日志
    
    Args:
        level: 日志级别
    """
    # 创建日志目录
    log_dir = os.path.join(get_app_data_dir(), "logs")
    os.makedirs(log_dir, exist_ok=True)
    
    # 日志文件路径
    log_file = os.path.join(
        log_dir,
        f"smartvault_{datetime.now().strftime('%Y%m%d')}.log"
    )
    
    # 配置日志
    logging.basicConfig(
        level=level,
        format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
        handlers=[
            logging.FileHandler(log_file, encoding="utf-8"),
            logging.StreamHandler()
        ]
    )
    
    # 设置第三方库的日志级别
    logging.getLogger("PIL").setLevel(logging.WARNING)
    logging.getLogger("watchdog").setLevel(logging.WARNING)
    
    return logging.getLogger("smartvault")
