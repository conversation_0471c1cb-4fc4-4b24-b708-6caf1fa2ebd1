#!/usr/bin/env python3
"""
测试退出优化效果
"""

import sys
import os
import time
import subprocess
import signal

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def test_exit_speed():
    """测试退出速度"""
    print("🚀 测试SmartVault退出速度优化...")
    
    results = []
    
    for test_num in range(3):
        print(f"\n📊 测试 {test_num + 1}/3")
        
        try:
            # 启动应用程序
            print("  🔄 启动应用程序...")
            start_time = time.time()
            
            process = subprocess.Popen(
                [sys.executable, "run.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=os.path.dirname(os.path.abspath(__file__))
            )
            
            # 等待应用程序完全启动
            startup_timeout = 30
            startup_success = False
            
            while time.time() - start_time < startup_timeout:
                if process.poll() is not None:
                    # 进程已退出
                    break
                
                # 检查是否有启动完成的标志
                try:
                    stdout, stderr = process.communicate(timeout=0.1)
                    if "延后启动任务完成" in stdout or "延后启动任务完成" in stderr:
                        startup_success = True
                        break
                except subprocess.TimeoutExpired:
                    pass
                
                time.sleep(0.5)
            
            if not startup_success:
                print("  ⚠️ 启动检测超时，继续测试...")
            else:
                startup_time = time.time() - start_time
                print(f"  ✅ 应用程序启动完成 (耗时: {startup_time:.2f}秒)")
            
            # 让应用程序运行一段时间
            print("  ⏳ 应用程序运行中...")
            time.sleep(3)
            
            # 测试退出速度
            print("  🛑 开始退出测试...")
            exit_start_time = time.time()
            
            # 发送关闭信号
            if os.name == 'nt':  # Windows
                process.terminate()
            else:  # Unix/Linux
                process.send_signal(signal.SIGTERM)
            
            # 等待进程退出
            try:
                process.wait(timeout=10)
                exit_time = time.time() - exit_start_time
                print(f"  ✅ 应用程序退出完成 (耗时: {exit_time:.2f}秒)")
                
                results.append({
                    'test_num': test_num + 1,
                    'success': True,
                    'exit_time': exit_time,
                    'error': None
                })
                
            except subprocess.TimeoutExpired:
                print("  ❌ 退出超时，强制终止...")
                process.kill()
                process.wait()
                
                results.append({
                    'test_num': test_num + 1,
                    'success': False,
                    'exit_time': 10.0,  # 超时时间
                    'error': "退出超时"
                })
            
        except Exception as e:
            print(f"  ❌ 测试失败: {e}")
            results.append({
                'test_num': test_num + 1,
                'success': False,
                'exit_time': None,
                'error': str(e)
            })
            
            # 确保进程被清理
            try:
                if process.poll() is None:
                    process.kill()
                    process.wait()
            except:
                pass
    
    return results


def analyze_results(results):
    """分析测试结果"""
    print("\n" + "=" * 50)
    print("📊 退出速度测试结果分析")
    print("=" * 50)
    
    successful_tests = [r for r in results if r['success']]
    failed_tests = [r for r in results if not r['success']]
    
    print(f"✅ 成功测试: {len(successful_tests)}/{len(results)}")
    print(f"❌ 失败测试: {len(failed_tests)}/{len(results)}")
    
    if successful_tests:
        exit_times = [r['exit_time'] for r in successful_tests]
        avg_exit_time = sum(exit_times) / len(exit_times)
        min_exit_time = min(exit_times)
        max_exit_time = max(exit_times)
        
        print(f"\n⏱️ 退出时间统计:")
        print(f"  平均退出时间: {avg_exit_time:.2f}秒")
        print(f"  最快退出时间: {min_exit_time:.2f}秒")
        print(f"  最慢退出时间: {max_exit_time:.2f}秒")
        
        # 评估退出速度
        if avg_exit_time <= 1.0:
            print(f"  🎉 退出速度: 优秀 (≤1秒)")
        elif avg_exit_time <= 2.0:
            print(f"  ✅ 退出速度: 良好 (≤2秒)")
        elif avg_exit_time <= 3.0:
            print(f"  ⚠️ 退出速度: 一般 (≤3秒)")
        else:
            print(f"  ❌ 退出速度: 需要优化 (>3秒)")
    
    if failed_tests:
        print(f"\n❌ 失败原因:")
        for test in failed_tests:
            print(f"  测试 {test['test_num']}: {test['error']}")
    
    print("\n💡 优化建议:")
    if any(r['exit_time'] and r['exit_time'] > 2.0 for r in successful_tests):
        print("  - 进一步减少线程等待时间")
        print("  - 跳过非关键的清理操作")
        print("  - 使用异步清理")
    else:
        print("  - 退出速度已经很好，无需进一步优化")


def main():
    """主测试函数"""
    print("🔧 SmartVault退出速度优化测试")
    print("=" * 50)
    print("📋 测试说明:")
    print("  1. 启动SmartVault应用程序")
    print("  2. 等待应用程序完全加载")
    print("  3. 发送退出信号并测量退出时间")
    print("  4. 重复测试3次取平均值")
    print()
    
    # 运行测试
    results = test_exit_speed()
    
    # 分析结果
    analyze_results(results)
    
    return len([r for r in results if r['success']]) == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
