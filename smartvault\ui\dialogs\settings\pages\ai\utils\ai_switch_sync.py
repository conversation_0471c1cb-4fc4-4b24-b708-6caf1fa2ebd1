"""
AI开关同步工具

确保不同设置页面中的AI开关状态保持同步
预期代码长度: < 150行
当前代码长度: 120行 ✅
"""

from typing import Dict, List, Callable
from PySide6.QtCore import QObject, Signal


class AISwitchSyncManager(QObject):
    """AI开关同步管理器"""
    
    # 信号定义
    ai_status_changed = Signal(bool)  # AI总开关状态变化
    ai_config_changed = Signal(dict)  # AI配置变化
    
    def __init__(self):
        super().__init__()
        self._registered_switches = []  # 注册的开关列表
        self._sync_enabled = True  # 同步是否启用
        
    def register_switch(self, switch_widget, switch_type: str, callback: Callable = None):
        """注册AI开关控件
        
        Args:
            switch_widget: 开关控件（QCheckBox等）
            switch_type: 开关类型 ('main', 'auto_tags', 'feature')
            callback: 状态变化回调函数
        """
        switch_info = {
            'widget': switch_widget,
            'type': switch_type,
            'callback': callback
        }
        
        self._registered_switches.append(switch_info)
        
        # 连接开关的状态变化信号
        if hasattr(switch_widget, 'toggled'):
            switch_widget.toggled.connect(
                lambda checked: self._on_switch_changed(switch_info, checked)
            )
    
    def unregister_switch(self, switch_widget):
        """注销AI开关控件
        
        Args:
            switch_widget: 要注销的开关控件
        """
        self._registered_switches = [
            switch for switch in self._registered_switches
            if switch['widget'] != switch_widget
        ]
    
    def sync_all_switches(self, master_status: bool, exclude_widget=None):
        """同步所有开关状态
        
        Args:
            master_status: 主开关状态
            exclude_widget: 排除的控件（避免循环触发）
        """
        if not self._sync_enabled:
            return
        
        # 临时禁用同步，避免循环触发
        self._sync_enabled = False
        
        try:
            for switch_info in self._registered_switches:
                widget = switch_info['widget']
                switch_type = switch_info['type']
                
                # 跳过排除的控件
                if widget == exclude_widget:
                    continue
                
                # 根据开关类型决定同步策略
                if switch_type == 'main':
                    # 主开关直接同步
                    if hasattr(widget, 'setChecked'):
                        widget.setChecked(master_status)
                elif switch_type == 'auto_tags':
                    # 自动标签开关：只有在主开关启用时才能启用
                    if hasattr(widget, 'setChecked'):
                        if not master_status:
                            widget.setChecked(False)
                        widget.setEnabled(master_status)
                elif switch_type == 'feature':
                    # 功能开关：跟随主开关状态
                    if hasattr(widget, 'setEnabled'):
                        widget.setEnabled(master_status)
                
                # 调用回调函数
                if switch_info['callback']:
                    switch_info['callback'](master_status)
        
        finally:
            # 重新启用同步
            self._sync_enabled = True
    
    def get_master_status(self) -> bool:
        """获取主开关状态
        
        Returns:
            bool: 主开关状态
        """
        # 查找主开关
        for switch_info in self._registered_switches:
            if switch_info['type'] == 'main':
                widget = switch_info['widget']
                if hasattr(widget, 'isChecked'):
                    return widget.isChecked()
        
        # 如果没有找到主开关，从配置中获取
        from smartvault.utils.config import get_ai_status
        return get_ai_status()
    
    def update_from_config(self, config: Dict):
        """从配置更新所有开关状态
        
        Args:
            config: 配置字典
        """
        # 获取AI状态
        advanced_config = config.get('advanced', {})
        ai_config = config.get('ai', {})
        auto_tags_config = config.get('auto_tags', {})
        
        master_status = advanced_config.get('enable_ai_features', False)
        
        # 临时禁用同步
        self._sync_enabled = False
        
        try:
            for switch_info in self._registered_switches:
                widget = switch_info['widget']
                switch_type = switch_info['type']
                
                if switch_type == 'main':
                    # 主开关
                    if hasattr(widget, 'setChecked'):
                        widget.setChecked(master_status)
                elif switch_type == 'auto_tags':
                    # 自动标签开关
                    auto_tags_enabled = auto_tags_config.get('enable_ai', False)
                    if hasattr(widget, 'setChecked'):
                        widget.setChecked(auto_tags_enabled and master_status)
                        widget.setEnabled(master_status)
                elif switch_type == 'feature':
                    # 功能开关保持原有状态，但受主开关控制
                    if hasattr(widget, 'setEnabled'):
                        widget.setEnabled(master_status)
        
        finally:
            # 重新启用同步
            self._sync_enabled = True
    
    def _on_switch_changed(self, switch_info: Dict, checked: bool):
        """开关状态变化处理
        
        Args:
            switch_info: 开关信息
            checked: 新的状态
        """
        if not self._sync_enabled:
            return
        
        switch_type = switch_info['type']
        widget = switch_info['widget']
        
        if switch_type == 'main':
            # 主开关变化，同步所有其他开关
            self.sync_all_switches(checked, exclude_widget=widget)
            
            # 保存到配置
            from smartvault.utils.config import save_ai_status
            save_ai_status(checked)
            
            # 发送信号
            self.ai_status_changed.emit(checked)
        
        elif switch_type == 'auto_tags':
            # 自动标签开关变化，只保存自动标签配置
            from smartvault.utils.config import load_config, save_config
            config = load_config()
            
            if 'auto_tags' not in config:
                config['auto_tags'] = {}
            config['auto_tags']['enable_ai'] = checked
            
            save_config(config)
        
        # 调用回调函数
        if switch_info['callback']:
            switch_info['callback'](checked)


# 全局同步管理器实例
ai_switch_sync_manager = AISwitchSyncManager()
