#!/usr/bin/env python3
"""
SmartVault B017 功能验证脚本
验证第二阶段所有功能的可用性
"""

import sys
import os
import time
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from smartvault.ui.main_window import MainWindow


class B017FeatureValidator:
    """B017功能验证器"""

    def __init__(self):
        self.test_results = []
        self.main_window = None

    def log_test(self, name, success, details=""):
        """记录测试结果"""
        result = {
            "name": name,
            "success": success,
            "details": details
        }
        self.test_results.append(result)

        status = "通过" if success else "失败"
        print(f"  {status}: {name}")
        if details:
            print(f"    详情: {details}")

    def test_main_window_creation(self):
        """测试主窗口创建"""
        print("\n=== 主窗口创建测试 ===")

        try:
            self.main_window = MainWindow()
            self.log_test("主窗口创建", True)

            # 检查主要组件
            has_file_view = hasattr(self.main_window, 'file_view')
            self.log_test("文件视图容器", has_file_view)

            has_toolbar = hasattr(self.main_window, 'toolbar_manager')
            self.log_test("工具栏管理器", has_toolbar)

            has_menu = hasattr(self.main_window, 'menu_manager')
            self.log_test("菜单管理器", has_menu)

            return True

        except Exception as e:
            self.log_test("主窗口创建", False, str(e))
            return False

    def test_tag_system_availability(self):
        """测试标签系统可用性"""
        print("\n=== 标签系统可用性测试 ===")

        try:
            # 检查标签服务
            from smartvault.services.tag_service import TagService
            tag_service = TagService()
            self.log_test("标签服务导入", True)

            # 检查标签管理对话框
            from smartvault.ui.dialogs.tag_management_dialog import TagManagementDialog
            self.log_test("标签管理对话框导入", True)

            # 检查标签导航面板
            from smartvault.ui.components.tag_navigation_panel import TagNavigationPanel
            self.log_test("标签导航面板导入", True)

            # 检查主窗口是否有标签相关功能
            if self.main_window:
                has_navigation_panel = hasattr(self.main_window, 'navigation_panel')
                self.log_test("导航面板（包含标签）", has_navigation_panel)

                # 检查菜单中是否有标签管理
                if hasattr(self.main_window, 'menu_manager'):
                    # 检查标签管理方法是否存在
                    has_tag_method = hasattr(self.main_window, 'on_tag_management')
                    self.log_test("标签管理功能", has_tag_method)

        except Exception as e:
            self.log_test("标签系统可用性", False, str(e))

    def test_file_monitor_availability(self):
        """测试文件监控功能可用性"""
        print("\n=== 文件监控功能可用性测试 ===")

        try:
            # 检查文件监控服务
            from smartvault.services.file_monitor_service import FileMonitorService
            monitor_service = FileMonitorService()
            self.log_test("文件监控服务导入", True)

            # 检查监控设置（集成在设置对话框中）
            from smartvault.ui.dialogs.settings_dialog import SettingsDialog
            # 检查设置对话框是否包含监控功能
            settings_dialog = SettingsDialog()
            has_monitor_tab = hasattr(settings_dialog, 'monitor_tab')
            self.log_test("监控设置功能", has_monitor_tab)

            # 检查主窗口是否有监控相关功能（集成在设置中）
            if self.main_window:
                has_settings_method = hasattr(self.main_window, 'on_settings')
                self.log_test("设置功能（包含监控）", has_settings_method)

        except Exception as e:
            self.log_test("文件监控功能可用性", False, str(e))

    def test_advanced_search_availability(self):
        """测试高级搜索功能可用性"""
        print("\n=== 高级搜索功能可用性测试 ===")

        try:
            # 检查搜索服务
            from smartvault.services.search_service import SearchService
            search_service = SearchService()
            self.log_test("搜索服务导入", True)

            # 检查高级搜索对话框
            from smartvault.ui.dialogs.advanced_search_dialog import AdvancedSearchDialog
            self.log_test("高级搜索对话框导入", True)

            # 检查主窗口是否有搜索相关功能
            if self.main_window:
                # 检查文件视图是否有搜索功能
                has_file_view = hasattr(self.main_window, 'file_view')
                self.log_test("文件视图（包含搜索）", has_file_view)

                # 检查搜索混入类功能
                has_search_mixin = hasattr(self.main_window, 'on_search')
                self.log_test("搜索功能混入", has_search_mixin)

        except Exception as e:
            self.log_test("高级搜索功能可用性", False, str(e))

    def test_view_modes_availability(self):
        """测试多视图模式可用性"""
        print("\n=== 多视图模式可用性测试 ===")

        try:
            # 检查视图组件
            from smartvault.ui.views.file_table_view import FileTableView
            self.log_test("表格视图导入", True)

            from smartvault.ui.views.file_grid_view import FileGridView
            self.log_test("网格视图导入", True)

            from smartvault.ui.views.file_details_view import FileDetailsView
            self.log_test("详情视图导入", True)

            # 检查主窗口是否有视图切换功能
            if self.main_window:
                # 检查文件视图容器
                has_file_view = hasattr(self.main_window, 'file_view')
                self.log_test("文件视图容器", has_file_view)

                # 检查工具栏管理器
                has_toolbar = hasattr(self.main_window, 'toolbar_manager')
                self.log_test("工具栏管理器", has_toolbar)

        except Exception as e:
            self.log_test("多视图模式可用性", False, str(e))

    def test_settings_availability(self):
        """测试设置功能可用性"""
        print("\n=== 设置功能可用性测试 ===")

        try:
            # 检查设置对话框
            from smartvault.ui.dialogs.settings_dialog import SettingsDialog
            self.log_test("设置对话框导入", True)

            # 检查配置管理
            from smartvault.utils.config import load_config, save_config
            config = load_config()
            self.log_test("配置加载", config is not None, f"配置项数: {len(config) if config else 0}")

            # 检查主窗口是否有设置功能
            if self.main_window:
                has_settings_method = hasattr(self.main_window, 'on_settings')
                self.log_test("设置功能", has_settings_method)

        except Exception as e:
            self.log_test("设置功能可用性", False, str(e))

    def run_all_tests(self):
        """运行所有功能验证测试"""
        print("SmartVault B017 功能验证测试")
        print("=" * 50)

        # 测试主窗口创建
        if not self.test_main_window_creation():
            print("主窗口创建失败，跳过后续测试")
            return False

        # 测试各功能模块可用性
        self.test_tag_system_availability()
        self.test_file_monitor_availability()
        self.test_advanced_search_availability()
        self.test_view_modes_availability()
        self.test_settings_availability()

        # 输出测试结果
        print("\n" + "=" * 50)
        print("功能验证结果汇总")
        print("=" * 50)

        passed = sum(1 for result in self.test_results if result["success"])
        total = len(self.test_results)

        print(f"总测试数: {total}")
        print(f"通过数: {passed}")
        print(f"失败数: {total - passed}")
        print(f"通过率: {passed/total*100:.1f}%")

        if passed == total:
            print("\n所有功能验证通过！第二阶段功能完整可用。")
            return True
        else:
            print("\n部分功能验证失败，需要进一步检查。")
            print("\n失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  失败 {result['name']}: {result['details']}")
            return False


def main():
    """主函数"""
    app = QApplication(sys.argv)

    try:
        validator = B017FeatureValidator()
        success = validator.run_all_tests()

        # 自动退出
        QTimer.singleShot(1000, app.quit)
        app.exec()

        return 0 if success else 1

    except Exception as e:
        print(f"功能验证过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
