import sys
import os
sys.path.append('.')

try:
    from PySide6.QtWidgets import QApplication
    from smartvault.ui.widgets.clipboard_floating_widget import ClipboardFloatingWidget
    
    print("测试浮动窗口修复...")
    
    # 创建Qt应用
    app = QApplication(sys.argv)
    
    # 创建浮动窗口
    widget = ClipboardFloatingWidget()
    
    print(f"窗口固定大小: {widget.size().width()}x{widget.size().height()}")
    print(f"内容标签固定高度: {widget.content_label.height()}")
    print(f"内容标签自动换行: {widget.content_label.wordWrap()}")
    
    # 测试文本截断功能
    test_cases = [
        "短文件名.txt",
        "这是一个非常长的文件名测试用例.txt",
        "测试1文件_- Ab古아み.txt",
        "非常非常非常非常非常非常长的文件名测试用例文档.docx"
    ]
    
    print("\n=== 测试文本截断功能 ===")
    for text in test_cases:
        truncated = widget._truncate_text(text)
        print(f"原文: {text}")
        print(f"截断: {truncated}")
        print(f"长度: {len(text)} -> {len(truncated)}")
        print("-" * 50)
    
    # 测试显示功能
    print("\n=== 测试显示功能 ===")
    
    # 测试文件重复显示
    file_duplicate_info = {
        'type': 'file',
        'source_name': 'test.txt',
        'duplicates': [{
            'id': '1',
            'name': '这是一个非常长的文件名测试用例，用来验证截断功能是否正常工作.txt'
        }]
    }
    
    print("测试文件重复显示...")
    widget.show_duplicate(file_duplicate_info)
    print(f"显示内容: '{widget.content_label.text()}'")
    
    # 测试文件名重复显示
    filename_duplicate_info = {
        'type': 'filename',
        'duplicates': [{
            'id': '2',
            'name': '测试1文件_- Ab古아み非常长的扩展名称.txt'
        }]
    }
    
    print("\n测试文件名重复显示...")
    widget.show_duplicate(filename_duplicate_info)
    print(f"显示内容: '{widget.content_label.text()}'")
    
    # 测试多个重复文件
    multiple_duplicate_info = {
        'type': 'file',
        'source_name': 'test.txt',
        'duplicates': [
            {'id': '1', 'name': 'file1.txt'},
            {'id': '2', 'name': 'file2.txt'},
            {'id': '3', 'name': 'file3.txt'}
        ]
    }
    
    print("\n测试多个重复文件显示...")
    widget.show_duplicate(multiple_duplicate_info)
    print(f"显示内容: '{widget.content_label.text()}'")
    
    print("\n=== 修复总结 ===")
    print("✅ 1. 简化显示内容为单行")
    print("   - 文件重复：只显示库中文件名")
    print("   - 文件名重复：只显示库中文件名")
    print("   - 数据库错误：只显示错误信息")
    print()
    print("✅ 2. 修复窗口几何设置冲突")
    print("   - 禁用内容标签自动换行")
    print("   - 设置内容标签固定高度")
    print("   - 添加文本截断功能")
    print()
    print("✅ 3. 防止显示异常")
    print("   - 长文件名自动截断并添加省略号")
    print("   - 窗口大小保持固定，不会因内容变化而改变")
    
    # 不启动事件循环，只测试功能
    widget.close()
    
except Exception as e:
    print(f"测试失败: {e}")
    import traceback
    traceback.print_exc()
