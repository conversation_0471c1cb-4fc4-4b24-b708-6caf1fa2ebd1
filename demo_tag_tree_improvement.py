#!/usr/bin/env python3
"""
演示标签管理对话框的树形结构改进
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel
from smartvault.ui.dialogs.tag_management_dialog import TagManagementDialog

class TagTreeDemo(QMainWindow):
    """标签树形结构演示窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("标签管理对话框 - 树形结构改进演示")
        self.setGeometry(100, 100, 400, 300)
        
        # 中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 布局
        layout = QVBoxLayout(central_widget)
        
        # 说明文本
        info_label = QLabel("""
🎉 标签管理对话框已改进为树形结构显示！

✨ 改进内容：
• 将原来的平铺列表改为树形结构
• 父子标签关系清晰可见
• 支持展开/折叠操作
• 保持所有原有功能（右键菜单、编辑、删除等）
• 与导航栏标签树保持一致的用户体验

📋 测试要点：
• 查看标签的层级关系
• 尝试展开/折叠标签
• 测试右键菜单功能
• 验证标签编辑功能
        """)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("""
            QLabel {
                padding: 15px;
                background-color: #f0f8ff;
                border: 1px solid #ddd;
                border-radius: 8px;
                font-size: 12px;
                line-height: 1.4;
            }
        """)
        layout.addWidget(info_label)
        
        # 打开按钮
        open_btn = QPushButton("打开标签管理对话框")
        open_btn.clicked.connect(self.open_tag_dialog)
        open_btn.setStyleSheet("""
            QPushButton {
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
        """)
        layout.addWidget(open_btn)
        
        layout.addStretch()
    
    def open_tag_dialog(self):
        """打开标签管理对话框"""
        dialog = TagManagementDialog(self)
        dialog.exec()

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建演示窗口
    demo = TagTreeDemo()
    demo.show()
    
    print("🎉 标签管理对话框树形结构改进演示")
    print("=" * 50)
    print("✨ 改进亮点：")
    print("  • 树形结构显示，父子关系清晰")
    print("  • 支持展开/折叠操作")
    print("  • 保持原有的所有功能")
    print("  • 与导航栏保持一致的用户体验")
    print("=" * 50)
    print("📋 请点击按钮打开标签管理对话框进行测试")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
