"""
代码转换工具 - 统一的文件处理工具
"""

import os
import re


class CodeConverter:
    """代码转换器，提供各种代码转换功能"""
    
    def __init__(self):
        """初始化转换器"""
        self.processed_files = []
        
    def convert_pyqt_to_pyside(self, file_path):
        """将 PyQt6 转换为 PySide6
        
        Args:
            file_path: 文件路径
        """
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换导入语句
        content = content.replace('from PyQt6', 'from PySide6')
        content = content.replace('import PyQt6', 'import PySide6')
        
        # 替换其他引用
        content = content.replace('PyQt6', 'PySide6')
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        self.processed_files.append(file_path)
        print(f"PyQt6 -> PySide6: {file_path}")
    
    def fix_pyside_signals(self, file_path):
        """修复 PySide6 信号相关问题
        
        Args:
            file_path: 文件路径
        """
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换信号名称
        content = content.replace('pyqtSignal', 'Signal')
        
        # 替换 exec() 方法
        content = re.sub(r'app\.exec\(\)', 'app.exec()', content)
        
        # 替换各种枚举
        replacements = [
            ('StandardButton.', ''),
            ('SelectionMode.', ''),
            ('ItemDataRole.', ''),
            ('ContextMenuPolicy.', ''),
            ('Orientation.', ''),
            ('DialogCode.', ''),
        ]
        
        for old, new in replacements:
            content = content.replace(old, new)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        self.processed_files.append(file_path)
        print(f"PySide6 信号修复: {file_path}")
    
    def process_directory(self, directory, operations=None):
        """处理目录中的所有 Python 文件
        
        Args:
            directory: 目录路径
            operations: 要执行的操作列表，如 ['pyqt_to_pyside', 'fix_signals']
        """
        if operations is None:
            operations = ['pyqt_to_pyside', 'fix_signals']
        
        for root, dirs, files in os.walk(directory):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    
                    if 'pyqt_to_pyside' in operations:
                        self.convert_pyqt_to_pyside(file_path)
                    
                    if 'fix_signals' in operations:
                        self.fix_pyside_signals(file_path)
    
    def get_summary(self):
        """获取处理摘要
        
        Returns:
            dict: 处理摘要
        """
        return {
            'total_files': len(set(self.processed_files)),
            'processed_files': list(set(self.processed_files))
        }


def main():
    """主函数"""
    converter = CodeConverter()
    
    # 处理 smartvault 目录
    if os.path.exists('smartvault'):
        converter.process_directory('smartvault')
    
    # 处理 tests 目录
    if os.path.exists('tests'):
        converter.process_directory('tests')
    
    # 显示摘要
    summary = converter.get_summary()
    print(f"\n转换完成！共处理 {summary['total_files']} 个文件")


if __name__ == "__main__":
    main()
