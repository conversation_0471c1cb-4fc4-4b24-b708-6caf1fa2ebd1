#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
文件监控功能行为测试 - 定义文件监控系统的预期行为

这个测试文件定义了文件监控系统应该具备的核心行为，
作为B006-B008任务的指导和验收标准。
"""

import os
import sys
import tempfile
import shutil
import time
import pytest
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from smartvault.services.file_monitor_service import FileMonitorService
from smartvault.services.file import FileService
from smartvault.data.database import Database


class TestFileMonitorBehavior:
    """文件监控功能行为测试类"""

    def setup_method(self):
        """测试前准备"""
        # 创建临时数据库
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = os.path.join(self.temp_dir, "test.db")
        self.db = Database(self.db_path)

        # 创建服务实例
        self.monitor_service = FileMonitorService()
        self.monitor_service._db = self.db  # 注入测试数据库

        self.file_service = FileService()
        self.file_service._db = self.db  # 注入测试数据库

        # 创建测试监控目录
        self.monitor_dir = os.path.join(self.temp_dir, "monitor_test")
        os.makedirs(self.monitor_dir, exist_ok=True)

        # 记录监控事件
        self.monitor_events = []

    def teardown_method(self):
        """测试后清理"""
        # 停止监控
        if hasattr(self, 'monitor_service'):
            self.monitor_service.stop_all_monitoring()

        if hasattr(self, 'db') and self.db:
            self.db.close()
        if hasattr(self, 'temp_dir') and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

    def test_add_monitor_folder(self):
        """测试添加监控文件夹

        行为期望：
        1. 用户可以添加文件夹到监控列表
        2. 监控配置正确保存到数据库
        3. 可以设置监控选项（入库模式、文件类型过滤等）
        """
        # 添加监控文件夹
        monitor_id = self.monitor_service.add_monitor_folder(
            folder_path=self.monitor_dir,
            entry_mode="copy",  # 复制模式
            file_patterns=["*.txt", "*.pdf"],  # 只监控txt和pdf文件
            auto_add=True,  # 自动添加到库
            recursive=True  # 递归监控子文件夹
        )

        # 验证监控配置
        assert monitor_id is not None
        assert len(monitor_id) > 0

        # 获取监控配置
        monitor_config = self.monitor_service.get_monitor_config(monitor_id)
        assert monitor_config is not None
        assert monitor_config["folder_path"] == self.monitor_dir
        assert monitor_config["entry_mode"] == "copy"
        assert monitor_config["auto_add"] is True
        assert monitor_config["recursive"] is True

        # 验证文件模式
        patterns = monitor_config["file_patterns"]
        assert "*.txt" in patterns
        assert "*.pdf" in patterns

    def test_start_stop_monitoring(self):
        """测试启动和停止监控

        行为期望：
        1. 可以启动指定文件夹的监控
        2. 可以停止指定文件夹的监控
        3. 可以获取监控状态
        4. 可以一次性停止所有监控
        """
        # 添加监控文件夹
        monitor_id = self.monitor_service.add_monitor_folder(
            folder_path=self.monitor_dir,
            entry_mode="link",
            auto_add=True
        )

        # 启动监控
        result = self.monitor_service.start_monitoring(monitor_id)
        assert result is True

        # 检查监控状态
        status = self.monitor_service.get_monitor_status(monitor_id)
        assert status["is_active"] is True
        assert status["folder_path"] == self.monitor_dir

        # 停止监控
        result = self.monitor_service.stop_monitoring(monitor_id)
        assert result is True

        # 检查监控状态
        status = self.monitor_service.get_monitor_status(monitor_id)
        assert status["is_active"] is False

    def test_file_creation_detection(self):
        """测试文件创建检测

        行为期望：
        1. 监控文件夹中创建新文件时能够检测到
        2. 根据配置自动添加文件到库
        3. 生成监控事件记录
        4. 支持文件类型过滤
        """
        # 设置事件回调
        def on_file_event(event_type, file_path, monitor_id):
            self.monitor_events.append({
                "type": event_type,
                "path": file_path,
                "monitor_id": monitor_id,
                "timestamp": datetime.now()
            })

        # 添加监控文件夹
        monitor_id = self.monitor_service.add_monitor_folder(
            folder_path=self.monitor_dir,
            entry_mode="link",
            file_patterns=["*.txt"],  # 只监控txt文件
            auto_add=True
        )

        # 设置事件回调
        self.monitor_service.set_event_callback(on_file_event)

        # 启动监控
        self.monitor_service.start_monitoring(monitor_id)

        # 创建测试文件
        test_file = os.path.join(self.monitor_dir, "test_file.txt")
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("测试文件内容")

        # 等待文件系统事件处理
        time.sleep(0.5)

        # 验证事件检测
        assert len(self.monitor_events) > 0

        # 查找文件创建事件
        create_events = [e for e in self.monitor_events if e["type"] == "created"]
        assert len(create_events) > 0

        create_event = create_events[0]
        assert create_event["path"] == test_file
        assert create_event["monitor_id"] == monitor_id

        # 验证文件自动添加到库
        files = self.file_service.get_files()
        added_files = [f for f in files if f["name"] == "test_file.txt"]
        assert len(added_files) == 1

        added_file = added_files[0]
        assert added_file["entry_type"] == "link"
        assert added_file["original_path"] == test_file

    def test_file_type_filtering(self):
        """测试文件类型过滤

        行为期望：
        1. 只监控指定类型的文件
        2. 忽略不匹配的文件类型
        3. 支持多种文件模式匹配
        """
        # 设置事件回调
        def on_file_event(event_type, file_path, monitor_id):
            self.monitor_events.append({
                "type": event_type,
                "path": file_path,
                "monitor_id": monitor_id
            })

        # 添加监控文件夹（只监控txt和pdf文件）
        monitor_id = self.monitor_service.add_monitor_folder(
            folder_path=self.monitor_dir,
            entry_mode="link",
            file_patterns=["*.txt", "*.pdf"],
            auto_add=True
        )

        self.monitor_service.set_event_callback(on_file_event)
        self.monitor_service.start_monitoring(monitor_id)

        # 创建不同类型的文件
        txt_file = os.path.join(self.monitor_dir, "document.txt")
        pdf_file = os.path.join(self.monitor_dir, "report.pdf")
        jpg_file = os.path.join(self.monitor_dir, "image.jpg")  # 应该被忽略

        with open(txt_file, 'w', encoding='utf-8') as f:
            f.write("文本文档")
        with open(pdf_file, 'wb') as f:
            f.write(b"PDF content")
        with open(jpg_file, 'wb') as f:
            f.write(b"Image content")

        # 等待文件系统事件处理
        time.sleep(0.5)

        # 验证只有txt和pdf文件被处理
        processed_files = [e["path"] for e in self.monitor_events if e["type"] == "created"]

        assert txt_file in processed_files
        assert pdf_file in processed_files
        assert jpg_file not in processed_files  # jpg文件应该被忽略

        # 验证数据库中的文件
        files = self.file_service.get_files()
        file_names = [f["name"] for f in files]

        assert "document.txt" in file_names
        assert "report.pdf" in file_names
        assert "image.jpg" not in file_names

    def test_recursive_monitoring(self):
        """测试递归监控

        行为期望：
        1. 启用递归监控时，监控所有子文件夹
        2. 禁用递归监控时，只监控根文件夹
        3. 子文件夹中的文件变化能够正确检测
        """
        # 创建子文件夹
        sub_dir = os.path.join(self.monitor_dir, "subfolder")
        os.makedirs(sub_dir, exist_ok=True)

        # 设置事件回调
        def on_file_event(event_type, file_path, monitor_id):
            self.monitor_events.append({
                "type": event_type,
                "path": file_path,
                "monitor_id": monitor_id
            })

        # 测试递归监控
        monitor_id = self.monitor_service.add_monitor_folder(
            folder_path=self.monitor_dir,
            entry_mode="link",
            recursive=True,  # 启用递归监控
            auto_add=True
        )

        self.monitor_service.set_event_callback(on_file_event)
        self.monitor_service.start_monitoring(monitor_id)

        # 在子文件夹中创建文件
        sub_file = os.path.join(sub_dir, "sub_file.txt")
        with open(sub_file, 'w', encoding='utf-8') as f:
            f.write("子文件夹中的文件")

        # 等待文件系统事件处理
        time.sleep(0.5)

        # 验证子文件夹中的文件被检测到
        sub_file_events = [e for e in self.monitor_events if e["path"] == sub_file]
        assert len(sub_file_events) > 0

        # 验证文件被添加到库
        files = self.file_service.get_files()
        sub_files = [f for f in files if f["name"] == "sub_file.txt"]
        assert len(sub_files) == 1

    def test_monitor_management(self):
        """测试监控管理功能

        行为期望：
        1. 可以获取所有监控配置列表
        2. 可以修改监控配置
        3. 可以删除监控配置
        4. 可以获取监控统计信息
        """
        # 添加多个监控文件夹
        monitor1_id = self.monitor_service.add_monitor_folder(
            folder_path=self.monitor_dir,
            entry_mode="link",
            auto_add=True
        )

        monitor2_dir = os.path.join(self.temp_dir, "monitor2")
        os.makedirs(monitor2_dir, exist_ok=True)
        monitor2_id = self.monitor_service.add_monitor_folder(
            folder_path=monitor2_dir,
            entry_mode="copy",
            auto_add=False
        )

        # 获取所有监控配置
        all_monitors = self.monitor_service.get_all_monitors()
        assert len(all_monitors) == 2

        monitor_ids = [m["id"] for m in all_monitors]
        assert monitor1_id in monitor_ids
        assert monitor2_id in monitor_ids

        # 修改监控配置
        success = self.monitor_service.update_monitor_config(
            monitor1_id,
            entry_mode="copy",  # 改为复制模式
            auto_add=False      # 改为手动添加
        )
        assert success is True

        # 验证配置修改
        updated_config = self.monitor_service.get_monitor_config(monitor1_id)
        assert updated_config["entry_mode"] == "copy"
        assert updated_config["auto_add"] is False

        # 删除监控配置
        success = self.monitor_service.remove_monitor_folder(monitor2_id)
        assert success is True

        # 验证删除
        remaining_monitors = self.monitor_service.get_all_monitors()
        assert len(remaining_monitors) == 1
        assert remaining_monitors[0]["id"] == monitor1_id

        # 获取监控统计
        stats = self.monitor_service.get_monitor_statistics()
        assert "total_monitors" in stats
        assert "active_monitors" in stats
        assert "total_files_added" in stats
        assert stats["total_monitors"] == 1

    def test_monitor_error_handling(self):
        """测试监控错误处理

        行为期望：
        1. 监控不存在的文件夹时返回错误
        2. 文件处理失败时记录错误日志
        3. 监控服务异常时能够恢复
        4. 提供错误信息查询接口
        """
        # 尝试监控不存在的文件夹
        non_existent_dir = os.path.join(self.temp_dir, "non_existent")

        try:
            monitor_id = self.monitor_service.add_monitor_folder(
                folder_path=non_existent_dir,
                entry_mode="link",
                auto_add=True
            )
            # 如果没有抛出异常，检查返回值
            assert monitor_id is None or monitor_id == ""
        except Exception as e:
            # 预期会抛出异常
            assert "不存在" in str(e) or "not exist" in str(e).lower()

        # 获取错误日志
        errors = self.monitor_service.get_recent_errors()
        assert isinstance(errors, list)

        # 测试监控状态检查
        invalid_monitor_id = "invalid-monitor-id"
        status = self.monitor_service.get_monitor_status(invalid_monitor_id)
        assert status is None or status.get("error") is not None


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
