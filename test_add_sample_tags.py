#!/usr/bin/env python3
"""
为测试添加示例标签数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from smartvault.data.database import Database
from smartvault.services.tag_service import TagService
from smartvault.services.file.core import FileServiceCore

def add_sample_tags():
    """添加示例标签数据"""
    print("开始添加示例标签数据...")

    # 初始化服务
    print("初始化数据库服务...")
    db_service = Database.create_from_config()
    print("初始化标签服务...")
    tag_service = TagService()
    print("初始化文件服务...")
    file_service = FileServiceCore()
    print("服务初始化完成")

    try:
        # 创建一些示例标签
        print("创建示例标签...")

        # 创建顶级标签
        work_tag_id = tag_service.create_tag("工作", "#FF5722")
        personal_tag_id = tag_service.create_tag("个人", "#2196F3")
        project_tag_id = tag_service.create_tag("项目", "#4CAF50")

        # 创建二级标签
        doc_tag_id = tag_service.create_tag("文档", "#FFC107", work_tag_id)
        code_tag_id = tag_service.create_tag("代码", "#9C27B0", work_tag_id)
        photo_tag_id = tag_service.create_tag("照片", "#E91E63", personal_tag_id)
        music_tag_id = tag_service.create_tag("音乐", "#FF9800", personal_tag_id)

        # 创建三级标签
        python_tag_id = tag_service.create_tag("Python", "#3776AB", code_tag_id)
        js_tag_id = tag_service.create_tag("JavaScript", "#F7DF1E", code_tag_id)

        print("标签创建完成")

        # 获取一些文件来添加标签
        print("获取文件列表...")
        files = file_service.get_files(limit=20)

        if not files:
            print("没有找到文件，请先添加一些文件到智能文件库")
            return

        print(f"找到 {len(files)} 个文件，开始添加标签...")

        # 为文件添加标签
        for i, file in enumerate(files):
            file_id = file['id']
            file_name = file['name'].lower()

            # 根据文件类型和名称添加相应标签
            if any(ext in file_name for ext in ['.py', '.pyw']):
                tag_service.add_tag_to_file(file_id, python_tag_id)
                tag_service.add_tag_to_file(file_id, code_tag_id)
                tag_service.add_tag_to_file(file_id, work_tag_id)
                print(f"为 {file['name']} 添加了 Python/代码/工作 标签")

            elif any(ext in file_name for ext in ['.js', '.ts', '.jsx', '.tsx']):
                tag_service.add_tag_to_file(file_id, js_tag_id)
                tag_service.add_tag_to_file(file_id, code_tag_id)
                tag_service.add_tag_to_file(file_id, work_tag_id)
                print(f"为 {file['name']} 添加了 JavaScript/代码/工作 标签")

            elif any(ext in file_name for ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']):
                tag_service.add_tag_to_file(file_id, photo_tag_id)
                tag_service.add_tag_to_file(file_id, personal_tag_id)
                print(f"为 {file['name']} 添加了 照片/个人 标签")

            elif any(ext in file_name for ext in ['.mp3', '.wav', '.flac', '.m4a']):
                tag_service.add_tag_to_file(file_id, music_tag_id)
                tag_service.add_tag_to_file(file_id, personal_tag_id)
                print(f"为 {file['name']} 添加了 音乐/个人 标签")

            elif any(ext in file_name for ext in ['.doc', '.docx', '.pdf', '.txt', '.md']):
                tag_service.add_tag_to_file(file_id, doc_tag_id)
                tag_service.add_tag_to_file(file_id, work_tag_id)
                print(f"为 {file['name']} 添加了 文档/工作 标签")

            else:
                # 为其他文件添加项目标签
                tag_service.add_tag_to_file(file_id, project_tag_id)
                print(f"为 {file['name']} 添加了 项目 标签")

        print("示例标签数据添加完成！")

        # 显示标签统计
        print("\n标签统计:")
        stats = tag_service.get_tag_statistics()
        for stat in stats:
            print(f"  {stat['name']}: {stat['file_count']} 个文件")

    except Exception as e:
        print(f"添加示例标签数据失败: {e}")
        import traceback
        traceback.print_exc()

    finally:
        if db_service:
            db_service.close()

if __name__ == "__main__":
    add_sample_tags()
