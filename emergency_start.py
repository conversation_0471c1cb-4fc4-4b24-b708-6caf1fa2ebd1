"""
紧急启动脚本 - 绕过所有检查直接启动
"""

import os
import sys
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def emergency_cleanup():
    """紧急清理"""
    print("🚨 紧急清理模式")
    
    try:
        import psutil
        current_pid = os.getpid()
        
        # 强制终止所有SmartVault进程
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.pid == current_pid:
                    continue
                    
                if proc.info['name'] and 'python' in proc.info['name'].lower():
                    cmdline = proc.info['cmdline']
                    if cmdline and any('smartvault' in arg.lower() or 'run.py' in arg.lower() for arg in cmdline):
                        print(f"  强制终止 PID: {proc.pid}")
                        proc.kill()  # 直接杀死，不等待
            except:
                continue
        
        print("✅ 进程清理完成")
        
    except ImportError:
        print("⚠️  psutil不可用，跳过进程清理")
    
    # 清理数据库锁定
    try:
        from smartvault.utils.config import load_config
        config = load_config()
        library_path = config["library_path"]
        db_path = os.path.join(library_path, "data", "smartvault.db")
        
        if os.path.exists(db_path):
            # 删除所有锁定文件
            lock_files = [
                db_path + "-wal",
                db_path + "-shm",
                db_path + "-journal"
            ]
            
            for lock_file in lock_files:
                if os.path.exists(lock_file):
                    try:
                        os.remove(lock_file)
                        print(f"  删除锁定文件: {os.path.basename(lock_file)}")
                    except:
                        pass
        
        print("✅ 数据库清理完成")
        
    except Exception as e:
        print(f"⚠️  数据库清理失败: {e}")
    
    # 等待资源释放
    time.sleep(2)


def emergency_start():
    """紧急启动"""
    print("🚀 紧急启动模式")
    
    try:
        from PySide6.QtWidgets import QApplication
        from smartvault.ui.main_window import MainWindow
        
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("SmartVault")
        app.setOrganizationName("SmartVault")
        app.setApplicationVersion("1.0.0")
        print("✅ 应用程序实例已创建")
        
        # 创建主窗口
        print("🏗️  创建主窗口...")
        window = MainWindow()
        print("✅ 主窗口已创建")
        
        # 显示窗口
        window.show()
        print("✅ 主窗口已显示")
        
        # 运行事件循环
        print("🔄 进入事件循环")
        return app.exec()
        
    except Exception as e:
        print(f"❌ 紧急启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


def main():
    """主函数"""
    print("=" * 50)
    print("🚨 SmartVault 紧急启动工具")
    print("=" * 50)
    
    # 1. 紧急清理
    emergency_cleanup()
    
    # 2. 紧急启动
    exit_code = emergency_start()
    
    print("=" * 50)
    print(f"程序退出，退出码: {exit_code}")
    
    return exit_code


if __name__ == "__main__":
    sys.exit(main())
