#!/usr/bin/env python3
"""
SmartVault B017 深度测试脚本
专门用于第二阶段功能的深度测试和问题发现
"""

import sys
import os
import time
import tempfile
import shutil
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from smartvault.services.file import FileService
from smartvault.ui.models.file_table_model import FileTableModel


class B017DeepTester:
    """B017深度测试器"""

    def __init__(self):
        self.test_results = []
        self.temp_dirs = []

    def log_test(self, name, success, details=""):
        """记录测试结果"""
        result = {
            "name": name,
            "success": success,
            "details": details
        }
        self.test_results.append(result)

        status = "通过" if success else "失败"
        print(f"  {status}: {name}")
        if not success and details:
            print(f"    详情: {details}")

    def test_basic_functionality(self):
        """测试基本功能"""
        print("\n=== 基本功能测试 ===")

        try:
            # 测试文件服务初始化
            file_service = FileService()
            self.log_test("文件服务初始化", True)

            # 测试数据库连接
            count = file_service.get_file_count()
            self.log_test("数据库连接", True, f"当前文件数: {count}")

            # 测试分页功能
            files = file_service.get_files(limit=10, offset=0)
            self.log_test("分页查询", True, f"获取到 {len(files)} 个文件")

            # 测试搜索功能
            search_results = file_service.get_files(limit=10, offset=0, search_keyword="test")
            self.log_test("搜索功能", True, f"搜索结果: {len(search_results)} 个文件")

            file_service.db.close()

        except Exception as e:
            self.log_test("基本功能测试", False, str(e))

    def test_file_operations(self):
        """测试文件操作功能"""
        print("\n=== 文件操作测试 ===")

        try:
            # 创建临时测试文件
            temp_dir = tempfile.mkdtemp()
            self.temp_dirs.append(temp_dir)

            test_file = os.path.join(temp_dir, "test_file.txt")
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write("测试文件内容")

            file_service = FileService()

            # 测试添加文件
            original_count = file_service.get_file_count()
            file_service.add_file(test_file, "link")
            new_count = file_service.get_file_count()

            self.log_test("添加文件", new_count > original_count,
                         f"文件数从 {original_count} 增加到 {new_count}")

            # 测试文件查询
            files = file_service.get_files(limit=100, offset=0)
            added_file = None
            for file_info in files:
                if file_info.get('name') == 'test_file.txt':
                    added_file = file_info
                    break

            self.log_test("文件查询", added_file is not None,
                         "能够查询到新添加的文件")

            file_service.db.close()

        except Exception as e:
            self.log_test("文件操作测试", False, str(e))

    def test_library_switching(self):
        """测试文件库切换功能"""
        print("\n=== 文件库切换测试 ===")

        try:
            # 创建临时文件库目录
            temp_library = tempfile.mkdtemp()
            self.temp_dirs.append(temp_library)

            file_service = FileService()
            original_count = file_service.get_file_count()

            # 切换到新文件库
            file_service.switch_library(temp_library)
            new_count = file_service.get_file_count()

            self.log_test("文件库切换", True,
                         f"切换后文件数: {new_count} (原: {original_count})")

            # 测试在新文件库中添加文件
            test_file = os.path.join(temp_library, "new_lib_test.txt")
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write("新文件库测试文件")

            file_service.add_file(test_file, "copy")
            final_count = file_service.get_file_count()

            self.log_test("新文件库操作", final_count > new_count,
                         f"新文件库中文件数: {final_count}")

            file_service.db.close()

        except Exception as e:
            self.log_test("文件库切换测试", False, str(e))

    def test_performance(self):
        """测试性能"""
        print("\n=== 性能测试 ===")

        try:
            file_service = FileService()

            # 测试启动时间
            start_time = time.time()
            count = file_service.get_file_count()
            startup_time = time.time() - start_time

            self.log_test("启动性能", startup_time < 3.0,
                         f"启动时间: {startup_time:.3f}s")

            # 测试分页性能
            start_time = time.time()
            files = file_service.get_files(limit=100, offset=0)
            page_time = time.time() - start_time

            self.log_test("分页性能", page_time < 1.0,
                         f"分页时间: {page_time:.3f}s")

            # 测试搜索性能
            start_time = time.time()
            search_results = file_service.get_files(limit=100, offset=0, search_keyword="test")
            search_time = time.time() - start_time

            self.log_test("搜索性能", search_time < 2.0,
                         f"搜索时间: {search_time:.3f}s")

            file_service.db.close()

        except Exception as e:
            self.log_test("性能测试", False, str(e))

    def test_ui_model(self):
        """测试UI模型"""
        print("\n=== UI模型测试 ===")

        try:
            # 测试文件表格模型
            model = FileTableModel()
            self.log_test("文件表格模型创建", True)

            # 测试模型数据设置
            test_files = [
                {
                    "id": 1,
                    "name": "test.txt",
                    "original_path": "/test/test.txt",
                    "library_path": "/lib/test.txt",
                    "size": 1024,
                    "entry_type": "link",
                    "created_at": "2024-01-01T00:00:00",
                    "modified_at": "2024-01-01T00:00:00",
                    "added_at": "2024-01-01T00:00:00"
                }
            ]
            model.setFiles(test_files)
            row_count = model.rowCount()

            self.log_test("模型数据设置", row_count > 0,
                         f"设置了 {row_count} 行数据")

            # 测试分页功能
            model.setPageSize(50)
            current_page = model.getCurrentPage()
            total_pages = model.getTotalPages()

            self.log_test("模型分页功能", True,
                         f"当前页: {current_page}, 总页数: {total_pages}")

        except Exception as e:
            self.log_test("UI模型测试", False, str(e))

    def cleanup(self):
        """清理测试环境"""
        print("\n=== 清理测试环境 ===")

        for temp_dir in self.temp_dirs:
            try:
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
                    print(f"  已清理: {temp_dir}")
            except Exception as e:
                print(f"  清理失败: {temp_dir} - {e}")

    def run_all_tests(self):
        """运行所有深度测试"""
        print("SmartVault B017 深度测试")
        print("=" * 50)

        try:
            # 运行各项测试
            self.test_basic_functionality()
            self.test_file_operations()
            self.test_library_switching()
            self.test_performance()
            self.test_ui_model()

        finally:
            # 清理环境
            self.cleanup()

        # 输出测试结果
        print("\n" + "=" * 50)
        print("测试结果汇总")
        print("=" * 50)

        passed = sum(1 for result in self.test_results if result["success"])
        total = len(self.test_results)

        print(f"总测试数: {total}")
        print(f"通过数: {passed}")
        print(f"失败数: {total - passed}")
        print(f"通过率: {passed/total*100:.1f}%")

        if passed == total:
            print("\n所有测试通过！第二阶段功能工作正常。")
            return True
        else:
            print("\n部分测试失败，需要进一步检查。")
            print("\n失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  失败 {result['name']}: {result['details']}")
            return False


def main():
    """主函数"""
    app = QApplication(sys.argv)

    try:
        tester = B017DeepTester()
        success = tester.run_all_tests()

        # 自动退出
        QTimer.singleShot(1000, app.quit)
        app.exec()

        return 0 if success else 1

    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
