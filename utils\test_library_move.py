"""
测试智能文件库移动功能
"""

import os
import sys
import shutil
import tempfile
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from smartvault.utils.config import load_config, save_config
from smartvault.data.database import Database
from smartvault.data.file_system import FileSystem
from smartvault.services.file import FileService


class LibraryMoveTester:
    """文件库移动测试器"""

    def __init__(self):
        """初始化测试器"""
        self.original_config = load_config()
        self.test_results = []

    def test_complete_move_flow(self):
        """测试完整的移动流程"""
        print("🔄 开始测试智能文件库移动完整流程...")

        # 1. 记录初始状态
        initial_state = self._capture_state("初始状态")

        # 2. 创建测试文件并添加到库中
        test_files = self._create_test_files()
        added_files = self._add_test_files(test_files)

        # 3. 记录添加文件后的状态
        after_add_state = self._capture_state("添加文件后")

        # 4. 执行移动操作
        new_library_path = self._perform_move()

        # 5. 记录移动后的状态
        after_move_state = self._capture_state("移动后")

        # 6. 验证移动结果
        self._verify_move_results(initial_state, after_add_state, after_move_state, new_library_path)

        # 7. 测试重新打开
        self._test_reopen_after_move(new_library_path)

        # 8. 清理
        self._cleanup(test_files, new_library_path)

        # 9. 生成报告
        self._generate_report()

    def _capture_state(self, stage_name):
        """捕获当前状态"""
        print(f"📸 捕获状态: {stage_name}")

        try:
            config = load_config()
            file_service = FileService()
            files = file_service.get_files(limit=1000)

            state = {
                "stage": stage_name,
                "config_path": config["library_path"],
                "file_count": len(files),
                "files": files,
                "library_exists": os.path.exists(config["library_path"]),
                "data_dir_exists": os.path.exists(os.path.join(config["library_path"], "data")),
                "db_exists": os.path.exists(os.path.join(config["library_path"], "data", "smartvault.db"))
            }

            print(f"  配置路径: {state['config_path']}")
            print(f"  文件数量: {state['file_count']}")
            print(f"  库目录存在: {state['library_exists']}")

            return state

        except Exception as e:
            print(f"❌ 捕获状态失败: {e}")
            return {"stage": stage_name, "error": str(e)}

    def _create_test_files(self):
        """创建测试文件"""
        print("📝 创建测试文件...")

        test_files = []
        temp_dir = tempfile.mkdtemp(prefix="smartvault_test_")

        for i in range(3):
            file_path = os.path.join(temp_dir, f"test_file_{i}.txt")
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(f"测试文件 {i} 的内容\n创建时间: {time.ctime()}")
            test_files.append(file_path)

        print(f"  创建了 {len(test_files)} 个测试文件")
        return test_files

    def _add_test_files(self, test_files):
        """添加测试文件到库中"""
        print("➕ 添加测试文件到库中...")

        file_service = FileService()
        added_files = []

        for i, file_path in enumerate(test_files):
            try:
                # 交替使用不同的添加模式
                mode = "link" if i % 2 == 0 else "copy"
                file_id = file_service.add_file(file_path, mode)
                added_files.append((file_id, file_path, mode))
                print(f"  ✅ 添加文件: {os.path.basename(file_path)} ({mode})")
            except Exception as e:
                print(f"  ❌ 添加文件失败: {os.path.basename(file_path)} - {e}")

        return added_files

    def _perform_move(self):
        """执行移动操作"""
        print("🚚 执行文件库移动...")

        # 创建新的目标目录
        new_library_path = tempfile.mkdtemp(prefix="smartvault_new_")
        print(f"  目标路径: {new_library_path}")

        try:
            # 使用FileSystem进行移动
            fs = FileSystem()

            def progress_callback(current, total, message):
                print(f"  进度: {current}/{total} - {message}")

            result = fs.move_library(new_library_path, progress_callback)

            if result:
                # 更新配置
                new_config = load_config()
                new_config["library_path"] = new_library_path
                save_config(new_config)

                print(f"  ✅ 移动成功")
                self.test_results.append(("移动操作", "成功", f"移动到: {new_library_path}"))
                return new_library_path
            else:
                print(f"  ❌ 移动失败")
                self.test_results.append(("移动操作", "失败", "FileSystem.move_library返回False"))
                return None

        except Exception as e:
            print(f"  ❌ 移动异常: {e}")
            self.test_results.append(("移动操作", "异常", str(e)))
            return None

    def _verify_move_results(self, initial_state, after_add_state, after_move_state, new_library_path):
        """验证移动结果"""
        print("✅ 验证移动结果...")

        if not new_library_path:
            self.test_results.append(("移动验证", "失败", "移动操作失败"))
            return

        try:
            # 验证新位置的文件库结构
            new_data_dir = os.path.join(new_library_path, "data")
            new_db_file = os.path.join(new_data_dir, "smartvault.db")

            # 标准化路径进行比较
            from smartvault.utils.config import normalize_path
            config_path_norm = normalize_path(after_move_state.get("config_path", ""))
            new_path_norm = normalize_path(new_library_path)

            checks = [
                ("新库目录存在", os.path.exists(new_library_path)),
                ("新data目录存在", os.path.exists(new_data_dir)),
                ("新数据库文件存在", os.path.exists(new_db_file)),
                ("配置已更新", config_path_norm == new_path_norm),
                ("文件数量一致", after_move_state.get("file_count") == after_add_state.get("file_count"))
            ]

            for check_name, result in checks:
                status = "✅" if result else "❌"
                print(f"  {status} {check_name}")
                self.test_results.append((check_name, "通过" if result else "失败", ""))

        except Exception as e:
            print(f"  ❌ 验证异常: {e}")
            self.test_results.append(("移动验证", "异常", str(e)))

    def _test_reopen_after_move(self, new_library_path):
        """测试移动后重新打开"""
        print("🔄 测试移动后重新打开...")

        if not new_library_path:
            return

        try:
            # 重新初始化服务
            file_service = FileService()
            files = file_service.get_files(limit=10)

            print(f"  ✅ 重新打开成功，文件数量: {len(files)}")
            self.test_results.append(("重新打开", "成功", f"文件数量: {len(files)}"))

            # 测试文件访问
            for file_info in files[:3]:  # 只测试前3个文件
                file_id = file_info["id"]
                file_name = file_info["name"]

                # 尝试获取文件信息
                detailed_info = file_service.get_file_by_id(file_id)
                if detailed_info:
                    print(f"    ✅ 文件可访问: {file_name}")
                else:
                    print(f"    ❌ 文件不可访问: {file_name}")

        except Exception as e:
            print(f"  ❌ 重新打开失败: {e}")
            self.test_results.append(("重新打开", "失败", str(e)))

    def _cleanup(self, test_files, new_library_path):
        """清理测试数据"""
        print("🧹 清理测试数据...")

        # 清理测试文件
        for file_path in test_files:
            try:
                if os.path.exists(file_path):
                    os.unlink(file_path)
                # 清理临时目录
                temp_dir = os.path.dirname(file_path)
                if os.path.exists(temp_dir) and temp_dir.startswith(tempfile.gettempdir()):
                    shutil.rmtree(temp_dir, ignore_errors=True)
            except Exception as e:
                print(f"  ⚠️  清理文件失败: {file_path} - {e}")

        # 恢复原始配置
        try:
            save_config(self.original_config)
            print(f"  ✅ 已恢复原始配置")
        except Exception as e:
            print(f"  ❌ 恢复配置失败: {e}")

        # 清理新的文件库目录（可选）
        # 注意：这里不自动删除，让用户手动决定
        if new_library_path and os.path.exists(new_library_path):
            print(f"  ℹ️  新文件库目录保留: {new_library_path}")
            print(f"     如需删除，请手动执行: rmdir /s \"{new_library_path}\"")

    def _generate_report(self):
        """生成测试报告"""
        print("\n" + "="*60)
        print("📊 智能文件库移动测试报告")
        print("="*60)

        passed = sum(1 for _, status, _ in self.test_results if status in ["成功", "通过"])
        total = len(self.test_results)

        print(f"总测试项: {total}")
        print(f"通过数: {passed}")
        print(f"失败数: {total - passed}")
        print(f"通过率: {passed/total*100:.1f}%" if total > 0 else "通过率: 0%")

        print("\n📋 详细结果:")
        for test_name, status, details in self.test_results:
            status_icon = "✅" if status in ["成功", "通过"] else "❌"
            print(f"  {status_icon} {test_name}: {status}")
            if details:
                print(f"    详情: {details}")

        if passed == total and total > 0:
            print("\n🎉 所有测试通过！文件库移动功能正常。")
        else:
            print(f"\n⚠️  有测试失败，建议检查相关功能。")


def main():
    """主函数"""
    print("🧪 智能文件库移动功能测试")
    print("="*40)

    tester = LibraryMoveTester()
    tester.test_complete_move_flow()


if __name__ == "__main__":
    main()
