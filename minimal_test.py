"""
最小化测试启动 - 逐步测试各个组件
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入"""
    print("🔍 测试导入...")

    try:
        print("  导入 PySide6...")
        from PySide6.QtWidgets import QApplication
        print("  ✅ PySide6 导入成功")

        print("  导入配置模块...")
        from smartvault.utils.config import load_config
        print("  ✅ 配置模块导入成功")

        print("  导入数据库模块...")
        from smartvault.data.database import Database
        print("  ✅ 数据库模块导入成功")

        print("  导入文件服务...")
        from smartvault.services.file import FileService
        print("  ✅ 文件服务导入成功")

        return True

    except Exception as e:
        print(f"  ❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_config():
    """测试配置"""
    print("🔍 测试配置...")

    try:
        from smartvault.utils.config import load_config
        config = load_config()

        print(f"  智能文件库路径: {config.get('library_path', '未设置')}")

        library_path = config.get("library_path")
        if library_path and os.path.exists(library_path):
            print("  ✅ 智能文件库目录存在")
        else:
            print("  ❌ 智能文件库目录不存在")
            return False

        return True

    except Exception as e:
        print(f"  ❌ 配置测试失败: {e}")
        return False


def test_database():
    """测试数据库"""
    print("🔍 测试数据库...")

    try:
        from smartvault.data.database import Database

        print("  创建数据库实例...")
        db = Database.create_from_config()
        print("  ✅ 数据库实例创建成功")

        print("  测试数据库查询...")
        cursor = db.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM files")
        count = cursor.fetchone()[0]
        print(f"  文件记录数量: {count}")

        print("  关闭数据库连接...")
        db.close()
        print("  ✅ 数据库测试成功")

        return True

    except Exception as e:
        print(f"  ❌ 数据库测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_file_service():
    """测试文件服务"""
    print("🔍 测试文件服务...")

    try:
        from smartvault.services.file import FileService

        print("  创建文件服务实例...")
        file_service = FileService()
        print("  ✅ 文件服务实例创建成功")

        print("  测试获取文件列表...")
        files = file_service.get_files(limit=5)
        print(f"  获取到 {len(files)} 个文件")

        return True

    except Exception as e:
        print(f"  ❌ 文件服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_qt_app():
    """测试Qt应用程序"""
    print("🔍 测试Qt应用程序...")

    try:
        from PySide6.QtWidgets import QApplication

        print("  创建QApplication...")
        app = QApplication(sys.argv)
        print("  ✅ QApplication创建成功")

        print("  退出QApplication...")
        app.quit()
        print("  ✅ QApplication测试成功")

        return True

    except Exception as e:
        print(f"  ❌ Qt应用程序测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_main_window():
    """测试主窗口创建"""
    print("🔍 测试主窗口创建...")

    try:
        from PySide6.QtWidgets import QApplication
        from smartvault.ui.main_window import MainWindow

        # 检查是否已有QApplication实例
        app = QApplication.instance()
        if app is None:
            print("  创建QApplication...")
            app = QApplication(sys.argv)
            created_app = True
        else:
            print("  使用现有QApplication...")
            created_app = False

        print("  创建主窗口...")
        window = MainWindow()
        print("  ✅ 主窗口创建成功")

        print("  清理资源...")
        window.close()

        if created_app:
            app.quit()

        print("  ✅ 主窗口测试成功")

        return True

    except Exception as e:
        print(f"  ❌ 主窗口测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("=" * 50)
    print("🧪 SmartVault 最小化测试")
    print("=" * 50)

    tests = [
        ("导入测试", test_imports),
        ("配置测试", test_config),
        ("数据库测试", test_database),
        ("文件服务测试", test_file_service),
        ("Qt应用程序测试", test_qt_app),
        ("主窗口测试", test_main_window),
    ]

    failed_tests = []

    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)

        if test_func():
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
            failed_tests.append(test_name)

    print("\n" + "=" * 50)
    print("📊 测试结果")
    print("=" * 50)

    if failed_tests:
        print(f"❌ {len(failed_tests)} 个测试失败:")
        for test in failed_tests:
            print(f"  - {test}")
        print("\n💡 建议:")
        print("1. 检查失败的组件")
        print("2. 查看详细错误信息")
        print("3. 重新安装相关依赖")
    else:
        print("✅ 所有测试通过！")
        print("💡 如果启动仍有问题，可能是线程或事件循环相关问题")


if __name__ == "__main__":
    main()
