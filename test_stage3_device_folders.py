#!/usr/bin/env python3
"""
阶段3：移动设备功能完善测试脚本

测试内容：
1. 设备卷标自动读取功能
2. 导航面板设备文件夹创建
3. 文件添加对话框设备来源选择
4. 设备文件夹文件筛选显示
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QTextEdit
from PySide6.QtCore import Qt

class Stage3TestWindow(QMainWindow):
    """阶段3测试窗口"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("SmartVault - 阶段3：移动设备功能完善测试")
        self.setGeometry(100, 100, 800, 600)

        # 创建中央窗口
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建布局
        layout = QVBoxLayout(central_widget)

        # 添加说明标签
        info_label = QLabel("""
🎯 阶段3：移动设备功能完善测试

本测试验证以下功能：

✅ 1. 设备卷标自动读取
• 用户右键"移动设备"节点 → "添加设备文件夹"
• 弹出文件夹选择对话框
• 用户选择USB设备根目录（如 F:\）
• 软件自动读取设备卷标并创建设备文件夹

✅ 2. 统一文件入口集成
• 创建设备文件夹后询问是否立即添加设备文件
• 自动弹出"添加文件到智能文件库"对话框
• 预填设备路径，自动选择设备来源
• 用户选择入库方式并一键完成文件入库

✅ 3. 文件添加对话框设备来源选择
• 文件添加对话框新增"设备来源"选择框
• 显示已创建的设备文件夹列表
• 文件添加时自动关联选择的设备标签

✅ 4. 设备文件夹文件筛选显示
• 点击设备文件夹时正确筛选显示该设备的文件
• 状态栏显示设备信息
• 支持设备文件夹的文件管理操作

🔧 测试步骤：
1. 点击下方按钮启动SmartVault主程序
2. 在导航面板右键"移动设备" → "添加设备文件夹"
3. 选择一个USB设备或驱动器根目录
4. 确认创建设备文件夹
5. 选择"是"立即添加设备文件
6. 在弹出的对话框中选择入库方式并确认
7. 测试设备文件夹的文件筛选功能
        """)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("font-size: 12px; padding: 10px;")
        layout.addWidget(info_label)

        # 添加测试按钮
        test_button = QPushButton("🚀 启动SmartVault主程序进行测试")
        test_button.clicked.connect(self.start_main_program)
        test_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 15px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        layout.addWidget(test_button)

        # 添加设备卷标读取测试按钮
        volume_test_button = QPushButton("🔍 测试设备卷标读取功能")
        volume_test_button.clicked.connect(self.test_volume_reader)
        volume_test_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 10px;
                font-size: 12px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        layout.addWidget(volume_test_button)

        # 添加结果显示区域
        self.result_text = QTextEdit()
        self.result_text.setMaximumHeight(200)
        self.result_text.setStyleSheet("font-family: 'Consolas', monospace; font-size: 10px;")
        layout.addWidget(self.result_text)

        # 添加状态标签
        self.status_label = QLabel("准备开始测试...")
        self.status_label.setStyleSheet("color: #666; font-size: 10px; padding: 5px;")
        layout.addWidget(self.status_label)

    def start_main_program(self):
        """启动SmartVault主程序"""
        try:
            self.status_label.setText("🚀 正在启动SmartVault主程序...")
            self.result_text.append("=" * 50)
            self.result_text.append("启动SmartVault主程序")
            self.result_text.append("=" * 50)

            # 导入并启动主程序
            from smartvault.ui.main_window import MainWindow

            # 创建主窗口
            main_window = MainWindow()
            main_window.show()

            self.result_text.append("✅ SmartVault主程序已启动")
            self.result_text.append("")
            self.result_text.append("📋 测试步骤：")
            self.result_text.append("1. 在导航面板右键'移动设备' → '添加设备文件夹'")
            self.result_text.append("2. 选择USB设备根目录（如 F:\）")
            self.result_text.append("3. 确认创建设备文件夹")
            self.result_text.append("4. 测试文件添加时的设备来源选择")
            self.result_text.append("5. 测试设备文件夹的文件筛选功能")

            self.status_label.setText("✅ 主程序已启动，请按照步骤进行测试")

        except Exception as e:
            self.result_text.append(f"❌ 启动失败: {e}")
            self.status_label.setText(f"❌ 启动失败: {e}")
            import traceback
            self.result_text.append(traceback.format_exc())

    def test_volume_reader(self):
        """测试设备卷标读取功能"""
        try:
            self.status_label.setText("🔍 正在测试设备卷标读取功能...")
            self.result_text.append("=" * 50)
            self.result_text.append("测试设备卷标读取功能")
            self.result_text.append("=" * 50)

            from smartvault.utils.device_volume_reader import DeviceVolumeReader
            import platform

            reader = DeviceVolumeReader()
            self.result_text.append(f"当前系统: {platform.system()}")
            self.result_text.append("")

            if platform.system() == 'Windows':
                # 测试Windows系统的所有驱动器
                import string
                for letter in string.ascii_uppercase:
                    drive_path = f"{letter}:\\"
                    if os.path.exists(drive_path):
                        device_info = reader.get_volume_info_from_path(drive_path)
                        if device_info:
                            self.result_text.append(f"驱动器 {letter}: - {device_info.volume_name}")
                            if device_info.file_system:
                                self.result_text.append(f"  文件系统: {device_info.file_system}")
            else:
                # 测试其他系统
                test_paths = ["/", "/media", "/mnt"]
                for path in test_paths:
                    if os.path.exists(path):
                        device_info = reader.get_volume_info_from_path(path)
                        if device_info:
                            self.result_text.append(f"路径 {path} - {device_info.volume_name}")

            self.result_text.append("")
            self.result_text.append("✅ 设备卷标读取功能测试完成")
            self.status_label.setText("✅ 设备卷标读取功能测试完成")

        except Exception as e:
            self.result_text.append(f"❌ 测试失败: {e}")
            self.status_label.setText(f"❌ 测试失败: {e}")
            import traceback
            self.result_text.append(traceback.format_exc())


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("SmartVault Stage3 Test")
    app.setApplicationVersion("1.0")

    # 创建测试窗口
    window = Stage3TestWindow()
    window.show()

    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
