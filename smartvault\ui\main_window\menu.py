"""
主窗口菜单管理
"""

from PySide6.QtWidgets import QMenuBar, QMenu
from PySide6.QtGui import QAction


class MenuManager:
    """菜单管理器类"""

    def __init__(self, main_window):
        """初始化菜单管理器

        Args:
            main_window: 主窗口实例
        """
        self.main_window = main_window
        self.menu_bar = main_window.menuBar()

        # 创建菜单
        self.create_file_menu()
        self.create_edit_menu()
        self.create_view_menu()
        self.create_tools_menu()
        self.create_help_menu()

    def create_file_menu(self):
        """创建文件菜单"""
        file_menu = self.menu_bar.addMenu("文件(&F)")

        # 添加文件
        add_action = QAction("添加文件(&A)...", self.main_window)
        add_action.setShortcut("Ctrl+A")
        add_action.triggered.connect(self.main_window.on_add_file)
        file_menu.addAction(add_action)

        # 添加文件夹
        add_folder_action = QAction("添加文件夹(&F)...", self.main_window)
        add_folder_action.setShortcut("Ctrl+F")
        add_folder_action.triggered.connect(self.main_window.on_add_folder)
        file_menu.addAction(add_folder_action)

        file_menu.addSeparator()

        # 退出
        exit_action = QAction("退出(&X)", self.main_window)
        exit_action.setShortcut("Alt+F4")
        exit_action.triggered.connect(self.main_window.close)
        file_menu.addAction(exit_action)

    def create_edit_menu(self):
        """创建编辑菜单"""
        edit_menu = self.menu_bar.addMenu("编辑(&E)")

        # 全选
        select_all_action = QAction("全选(&A)", self.main_window)
        select_all_action.setShortcut("Ctrl+A")
        select_all_action.triggered.connect(self.on_select_all)
        edit_menu.addAction(select_all_action)

        # 反选
        invert_selection_action = QAction("反选(&I)", self.main_window)
        invert_selection_action.setShortcut("Ctrl+I")
        invert_selection_action.triggered.connect(self.on_invert_selection)
        edit_menu.addAction(invert_selection_action)

        # 清除选择
        clear_selection_action = QAction("清除选择(&C)", self.main_window)
        clear_selection_action.setShortcut("Ctrl+D")
        clear_selection_action.triggered.connect(self.on_clear_selection)
        edit_menu.addAction(clear_selection_action)

        edit_menu.addSeparator()

        # 批量操作子菜单
        batch_menu = edit_menu.addMenu("批量操作(&B)")

        # 批量移入中转文件夹
        batch_to_staging_action = QAction("批量移入中转文件夹(&S)", self.main_window)
        batch_to_staging_action.triggered.connect(self.on_batch_to_staging)
        batch_menu.addAction(batch_to_staging_action)

        # 批量移出中转文件夹
        batch_from_staging_action = QAction("批量移出中转文件夹(&O)", self.main_window)
        batch_from_staging_action.triggered.connect(self.on_batch_from_staging)
        batch_menu.addAction(batch_from_staging_action)

        batch_menu.addSeparator()

        # 批量导出
        batch_export_action = QAction("批量导出文件(&E)...", self.main_window)
        batch_export_action.triggered.connect(self.on_batch_export)
        batch_menu.addAction(batch_export_action)

        # 批量删除
        batch_delete_action = QAction("批量删除文件(&D)...", self.main_window)
        batch_delete_action.triggered.connect(self.on_batch_delete)
        batch_menu.addAction(batch_delete_action)

        # 保存菜单项引用以便后续更新状态
        self.select_all_action = select_all_action
        self.invert_selection_action = invert_selection_action
        self.clear_selection_action = clear_selection_action
        self.batch_menu = batch_menu
        self.batch_to_staging_action = batch_to_staging_action
        self.batch_from_staging_action = batch_from_staging_action
        self.batch_export_action = batch_export_action
        self.batch_delete_action = batch_delete_action

    def create_view_menu(self):
        """创建视图菜单"""
        view_menu = self.menu_bar.addMenu("视图(&V)")

        # 主题子菜单
        theme_menu = view_menu.addMenu("主题(&T)")

        # 导入主题管理器
        from smartvault.ui.themes import theme_manager

        # 创建主题动作组（确保只能选择一个主题）
        from PySide6.QtGui import QActionGroup
        theme_group = QActionGroup(self.main_window)

        current_theme = theme_manager.get_current_theme()

        # 添加主题选项
        for theme_key, theme_name in theme_manager.THEMES.items():
            theme_action = QAction(theme_name, self.main_window)
            theme_action.setCheckable(True)
            theme_action.setChecked(theme_key == current_theme)
            # 使用functools.partial避免lambda闭包问题
            from functools import partial
            theme_action.triggered.connect(
                partial(self.on_theme_changed, theme_key)
            )
            theme_group.addAction(theme_action)
            theme_menu.addAction(theme_action)

        view_menu.addSeparator()

        # 刷新视图
        refresh_action = QAction("刷新(&R)", self.main_window)
        refresh_action.setShortcut("F5")
        refresh_action.triggered.connect(self.main_window.on_refresh_view)
        view_menu.addAction(refresh_action)

    def on_theme_changed(self, theme_name):
        """主题变化处理

        Args:
            theme_name: 主题名称
        """
        from smartvault.ui.themes import theme_manager
        theme_manager.set_theme(theme_name)

        # 显示状态消息
        theme_display_name = theme_manager.THEMES.get(theme_name, theme_name)
        self.main_window.show_status_message(f"已切换到{theme_display_name}", True)

    def on_select_all(self):
        """全选文件"""
        if hasattr(self.main_window, 'file_view'):
            self.main_window.file_view.select_all_files()

    def on_invert_selection(self):
        """反选文件"""
        if hasattr(self.main_window, 'file_view'):
            self.main_window.file_view.invert_selection()

    def on_clear_selection(self):
        """清除选择"""
        if hasattr(self.main_window, 'file_view'):
            self.main_window.file_view.clear_selection()

    def on_batch_to_staging(self):
        """批量移入中转文件夹"""
        if hasattr(self.main_window, 'on_batch_to_staging'):
            self.main_window.on_batch_to_staging()

    def on_batch_from_staging(self):
        """批量移出中转文件夹"""
        if hasattr(self.main_window, 'on_batch_from_staging'):
            self.main_window.on_batch_from_staging()

    def on_batch_export(self):
        """批量导出文件"""
        if hasattr(self.main_window, 'on_batch_export'):
            self.main_window.on_batch_export()

    def on_batch_delete(self):
        """批量删除文件"""
        if hasattr(self.main_window, 'on_batch_delete'):
            self.main_window.on_batch_delete()

    def update_menu_states(self, selected_count):
        """更新菜单状态

        Args:
            selected_count: 选中的文件数量
        """
        # 更新选择相关菜单项的启用状态
        has_selection = selected_count > 0
        has_multiple_selection = selected_count > 1

        if hasattr(self, 'invert_selection_action'):
            self.invert_selection_action.setEnabled(has_selection)
        if hasattr(self, 'clear_selection_action'):
            self.clear_selection_action.setEnabled(has_selection)

        # 更新批量操作菜单的启用状态
        if hasattr(self, 'batch_menu'):
            self.batch_menu.setEnabled(has_selection)
        if hasattr(self, 'batch_to_staging_action'):
            self.batch_to_staging_action.setEnabled(has_selection)
        if hasattr(self, 'batch_from_staging_action'):
            self.batch_from_staging_action.setEnabled(has_selection)
        if hasattr(self, 'batch_export_action'):
            self.batch_export_action.setEnabled(has_selection)
        if hasattr(self, 'batch_delete_action'):
            self.batch_delete_action.setEnabled(has_selection)

    def create_tools_menu(self):
        """创建工具菜单"""
        tools_menu = self.menu_bar.addMenu("工具(&T)")

        # 标签管理
        tag_management_action = QAction("标签管理(&T)...", self.main_window)
        tag_management_action.triggered.connect(self.main_window.on_tag_management)
        tools_menu.addAction(tag_management_action)

        tools_menu.addSeparator()

        # 设置
        settings_action = QAction("设置(&S)...", self.main_window)
        settings_action.triggered.connect(self.main_window.on_settings)
        tools_menu.addAction(settings_action)

    def create_help_menu(self):
        """创建帮助菜单"""
        help_menu = self.menu_bar.addMenu("帮助(&H)")

        # 用户帮助
        help_action = QAction("用户帮助(&H)...", self.main_window)
        help_action.setShortcut("F1")
        help_action.triggered.connect(self.main_window.on_help)
        help_menu.addAction(help_action)

        help_menu.addSeparator()

        # 关于
        about_action = QAction("关于(&A)", self.main_window)
        about_action.triggered.connect(self.main_window.on_about)
        help_menu.addAction(about_action)
