#!/usr/bin/env python3
"""
SmartVault 代码质量监控工具
自动检查core.py文件大小并提供拆分建议
"""

import os
import sys
import json
from datetime import datetime
from pathlib import Path

# 代码质量标准
QUALITY_STANDARDS = {
    "core_file_path": "smartvault/ui/main_window/core.py",
    "warning_threshold": 2500,  # 警告阈值
    "critical_threshold": 3000,  # 强制拆分阈值
    "method_max_lines": 100,    # 单个方法最大行数
    "class_max_lines": 1500,    # 单个类最大行数
}

# 核心功能关键词（这些功能应该保留在core.py中）
CORE_FUNCTIONS = [
    "__init__", "init_ui", "closeEvent", "keyPressEvent",
    "restore_window_geometry", "apply_user_config", 
    "on_library_changed", "show_status_message"
]

# 非核心功能关键词（这些功能应该考虑移到其他模块）
NON_CORE_FUNCTIONS = [
    "backup", "clipboard", "drag", "monitor", "search_",
    "tag_", "device_", "usb_", "floating_", "duplicate_"
]

class CodeQualityMonitor:
    """代码质量监控器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.core_file = self.project_root / QUALITY_STANDARDS["core_file_path"]
        self.report_file = self.project_root / "code_quality_status.json"
        
    def check_file_size(self):
        """检查文件大小"""
        if not self.core_file.exists():
            return {"error": f"文件不存在: {self.core_file}"}
            
        with open(self.core_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        total_lines = len(lines)
        non_empty_lines = len([line for line in lines if line.strip()])
        
        # 判断风险等级
        if total_lines >= QUALITY_STANDARDS["critical_threshold"]:
            risk_level = "🔴 CRITICAL"
            action_required = "立即拆分"
        elif total_lines >= QUALITY_STANDARDS["warning_threshold"]:
            risk_level = "🟡 WARNING"
            action_required = "考虑拆分"
        else:
            risk_level = "🟢 GOOD"
            action_required = "继续监控"
            
        return {
            "total_lines": total_lines,
            "non_empty_lines": non_empty_lines,
            "risk_level": risk_level,
            "action_required": action_required,
            "threshold_warning": QUALITY_STANDARDS["warning_threshold"],
            "threshold_critical": QUALITY_STANDARDS["critical_threshold"]
        }
    
    def analyze_methods(self):
        """分析方法复杂度"""
        if not self.core_file.exists():
            return []
            
        with open(self.core_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        methods = []
        current_method = None
        method_start = 0
        
        for i, line in enumerate(lines, 1):
            stripped = line.strip()
            
            # 检测方法定义
            if stripped.startswith('def ') and ':' in stripped:
                # 保存上一个方法
                if current_method:
                    method_lines = i - method_start - 1
                    methods.append({
                        "name": current_method,
                        "start_line": method_start,
                        "end_line": i - 1,
                        "lines": method_lines,
                        "is_core": any(core in current_method for core in CORE_FUNCTIONS),
                        "is_non_core": any(non_core in current_method for non_core in NON_CORE_FUNCTIONS),
                        "risk": "🔴 HIGH" if method_lines > QUALITY_STANDARDS["method_max_lines"] else "🟢 OK"
                    })
                
                # 开始新方法
                current_method = stripped.split('(')[0].replace('def ', '')
                method_start = i
        
        # 处理最后一个方法
        if current_method:
            method_lines = len(lines) - method_start
            methods.append({
                "name": current_method,
                "start_line": method_start,
                "end_line": len(lines),
                "lines": method_lines,
                "is_core": any(core in current_method for core in CORE_FUNCTIONS),
                "is_non_core": any(non_core in current_method for non_core in NON_CORE_FUNCTIONS),
                "risk": "🔴 HIGH" if method_lines > QUALITY_STANDARDS["method_max_lines"] else "🟢 OK"
            })
            
        return methods
    
    def suggest_refactoring(self, methods):
        """提供重构建议"""
        suggestions = []
        
        # 找出可以移动的非核心方法
        movable_methods = [m for m in methods if m["is_non_core"] and m["lines"] > 20]
        if movable_methods:
            total_movable_lines = sum(m["lines"] for m in movable_methods)
            suggestions.append({
                "type": "移动非核心功能",
                "description": f"可以将 {len(movable_methods)} 个非核心方法移到其他模块",
                "methods": [m["name"] for m in movable_methods],
                "potential_reduction": total_movable_lines,
                "target_modules": {
                    "backup": "backup_manager.py",
                    "clipboard": "clipboard_handler.py", 
                    "drag": "drag_drop_handler.py",
                    "monitor": "monitor_manager.py",
                    "search": "search_handler.py"
                }
            })
        
        # 找出过长的方法
        long_methods = [m for m in methods if m["lines"] > QUALITY_STANDARDS["method_max_lines"]]
        if long_methods:
            suggestions.append({
                "type": "拆分长方法",
                "description": f"有 {len(long_methods)} 个方法超过 {QUALITY_STANDARDS['method_max_lines']} 行",
                "methods": [(m["name"], m["lines"]) for m in long_methods],
                "recommendation": "将复杂逻辑抽取为独立的私有方法"
            })
            
        return suggestions
    
    def generate_report(self):
        """生成完整报告"""
        file_status = self.check_file_size()
        methods = self.analyze_methods()
        suggestions = self.suggest_refactoring(methods)
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "file_status": file_status,
            "method_analysis": {
                "total_methods": len(methods),
                "core_methods": len([m for m in methods if m["is_core"]]),
                "non_core_methods": len([m for m in methods if m["is_non_core"]]),
                "high_risk_methods": len([m for m in methods if m["risk"] == "🔴 HIGH"])
            },
            "refactoring_suggestions": suggestions,
            "quality_standards": QUALITY_STANDARDS
        }
        
        # 保存报告
        with open(self.report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
            
        return report
    
    def print_summary(self, report):
        """打印摘要报告"""
        print("=" * 60)
        print("🔍 SmartVault 代码质量监控报告")
        print("=" * 60)
        
        # 文件状态
        file_status = report["file_status"]
        if "error" in file_status:
            print(f"❌ 错误: {file_status['error']}")
            return
            
        print(f"📁 文件: {QUALITY_STANDARDS['core_file_path']}")
        print(f"📏 总行数: {file_status['total_lines']}")
        print(f"📊 风险等级: {file_status['risk_level']}")
        print(f"🎯 建议行动: {file_status['action_required']}")
        print()
        
        # 方法分析
        method_analysis = report["method_analysis"]
        print("📋 方法分析:")
        print(f"  • 总方法数: {method_analysis['total_methods']}")
        print(f"  • 核心方法: {method_analysis['core_methods']}")
        print(f"  • 非核心方法: {method_analysis['non_core_methods']}")
        print(f"  • 高风险方法: {method_analysis['high_risk_methods']}")
        print()
        
        # 重构建议
        suggestions = report["refactoring_suggestions"]
        if suggestions:
            print("💡 重构建议:")
            for i, suggestion in enumerate(suggestions, 1):
                print(f"  {i}. {suggestion['type']}")
                print(f"     {suggestion['description']}")
                if 'potential_reduction' in suggestion:
                    print(f"     预计减少: {suggestion['potential_reduction']} 行")
                print()
        else:
            print("✅ 暂无重构建议")
            
        print("=" * 60)

def main():
    """主函数"""
    monitor = CodeQualityMonitor()
    report = monitor.generate_report()
    monitor.print_summary(report)
    
    # 如果达到临界阈值，返回错误码
    if report["file_status"].get("total_lines", 0) >= QUALITY_STANDARDS["critical_threshold"]:
        print("🚨 警告: 文件大小已达到临界阈值，必须进行拆分！")
        sys.exit(1)
    elif report["file_status"].get("total_lines", 0) >= QUALITY_STANDARDS["warning_threshold"]:
        print("⚠️  注意: 文件大小接近警告阈值，建议考虑拆分")
        sys.exit(2)
    
    print("✅ 代码质量检查通过")
    sys.exit(0)

if __name__ == "__main__":
    main()
