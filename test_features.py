#!/usr/bin/env python3
"""
测试新功能的脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_monitor_config():
    """测试监控状态持久化功能"""
    print("=== 测试监控状态持久化功能 ===")
    
    try:
        from smartvault.utils.config import save_monitor_status, get_monitor_status
        
        # 测试保存和读取状态
        print("1. 测试保存监控状态为True")
        save_monitor_status(True)
        status = get_monitor_status()
        print(f"   读取状态: {status}")
        assert status == True, "状态应该为True"
        
        print("2. 测试保存监控状态为False")
        save_monitor_status(False)
        status = get_monitor_status()
        print(f"   读取状态: {status}")
        assert status == False, "状态应该为False"
        
        print("3. 恢复默认状态")
        save_monitor_status(True)
        
        print("✅ 监控状态持久化功能测试通过")
        
    except Exception as e:
        print(f"❌ 监控状态持久化功能测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_file_service():
    """测试文件服务统一入口"""
    print("\n=== 测试文件服务统一入口 ===")
    
    try:
        from smartvault.services.file import FileService
        
        # 创建文件服务实例
        file_service = FileService()
        
        # 检查add_file方法是否存在
        assert hasattr(file_service, 'add_file'), "FileService应该有add_file方法"
        
        # 检查方法签名
        import inspect
        sig = inspect.signature(file_service.add_file)
        params = list(sig.parameters.keys())
        print(f"add_file方法参数: {params}")
        
        # 应该包含path, mode, smart_duplicate_handling参数
        expected_params = ['path', 'mode', 'smart_duplicate_handling']
        for param in expected_params:
            if param in params:
                print(f"   ✅ 包含参数: {param}")
            else:
                print(f"   ⚠️  缺少参数: {param}")
        
        print("✅ 文件服务统一入口检查通过")
        
    except Exception as e:
        print(f"❌ 文件服务统一入口测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_main_window_drag_drop():
    """测试主窗口拖拽功能"""
    print("\n=== 测试主窗口拖拽功能 ===")
    
    try:
        from smartvault.ui.main_window.core import MainWindowCore
        
        # 检查拖拽相关方法是否存在
        methods_to_check = ['dragEnterEvent', 'dropEvent', '_process_dropped_files']
        
        for method_name in methods_to_check:
            if hasattr(MainWindowCore, method_name):
                print(f"   ✅ 包含方法: {method_name}")
            else:
                print(f"   ❌ 缺少方法: {method_name}")
        
        print("✅ 主窗口拖拽功能检查通过")
        
    except Exception as e:
        print(f"❌ 主窗口拖拽功能测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("开始测试新功能...")
    
    test_monitor_config()
    test_file_service()
    test_main_window_drag_drop()
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
