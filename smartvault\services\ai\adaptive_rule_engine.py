"""
自适应规则引擎

实现基于用户反馈的规则性能评估和动态规则生成功能，包括：
- 规则性能评估
- 动态规则生成
- 规则优化算法
- 自适应学习机制
"""

import json
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime
from collections import defaultdict, Counter


class AdaptiveRuleEngine:
    """自适应标签规则引擎"""

    def __init__(self):
        self.db = None  # 数据库连接
        self.dynamic_rules = []  # 动态生成的规则
        self.rule_performance = {}  # 规则性能统计
        self.user_behavior_patterns = {}  # 用户行为模式

    def initialize(self, db=None):
        """初始化自适应规则引擎

        Args:
            db: 数据库连接
        """
        self.db = db

        # 加载已有的规则性能数据
        if self.db:
            self._load_rule_performance()
            self._load_dynamic_rules()

    def update_rule_performance(self, rule_id: str, feedback: str, context: Dict):
        """更新规则性能

        Args:
            rule_id: 规则ID
            feedback: 用户反馈 ('accept', 'reject', 'modify')
            context: 上下文信息
        """
        try:
            if rule_id not in self.rule_performance:
                self.rule_performance[rule_id] = {
                    'total_applications': 0,
                    'positive_feedback': 0,
                    'negative_feedback': 0,
                    'neutral_feedback': 0,
                    'performance_score': 0.5,
                    'last_updated': datetime.now().isoformat(),
                    'context_patterns': defaultdict(int)
                }

            perf = self.rule_performance[rule_id]
            perf['total_applications'] += 1
            perf['last_updated'] = datetime.now().isoformat()

            # 更新反馈统计
            if feedback == 'accept':
                perf['positive_feedback'] += 1
            elif feedback == 'reject':
                perf['negative_feedback'] += 1
            else:
                perf['neutral_feedback'] += 1

            # 计算性能评分
            if perf['total_applications'] > 0:
                perf['performance_score'] = perf['positive_feedback'] / perf['total_applications']

            # 记录上下文模式
            context_key = self._generate_context_key(context)
            perf['context_patterns'][context_key] += 1

            # 保存到数据库
            if self.db:
                self._save_rule_performance(rule_id, perf)

            # 如果规则表现太差，标记为待优化
            if perf['performance_score'] < 0.3 and perf['total_applications'] > 10:
                self._optimize_or_disable_rule(rule_id)

        except Exception as e:
            print(f"更新规则性能失败: {e}")

    def generate_adaptive_rules(self, user_behavior_data: List[Dict]) -> List[Dict]:
        """基于用户行为生成自适应规则

        Args:
            user_behavior_data: 用户行为数据列表

        Returns:
            List[Dict]: 新生成的规则列表
        """
        try:
            # 提取行为模式
            patterns = self._extract_behavior_patterns(user_behavior_data)

            new_rules = []
            for pattern in patterns:
                if pattern['confidence'] > 0.8 and pattern['frequency'] > 5:
                    rule = self._create_adaptive_rule(pattern)
                    if rule:
                        new_rules.append(rule)
                        self.dynamic_rules.append(rule)

            # 保存新规则到数据库
            if self.db and new_rules:
                self._save_dynamic_rules(new_rules)

            return new_rules

        except Exception as e:
            print(f"生成自适应规则失败: {e}")
            return []

    def _extract_behavior_patterns(self, behavior_data: List[Dict]) -> List[Dict]:
        """提取用户行为模式

        Args:
            behavior_data: 用户行为数据

        Returns:
            List[Dict]: 行为模式列表
        """
        patterns = []

        # 按文件类型分组行为
        type_behaviors = defaultdict(list)
        for behavior in behavior_data:
            file_info = behavior.get('file_info', {})
            file_type = file_info.get('extension', '').lower()
            if file_type:
                type_behaviors[file_type].append(behavior)

        # 分析每种文件类型的标签使用模式
        for file_type, behaviors in type_behaviors.items():
            if len(behaviors) >= 3:  # 至少需要3个样本
                pattern = self._analyze_type_pattern(file_type, behaviors)
                if pattern:
                    patterns.append(pattern)

        # 按路径模式分组行为
        path_behaviors = defaultdict(list)
        for behavior in behavior_data:
            file_info = behavior.get('file_info', {})
            path = file_info.get('path', '')
            path_pattern = self._extract_path_pattern(path)
            if path_pattern:
                path_behaviors[path_pattern].append(behavior)

        # 分析路径模式
        for path_pattern, behaviors in path_behaviors.items():
            if len(behaviors) >= 3:
                pattern = self._analyze_path_pattern(path_pattern, behaviors)
                if pattern:
                    patterns.append(pattern)

        return patterns

    def _analyze_type_pattern(self, file_type: str, behaviors: List[Dict]) -> Optional[Dict]:
        """分析文件类型的标签使用模式

        Args:
            file_type: 文件类型
            behaviors: 行为数据列表

        Returns:
            Optional[Dict]: 模式信息
        """
        # 统计标签使用频率
        tag_counter = Counter()
        total_behaviors = len(behaviors)

        for behavior in behaviors:
            if behavior.get('action_type') == 'tag_applied':
                for tag in behavior.get('applied_tags', []):
                    tag_counter[tag] += 1

        # 找到高频标签
        frequent_tags = []
        for tag, count in tag_counter.items():
            frequency = count / total_behaviors
            if frequency > 0.6:  # 60%以上的文件都使用这个标签
                frequent_tags.append(tag)

        if frequent_tags:
            return {
                'type': 'file_extension',
                'condition': {'extension': file_type},
                'tags': frequent_tags,
                'frequency': total_behaviors,
                'confidence': max(tag_counter.values()) / total_behaviors,
                'description': f"{file_type}文件自动标签规则"
            }

        return None

    def _analyze_path_pattern(self, path_pattern: str, behaviors: List[Dict]) -> Optional[Dict]:
        """分析路径模式的标签使用模式

        Args:
            path_pattern: 路径模式
            behaviors: 行为数据列表

        Returns:
            Optional[Dict]: 模式信息
        """
        # 统计标签使用频率
        tag_counter = Counter()
        total_behaviors = len(behaviors)

        for behavior in behaviors:
            if behavior.get('action_type') == 'tag_applied':
                for tag in behavior.get('applied_tags', []):
                    tag_counter[tag] += 1

        # 找到高频标签
        frequent_tags = []
        for tag, count in tag_counter.items():
            frequency = count / total_behaviors
            if frequency > 0.5:  # 50%以上的文件都使用这个标签
                frequent_tags.append(tag)

        if frequent_tags:
            return {
                'type': 'path_pattern',
                'condition': {'path_contains': path_pattern},
                'tags': frequent_tags,
                'frequency': total_behaviors,
                'confidence': max(tag_counter.values()) / total_behaviors,
                'description': f"路径包含'{path_pattern}'的文件自动标签规则"
            }

        return None

    def _extract_path_pattern(self, path: str) -> str:
        """提取路径模式

        Args:
            path: 文件路径

        Returns:
            str: 路径模式
        """
        import os

        # 提取路径中的关键词
        path_parts = path.lower().split(os.sep)
        keywords = ['work', 'project', 'personal', 'download', 'desktop', 'document']

        for part in path_parts:
            for keyword in keywords:
                if keyword in part:
                    return keyword

        return ''

    def _create_adaptive_rule(self, pattern: Dict) -> Optional[Dict]:
        """创建自适应规则

        Args:
            pattern: 行为模式

        Returns:
            Optional[Dict]: 规则对象
        """
        try:
            rule_id = f"adaptive_{uuid.uuid4().hex[:8]}"

            rule = {
                'id': rule_id,
                'name': f"自适应规则: {pattern['description']}",
                'type': pattern['type'],
                'condition': pattern['condition'],
                'suggested_tags': pattern['tags'],
                'confidence': pattern['confidence'],
                'frequency': pattern['frequency'],
                'source': 'adaptive_learning',
                'created_at': datetime.now().isoformat(),
                'enabled': True
            }

            return rule

        except Exception as e:
            print(f"创建自适应规则失败: {e}")
            return None

    def _generate_context_key(self, context: Dict) -> str:
        """生成上下文键

        Args:
            context: 上下文信息

        Returns:
            str: 上下文键
        """
        file_info = context.get('file_info', {})
        extension = file_info.get('extension', '').lower()

        # 提取路径特征
        path = file_info.get('path', '').lower()
        path_features = []

        keywords = ['work', 'project', 'personal', 'download']
        for keyword in keywords:
            if keyword in path:
                path_features.append(keyword)

        context_key = f"{extension}_{'-'.join(path_features[:2])}"
        return context_key

    def _optimize_or_disable_rule(self, rule_id: str):
        """优化或禁用表现差的规则

        Args:
            rule_id: 规则ID
        """
        try:
            perf = self.rule_performance.get(rule_id)
            if not perf:
                return

            print(f"⚠️ 规则 {rule_id} 表现不佳 (成功率: {perf['performance_score']:.2f})")

            # 分析失败的上下文模式
            context_patterns = perf.get('context_patterns', {})
            if context_patterns:
                # 找到最常失败的上下文
                most_common_context = max(context_patterns, key=context_patterns.get)
                print(f"   最常失败的上下文: {most_common_context}")

            # 标记规则为需要优化
            perf['needs_optimization'] = True
            perf['optimization_reason'] = f"成功率过低: {perf['performance_score']:.2f}"

            # 保存到数据库
            if self.db:
                self._save_rule_performance(rule_id, perf)

        except Exception as e:
            print(f"优化规则失败: {e}")

    def get_rule_recommendations(self) -> List[Dict]:
        """获取规则优化建议

        Returns:
            List[Dict]: 优化建议列表
        """
        recommendations = []

        for rule_id, perf in self.rule_performance.items():
            if perf.get('needs_optimization'):
                recommendations.append({
                    'rule_id': rule_id,
                    'issue': perf.get('optimization_reason', '未知问题'),
                    'performance_score': perf['performance_score'],
                    'total_applications': perf['total_applications'],
                    'suggestion': self._generate_optimization_suggestion(perf)
                })

        return recommendations

    def _generate_optimization_suggestion(self, perf: Dict) -> str:
        """生成优化建议

        Args:
            perf: 性能数据

        Returns:
            str: 优化建议
        """
        score = perf['performance_score']

        if score < 0.2:
            return "建议禁用此规则，成功率过低"
        elif score < 0.4:
            return "建议调整规则条件，提高准确性"
        else:
            return "建议收集更多用户反馈数据"

    def get_performance_statistics(self) -> Dict:
        """获取性能统计信息

        Returns:
            Dict: 性能统计
        """
        stats = {
            'total_rules': len(self.rule_performance),
            'high_performance_rules': 0,
            'medium_performance_rules': 0,
            'low_performance_rules': 0,
            'rules_needing_optimization': 0,
            'average_performance': 0.0,
            'top_performing_rules': [],
            'worst_performing_rules': []
        }

        if not self.rule_performance:
            return stats

        # 统计性能分布
        performance_scores = []
        rule_scores = []

        for rule_id, perf in self.rule_performance.items():
            score = perf['performance_score']
            performance_scores.append(score)
            rule_scores.append((rule_id, score, perf))

            if score >= 0.8:
                stats['high_performance_rules'] += 1
            elif score >= 0.5:
                stats['medium_performance_rules'] += 1
            else:
                stats['low_performance_rules'] += 1

            if perf.get('needs_optimization'):
                stats['rules_needing_optimization'] += 1

        # 计算平均性能
        stats['average_performance'] = sum(performance_scores) / len(performance_scores)

        # 获取最佳和最差规则
        rule_scores.sort(key=lambda x: x[1], reverse=True)

        stats['top_performing_rules'] = [
            {
                'rule_id': rule_id,
                'performance_score': score,
                'total_applications': perf['total_applications']
            }
            for rule_id, score, perf in rule_scores[:5]
        ]

        stats['worst_performing_rules'] = [
            {
                'rule_id': rule_id,
                'performance_score': score,
                'total_applications': perf['total_applications']
            }
            for rule_id, score, perf in rule_scores[-5:]
        ]

        return stats

    def _load_rule_performance(self):
        """从数据库加载规则性能数据"""
        try:
            if not self.db:
                return

            cursor = self.db.conn.cursor()
            cursor.execute("""
                SELECT pattern_type, pattern_data, frequency, success_rate, last_used
                FROM ai_learning_patterns
                WHERE pattern_type = 'rule_performance'
            """)

            rows = cursor.fetchall()
            for row in rows:
                pattern_type, pattern_data_str, frequency, success_rate, last_used = row

                try:
                    pattern_data = json.loads(pattern_data_str)
                    rule_id = pattern_data.get('rule_id', '')

                    if rule_id:
                        self.rule_performance[rule_id] = {
                            'total_applications': frequency,
                            'positive_feedback': int(frequency * success_rate),
                            'negative_feedback': frequency - int(frequency * success_rate),
                            'neutral_feedback': 0,
                            'performance_score': success_rate,
                            'last_updated': last_used,
                            'context_patterns': defaultdict(int, pattern_data.get('context_patterns', {})),
                            'needs_optimization': pattern_data.get('needs_optimization', False),
                            'optimization_reason': pattern_data.get('optimization_reason', '')
                        }

                except json.JSONDecodeError as e:
                    print(f"解析规则性能数据失败: {e}")

        except Exception as e:
            print(f"加载规则性能数据失败: {e}")

    def _save_rule_performance(self, rule_id: str, perf: Dict):
        """保存规则性能数据到数据库"""
        try:
            if not self.db:
                return

            cursor = self.db.conn.cursor()

            # 准备数据
            pattern_data = {
                'rule_id': rule_id,
                'context_patterns': dict(perf.get('context_patterns', {})),
                'needs_optimization': perf.get('needs_optimization', False),
                'optimization_reason': perf.get('optimization_reason', '')
            }
            pattern_data_str = json.dumps(pattern_data, ensure_ascii=False)

            # 使用UPSERT操作
            cursor.execute("""
                INSERT OR REPLACE INTO ai_learning_patterns
                (id, pattern_type, pattern_data, frequency, success_rate, last_used, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                f"rule_performance_{rule_id}",
                'rule_performance',
                pattern_data_str,
                perf['total_applications'],
                perf['performance_score'],
                perf['last_updated'],
                perf.get('created_at', perf['last_updated'])
            ))

            self.db.conn.commit()

        except Exception as e:
            print(f"保存规则性能数据失败: {e}")

    def _load_dynamic_rules(self):
        """从数据库加载动态规则"""
        try:
            if not self.db:
                return

            cursor = self.db.conn.cursor()
            cursor.execute("""
                SELECT pattern_type, pattern_data, frequency, success_rate, last_used
                FROM ai_learning_patterns
                WHERE pattern_type = 'dynamic_rule'
            """)

            rows = cursor.fetchall()
            for row in rows:
                pattern_type, pattern_data_str, frequency, success_rate, last_used = row

                try:
                    rule_data = json.loads(pattern_data_str)
                    self.dynamic_rules.append(rule_data)

                except json.JSONDecodeError as e:
                    print(f"解析动态规则数据失败: {e}")

        except Exception as e:
            print(f"加载动态规则失败: {e}")

    def _save_dynamic_rules(self, rules: List[Dict]):
        """保存动态规则到数据库"""
        try:
            if not self.db:
                return

            cursor = self.db.conn.cursor()

            for rule in rules:
                rule_data_str = json.dumps(rule, ensure_ascii=False)

                cursor.execute("""
                    INSERT OR REPLACE INTO ai_learning_patterns
                    (id, pattern_type, pattern_data, frequency, success_rate, last_used, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    f"dynamic_rule_{rule['id']}",
                    'dynamic_rule',
                    rule_data_str,
                    rule.get('frequency', 1),
                    rule.get('confidence', 0.5),
                    rule.get('created_at', datetime.now().isoformat()),
                    rule.get('created_at', datetime.now().isoformat())
                ))

            self.db.conn.commit()

        except Exception as e:
            print(f"保存动态规则失败: {e}")