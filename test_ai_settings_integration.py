#!/usr/bin/env python3
"""
AI设置集成测试
测试AI设置页面是否能正确集成到设置对话框中
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_settings_dialog_with_ai():
    """测试设置对话框中的AI页面"""
    print("🔍 测试设置对话框中的AI页面...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from smartvault.ui.dialogs.settings_dialog import SettingsDialog
        
        # 创建应用程序（如果还没有）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建设置对话框
        dialog = SettingsDialog()
        
        # 检查AI页面是否成功添加
        ai_page_found = False
        tab_names = []
        for i in range(dialog.tab_widget.count()):
            tab_text = dialog.tab_widget.tabText(i)
            tab_names.append(tab_text)
            if tab_text == 'AI功能':
                ai_page_found = True
        
        print(f"所有标签页: {tab_names}")
        
        if ai_page_found:
            print("✅ AI设置页面成功集成到设置对话框")
        else:
            print("❌ AI设置页面未找到")
        
        # 检查AI页面是否在pages字典中
        if 'ai' in dialog.pages:
            print("✅ AI页面已添加到pages字典")
            ai_page = dialog.pages['ai']
            print(f"AI页面类型: {type(ai_page).__name__}")
            print(f"AI页面标题: {ai_page.get_page_title()}")
            
            # 测试AI页面的基本功能
            try:
                # 测试加载配置
                test_config = {'ai': {'enabled': False, 'stage': 'rule_based'}}
                ai_page.load_settings(test_config)
                print("✅ AI页面配置加载成功")
                
                # 测试保存配置
                saved_config = ai_page.save_settings()
                print(f"✅ AI页面配置保存成功: {type(saved_config)}")
                
                # 测试页面验证
                is_valid, error_msg = ai_page.validate_settings()
                print(f"✅ AI页面验证成功: {is_valid}, {error_msg}")
                
            except Exception as e:
                print(f"⚠️ AI页面功能测试失败: {e}")
                
        else:
            print("❌ AI页面未添加到pages字典")
            
        return ai_page_found and ('ai' in dialog.pages)
        
    except Exception as e:
        print(f"❌ 设置对话框集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_page_creation():
    """测试AI页面创建"""
    print("\n🔍 测试AI页面创建...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from smartvault.ui.dialogs.settings.pages.ai.ai_main_page import AISettingsPage
        
        # 创建应用程序（如果还没有）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建AI设置页面
        ai_page = AISettingsPage()
        print("✅ AI设置页面创建成功")
        print(f"页面标题: {ai_page.get_page_title()}")
        
        # 测试页面基本方法
        try:
            test_config = {'enabled': False, 'stage': 'rule_based', 'features': {}}
            ai_page.load_settings({'ai': test_config})
            print("✅ 配置加载测试成功")
            
            saved = ai_page.save_settings()
            print(f"✅ 配置保存测试成功: {len(saved)} 项")
            
            is_valid, msg = ai_page.validate_settings()
            print(f"✅ 配置验证测试成功: {is_valid}")
            
        except Exception as e:
            print(f"⚠️ AI页面方法测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI页面创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_switch_to_ai_page():
    """测试切换到AI页面"""
    print("\n🔍 测试切换到AI页面...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from smartvault.ui.dialogs.settings_dialog import SettingsDialog
        
        # 创建应用程序（如果还没有）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建设置对话框
        dialog = SettingsDialog()
        
        # 测试切换到AI页面
        dialog.switch_to_page('ai')
        current_index = dialog.tab_widget.currentIndex()
        current_tab_text = dialog.tab_widget.tabText(current_index)
        
        if current_tab_text == 'AI功能':
            print("✅ 成功切换到AI功能页面")
            return True
        else:
            print(f"❌ 切换失败，当前页面: {current_tab_text}")
            return False
        
    except Exception as e:
        print(f"❌ 切换到AI页面失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 AI设置集成测试")
    print("=" * 60)
    
    # 测试1: AI页面创建
    test1 = test_ai_page_creation()
    
    # 测试2: 设置对话框集成
    test2 = test_settings_dialog_with_ai()
    
    # 测试3: 页面切换
    test3 = test_switch_to_ai_page()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"AI页面创建: {'✅ 通过' if test1 else '❌ 失败'}")
    print(f"设置对话框集成: {'✅ 通过' if test2 else '❌ 失败'}")
    print(f"页面切换: {'✅ 通过' if test3 else '❌ 失败'}")
    
    if all([test1, test2, test3]):
        print("\n🎉 AI设置页面集成测试全部通过！")
        print("✅ AI设置页面已成功集成到主设置对话框")
        return 0
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查")
        return 1

if __name__ == "__main__":
    sys.exit(main())
