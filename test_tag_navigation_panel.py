#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
标签导航面板测试
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PySide6.QtWidgets import QApplication, QMainWindow, QHBoxLayout, QWidget, QTextEdit
from PySide6.QtCore import QTimer

from smartvault.ui.components.tag_navigation_panel import TagNavigationPanel
from smartvault.services.tag_service import TagService
from smartvault.services.file import FileService


class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.setup_test_data()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("标签导航面板测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 布局
        layout = QHBoxLayout(central_widget)
        
        # 标签导航面板
        self.tag_panel = TagNavigationPanel()
        self.tag_panel.setMaximumWidth(250)
        self.tag_panel.tag_selected.connect(self.on_tag_selected)
        self.tag_panel.tag_filter_cleared.connect(self.on_tag_filter_cleared)
        layout.addWidget(self.tag_panel)
        
        # 右侧内容区域（模拟文件列表）
        self.content_area = QTextEdit()
        self.content_area.setPlainText("选择左侧的标签来筛选文件...")
        layout.addWidget(self.content_area)
    
    def setup_test_data(self):
        """设置测试数据"""
        print("🏷️  设置测试数据...")
        
        tag_service = TagService()
        file_service = FileService()
        
        # 创建层级标签
        work_tag = tag_service.create_tag("工作文档", "#FF9800")
        project_a = tag_service.create_tag("项目A", "#2196F3", work_tag)
        project_b = tag_service.create_tag("项目B", "#4CAF50", work_tag)
        
        personal_tag = tag_service.create_tag("个人文件", "#9C27B0")
        photos_tag = tag_service.create_tag("照片", "#E91E63", personal_tag)
        documents_tag = tag_service.create_tag("文档", "#795548", personal_tag)
        
        archive_tag = tag_service.create_tag("归档", "#9E9E9E")
        
        print(f"   ✅ 创建了层级标签结构")
        
        # 刷新标签面板
        QTimer.singleShot(1000, self.tag_panel.refresh_tags)
    
    def on_tag_selected(self, tag_id):
        """标签选择事件"""
        print(f"📋 选择了标签: {tag_id}")
        
        try:
            tag_service = TagService()
            
            # 获取标签信息
            tag = tag_service.get_tag_by_id(tag_id)
            if not tag:
                return
            
            # 获取该标签的文件
            files = tag_service.get_files_by_tag(tag_id)
            
            # 获取层级文件（包含子标签）
            hierarchy_files = tag_service.get_files_by_tag_hierarchy(tag_id)
            
            # 更新内容区域
            content = f"标签筛选结果\n"
            content += f"=" * 30 + "\n\n"
            content += f"选中标签: {tag['name']}\n"
            content += f"标签颜色: {tag.get('color', '未设置')}\n\n"
            content += f"直接关联文件: {len(files)} 个\n"
            content += f"层级关联文件: {len(hierarchy_files)} 个\n\n"
            
            if files:
                content += "直接关联的文件:\n"
                for i, file_info in enumerate(files, 1):
                    content += f"  {i}. {file_info['name']}\n"
                content += "\n"
            
            if hierarchy_files and len(hierarchy_files) != len(files):
                content += "层级关联的文件:\n"
                for i, file_info in enumerate(hierarchy_files, 1):
                    content += f"  {i}. {file_info['name']}\n"
                content += "\n"
            
            if not files and not hierarchy_files:
                content += "该标签下暂无文件。\n\n"
            
            content += "💡 提示:\n"
            content += "• 单击标签进行筛选\n"
            content += "• 双击标签展开/折叠\n"
            content += "• 右键标签查看更多选项\n"
            content += "• 点击'清除筛选'显示所有文件\n"
            
            self.content_area.setPlainText(content)
            
        except Exception as e:
            self.content_area.setPlainText(f"获取标签信息失败: {e}")
    
    def on_tag_filter_cleared(self):
        """标签筛选清除事件"""
        print("🧹 清除了标签筛选")
        
        content = "显示所有文件\n"
        content += "=" * 30 + "\n\n"
        content += "当前没有应用标签筛选，显示所有文件。\n\n"
        content += "💡 提示:\n"
        content += "• 点击左侧标签进行筛选\n"
        content += "• 使用搜索框快速查找标签\n"
        content += "• 点击'管理'按钮管理标签\n"
        
        self.content_area.setPlainText(content)


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestMainWindow()
    window.show()
    
    print("🖥️  标签导航面板测试窗口已显示")
    print("   💡 你可以测试以下功能:")
    print("      • 查看层级标签树")
    print("      • 点击标签进行筛选")
    print("      • 使用搜索框筛选标签")
    print("      • 右键标签查看选项")
    print("      • 点击'管理'按钮打开标签管理")
    print("      • 点击'清除筛选'清除选择")
    
    # 运行应用
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
