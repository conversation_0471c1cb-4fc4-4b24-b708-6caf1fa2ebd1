"""
文件备注菜单组件
用于在文件右键菜单中提供备注操作
"""

from PySide6.QtWidgets import QMessageBox
from PySide6.QtCore import QObject, Signal
from PySide6.QtGui import QAction
from smartvault.ui.localization import warning
from smartvault.services.file import FileService


class NoteMenu(QObject):
    """文件备注菜单类"""

    # 信号：备注发生变化时发出
    notes_changed = Signal()

    def __init__(self, parent=None):
        """初始化备注菜单

        Args:
            parent: 父对象
        """
        super().__init__(parent)
        self.file_service = FileService()
        self.file_ids = []

    def create_action(self, parent_menu, file_ids):
        """创建编辑备注菜单项

        Args:
            parent_menu: 父菜单
            file_ids: 文件ID列表

        Returns:
            QAction: 创建的备注菜单项
        """
        self.file_ids = file_ids if isinstance(file_ids, list) else [file_ids]

        # 根据选择的文件数量和备注状态确定菜单文本
        action_text = self._get_action_text()

        # 创建备注菜单项
        note_action = QAction(action_text, parent_menu)
        note_action.triggered.connect(self._edit_note)

        return note_action

    def _get_action_text(self):
        """获取菜单项文本

        Returns:
            str: 菜单项文本
        """
        # 始终显示简洁的菜单文本，不显示备注内容预览
        # 这样可以保持菜单的简洁性和一致性
        return "编辑备注..."

    def _edit_note(self):
        """编辑备注功能"""
        try:
            from smartvault.ui.dialogs.tag_note_dialog import TagNoteDialog

            # 如果只选择了一个文件，直接打开备注对话框
            if len(self.file_ids) == 1:
                dialog = TagNoteDialog(self.file_ids[0], parent=None)
                dialog.notes_changed.connect(self.notes_changed.emit)  # 连接备注变化信号
                if dialog.exec():
                    # 发出备注变化信号以刷新显示
                    self.notes_changed.emit()
            else:
                # 多个文件时，提示用户选择单个文件
                QMessageBox.information(
                    None,
                    "编辑备注",
                    "备注功能仅支持单个文件，请选择一个文件后再试。"
                )

        except Exception as e:
            warning(
                None,
                "编辑备注失败",
                f"无法打开备注对话框: {e}"
            )


if __name__ == "__main__":
    """测试备注菜单组件"""
    import sys
    from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QMenu

    class TestWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("备注菜单测试")
            self.setGeometry(100, 100, 400, 300)

            # 创建中央部件
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            layout = QVBoxLayout(central_widget)

            # 创建测试按钮
            self.test_button = QPushButton("右键显示备注菜单")
            self.test_button.setContextMenuPolicy(3)  # Qt.CustomContextMenu
            self.test_button.customContextMenuRequested.connect(self.show_note_menu)
            layout.addWidget(self.test_button)

            # 测试文件ID
            self.test_file_ids = ["test_file_1"]

        def show_note_menu(self, position):
            """显示备注菜单"""
            try:
                # 创建备注菜单
                note_menu = NoteMenu(self)

                # 连接备注变化信号
                note_menu.notes_changed.connect(self.on_notes_changed)

                # 创建父菜单
                menu = QMenu(self)

                # 添加备注菜单项
                note_action = note_menu.create_action(menu, self.test_file_ids)
                menu.addAction(note_action)

                # 添加分隔符和其他选项
                menu.addSeparator()
                menu.addAction("其他操作...")

                # 显示菜单
                global_pos = self.test_button.mapToGlobal(position)
                menu.exec(global_pos)

            except Exception as e:
                print(f"显示备注菜单失败: {e}")
                import traceback
                traceback.print_exc()

        def on_notes_changed(self):
            """备注变化处理"""
            print("备注已变化，刷新显示")

    app = QApplication(sys.argv)
    window = TestWindow()
    window.show()
    sys.exit(app.exec())
