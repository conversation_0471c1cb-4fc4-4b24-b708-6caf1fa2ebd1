import sys
import os
sys.path.append('.')

try:
    from smartvault.data.database import Database
    from smartvault.utils.config import load_config
    
    print("测试数据库连接...")
    
    # 检查配置
    config = load_config()
    library_path = config.get("library_path", "")
    print(f"文件库路径: {library_path}")
    
    if library_path:
        db_path = os.path.join(library_path, "data", "smartvault.db")
        print(f"数据库路径: {db_path}")
        print(f"数据库文件存在: {os.path.exists(db_path)}")
        
        # 测试直接创建数据库连接
        print("\n测试直接数据库连接...")
        db = Database(db_path)
        print(f"数据库连接成功: {db.conn is not None}")
        
        if db.conn:
            # 测试简单查询
            try:
                cursor = db.conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM files")
                count = cursor.fetchone()[0]
                print(f"数据库中文件数量: {count}")
                
                # 测试查询表结构
                cursor.execute("PRAGMA table_info(files)")
                columns = cursor.fetchall()
                print(f"files表字段数量: {len(columns)}")
                
                # 检查是否有file_hash字段
                has_file_hash = any(col[1] == 'file_hash' for col in columns)
                print(f"files表包含file_hash字段: {has_file_hash}")
                
            except Exception as e:
                print(f"查询数据库失败: {e}")
        
        db.close()
    else:
        print("❌ 文件库路径未配置")
    
except Exception as e:
    print(f"测试失败: {e}")
    import traceback
    traceback.print_exc()
