#!/usr/bin/env python3
"""
SmartVault 文件库移动问题诊断
诊断移动文件库时的资源占用问题
"""

import sys
import os
import tempfile
import shutil
import sqlite3
import psutil
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from smartvault.utils.config import load_config, save_config
from smartvault.data.file_system import FileSystem
from smartvault.services.file import FileService
from smartvault.data.database import Database


class MoveIssueDiagnoser:
    """移动问题诊断器"""
    
    def __init__(self):
        self.original_config = load_config()
        self.temp_dirs = []
        
    def check_file_locks(self, file_path):
        """检查文件是否被进程锁定"""
        try:
            # 尝试以独占模式打开文件
            with open(file_path, 'r+b') as f:
                pass
            return False, "文件未被锁定"
        except PermissionError:
            return True, "文件被锁定"
        except FileNotFoundError:
            return False, "文件不存在"
        except Exception as e:
            return True, f"检查失败: {e}"
    
    def get_process_using_file(self, file_path):
        """获取正在使用文件的进程"""
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'open_files']):
            try:
                if proc.info['open_files']:
                    for file_info in proc.info['open_files']:
                        if os.path.normpath(file_info.path) == os.path.normpath(file_path):
                            processes.append({
                                'pid': proc.info['pid'],
                                'name': proc.info['name'],
                                'path': file_info.path
                            })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return processes
    
    def diagnose_current_state(self):
        """诊断当前状态"""
        print("🔍 诊断当前文件库状态...")
        
        config = load_config()
        current_library = config["library_path"]
        db_path = os.path.join(current_library, "data", "smartvault.db")
        
        print(f"   当前文件库: {current_library}")
        print(f"   数据库文件: {db_path}")
        
        # 检查数据库文件锁定状态
        if os.path.exists(db_path):
            is_locked, lock_info = self.check_file_locks(db_path)
            print(f"   数据库锁定状态: {lock_info}")
            
            if is_locked:
                processes = self.get_process_using_file(db_path)
                if processes:
                    print("   占用进程:")
                    for proc in processes:
                        print(f"     PID: {proc['pid']}, 名称: {proc['name']}")
                else:
                    print("   无法确定占用进程")
        else:
            print("   ❌ 数据库文件不存在")
        
        return db_path
    
    def test_database_connections(self):
        """测试数据库连接状态"""
        print("\n🔍 测试数据库连接状态...")
        
        config = load_config()
        db_path = os.path.join(config["library_path"], "data", "smartvault.db")
        
        # 测试1: 直接SQLite连接
        try:
            conn = sqlite3.connect(db_path, timeout=1.0)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM files")
            count = cursor.fetchone()[0]
            conn.close()
            print(f"   ✅ 直接SQLite连接成功，文件数: {count}")
        except Exception as e:
            print(f"   ❌ 直接SQLite连接失败: {e}")
        
        # 测试2: Database类连接
        try:
            db = Database(db_path)
            cursor = db.conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM files")
            count = cursor.fetchone()[0]
            db.close()
            print(f"   ✅ Database类连接成功，文件数: {count}")
        except Exception as e:
            print(f"   ❌ Database类连接失败: {e}")
        
        # 测试3: FileService连接
        try:
            file_service = FileService()
            count = file_service.get_file_count()
            file_service.db.close()
            print(f"   ✅ FileService连接成功，文件数: {count}")
        except Exception as e:
            print(f"   ❌ FileService连接失败: {e}")
    
    def simulate_move_operation(self):
        """模拟移动操作"""
        print("\n🔍 模拟文件库移动操作...")
        
        # 创建源文件库
        source_temp = tempfile.mkdtemp(prefix="smartvault_source_")
        self.temp_dirs.append(source_temp)
        
        file_system = FileSystem()
        success, source_library, details = file_system.create_library(source_temp)
        
        if not success:
            print(f"❌ 创建源文件库失败: {details}")
            return
        
        print(f"   源文件库: {source_library}")
        
        # 设置为当前文件库
        config = load_config()
        config["library_path"] = source_library
        save_config(config)
        
        # 创建FileService实例（模拟主程序状态）
        file_service = FileService()
        file_count = file_service.get_file_count()
        print(f"   源文件库文件数: {file_count}")
        
        # 检查数据库文件锁定状态
        source_db_path = os.path.join(source_library, "data", "smartvault.db")
        is_locked, lock_info = self.check_file_locks(source_db_path)
        print(f"   移动前数据库锁定状态: {lock_info}")
        
        # 创建目标目录
        target_temp = tempfile.mkdtemp(prefix="smartvault_target_")
        self.temp_dirs.append(target_temp)
        
        print(f"   目标目录: {target_temp}")
        
        # 模拟移动操作（不关闭数据库连接）
        print("   开始移动操作（不关闭数据库连接）...")
        
        try:
            # 这里模拟FileSystem.move_library的操作
            # 但不关闭数据库连接
            
            # 检查移动过程中的锁定状态
            is_locked_during, lock_info_during = self.check_file_locks(source_db_path)
            print(f"   移动过程中数据库锁定状态: {lock_info_during}")
            
            # 尝试移动数据库文件
            target_db_path = os.path.join(target_temp, "data", "smartvault.db")
            os.makedirs(os.path.dirname(target_db_path), exist_ok=True)
            
            try:
                shutil.move(source_db_path, target_db_path)
                print("   ✅ 数据库文件移动成功")
            except Exception as e:
                print(f"   ❌ 数据库文件移动失败: {e}")
                
                # 尝试复制
                try:
                    shutil.copy2(source_db_path, target_db_path)
                    print("   ✅ 数据库文件复制成功")
                except Exception as e2:
                    print(f"   ❌ 数据库文件复制也失败: {e2}")
        
        finally:
            # 关闭数据库连接
            file_service.db.close()
            print("   数据库连接已关闭")
            
            # 再次检查锁定状态
            if os.path.exists(source_db_path):
                is_locked_after, lock_info_after = self.check_file_locks(source_db_path)
                print(f"   关闭连接后数据库锁定状态: {lock_info_after}")
    
    def test_proper_move_sequence(self):
        """测试正确的移动序列"""
        print("\n🔍 测试正确的移动序列...")
        
        # 创建测试文件库
        test_temp = tempfile.mkdtemp(prefix="smartvault_proper_")
        self.temp_dirs.append(test_temp)
        
        file_system = FileSystem()
        success, test_library, details = file_system.create_library(test_temp)
        
        if not success:
            print(f"❌ 创建测试文件库失败: {details}")
            return
        
        print(f"   测试文件库: {test_library}")
        
        # 设置为当前文件库
        config = load_config()
        config["library_path"] = test_library
        save_config(config)
        
        # 创建FileService并立即关闭
        file_service = FileService()
        file_count = file_service.get_file_count()
        print(f"   文件数: {file_count}")
        
        # 关键：在移动前关闭所有数据库连接
        file_service.db.close()
        print("   ✅ 数据库连接已关闭")
        
        # 检查锁定状态
        test_db_path = os.path.join(test_library, "data", "smartvault.db")
        is_locked, lock_info = self.check_file_locks(test_db_path)
        print(f"   关闭后数据库锁定状态: {lock_info}")
        
        # 创建目标目录
        target_temp = tempfile.mkdtemp(prefix="smartvault_proper_target_")
        self.temp_dirs.append(target_temp)
        
        # 尝试移动
        try:
            target_db_path = os.path.join(target_temp, "data", "smartvault.db")
            os.makedirs(os.path.dirname(target_db_path), exist_ok=True)
            
            shutil.move(test_db_path, target_db_path)
            print("   ✅ 正确序列下数据库文件移动成功")
            
            # 尝试删除源目录
            shutil.rmtree(test_library)
            print("   ✅ 源目录删除成功")
            
        except Exception as e:
            print(f"   ❌ 移动失败: {e}")
    
    def cleanup(self):
        """清理测试环境"""
        print("\n🧹 清理测试环境...")
        
        # 恢复原始配置
        save_config(self.original_config)
        
        # 删除临时目录
        for temp_dir in self.temp_dirs:
            try:
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
                    print(f"   ✅ 已删除: {temp_dir}")
            except Exception as e:
                print(f"   ⚠️  删除失败: {temp_dir} - {e}")
    
    def run_diagnosis(self):
        """运行完整诊断"""
        print("🚀 SmartVault 文件库移动问题诊断")
        print("=" * 60)
        
        try:
            self.diagnose_current_state()
            self.test_database_connections()
            self.simulate_move_operation()
            self.test_proper_move_sequence()
            
        finally:
            self.cleanup()
        
        print("\n" + "=" * 60)
        print("📊 诊断结论")
        print("=" * 60)
        print("如果看到数据库锁定问题，说明需要在移动前关闭所有数据库连接。")
        print("建议的修复方案：")
        print("1. 在移动前关闭主程序的数据库连接")
        print("2. 在移动线程中不要创建数据库连接")
        print("3. 移动完成后重新建立数据库连接")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    try:
        diagnoser = MoveIssueDiagnoser()
        diagnoser.run_diagnosis()
        
        # 自动退出
        QTimer.singleShot(1000, app.quit)
        app.exec()
        
    except Exception as e:
        print(f"❌ 诊断过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
