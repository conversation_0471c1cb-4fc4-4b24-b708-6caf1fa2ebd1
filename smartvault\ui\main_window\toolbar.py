"""
主窗口工具栏管理
"""

from PySide6.QtWidgets import QToolBar, QWidget, QHBoxLayout, QSizePolicy
from PySide6.QtGui import QIcon, QAction
from PySide6.QtCore import Qt
from smartvault.ui.resources import get_icon
from smartvault.ui.widgets.compact_progress_bar import CompactProgressBarWidget


class ToolbarManager:
    """工具栏管理器类"""

    def __init__(self, main_window):
        """初始化工具栏管理器

        Args:
            main_window: 主窗口实例
        """
        self.main_window = main_window

        # 创建主工具栏
        self.main_toolbar = QToolBar("主工具栏", main_window)
        self.main_toolbar.setMovable(False)
        main_window.addToolBar(self.main_toolbar)

        # 添加工具栏按钮
        self.create_toolbar_actions()

        # 添加右侧按钮和进度条
        self.create_right_section()

        # 保存进度条引用到主窗口
        main_window.compact_progress_bar = self.progress_bar



    def create_toolbar_actions(self):
        """创建工具栏按钮"""
        # 文件监控总开关（放在最左侧，最重要的功能）
        self.create_monitor_toggle_button()

        # 剪贴板查重按钮（第二重要的功能）
        self.create_clipboard_monitor_button()

        # 添加分隔符
        self.main_toolbar.addSeparator()

        # 创建添加文件按钮（与剪贴板查重按钮样式一致）
        self.create_add_file_button()

        # 创建添加文件夹按钮（与剪贴板查重按钮样式一致）
        self.create_add_folder_button()

        # 移除视图切换按钮，因为文件视图已经有选项卡按钮
        # 移除搜索按钮，搜索功能已集成在文件视图中

    def create_right_section(self):
        """创建右侧区域（进度条、帮助按钮、退出按钮）"""
        # 添加弹性空间，将右侧组件推到右边
        spacer = QWidget()
        spacer.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.main_toolbar.addWidget(spacer)

        # 创建紧凑型进度条
        self.progress_container = QWidget()
        self.progress_layout = QHBoxLayout(self.progress_container)
        self.progress_layout.setContentsMargins(5, 0, 5, 0)
        self.progress_layout.setSpacing(0)

        # 创建进度条组件
        self.progress_bar = CompactProgressBarWidget(self.progress_container)
        self.progress_layout.addWidget(self.progress_bar)

        # 将进度条容器添加到工具栏
        self.main_toolbar.addWidget(self.progress_container)

        # 添加分隔符
        self.main_toolbar.addSeparator()

        # 创建帮助按钮（与其他按钮样式一致）
        self.create_help_button()

        # 创建退出按钮（与其他按钮样式一致）
        self.create_exit_button()

    def create_monitor_toggle_button(self):
        """创建文件监控总开关按钮"""
        from PySide6.QtWidgets import QPushButton
        from PySide6.QtCore import Qt

        # 创建监控开关按钮
        self.monitor_toggle_button = QPushButton()
        self.monitor_toggle_button.setCheckable(True)
        self.monitor_toggle_button.setToolTip("文件监控总开关")
        self.monitor_toggle_button.clicked.connect(self.on_monitor_toggle)

        # 设置按钮样式
        self.monitor_toggle_button.setMaximumWidth(120)
        self.monitor_toggle_button.setMinimumWidth(100)

        # 初始化按钮状态
        self.update_monitor_status()

        # 添加到工具栏
        self.main_toolbar.addWidget(self.monitor_toggle_button)

    def on_monitor_toggle(self):
        """处理监控开关点击事件"""
        try:
            # 调用主窗口的监控切换方法
            if hasattr(self.main_window, 'toggle_all_monitors'):
                self.main_window.toggle_all_monitors()
            else:
                print("主窗口没有toggle_all_monitors方法")
        except Exception as e:
            print(f"切换监控状态失败: {e}")

    def update_monitor_status(self):
        """更新监控状态显示"""
        try:
            if not hasattr(self, 'monitor_toggle_button'):
                return

            # 获取监控统计信息
            if hasattr(self.main_window, 'monitor_service'):
                stats = self.main_window.monitor_service.get_monitor_statistics()

                running_count = stats['running_monitors']
                total_count = stats['total_monitors']

                # 更新按钮状态和文本
                if running_count > 0:
                    self.monitor_toggle_button.setChecked(True)
                    self.monitor_toggle_button.setText("文件夹监控")
                    self.monitor_toggle_button.setStyleSheet("""
                        QPushButton {
                            background-color: #4CAF50;
                            color: white;
                            border: 1px solid #45a049;
                            border-radius: 3px;
                            padding: 4px 8px;
                        }
                        QPushButton:hover {
                            background-color: #45a049;
                        }
                    """)
                    self.monitor_toggle_button.setToolTip(f"文件夹监控已启动 ({running_count}/{total_count} 活动)\n点击停止所有监控")
                else:
                    self.monitor_toggle_button.setChecked(False)
                    self.monitor_toggle_button.setText("文件夹监控")
                    self.monitor_toggle_button.setStyleSheet("""
                        QPushButton {
                            background-color: #9e9e9e;
                            color: white;
                            border: 1px solid #757575;
                            border-radius: 3px;
                            padding: 4px 8px;
                        }
                        QPushButton:hover {
                            background-color: #757575;
                        }
                    """)
                    self.monitor_toggle_button.setToolTip(f"文件夹监控已停止 (共 {total_count} 个配置)\n点击启动所有监控")
            else:
                # 没有监控服务时的默认状态
                self.monitor_toggle_button.setChecked(False)
                self.monitor_toggle_button.setText("监控 0/0")
                self.monitor_toggle_button.setStyleSheet("")
                self.monitor_toggle_button.setToolTip("监控服务未初始化")

        except Exception as e:
            print(f"更新监控状态显示失败: {e}")
            # 设置错误状态
            if hasattr(self, 'monitor_toggle_button'):
                self.monitor_toggle_button.setChecked(False)
                self.monitor_toggle_button.setText("监控错误")
                self.monitor_toggle_button.setStyleSheet("""
                    QPushButton {
                        background-color: #ff9800;
                        color: white;
                        border: 1px solid #f57c00;
                        border-radius: 3px;
                        padding: 4px 8px;
                    }
                """)
                self.monitor_toggle_button.setToolTip(f"监控状态获取失败: {e}")

    def create_clipboard_monitor_button(self):
        """创建剪贴板查重按钮"""
        from PySide6.QtWidgets import QPushButton

        # 创建剪贴板监控按钮
        self.clipboard_button = QPushButton()
        self.clipboard_button.setCheckable(True)
        self.clipboard_button.setText("剪贴板查重")
        self.clipboard_button.setToolTip("监控剪贴板文件和文件名查重")
        self.clipboard_button.setMaximumWidth(100)
        self.clipboard_button.setMinimumWidth(80)
        self.clipboard_button.clicked.connect(self.on_clipboard_toggle)

        # 设置初始样式
        self.update_clipboard_status(False)

        # 添加到工具栏
        self.main_toolbar.addWidget(self.clipboard_button)

    def create_add_file_button(self):
        """创建添加文件按钮"""
        from PySide6.QtWidgets import QPushButton

        # 创建添加文件按钮
        self.add_file_button = QPushButton()
        self.add_file_button.setText("添加文件")
        self.add_file_button.setToolTip("添加文件到智能文件库")
        self.add_file_button.setMaximumWidth(100)
        self.add_file_button.setMinimumWidth(80)
        self.add_file_button.clicked.connect(self.main_window.on_add_file)

        # 设置样式（与剪贴板查重按钮一致）
        self.add_file_button.setStyleSheet("""
            QPushButton {
                background-color: #2196f3 !important;
                color: white !important;
                border: 1px solid #1976d2 !important;
                border-radius: 3px !important;
                padding: 4px 8px !important;
                font-size: 12px !important;
                font-weight: normal !important;
            }
            QPushButton:hover {
                background-color: #1976d2 !important;
                border-color: #1565c0 !important;
            }
            QPushButton:pressed {
                background-color: #1565c0 !important;
            }
        """)

        # 添加到工具栏
        self.main_toolbar.addWidget(self.add_file_button)

    def create_add_folder_button(self):
        """创建添加文件夹按钮"""
        from PySide6.QtWidgets import QPushButton

        # 创建添加文件夹按钮
        self.add_folder_button = QPushButton()
        self.add_folder_button.setText("添加文件夹")
        self.add_folder_button.setToolTip("添加文件夹到智能文件库")
        self.add_folder_button.setMaximumWidth(100)
        self.add_folder_button.setMinimumWidth(80)
        self.add_folder_button.clicked.connect(self.main_window.on_add_folder)

        # 设置样式（与剪贴板查重按钮一致）
        self.add_folder_button.setStyleSheet("""
            QPushButton {
                background-color: #ff9800 !important;
                color: white !important;
                border: 1px solid #f57c00 !important;
                border-radius: 3px !important;
                padding: 4px 8px !important;
                font-size: 12px !important;
                font-weight: normal !important;
            }
            QPushButton:hover {
                background-color: #f57c00 !important;
                border-color: #ef6c00 !important;
            }
            QPushButton:pressed {
                background-color: #ef6c00 !important;
            }
        """)

        # 添加到工具栏
        self.main_toolbar.addWidget(self.add_folder_button)

    def create_help_button(self):
        """创建帮助按钮"""
        from PySide6.QtWidgets import QPushButton

        # 创建帮助按钮
        self.help_button = QPushButton()
        self.help_button.setText("帮助")
        self.help_button.setToolTip("打开用户帮助 (F1)")
        self.help_button.setMaximumWidth(100)
        self.help_button.setMinimumWidth(80)
        self.help_button.clicked.connect(self.main_window.on_help)

        # 设置样式（与其他按钮一致）
        self.help_button.setStyleSheet("""
            QPushButton {
                background-color: #607d8b !important;
                color: white !important;
                border: 1px solid #455a64 !important;
                border-radius: 3px !important;
                padding: 4px 8px !important;
                font-size: 12px !important;
                font-weight: normal !important;
            }
            QPushButton:hover {
                background-color: #455a64 !important;
                border-color: #37474f !important;
            }
            QPushButton:pressed {
                background-color: #37474f !important;
            }
        """)

        # 添加到工具栏
        self.main_toolbar.addWidget(self.help_button)

    def create_exit_button(self):
        """创建退出按钮"""
        from PySide6.QtWidgets import QPushButton

        # 创建退出按钮
        self.exit_button = QPushButton()
        self.exit_button.setText("退出")
        self.exit_button.setToolTip("退出SmartVault")
        self.exit_button.setMaximumWidth(100)
        self.exit_button.setMinimumWidth(80)
        self.exit_button.clicked.connect(self.main_window.close)

        # 设置样式（与其他按钮一致）
        self.exit_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336 !important;
                color: white !important;
                border: 1px solid #d32f2f !important;
                border-radius: 3px !important;
                padding: 4px 8px !important;
                font-size: 12px !important;
                font-weight: normal !important;
            }
            QPushButton:hover {
                background-color: #d32f2f !important;
                border-color: #c62828 !important;
            }
            QPushButton:pressed {
                background-color: #c62828 !important;
            }
        """)

        # 添加到工具栏
        self.main_toolbar.addWidget(self.exit_button)

    def on_clipboard_toggle(self):
        """处理剪贴板监控开关点击事件"""
        try:
            # 调用主窗口的剪贴板监控切换方法
            if hasattr(self.main_window, 'toggle_clipboard_monitor'):
                self.main_window.toggle_clipboard_monitor()
            else:
                print("主窗口没有toggle_clipboard_monitor方法")
        except Exception as e:
            print(f"切换剪贴板监控状态失败: {e}")

    def update_clipboard_status(self, is_active: bool):
        """更新剪贴板监控状态显示"""
        try:
            if not hasattr(self, 'clipboard_button'):
                return

            if is_active:
                self.clipboard_button.setChecked(True)
                self.clipboard_button.setStyleSheet("""
                    QPushButton {
                        background-color: #4CAF50 !important;
                        color: white !important;
                        border: 1px solid #45a049 !important;
                        border-radius: 3px !important;
                        padding: 4px 8px !important;
                        font-size: 12px !important;
                        font-weight: normal !important;
                    }
                    QPushButton:hover {
                        background-color: #45a049 !important;
                        border-color: #388e3c !important;
                    }
                    QPushButton:pressed {
                        background-color: #388e3c !important;
                    }
                """)
                self.clipboard_button.setToolTip("剪贴板查重已启动\n点击停止监控")
            else:
                self.clipboard_button.setChecked(False)
                self.clipboard_button.setStyleSheet("""
                    QPushButton {
                        background-color: #9e9e9e !important;
                        color: white !important;
                        border: 1px solid #757575 !important;
                        border-radius: 3px !important;
                        padding: 4px 8px !important;
                        font-size: 12px !important;
                        font-weight: normal !important;
                    }
                    QPushButton:hover {
                        background-color: #757575 !important;
                        border-color: #616161 !important;
                    }
                    QPushButton:pressed {
                        background-color: #616161 !important;
                    }
                """)
                self.clipboard_button.setToolTip("剪贴板查重已停止\n点击启动监控")

        except Exception as e:
            print(f"更新剪贴板状态显示失败: {e}")
