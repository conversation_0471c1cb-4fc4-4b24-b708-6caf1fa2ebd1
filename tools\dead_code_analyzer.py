#!/usr/bin/env python3
"""
SmartVault 死代码分析工具
专门用于识别明显的死代码方法
"""

import os
import ast
import re
from pathlib import Path
from typing import Dict, List, Set
from collections import defaultdict

class DeadCodeAnalyzer:
    """死代码分析器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.python_files = []
        self.all_methods = {}  # method_name -> [file_info, ...]
        self.all_calls = set()
        self.obvious_dead_code = []
        self.suspicious_dead_code = []
        
    def scan_smartvault_files(self):
        """扫描SmartVault项目文件"""
        print("🔍 扫描SmartVault项目文件...")
        
        smartvault_dir = self.project_root / "smartvault"
        if smartvault_dir.exists():
            for file_path in smartvault_dir.rglob("*.py"):
                if not any(skip in str(file_path) for skip in ['__pycache__', '.pyc']):
                    self.python_files.append(file_path)
        
        print(f"📊 找到 {len(self.python_files)} 个Python文件")
        
    def analyze_methods_and_calls(self):
        """分析方法定义和调用"""
        print("🔍 分析方法定义和调用...")
        
        # 第一遍：收集所有方法定义
        for file_path in self.python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    tree = ast.parse(content)
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.FunctionDef):
                        method_name = node.name
                        if method_name not in self.all_methods:
                            self.all_methods[method_name] = []
                        
                        self.all_methods[method_name].append({
                            'file': str(file_path.relative_to(self.project_root)),
                            'line': node.lineno,
                            'is_private': method_name.startswith('_'),
                            'is_dunder': method_name.startswith('__') and method_name.endswith('__'),
                            'is_test': method_name.startswith('test_'),
                            'is_init': method_name in ['__init__', '__new__'],
                            'is_signal_slot': any(keyword in method_name.lower() 
                                                for keyword in ['signal', 'slot', 'connect', 'emit', 'on_']),
                            'docstring': ast.get_docstring(node) or "",
                            'content': content.split('\n')[node.lineno-1:node.end_lineno] if hasattr(node, 'end_lineno') else []
                        })
            
            except Exception as e:
                print(f"⚠️ 解析文件失败: {file_path} - {e}")
        
        # 第二遍：收集所有方法调用
        for file_path in self.python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    tree = ast.parse(content)
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.Call):
                        if isinstance(node.func, ast.Name):
                            self.all_calls.add(node.func.id)
                        elif isinstance(node.func, ast.Attribute):
                            self.all_calls.add(node.func.attr)
                    elif isinstance(node, ast.Attribute):
                        # 处理属性访问，可能是方法引用
                        self.all_calls.add(node.attr)
                
                # 检查字符串中的方法引用（动态调用）
                for line in content.split('\n'):
                    # 查找字符串中可能的方法名
                    string_matches = re.findall(r'["\']([a-zA-Z_][a-zA-Z0-9_]*)["\']', line)
                    for match in string_matches:
                        if match in self.all_methods:
                            self.all_calls.add(match)
                            
            except Exception as e:
                print(f"⚠️ 分析调用失败: {file_path} - {e}")
        
        print(f"📊 发现 {len(self.all_methods)} 个方法定义")
        print(f"📊 发现 {len(self.all_calls)} 个方法调用")
    
    def classify_dead_code(self):
        """分类死代码"""
        print("🔍 分类死代码...")
        
        for method_name, method_infos in self.all_methods.items():
            if method_name not in self.all_calls:
                for method_info in method_infos:
                    # 跳过特殊方法
                    if (method_info['is_dunder'] or 
                        method_info['is_init'] or 
                        method_info['is_test'] or
                        method_name in ['main', 'run']):
                        continue
                    
                    # 分类为明显死代码或可疑死代码
                    if self._is_obvious_dead_code(method_name, method_info):
                        self.obvious_dead_code.append({
                            'method': method_name,
                            'file': method_info['file'],
                            'line': method_info['line'],
                            'reason': self._get_obvious_reason(method_name, method_info)
                        })
                    else:
                        self.suspicious_dead_code.append({
                            'method': method_name,
                            'file': method_info['file'],
                            'line': method_info['line'],
                            'reason': self._get_suspicious_reason(method_name, method_info)
                        })
        
        print(f"📊 明显死代码: {len(self.obvious_dead_code)} 个")
        print(f"📊 可疑死代码: {len(self.suspicious_dead_code)} 个")
    
    def _is_obvious_dead_code(self, method_name, method_info):
        """判断是否为明显的死代码"""
        # 私有方法且不是信号槽相关
        if (method_info['is_private'] and 
            not method_info['is_signal_slot'] and
            not method_name.startswith('_on_') and
            not method_name.startswith('_handle_')):
            return True
        
        # 明显的调试方法
        if any(keyword in method_name.lower() for keyword in ['debug', 'test_', 'temp_', 'old_']):
            return True
        
        # 文档字符串明确标记为废弃
        docstring = method_info.get('docstring', '').lower()
        if any(keyword in docstring for keyword in ['废弃', 'deprecated', '不再使用', 'unused']):
            return True
        
        # 方法体很简单（只有pass或注释）
        content = method_info.get('content', [])
        if len(content) <= 3:  # 方法定义 + pass/return + 可能的注释
            non_empty_lines = [line.strip() for line in content if line.strip() and not line.strip().startswith('#')]
            if len(non_empty_lines) <= 2:  # 只有def行和pass/return
                return True
        
        return False
    
    def _get_obvious_reason(self, method_name, method_info):
        """获取明显死代码的原因"""
        if method_info['is_private'] and not method_info['is_signal_slot']:
            return "私有方法且无调用"
        if 'debug' in method_name.lower():
            return "调试方法"
        if 'temp' in method_name.lower():
            return "临时方法"
        if 'deprecated' in method_info.get('docstring', '').lower():
            return "已标记为废弃"
        return "简单方法且无调用"
    
    def _get_suspicious_reason(self, method_name, method_info):
        """获取可疑死代码的原因"""
        if method_info['is_signal_slot']:
            return "可能是信号槽方法，需要验证"
        if not method_info['is_private']:
            return "公共方法但无明显调用"
        if method_name.startswith('_on_'):
            return "事件处理方法，可能通过Qt连接"
        return "需要进一步验证"
    
    def generate_report(self):
        """生成分析报告"""
        report = []
        report.append("# SmartVault 死代码分析报告")
        report.append(f"分析时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"分析文件数: {len(self.python_files)}")
        report.append("")
        
        # 明显死代码
        report.append("## 🚨 明显死代码 (建议立即删除)")
        report.append(f"发现 {len(self.obvious_dead_code)} 个明显的死代码方法：")
        report.append("")
        
        for item in self.obvious_dead_code:
            report.append(f"- `{item['method']}()` in {item['file']}:{item['line']}")
            report.append(f"  理由: {item['reason']}")
        
        report.append("")
        
        # 可疑死代码
        report.append("## ⚠️ 可疑死代码 (需要验证)")
        report.append(f"发现 {len(self.suspicious_dead_code)} 个可疑的死代码方法：")
        report.append("")
        
        for item in self.suspicious_dead_code[:10]:  # 只显示前10个
            report.append(f"- `{item['method']}()` in {item['file']}:{item['line']}")
            report.append(f"  理由: {item['reason']}")
        
        if len(self.suspicious_dead_code) > 10:
            report.append(f"... 还有 {len(self.suspicious_dead_code) - 10} 个")
        
        report.append("")
        
        # 删除建议
        report.append("## 💡 删除建议")
        report.append("### 第一批 (低风险)")
        obvious_count = len(self.obvious_dead_code)
        report.append(f"建议立即删除 {obvious_count} 个明显死代码方法")
        
        report.append("### 第二批 (需要验证)")
        suspicious_count = len(self.suspicious_dead_code)
        report.append(f"需要逐个验证 {suspicious_count} 个可疑方法")
        
        return "\n".join(report)
    
    def run_analysis(self):
        """运行完整分析"""
        self.scan_smartvault_files()
        self.analyze_methods_and_calls()
        self.classify_dead_code()
        return self.generate_report()

def main():
    """主函数"""
    print("SmartVault 死代码分析工具")
    print("=" * 50)
    
    analyzer = DeadCodeAnalyzer(".")
    report = analyzer.run_analysis()
    
    # 保存报告
    report_file = "dead_code_analysis_report.md"
    with open(report_file, "w", encoding="utf-8") as f:
        f.write(report)
    
    print(f"\n📄 分析报告已保存到 {report_file}")
    print("\n" + "="*50)
    print("分析摘要:")
    print(f"- 明显死代码: {len(analyzer.obvious_dead_code)} 个")
    print(f"- 可疑死代码: {len(analyzer.suspicious_dead_code)} 个")
    
    return analyzer

if __name__ == "__main__":
    main()
