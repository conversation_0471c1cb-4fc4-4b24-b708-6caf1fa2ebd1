#!/usr/bin/env python3
"""
AI功能集成测试
测试AI设置页面是否能正确集成到设置对话框中
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ai_page_import():
    """测试AI设置页面导入"""
    try:
        from smartvault.ui.dialogs.settings.pages.ai.ai_main_page import AISettingsPage
        print("✅ AISettingsPage导入成功")
        
        # 测试创建实例
        page = AISettingsPage()
        print(f"✅ AISettingsPage实例创建成功: {type(page).__name__}")
        print(f"页面标题: {page.get_page_title()}")
        return True
        
    except Exception as e:
        print(f"❌ AISettingsPage导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_settings_dialog_integration():
    """测试设置对话框集成"""
    try:
        from PySide6.QtWidgets import QApplication
        from smartvault.ui.dialogs.settings_dialog import SettingsDialog
        
        # 创建应用程序（如果还没有）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建设置对话框
        dialog = SettingsDialog()
        
        # 检查AI页面是否成功添加
        ai_page_found = False
        tab_names = []
        for i in range(dialog.tab_widget.count()):
            tab_text = dialog.tab_widget.tabText(i)
            tab_names.append(tab_text)
            if tab_text == 'AI功能':
                ai_page_found = True
        
        print(f"所有标签页: {tab_names}")
        
        if ai_page_found:
            print("✅ AI设置页面成功集成到设置对话框")
        else:
            print("❌ AI设置页面未找到")
        
        # 检查AI页面是否在pages字典中
        if 'ai' in dialog.pages:
            print("✅ AI页面已添加到pages字典")
            ai_page = dialog.pages['ai']
            print(f"AI页面类型: {type(ai_page).__name__}")
            print(f"AI页面标题: {ai_page.get_page_title()}")
        else:
            print("❌ AI页面未添加到pages字典")
            
        return ai_page_found and ('ai' in dialog.pages)
        
    except Exception as e:
        print(f"❌ 设置对话框集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_config_manager():
    """测试AI配置管理器"""
    try:
        from smartvault.ui.dialogs.settings.pages.ai.utils.ai_config_helper import AIConfigManager
        
        manager = AIConfigManager()
        print("✅ AI配置管理器创建成功")
        
        # 测试加载配置
        config = manager.load_ai_config()
        print(f"✅ AI配置加载成功，配置项数量: {len(config)}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI配置管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔍 AI功能集成测试开始")
    print("=" * 60)
    
    # 测试1: AI页面导入
    print("\n📋 测试1: AI设置页面导入")
    test1_result = test_ai_page_import()
    
    # 测试2: AI配置管理器
    print("\n📋 测试2: AI配置管理器")
    test2_result = test_ai_config_manager()
    
    # 测试3: 设置对话框集成
    print("\n📋 测试3: 设置对话框集成")
    test3_result = test_settings_dialog_integration()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"AI页面导入: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"AI配置管理器: {'✅ 通过' if test2_result else '❌ 失败'}")
    print(f"设置对话框集成: {'✅ 通过' if test3_result else '❌ 失败'}")
    
    if all([test1_result, test2_result, test3_result]):
        print("\n🎉 所有测试通过！AI设置页面集成成功")
        return 0
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查")
        return 1

if __name__ == "__main__":
    sys.exit(main())
