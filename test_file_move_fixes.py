#!/usr/bin/env python3
"""
测试文件移动修复功能
验证中转状态清除和文件夹标签移除的逻辑
"""

import sys
import os
import tempfile
import shutil

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_staging_status_clear():
    """测试中转状态清除功能"""
    print("🧪 测试中转状态清除功能")
    
    try:
        from smartvault.services.file.operations import FileOperations
        from smartvault.services.database import DatabaseService
        from smartvault.utils.config import load_config
        
        # 获取配置
        config = load_config()
        library_path = config.get("library_path", "")
        
        if not library_path or not os.path.exists(library_path):
            print("❌ 智能文件库路径未配置或不存在")
            return False
            
        # 初始化服务
        db_service = DatabaseService()
        file_ops = FileOperations(db_service)
        
        # 创建测试文件
        test_file_name = "test_staging_clear.txt"
        staging_folder = os.path.join(library_path, "中转")
        os.makedirs(staging_folder, exist_ok=True)
        
        test_file_path = os.path.join(staging_folder, test_file_name)
        with open(test_file_path, 'w', encoding='utf-8') as f:
            f.write("测试文件内容")
            
        print(f"📁 创建测试文件: {test_file_path}")
        
        # 模拟文件在数据库中的记录（假设已存在）
        # 这里只是测试逻辑，实际使用时文件应该已经在数据库中
        
        # 创建目标文件夹
        target_folder = os.path.join(library_path, "测试文件夹")
        os.makedirs(target_folder, exist_ok=True)
        
        # 测试路径检查逻辑
        old_path = test_file_path
        new_path = os.path.join(target_folder, test_file_name)
        
        # 规范化路径
        library_path_norm = os.path.normpath(library_path)
        old_path_norm = os.path.normpath(old_path)
        new_path_norm = os.path.normpath(new_path)
        
        # 检查是否从中转区域移出
        staging_folder_norm = os.path.normpath(staging_folder)
        
        old_in_staging = old_path_norm.startswith(staging_folder_norm)
        new_in_staging = new_path_norm.startswith(staging_folder_norm)
        
        print(f"📊 路径检查结果:")
        print(f"   原路径在中转文件夹: {old_in_staging}")
        print(f"   新路径在中转文件夹: {new_in_staging}")
        print(f"   应该清除中转状态: {old_in_staging and not new_in_staging}")
        
        # 清理测试文件
        if os.path.exists(test_file_path):
            os.remove(test_file_path)
        if os.path.exists(target_folder):
            shutil.rmtree(target_folder, ignore_errors=True)
            
        print("✅ 中转状态清除逻辑测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 中转状态清除测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_folder_tag_removal():
    """测试文件夹标签移除功能"""
    print("\n🧪 测试文件夹标签移除功能")
    
    try:
        from smartvault.utils.config import load_config
        
        # 获取配置
        config = load_config()
        library_path = config.get("library_path", "")
        
        if not library_path or not os.path.exists(library_path):
            print("❌ 智能文件库路径未配置或不存在")
            return False
            
        # 创建测试文件夹
        test_folder_name = "测试文件夹"
        test_folder_path = os.path.join(library_path, test_folder_name)
        os.makedirs(test_folder_path, exist_ok=True)
        
        # 创建测试文件
        test_file_name = "test_folder_tag.txt"
        test_file_path = os.path.join(test_folder_path, test_file_name)
        with open(test_file_path, 'w', encoding='utf-8') as f:
            f.write("测试文件内容")
            
        print(f"📁 创建测试文件: {test_file_path}")
        
        # 创建目标文件夹
        target_folder = os.path.join(library_path, "目标文件夹")
        os.makedirs(target_folder, exist_ok=True)
        
        # 测试路径检查逻辑
        old_path = test_file_path
        new_path = os.path.join(target_folder, test_file_name)
        
        # 规范化路径
        library_path_norm = os.path.normpath(library_path)
        old_path_norm = os.path.normpath(old_path)
        new_path_norm = os.path.normpath(new_path)
        
        # 模拟文件夹标签检查
        folder_name = test_folder_name
        folder_path = os.path.join(library_path, folder_name)
        folder_path_norm = os.path.normpath(folder_path)
        
        old_in_folder = old_path_norm.startswith(folder_path_norm)
        new_in_folder = new_path_norm.startswith(folder_path_norm)
        
        print(f"📊 文件夹标签检查结果:")
        print(f"   文件夹名称: {folder_name}")
        print(f"   原路径在文件夹内: {old_in_folder}")
        print(f"   新路径在文件夹内: {new_in_folder}")
        print(f"   应该移除文件夹标签: {old_in_folder and not new_in_folder}")
        
        # 清理测试文件
        if os.path.exists(test_file_path):
            os.remove(test_file_path)
        if os.path.exists(test_folder_path):
            shutil.rmtree(test_folder_path, ignore_errors=True)
        if os.path.exists(target_folder):
            shutil.rmtree(target_folder, ignore_errors=True)
            
        print("✅ 文件夹标签移除逻辑测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 文件夹标签移除测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_path_normalization():
    """测试路径规范化功能"""
    print("\n🧪 测试路径规范化功能")
    
    try:
        # 测试不同格式的路径
        test_paths = [
            "C:\\Users\\<USER>\\Documents",
            "C:/Users/<USER>/Documents",
            "C:\\Users\\<USER>\\Documents\\",
            "C:/Users/<USER>/Documents/",
            "C:\\Users\\<USER>\\Documents\\..\\Documents",
        ]
        
        print("📊 路径规范化测试:")
        for path in test_paths:
            normalized = os.path.normpath(path)
            print(f"   原路径: {path}")
            print(f"   规范化: {normalized}")
            print()
            
        # 测试路径包含关系
        parent_path = "C:\\Users\\<USER>\\Documents"
        child_path = "C:/Users/<USER>/Documents/subfolder/file.txt"
        
        parent_norm = os.path.normpath(parent_path)
        child_norm = os.path.normpath(child_path)
        
        is_child = child_norm.startswith(parent_norm)
        
        print(f"📊 路径包含关系测试:")
        print(f"   父路径: {parent_norm}")
        print(f"   子路径: {child_norm}")
        print(f"   包含关系: {is_child}")
        
        print("✅ 路径规范化测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 路径规范化测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始文件移动修复功能测试")
    print("=" * 50)
    
    # 运行测试
    tests = [
        test_path_normalization,
        test_staging_status_clear,
        test_folder_tag_removal,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！文件移动修复功能正常")
    else:
        print("⚠️  部分测试失败，请检查相关功能")

if __name__ == "__main__":
    main()
