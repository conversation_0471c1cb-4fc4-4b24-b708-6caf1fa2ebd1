"""
剪贴板设置页面
管理剪贴板查重功能和文件名清洗规则
"""

import re
from PySide6.QtWidgets import (
    QVBoxLayout, QHBoxLayout, QGroupBox, QLabel, QCheckBox,
    QTableWidget, QTableWidgetItem, QPushButton, QLineEdit,
    QMessageBox, QHeaderView, QAbstractItemView, QComboBox,
    QSpinBox, QTextEdit, QSplitter, QFrame
)
from PySide6.QtCore import Qt
from typing import Tuple
from ..base.base_page import BaseSettingsPage


class ClipboardSettingsPage(BaseSettingsPage):
    """剪贴板设置页面"""

    def __init__(self, parent=None):
        """初始化剪贴板设置页面"""
        super().__init__(parent)

    def setup_ui(self):
        """设置UI界面"""
        layout = QVBoxLayout(self)

        # 创建分割器
        splitter = QSplitter(Qt.Vertical)
        layout.addWidget(splitter)

        # 上半部分：基础设置
        top_widget = QFrame()
        top_layout = QVBoxLayout(top_widget)

        # 剪贴板监控设置组
        monitor_group = QGroupBox("剪贴板监控设置")
        monitor_layout = QVBoxLayout(monitor_group)

        # 添加说明文本
        info_label = QLabel("剪贴板监控开关已移至主界面工具栏，请在工具栏中控制启用/禁用状态。")
        info_label.setWordWrap(True)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #e8f4fd;
                border: 1px solid #bee5eb;
                border-radius: 4px;
                padding: 8px;
                color: #0c5460;
            }
        """)
        monitor_layout.addWidget(info_label)

        # 监控延迟设置
        delay_layout = QHBoxLayout()
        delay_layout.addWidget(QLabel("检测延迟(毫秒):"))

        self.delay_spin = QSpinBox()
        self.delay_spin.setRange(100, 5000)
        self.delay_spin.setSingleStep(100)
        self.delay_spin.setValue(500)
        self.delay_spin.setToolTip("剪贴板内容变化后的检测延迟，避免频繁触发")
        delay_layout.addWidget(self.delay_spin)
        delay_layout.addStretch()

        monitor_layout.addLayout(delay_layout)

        # 浮动窗口显示模式
        mode_layout = QHBoxLayout()
        mode_layout.addWidget(QLabel("浮动窗口模式:"))

        self.float_mode_combo = QComboBox()
        self.float_mode_combo.addItems(["按需弹出", "持续显示"])
        self.float_mode_combo.setToolTip("按需弹出：只在检测到重复时显示\n持续显示：浮动窗口一直显示，实时更新状态")
        mode_layout.addWidget(self.float_mode_combo)
        mode_layout.addStretch()

        monitor_layout.addLayout(mode_layout)

        # 浮动窗口设置
        float_layout = QHBoxLayout()
        float_layout.addWidget(QLabel("浮动提示显示时间(秒):"))

        self.float_duration_spin = QSpinBox()
        self.float_duration_spin.setRange(3, 30)
        self.float_duration_spin.setValue(10)
        self.float_duration_spin.setToolTip("发现重复文件时浮动提示窗的显示时间（仅在按需弹出模式下有效）")
        float_layout.addWidget(self.float_duration_spin)
        float_layout.addStretch()

        monitor_layout.addLayout(float_layout)

        top_layout.addWidget(monitor_group)
        splitter.addWidget(top_widget)

        # 下半部分：文件名清洗规则
        bottom_widget = QFrame()
        bottom_layout = QVBoxLayout(bottom_widget)

        # 文件名清洗设置组
        cleaning_group = QGroupBox("文件名清洗规则")
        cleaning_layout = QVBoxLayout(cleaning_group)

        # 启用文件名清洗
        self.enable_cleaning_checkbox = QCheckBox("启用文件名清洗")
        self.enable_cleaning_checkbox.setToolTip("对剪贴板中的文件名进行清洗处理，提高查重准确性")
        cleaning_layout.addWidget(self.enable_cleaning_checkbox)

        # 规则管理按钮
        button_layout = QHBoxLayout()

        self.add_rule_button = QPushButton("添加规则")
        self.add_rule_button.clicked.connect(self.add_rule)
        button_layout.addWidget(self.add_rule_button)

        self.edit_rule_button = QPushButton("编辑规则")
        self.edit_rule_button.clicked.connect(self.edit_rule)
        button_layout.addWidget(self.edit_rule_button)

        self.delete_rule_button = QPushButton("删除规则")
        self.delete_rule_button.clicked.connect(self.delete_rule)
        button_layout.addWidget(self.delete_rule_button)

        self.test_rule_button = QPushButton("测试规则")
        self.test_rule_button.clicked.connect(self.test_rules)
        button_layout.addWidget(self.test_rule_button)

        button_layout.addStretch()

        self.reset_rules_button = QPushButton("重置为默认")
        self.reset_rules_button.clicked.connect(self.reset_to_default_rules)
        button_layout.addWidget(self.reset_rules_button)

        cleaning_layout.addLayout(button_layout)

        # 规则列表表格
        self.rules_table = QTableWidget()
        self.rules_table.setColumnCount(4)
        self.rules_table.setHorizontalHeaderLabels(["启用", "规则名称", "匹配模式", "替换内容"])

        # 设置表格属性
        self.rules_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.rules_table.setAlternatingRowColors(True)

        # 设置列宽
        header = self.rules_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        self.rules_table.setColumnWidth(0, 60)

        cleaning_layout.addWidget(self.rules_table)

        bottom_layout.addWidget(cleaning_group)
        splitter.addWidget(bottom_widget)

        # 设置分割器比例
        splitter.setSizes([200, 400])

    def get_page_title(self) -> str:
        """获取页面标题"""
        return "剪贴板查重"

    def load_settings(self, config: dict):
        """从配置加载设置到UI控件"""
        self.config = config

        # 加载剪贴板设置
        clipboard_config = config.get("clipboard", {})

        # 基础监控设置
        self.delay_spin.setValue(clipboard_config.get("detection_delay_ms", 500))

        # 浮动窗口模式设置
        float_mode = clipboard_config.get("float_mode", "on_demand")  # "on_demand" 或 "persistent"
        if float_mode == "persistent":
            self.float_mode_combo.setCurrentText("持续显示")
        else:
            self.float_mode_combo.setCurrentText("按需弹出")

        self.float_duration_spin.setValue(clipboard_config.get("float_duration_seconds", 10))

        # 文件名清洗设置
        cleaning_config = clipboard_config.get("filename_cleaning", {})
        self.enable_cleaning_checkbox.setChecked(cleaning_config.get("enabled", True))

        # 加载清洗规则
        rules = cleaning_config.get("rules", self.get_default_rules())
        self.load_rules_to_table(rules)

    def save_settings(self) -> dict:
        """从UI控件保存设置到配置"""
        # 收集清洗规则
        rules = self.get_rules_from_table()

        # 获取浮动窗口模式
        float_mode = "persistent" if self.float_mode_combo.currentText() == "持续显示" else "on_demand"

        # 保留当前的enabled状态（由UI工具栏控制）
        current_config = self.config.get("clipboard", {})
        current_enabled = current_config.get("enabled", True)

        return {
            "enabled": current_enabled,  # 保持当前状态，不在设置页面修改
            "detection_delay_ms": self.delay_spin.value(),
            "float_mode": float_mode,
            "float_duration_seconds": self.float_duration_spin.value(),
            "filename_cleaning": {
                "enabled": self.enable_cleaning_checkbox.isChecked(),
                "rules": rules
            }
        }

    def validate_settings(self) -> Tuple[bool, str]:
        """验证设置有效性"""
        # 验证清洗规则的正则表达式
        rules = self.get_rules_from_table()

        for rule in rules:
            try:
                re.compile(rule["pattern"])
            except re.error as e:
                return False, f"规则 '{rule['name']}' 的正则表达式无效: {e}"

        return True, ""

    def get_default_rules(self) -> list:
        """获取默认清洗规则"""
        return [
            {"name": "移除下载后缀", "pattern": r" - 下载.*$", "replace": "", "enabled": True},
            {"name": "移除文件大小", "pattern": r" \([0-9.]+[KMGT]?B\)", "replace": "", "enabled": True},
            {"name": "移除网站标记", "pattern": r"^\[.*?\]\s*", "replace": "", "enabled": True},
            {"name": "移除副本标记", "pattern": r" - 副本(\(\d+\))?", "replace": "", "enabled": True},
            {"name": "移除时间戳", "pattern": r"_\d{8}_", "replace": "_", "enabled": True},
            {"name": "移除括号数字", "pattern": r" \(\d+\)", "replace": "", "enabled": True},
        ]

    def load_rules_to_table(self, rules: list):
        """将规则加载到表格"""
        self.rules_table.setRowCount(len(rules))

        for row, rule in enumerate(rules):
            # 启用状态复选框
            enabled_item = QTableWidgetItem()
            enabled_item.setFlags(Qt.ItemIsUserCheckable | Qt.ItemIsEnabled)
            enabled_item.setCheckState(Qt.Checked if rule.get("enabled", True) else Qt.Unchecked)
            self.rules_table.setItem(row, 0, enabled_item)

            # 规则名称
            name_item = QTableWidgetItem(rule.get("name", ""))
            self.rules_table.setItem(row, 1, name_item)

            # 匹配模式
            pattern_item = QTableWidgetItem(rule.get("pattern", ""))
            self.rules_table.setItem(row, 2, pattern_item)

            # 替换内容
            replace_item = QTableWidgetItem(rule.get("replace", ""))
            self.rules_table.setItem(row, 3, replace_item)

    def get_rules_from_table(self) -> list:
        """从表格获取规则列表"""
        rules = []

        for row in range(self.rules_table.rowCount()):
            enabled_item = self.rules_table.item(row, 0)
            name_item = self.rules_table.item(row, 1)
            pattern_item = self.rules_table.item(row, 2)
            replace_item = self.rules_table.item(row, 3)

            if name_item and pattern_item and replace_item:
                rule = {
                    "name": name_item.text(),
                    "pattern": pattern_item.text(),
                    "replace": replace_item.text(),
                    "enabled": enabled_item.checkState() == Qt.Checked if enabled_item else True
                }
                rules.append(rule)

        return rules

    def add_rule(self):
        """添加新规则"""
        dialog = RuleEditDialog(self)
        if dialog.exec() == RuleEditDialog.Accepted:
            rule = dialog.get_rule()
            if rule:
                # 添加到表格
                row = self.rules_table.rowCount()
                self.rules_table.insertRow(row)

                # 启用状态复选框
                enabled_item = QTableWidgetItem()
                enabled_item.setFlags(Qt.ItemIsUserCheckable | Qt.ItemIsEnabled)
                enabled_item.setCheckState(Qt.Checked if rule.get("enabled", True) else Qt.Unchecked)
                self.rules_table.setItem(row, 0, enabled_item)

                # 规则信息
                self.rules_table.setItem(row, 1, QTableWidgetItem(rule["name"]))
                self.rules_table.setItem(row, 2, QTableWidgetItem(rule["pattern"]))
                self.rules_table.setItem(row, 3, QTableWidgetItem(rule["replace"]))

    def edit_rule(self):
        """编辑选中的规则"""
        current_row = self.rules_table.currentRow()
        if current_row < 0:
            QMessageBox.information(self, "提示", "请先选择要编辑的规则")
            return

        # 获取当前规则
        enabled_item = self.rules_table.item(current_row, 0)
        name_item = self.rules_table.item(current_row, 1)
        pattern_item = self.rules_table.item(current_row, 2)
        replace_item = self.rules_table.item(current_row, 3)

        if not all([name_item, pattern_item, replace_item]):
            return

        current_rule = {
            "name": name_item.text(),
            "pattern": pattern_item.text(),
            "replace": replace_item.text(),
            "enabled": enabled_item.checkState() == Qt.Checked if enabled_item else True
        }

        # 打开编辑对话框
        dialog = RuleEditDialog(self, current_rule)
        if dialog.exec() == RuleEditDialog.Accepted:
            rule = dialog.get_rule()
            if rule:
                # 更新表格
                enabled_item.setCheckState(Qt.Checked if rule.get("enabled", True) else Qt.Unchecked)
                name_item.setText(rule["name"])
                pattern_item.setText(rule["pattern"])
                replace_item.setText(rule["replace"])

    def delete_rule(self):
        """删除选中的规则"""
        current_row = self.rules_table.currentRow()
        if current_row < 0:
            QMessageBox.information(self, "提示", "请先选择要删除的规则")
            return

        # 确认删除
        name_item = self.rules_table.item(current_row, 1)
        rule_name = name_item.text() if name_item else "未知规则"

        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除规则 '{rule_name}' 吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.rules_table.removeRow(current_row)

    def test_rules(self):
        """测试清洗规则"""
        dialog = RuleTestDialog(self)
        dialog.set_rules(self.get_rules_from_table())
        dialog.exec()

    def reset_to_default_rules(self):
        """重置为默认规则"""
        reply = QMessageBox.question(
            self, "确认重置",
            "确定要重置为默认清洗规则吗？这将删除所有自定义规则。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            default_rules = self.get_default_rules()
            self.load_rules_to_table(default_rules)

    def reset_to_defaults(self):
        """重置为默认设置"""
        # 重置基础设置
        self.delay_spin.setValue(500)
        self.float_mode_combo.setCurrentText("按需弹出")
        self.float_duration_spin.setValue(10)

        # 重置清洗设置
        self.enable_cleaning_checkbox.setChecked(True)

        # 重置规则
        self.reset_to_default_rules()


class RuleEditDialog(QMessageBox):
    """规则编辑对话框"""

    def __init__(self, parent=None, rule=None):
        """初始化规则编辑对话框

        Args:
            parent: 父窗口
            rule: 要编辑的规则，None表示新建
        """
        super().__init__(parent)
        self.rule = rule or {}
        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("编辑清洗规则" if self.rule else "添加清洗规则")
        self.setIcon(QMessageBox.Question)

        # 创建输入控件
        from PySide6.QtWidgets import QDialog, QFormLayout, QDialogButtonBox

        # 重新创建为对话框
        self.dialog = QDialog(self.parent())
        self.dialog.setWindowTitle("编辑清洗规则" if self.rule else "添加清洗规则")
        self.dialog.setModal(True)
        self.dialog.resize(500, 300)

        layout = QVBoxLayout(self.dialog)

        # 表单布局
        form_layout = QFormLayout()

        # 规则名称
        self.name_edit = QLineEdit()
        self.name_edit.setText(self.rule.get("name", ""))
        self.name_edit.setPlaceholderText("例如：移除下载后缀")
        form_layout.addRow("规则名称:", self.name_edit)

        # 启用状态
        self.enabled_checkbox = QCheckBox("启用此规则")
        self.enabled_checkbox.setChecked(self.rule.get("enabled", True))
        form_layout.addRow("", self.enabled_checkbox)

        # 匹配模式
        self.pattern_edit = QLineEdit()
        self.pattern_edit.setText(self.rule.get("pattern", ""))
        self.pattern_edit.setPlaceholderText("正则表达式，例如：r' - 下载.*$'")
        form_layout.addRow("匹配模式:", self.pattern_edit)

        # 替换内容
        self.replace_edit = QLineEdit()
        self.replace_edit.setText(self.rule.get("replace", ""))
        self.replace_edit.setPlaceholderText("替换为的内容，空白表示删除")
        form_layout.addRow("替换内容:", self.replace_edit)

        layout.addLayout(form_layout)

        # 说明文本
        help_text = QLabel("""
<b>使用说明：</b><br>
• 匹配模式使用正则表达式语法<br>
• 替换内容可以为空（表示删除匹配的部分）<br>
• 常用模式示例：<br>
  - 移除括号内容：<code>r'\\([^)]*\\)'</code><br>
  - 移除文件扩展名：<code>r'\\.[^.]*$'</code><br>
  - 移除数字：<code>r'\\d+'</code>
        """)
        help_text.setWordWrap(True)
        help_text.setStyleSheet("QLabel { background-color: #f0f0f0; padding: 10px; border-radius: 5px; }")
        layout.addWidget(help_text)

        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel,
            Qt.Horizontal, self.dialog
        )
        button_box.accepted.connect(self.dialog.accept)
        button_box.rejected.connect(self.dialog.reject)

        # 设置中文按钮文本
        ok_button = button_box.button(QDialogButtonBox.Ok)
        cancel_button = button_box.button(QDialogButtonBox.Cancel)
        if ok_button:
            ok_button.setText("确定")
        if cancel_button:
            cancel_button.setText("取消")

        layout.addWidget(button_box)

    def exec(self):
        """执行对话框"""
        return self.dialog.exec()

    def get_rule(self):
        """获取规则数据"""
        name = self.name_edit.text().strip()
        pattern = self.pattern_edit.text().strip()
        replace = self.replace_edit.text()

        if not name or not pattern:
            QMessageBox.warning(self.dialog, "输入错误", "规则名称和匹配模式不能为空")
            return None

        # 验证正则表达式
        try:
            re.compile(pattern)
        except re.error as e:
            QMessageBox.warning(self.dialog, "正则表达式错误", f"匹配模式无效: {e}")
            return None

        return {
            "name": name,
            "pattern": pattern,
            "replace": replace,
            "enabled": self.enabled_checkbox.isChecked()
        }


class RuleTestDialog(QMessageBox):
    """规则测试对话框"""

    def __init__(self, parent=None):
        """初始化规则测试对话框"""
        super().__init__(parent)
        self.rules = []
        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        from PySide6.QtWidgets import QDialog, QDialogButtonBox

        # 创建对话框
        self.dialog = QDialog(self.parent())
        self.dialog.setWindowTitle("测试清洗规则")
        self.dialog.setModal(True)
        self.dialog.resize(600, 500)

        layout = QVBoxLayout(self.dialog)

        # 说明文本
        info_label = QLabel("在下方输入文件名，查看清洗规则的效果：")
        layout.addWidget(info_label)

        # 输入区域
        input_group = QGroupBox("测试输入")
        input_layout = QVBoxLayout(input_group)

        self.input_edit = QTextEdit()
        self.input_edit.setPlaceholderText("请输入要测试的文件名，每行一个\n例如：\n文档 - 下载 (1).pdf\n[网站]重要文件 (2.5MB).docx\n照片_20231201_副本(3).jpg")
        self.input_edit.setMaximumHeight(120)
        input_layout.addWidget(self.input_edit)

        # 测试按钮
        test_button = QPushButton("执行测试")
        test_button.clicked.connect(self.run_test)
        input_layout.addWidget(test_button)

        layout.addWidget(input_group)

        # 结果区域
        result_group = QGroupBox("清洗结果")
        result_layout = QVBoxLayout(result_group)

        self.result_edit = QTextEdit()
        self.result_edit.setReadOnly(True)
        result_layout.addWidget(self.result_edit)

        layout.addWidget(result_group)

        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.Close,
            Qt.Horizontal, self.dialog
        )
        button_box.rejected.connect(self.dialog.reject)

        # 设置中文按钮文本
        close_button = button_box.button(QDialogButtonBox.Close)
        if close_button:
            close_button.setText("关闭")

        layout.addWidget(button_box)

    def set_rules(self, rules):
        """设置要测试的规则"""
        self.rules = rules

    def exec(self):
        """执行对话框"""
        return self.dialog.exec()

    def run_test(self):
        """执行测试"""
        input_text = self.input_edit.toPlainText().strip()
        if not input_text:
            self.result_edit.setPlainText("请先输入要测试的文件名")
            return

        # 获取启用的规则
        enabled_rules = [rule for rule in self.rules if rule.get("enabled", True)]

        if not enabled_rules:
            self.result_edit.setPlainText("没有启用的清洗规则")
            return

        # 处理每行输入
        lines = input_text.split('\n')
        results = []

        for line in lines:
            original = line.strip()
            if not original:
                continue

            cleaned = original
            applied_rules = []

            # 应用每个规则
            for rule in enabled_rules:
                try:
                    pattern = rule["pattern"]
                    replace = rule["replace"]

                    # 检查是否匹配
                    if re.search(pattern, cleaned):
                        new_cleaned = re.sub(pattern, replace, cleaned)
                        if new_cleaned != cleaned:
                            applied_rules.append(rule["name"])
                            cleaned = new_cleaned

                except re.error as e:
                    applied_rules.append(f"{rule['name']} (错误: {e})")

            # 格式化结果
            result_line = f"原始: {original}\n"
            result_line += f"清洗: {cleaned}\n"
            if applied_rules:
                result_line += f"应用规则: {', '.join(applied_rules)}\n"
            else:
                result_line += "应用规则: 无匹配\n"
            result_line += "-" * 50 + "\n"

            results.append(result_line)

        # 显示结果
        self.result_edit.setPlainText('\n'.join(results))
