"""
剪贴板查重浮动提示窗
类似下载软件的小巧浮动窗口，显示查重结果
"""

from typing import Dict
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                               QPushButton, QFrame, QApplication)
from PySide6.QtCore import Qt, QTimer, QPropertyAnimation, Signal
from PySide6.QtGui import QFont


class ClipboardFloatingWidget(QWidget):
    """剪贴板查重浮动提示窗"""

    # 信号定义
    open_file_requested = Signal(str)  # 请求打开文件

    def __init__(self):
        """初始化浮动窗"""
        super().__init__()

        # 设置窗口属性
        self.setWindowFlags(
            Qt.WindowStaysOnTopHint |
            Qt.FramelessWindowHint |
            Qt.Tool
        )

        # 设置窗口大小和样式
        self.setFixedSize(320, 85)
        self.setAttribute(Qt.WA_TranslucentBackground)

        # 初始化UI
        self.init_ui()

        # 自动行为定时器（可用于隐藏或状态切换）
        self.hide_timer = QTimer()
        self.hide_timer.setSingleShot(True)
        # 初始连接到隐藏动画，后续会根据模式动态调整

        # 动画对象
        self.fade_animation = None

        # 初始隐藏
        self.hide()

        # 定位到屏幕右下角
        self.position_to_corner()

    def init_ui(self):
        """初始化UI组件"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 主框架
        self.main_frame = QFrame()
        self.main_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(45, 45, 45, 240);
                border: 1px solid rgba(100, 100, 100, 180);
                border-radius: 8px;
            }
        """)
        main_layout.addWidget(self.main_frame)

        # 框架内布局
        frame_layout = QVBoxLayout(self.main_frame)
        frame_layout.setContentsMargins(12, 8, 12, 8)
        frame_layout.setSpacing(4)

        # 标题行
        title_layout = QHBoxLayout()
        title_layout.setSpacing(8)

        # 图标标签
        self.icon_label = QLabel("🔍")
        self.icon_label.setFont(QFont("Segoe UI Emoji", 16))
        self.icon_label.setFixedSize(22, 22)
        self.icon_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(self.icon_label)

        # 标题标签
        self.title_label = QLabel("剪贴板查重")
        self.title_label.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        self.title_label.setStyleSheet("color: #ffffff;")
        self.title_label.setFixedHeight(22)
        self.title_label.setAlignment(Qt.AlignVCenter)
        title_layout.addWidget(self.title_label)

        # 查看按钮（固定在标题行）
        self.view_button = QPushButton("查看")
        self.view_button.setFixedSize(50, 22)
        self.view_button.setStyleSheet("""
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 9px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
        """)
        self.view_button.clicked.connect(self.on_view_clicked)
        self.view_button.hide()  # 默认隐藏
        title_layout.addWidget(self.view_button)

        # 弹性空间
        title_layout.addStretch()

        # 关闭按钮
        self.close_button = QPushButton("×")
        self.close_button.setFixedSize(22, 22)
        self.close_button.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #cccccc;
                border: none;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 30);
                border-radius: 11px;
            }
        """)
        self.close_button.clicked.connect(self.on_close_clicked)
        title_layout.addWidget(self.close_button)

        frame_layout.addLayout(title_layout)

        # 内容标签
        self.content_label = QLabel()
        self.content_label.setFont(QFont("Microsoft YaHei", 9))
        self.content_label.setStyleSheet("color: #e0e0e0;")
        self.content_label.setWordWrap(False)  # 禁用自动换行，确保单行显示
        self.content_label.setFixedHeight(18)  # 固定高度，防止内容变化导致窗口大小变化
        self.content_label.setAlignment(Qt.AlignVCenter)
        # 设置文本省略模式，超长文本显示省略号
        self.content_label.setTextFormat(Qt.PlainText)
        frame_layout.addWidget(self.content_label)

        # 存储当前重复信息
        self.current_duplicate_info = None

        # 显示模式状态
        self.is_persistent_mode = False
        self.monitoring_enabled = False  # 监控启用状态
        self.monitoring_status = "未启动"

    def _truncate_text(self, text: str, max_length: int = 25) -> str:
        """截断文本，避免显示过长"""
        if len(text) <= max_length:
            return text
        return text[:max_length-3] + "..."

    def _setup_timer_for_monitoring_return(self, duration: int):
        """设置定时器用于回到监控状态"""
        try:
            self.hide_timer.stop()
            # 安全地断开所有连接
            self.hide_timer.timeout.disconnect()
        except:
            pass  # 忽略断开连接时的错误

        # 连接到监控状态显示
        self.hide_timer.timeout.connect(self.show_monitoring_status)
        self.hide_timer.start(duration * 1000)

    def _setup_timer_for_hide(self, duration: int):
        """设置定时器用于隐藏窗口"""
        try:
            self.hide_timer.stop()
            # 安全地断开所有连接
            self.hide_timer.timeout.disconnect()
        except:
            pass  # 忽略断开连接时的错误

        # 连接到隐藏动画
        self.hide_timer.timeout.connect(self.hide_with_animation)
        self.hide_timer.start(duration * 1000)

    def position_to_corner(self):
        """定位到屏幕右下角"""
        try:
            screen = QApplication.primaryScreen()
            screen_geometry = screen.availableGeometry()

            # 计算位置（右下角，留出边距）
            x = screen_geometry.width() - self.width() - 20
            y = screen_geometry.height() - self.height() - 60

            self.move(x, y)

        except Exception as e:
            print(f"定位浮动窗失败: {e}")
            # 默认位置
            self.move(100, 100)

    def show_duplicate(self, duplicate_info: Dict):
        """显示重复文件信息"""
        try:
            self.current_duplicate_info = duplicate_info

            # 根据类型设置不同的显示内容
            if duplicate_info['type'] == 'file':
                self._show_file_duplicate(duplicate_info)
            elif duplicate_info['type'] == 'filename':
                self._show_filename_duplicate(duplicate_info)
            elif duplicate_info['type'] == 'demo':
                self._show_demo_message(duplicate_info)
            elif duplicate_info['type'] == 'database_error':
                self._show_database_error(duplicate_info)
            elif duplicate_info['type'] == 'no_duplicate':
                self._show_no_duplicate(duplicate_info)

            # 根据类型决定是否显示按钮
            if duplicate_info['type'] in ['file', 'filename', 'demo']:
                # 有重复信息或演示时显示查看按钮
                self.view_button.show()
            else:
                # 数据库错误或未发现重复时隐藏查看按钮
                self.view_button.hide()

            # 根据显示模式决定是否显示关闭按钮
            if self.is_persistent_mode:
                # 持续显示模式：隐藏关闭按钮，状态自动管理
                self.close_button.hide()
            else:
                # 按需弹出模式：显示关闭按钮，用户可手动关闭
                self.close_button.show()

            # 显示窗口
            self.show_with_animation()

            # 设置自动行为
            duration = self.get_display_duration()
            if self.is_persistent_mode:
                # 持续显示模式：显示重复信息一段时间后自动回到监控状态
                self._setup_timer_for_monitoring_return(duration)
            else:
                # 按需弹出模式：显示一段时间后自动隐藏
                self._setup_timer_for_hide(duration)

        except Exception as e:
            print(f"显示重复信息失败: {e}")

    def get_display_duration(self) -> int:
        """获取显示时长（秒）"""
        try:
            from smartvault.utils.config import load_config
            config = load_config()
            clipboard_config = config.get("clipboard", {})
            return clipboard_config.get("float_duration_seconds", 10)
        except Exception:
            return 10  # 默认10秒

    def set_persistent_mode(self, enabled: bool):
        """设置持续显示模式"""
        self.is_persistent_mode = enabled

        if enabled:
            # 持续显示模式：只有在监控启用时才显示窗口
            if self.monitoring_enabled:
                self.show_monitoring_status()
        else:
            # 按需弹出模式：隐藏窗口
            if not self.current_duplicate_info:  # 如果没有重复信息，则隐藏
                self.hide()

    def set_monitoring_enabled(self, enabled: bool):
        """设置监控启用状态"""
        self.monitoring_enabled = enabled

        if enabled:
            # 监控启用时，根据模式决定是否显示
            if self.is_persistent_mode:
                self.show_monitoring_status()
        else:
            # 监控禁用时，隐藏浮动窗口（无论什么模式）
            self.hide()

    def show_monitoring_status(self):
        """显示监控状态（持续显示模式）"""
        try:
            # 更新显示内容为监控状态
            self.title_label.setText("剪贴板查重")
            self.content_label.setText(f"状态: {self.monitoring_status}")
            self.icon_label.setText("📋")

            # 隐藏按钮（持续显示模式下不需要操作按钮）
            self.view_button.hide()
            self.close_button.hide()

            # 清除当前重复信息，表示回到监控状态
            self.current_duplicate_info = None

            # 显示窗口并确保保持最前
            self.show()
            self.raise_()
            self.activateWindow()  # 激活窗口，确保保持最前

        except Exception as e:
            print(f"显示监控状态失败: {e}")

    def update_monitoring_status(self, status: str):
        """更新监控状态"""
        self.monitoring_status = status

        if self.is_persistent_mode:
            self.show_monitoring_status()

    def _show_file_duplicate(self, duplicate_info: Dict):
        """显示文件重复信息"""
        duplicate_count = len(duplicate_info['duplicates'])

        self.title_label.setText("剪贴板查重 - 发现重复")
        self.icon_label.setText("📁")

        if duplicate_count == 1:
            duplicate_file = duplicate_info['duplicates'][0]
            # 只显示库中文件名，单行显示，截断过长文本
            content = self._truncate_text(duplicate_file['name'])
        else:
            content = f"库中有 {duplicate_count} 个重复文件"

        self.content_label.setText(content)

    def _show_filename_duplicate(self, duplicate_info: Dict):
        """显示文件名重复信息"""
        duplicate_count = len(duplicate_info['duplicates'])

        self.title_label.setText("剪贴板查重 - 相似文件名")
        self.icon_label.setText("📝")

        if duplicate_count == 1:
            duplicate_file = duplicate_info['duplicates'][0]
            # 只显示库中文件名，单行显示，截断过长文本
            content = self._truncate_text(duplicate_file['name'])
        else:
            content = f"库中有 {duplicate_count} 个相似文件"

        self.content_label.setText(content)

    def _show_demo_message(self, duplicate_info: Dict):
        """显示演示信息"""
        self.title_label.setText("剪贴板查重 - 演示")
        self.icon_label.setText("🎯")
        self.content_label.setText(duplicate_info.get('source_text', '演示：剪贴板查重功能'))

    def _show_database_error(self, error_info: Dict):
        """显示数据库错误信息"""
        self.title_label.setText("数据库连接失败")
        self.icon_label.setText("❌")

        error_message = error_info.get('error_message', '数据库连接失败，请检查文件库路径配置')
        # 单行显示错误信息
        self.content_label.setText(error_message)

    def _show_no_duplicate(self, info: Dict):
        """显示未发现重复信息"""
        self.title_label.setText("未发现重复")
        self.icon_label.setText("✅")

        cleaned_name = info.get('cleaned_name', info.get('source_name', ''))
        message = info.get('message', f'{cleaned_name} 在文件库中未发现重复')

        self.content_label.setText(message)

    def show_with_animation(self):
        """带动画显示"""
        self.setWindowOpacity(0.0)
        self.show()

        # 淡入动画
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(300)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.start()

    def hide_with_animation(self):
        """带动画隐藏"""
        if self.fade_animation:
            self.fade_animation.stop()

        # 淡出动画
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(200)
        self.fade_animation.setStartValue(1.0)
        self.fade_animation.setEndValue(0.0)
        self.fade_animation.finished.connect(self.hide)
        self.fade_animation.start()

    def on_close_clicked(self):
        """关闭按钮点击处理"""
        if self.is_persistent_mode:
            # 持续显示模式：回到监控状态，保持窗口可见
            self.show_monitoring_status()
        else:
            # 按需弹出模式：隐藏窗口
            self.hide_with_animation()

    def on_view_clicked(self):
        """查看按钮点击处理"""
        if self.current_duplicate_info and self.current_duplicate_info['duplicates']:
            # 获取第一个重复文件的ID
            first_duplicate = self.current_duplicate_info['duplicates'][0]
            file_id = first_duplicate['id']

            # 发出打开文件请求信号
            self.open_file_requested.emit(file_id)

            # 根据显示模式决定后续行为
            if self.is_persistent_mode:
                # 持续显示模式：恢复到监控状态显示，保持窗口可见
                self.show_monitoring_status()
            else:
                # 按需弹出模式：隐藏窗口
                self.hide_with_animation()

    def mousePressEvent(self, event):
        """鼠标点击事件 - 支持拖拽移动"""
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        """鼠标移动事件 - 拖拽移动窗口"""
        if event.buttons() == Qt.LeftButton and hasattr(self, 'drag_position'):
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()

    def enterEvent(self, event):
        """鼠标进入事件 - 停止自动隐藏"""
        self.hide_timer.stop()
        super().enterEvent(event)

    def leaveEvent(self, event):
        """鼠标离开事件 - 重新启动自动行为"""
        if not self.is_persistent_mode:
            # 按需弹出模式：3秒后隐藏
            self._setup_timer_for_hide(3)
        elif self.current_duplicate_info:
            # 持续显示模式且正在显示重复信息：3秒后回到监控状态
            self._setup_timer_for_monitoring_return(3)
        # 持续显示模式且在监控状态：不启动定时器
        super().leaveEvent(event)
