#!/usr/bin/env python3
"""
拆分功能安全验证测试
避免死循环和过度初始化，专注于核心功能验证
"""

import sys
import os
sys.path.append('.')

def test_backup_manager_isolated():
    """隔离测试备份管理器"""
    print("=" * 60)
    print("💾 备份管理器隔离测试")
    print("=" * 60)
    
    try:
        # 模拟主窗口，避免完整初始化
        class MockMainWindow:
            def __init__(self):
                # 模拟必要的服务
                self.backup_service = MockBackupService()
                
            def show_status_message(self, message, success=True):
                print(f"📢 状态消息: {message} ({'成功' if success else '失败'})")
                
            def statusBar(self):
                return MockStatusBar()
        
        class MockBackupService:
            def get_backup_status(self):
                return {
                    'enabled': True,
                    'is_running': False,
                    'total_backups': 5,
                    'latest_backup': {'date': '2025-01-27', 'size_mb': 2.5}
                }
            
            def start_auto_backup(self):
                print("模拟启动自动备份")
                
            def stop_auto_backup(self):
                print("模拟停止自动备份")
        
        class MockStatusBar:
            def addPermanentWidget(self, widget):
                print(f"添加状态栏组件: {type(widget).__name__}")
        
        # 创建模拟主窗口
        main_window = MockMainWindow()
        
        # 测试备份管理器
        from smartvault.ui.main_window.backup_manager import BackupManager
        backup_manager = BackupManager(main_window)
        
        print("✅ 备份管理器创建成功")
        
        # 测试核心方法
        print("\n🔍 测试核心方法:")
        
        # 1. 获取状态
        status = backup_manager.get_backup_status()
        print(f"✅ 状态获取: {status}")
        
        # 2. 启动服务
        backup_manager.start_backup_service()
        print("✅ 启动服务方法调用成功")
        
        # 3. 停止服务
        backup_manager.stop_backup_service()
        print("✅ 停止服务方法调用成功")
        
        # 4. 设置状态显示
        backup_manager.setup_backup_status_display()
        print("✅ 状态显示设置成功")
        
        # 5. 更新状态显示
        backup_manager.update_backup_status_display()
        print("✅ 状态显示更新成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 备份管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_clipboard_handler_isolated():
    """隔离测试剪贴板处理器"""
    print("\n" + "=" * 60)
    print("📋 剪贴板处理器隔离测试")
    print("=" * 60)
    
    try:
        # 模拟主窗口，避免完整初始化
        class MockMainWindow:
            def __init__(self):
                # 模拟必要的服务和组件
                self.clipboard_service = MockClipboardService()
                self.clipboard_floating_widget = MockFloatingWidget()
                self.file_view = MockFileView()
                self.file_service = MockFileService()
                self.toolbar_manager = MockToolbarManager()
                
            def show_status_message(self, message, success=True):
                print(f"📢 状态消息: {message} ({'成功' if success else '失败'})")
        
        class MockClipboardService:
            def __init__(self):
                self.is_monitoring = False
                
            def start_monitoring(self):
                self.is_monitoring = True
                print("模拟启动剪贴板监控")
                
            def stop_monitoring(self):
                self.is_monitoring = False
                print("模拟停止剪贴板监控")
        
        class MockFloatingWidget:
            def __init__(self):
                self.current_duplicate_info = None
                
            def show_duplicate(self, info):
                self.current_duplicate_info = info
                print(f"模拟显示重复文件: {info.get('type', 'unknown')}")
                
            def update_monitoring_status(self, status):
                print(f"模拟更新监控状态: {status}")
                
            def set_monitoring_enabled(self, enabled):
                print(f"模拟设置监控启用: {enabled}")
        
        class MockFileView:
            def get_current_model(self):
                return MockModel()
                
            def get_current_view(self):
                return MockView()
        
        class MockModel:
            def __init__(self):
                self.FileIdRole = 257  # Qt.UserRole + 1
                
            def rowCount(self):
                return 3
                
            def index(self, row, col):
                return MockIndex(row, col)
                
            def data(self, index, role):
                if role == self.FileIdRole:
                    return f"test-file-id-{index.row}"
                return f"test-file-{index.row}"
        
        class MockView:
            def selectRow(self, row):
                print(f"模拟选中行: {row}")
                
            def scrollTo(self, index):
                print(f"模拟滚动到: 行{index.row}")
                
            def setFocus(self):
                print("模拟设置焦点")
        
        class MockIndex:
            def __init__(self, row, col):
                self.row = row
                self.col = col
        
        class MockFileService:
            def get_file_by_id(self, file_id):
                return {
                    'id': file_id,
                    'name': f'test-file-{file_id}.jpg',
                    'original_path': f'/test/path/{file_id}.jpg'
                }
        
        class MockToolbarManager:
            def update_clipboard_status(self, enabled):
                print(f"模拟更新工具栏状态: {enabled}")
        
        # 创建模拟主窗口
        main_window = MockMainWindow()
        
        # 测试剪贴板处理器
        from smartvault.ui.main_window.clipboard_handler import ClipboardHandler
        clipboard_handler = ClipboardHandler(main_window)
        
        print("✅ 剪贴板处理器创建成功")
        
        # 测试核心方法
        print("\n🔍 测试核心方法:")
        
        # 1. 切换监控状态
        clipboard_handler.toggle_clipboard_monitor()
        print("✅ 监控切换方法调用成功")
        
        # 2. 显示演示
        clipboard_handler.show_clipboard_demo()
        print("✅ 演示显示方法调用成功")
        
        # 3. 文件定位
        clipboard_handler._locate_and_select_file("test-file-id-1")
        print("✅ 文件定位方法调用成功")
        
        # 4. 重复文件处理
        test_duplicate_info = {
            'type': 'test',
            'source_text': 'test.jpg',
            'cleaned_name': 'test.jpg',
            'duplicates': [{'id': 'test-id', 'name': 'test.jpg'}]
        }
        clipboard_handler.on_clipboard_duplicate_found(test_duplicate_info)
        print("✅ 重复文件处理方法调用成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 剪贴板处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_role_compatibility():
    """测试数据角色兼容性"""
    print("\n" + "=" * 60)
    print("🔍 数据角色兼容性测试")
    print("=" * 60)
    
    try:
        from PySide6.QtCore import Qt
        from smartvault.ui.models.file_table_model import FileTableModel
        
        # 创建模型
        model = FileTableModel()
        
        # 检查角色定义
        print(f"✅ FileIdRole 定义: {model.FileIdRole}")
        print(f"✅ Qt.UserRole 值: {Qt.UserRole}")
        print(f"✅ 角色差异: {model.FileIdRole - Qt.UserRole}")
        
        # 模拟文件数据
        test_files = [
            {
                'id': 'test-id-1',
                'name': 'test1.jpg',
                'original_path': '/test/test1.jpg',
                'library_path': '/lib/test1.jpg',
                'entry_type': 'copy',
                'size': 1024,
                'created_at': '2025-01-27',
                'modified_at': '2025-01-27',
                'added_at': '2025-01-27'
            }
        ]
        
        # 设置文件数据
        model.setFiles(test_files)
        
        # 测试数据获取
        index = model.index(0, 0)
        
        # 使用不同角色获取数据
        file_id_role = model.data(index, model.FileIdRole)
        user_role = model.data(index, Qt.UserRole)
        display_role = model.data(index, Qt.DisplayRole)
        
        print(f"✅ FileIdRole 数据: {file_id_role}")
        print(f"✅ UserRole 数据: {user_role}")
        print(f"✅ DisplayRole 数据: {display_role}")
        
        # 验证正确性
        if file_id_role == 'test-id-1':
            print("✅ FileIdRole 返回正确的文件ID")
        else:
            print(f"❌ FileIdRole 返回错误: 期望 'test-id-1', 实际 '{file_id_role}'")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 数据角色兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始拆分功能安全验证测试")
    print("避免死循环和过度初始化，专注于核心功能验证")
    
    test_results = []
    
    # 执行测试
    test_results.append(("备份管理器隔离测试", test_backup_manager_isolated()))
    test_results.append(("剪贴板处理器隔离测试", test_clipboard_handler_isolated()))
    test_results.append(("数据角色兼容性测试", test_data_role_compatibility()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 安全验证测试结果汇总")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n总计: {passed} 个测试通过, {failed} 个测试失败")
    
    if failed == 0:
        print("🎉 安全验证测试全部通过！")
        return True
    else:
        print("⚠️ 存在问题，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
