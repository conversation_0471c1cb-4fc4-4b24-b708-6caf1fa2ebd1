"""
B021任务测试：完善三层标签逻辑交互

测试内容：
1. 标签权重系统
2. 标签关联关系
3. 属性继承机制
4. 搜索优化逻辑
"""

import pytest
import tempfile
import os
import shutil
# from unittest.mock import Mock, patch  # 暂时不需要

from smartvault.data.database import Database
from smartvault.services.tag_service import TagService


class TestB021TagHierarchyLogic:
    """B021标签层级逻辑测试类"""

    @pytest.fixture
    def temp_db(self):
        """创建临时数据库"""
        temp_dir = tempfile.mkdtemp()
        db_path = os.path.join(temp_dir, "test.db")
        db = Database(db_path)
        yield db
        db.close()
        shutil.rmtree(temp_dir)

    @pytest.fixture
    def tag_service(self, temp_db):
        """创建标签服务实例"""
        return TagService(temp_db)

    def test_tag_weight_system(self, tag_service):
        """测试标签权重系统"""
        # 创建带权重的标签
        tag1_id = tag_service.create_tag("重要文档", color="#FF0000", weight=10)
        tag2_id = tag_service.create_tag("普通文档", color="#00FF00", weight=5)
        tag3_id = tag_service.create_tag("临时文档", color="#0000FF", weight=1)

        # 验证权重设置
        tag1 = tag_service.get_tag_by_id(tag1_id)
        tag2 = tag_service.get_tag_by_id(tag2_id)
        tag3 = tag_service.get_tag_by_id(tag3_id)

        assert tag1["weight"] == 10
        assert tag2["weight"] == 5
        assert tag3["weight"] == 1

        # 测试按权重排序
        sorted_tags = tag_service.get_tags_sorted_by_weight()
        assert sorted_tags[0]["id"] == tag1_id  # 权重最高
        assert sorted_tags[1]["id"] == tag2_id
        assert sorted_tags[2]["id"] == tag3_id  # 权重最低

    def test_tag_inheritance_mechanism(self, tag_service):
        """测试标签继承机制"""
        # 创建父标签
        parent_id = tag_service.create_tag("工作", color="#FF0000", weight=8)

        # 创建子标签（应继承父标签的属性）
        child_id = tag_service.create_tag("项目A", parent_id=parent_id)

        # 创建孙标签
        grandchild_id = tag_service.create_tag("文档", parent_id=child_id)

        # 测试属性继承
        inherited_attrs = tag_service.get_inherited_attributes(grandchild_id)

        # 孙标签应继承父标签的颜色和权重
        assert inherited_attrs["color"] == "#FF0000"
        assert inherited_attrs["weight"] == 8

        # 测试继承链
        inheritance_chain = tag_service.get_inheritance_chain(grandchild_id)
        assert len(inheritance_chain) == 3
        assert inheritance_chain[0]["id"] == parent_id
        assert inheritance_chain[1]["id"] == child_id
        assert inheritance_chain[2]["id"] == grandchild_id

    def test_tag_relations_system(self, tag_service):
        """测试标签关联关系系统"""
        # 创建相关标签
        tag1_id = tag_service.create_tag("Python")
        tag2_id = tag_service.create_tag("编程")
        tag3_id = tag_service.create_tag("开发")
        tag4_id = tag_service.create_tag("Java")

        # 建立标签关联关系
        tag_service.add_tag_relation(tag1_id, tag2_id, "related", strength=0.8)
        tag_service.add_tag_relation(tag1_id, tag3_id, "related", strength=0.7)
        tag_service.add_tag_relation(tag2_id, tag3_id, "related", strength=0.9)
        tag_service.add_tag_relation(tag4_id, tag2_id, "related", strength=0.6)

        # 测试获取相关标签
        related_tags = tag_service.get_related_tags(tag1_id)
        assert len(related_tags) >= 2

        # 验证关联强度排序
        assert related_tags[0]["strength"] >= related_tags[1]["strength"]

        # 测试标签推荐
        recommendations = tag_service.get_tag_recommendations(tag1_id, limit=3)
        assert len(recommendations) <= 3
        assert all(rec["id"] != tag1_id for rec in recommendations)  # 不推荐自己

    def test_enhanced_search_logic(self, tag_service):
        """测试增强的搜索逻辑"""
        # 创建标签层级
        work_id = tag_service.create_tag("工作", weight=10)
        project_id = tag_service.create_tag("项目", parent_id=work_id, weight=8)
        docs_id = tag_service.create_tag("文档", parent_id=project_id, weight=5)

        # 测试权重排序搜索（空结果也是正常的，因为没有文件）
        results = tag_service.search_files_with_weight_sorting([work_id, docs_id])

        # 验证方法可以正常调用
        assert isinstance(results, list)

    def test_multi_tag_combination_filtering(self, tag_service):
        """测试多标签组合筛选"""
        # 创建标签
        urgent_id = tag_service.create_tag("紧急", weight=10)
        work_id = tag_service.create_tag("工作", weight=8)
        personal_id = tag_service.create_tag("个人", weight=5)

        # 测试AND组合筛选
        and_results = tag_service.filter_files_by_tags_and([urgent_id, work_id])
        assert isinstance(and_results, list)  # 应返回同时具有"紧急"和"工作"标签的文件

        # 测试OR组合筛选
        or_results = tag_service.filter_files_by_tags_or([urgent_id, personal_id])
        assert isinstance(or_results, list)  # 应返回具有"紧急"或"个人"标签的文件

        # 测试NOT组合筛选
        not_results = tag_service.filter_files_by_tags_not([work_id], [personal_id])
        assert isinstance(not_results, list)  # 应返回具有"工作"但不具有"个人"标签的文件

    def test_tag_statistics_and_analysis(self, tag_service):
        """测试标签统计和分析功能"""
        # 创建标签层级
        parent_id = tag_service.create_tag("技术", weight=10)
        tag_service.create_tag("前端", parent_id=parent_id, weight=8)
        tag_service.create_tag("后端", parent_id=parent_id, weight=8)

        # 测试标签使用统计
        stats = tag_service.get_tag_usage_statistics()
        assert isinstance(stats, dict)
        assert "total_tags" in stats
        assert "tags_with_files" in stats
        assert "average_tags_per_file" in stats

        # 测试标签层级统计
        hierarchy_stats = tag_service.get_tag_hierarchy_statistics()
        assert isinstance(hierarchy_stats, dict)
        assert "max_depth" in hierarchy_stats
        assert "total_parent_tags" in hierarchy_stats
        assert "total_leaf_tags" in hierarchy_stats

        # 测试热门标签分析
        popular_tags = tag_service.get_popular_tags(limit=5)
        assert len(popular_tags) <= 5
        # 验证按使用频率排序
        if len(popular_tags) > 1:
            assert popular_tags[0]["usage_count"] >= popular_tags[1]["usage_count"]

    def test_tag_weight_inheritance_in_search(self, tag_service):
        """测试搜索中的标签权重继承"""
        # 创建带权重的标签层级
        root_id = tag_service.create_tag("根标签", weight=10)
        branch_id = tag_service.create_tag("分支标签", parent_id=root_id, weight=8)
        tag_service.create_tag("叶子标签", parent_id=branch_id, weight=5)

        # 测试搜索时权重继承
        search_results = tag_service.search_with_inherited_weights("叶子标签")

        # 验证继承的权重影响搜索排序
        assert len(search_results) >= 0
        # 具有更高继承权重的结果应排在前面

    def test_performance_with_large_tag_hierarchy(self, tag_service):
        """测试大型标签层级的性能"""
        import time

        # 创建大量标签层级
        root_tags = []
        for i in range(10):
            root_id = tag_service.create_tag(f"根标签{i}", weight=10-i)
            root_tags.append(root_id)

            # 为每个根标签创建子标签
            for j in range(5):
                child_id = tag_service.create_tag(f"子标签{i}-{j}", parent_id=root_id, weight=8-j)

                # 为每个子标签创建孙标签
                for k in range(3):
                    tag_service.create_tag(f"孙标签{i}-{j}-{k}", parent_id=child_id, weight=5-k)

        # 测试查询性能
        start_time = time.time()
        tag_tree = tag_service.get_tag_tree()
        end_time = time.time()

        # 验证查询时间在合理范围内（< 1秒）
        query_time = end_time - start_time
        assert query_time < 1.0, f"标签树查询时间过长: {query_time:.2f}秒"

        # 验证结果正确性
        assert len(tag_tree) == 10  # 10个根标签
        for root_tag in tag_tree:
            assert len(root_tag["children"]) == 5  # 每个根标签有5个子标签
