#!/usr/bin/env python3
"""
第二阶段手动测试脚本
用于验证文件右键菜单的"添加到文件夹"和"移动到文件夹"功能
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from smartvault.ui.main_window import MainWindow


def main():
    """主函数"""
    app = QApplication.instance() or QApplication(sys.argv)
    
    # 创建主窗口
    main_window = MainWindow()
    main_window.show()
    
    print("🎯 第二阶段手动测试指南")
    print("=" * 50)
    print()
    print("请按照以下步骤进行手动测试：")
    print()
    print("1. 📁 测试新建文件夹功能：")
    print("   - 右键点击导航面板的'📁 自定义文件夹'")
    print("   - 选择'添加文件夹'")
    print("   - 输入文件夹名称，如'测试文件夹'")
    print("   - 验证文件夹是否自动出现在导航面板中")
    print()
    print("2. 📂 测试文件右键菜单功能：")
    print("   - 确保在'📁 所有文件'视图中（点击导航面板的'📁 所有文件'）")
    print("   - 右键点击任意文件")
    print("   - 查看是否有'添加到文件夹'和'移动到文件夹'菜单项")
    print("   - 测试'添加到文件夹'功能（文件保持在中转状态）")
    print("   - 测试'移动到文件夹'功能（文件移出中转状态）")
    print()
    print("3. 📦 测试批量操作：")
    print("   - 选择多个文件（按住Ctrl键点击）")
    print("   - 右键查看批量操作菜单")
    print("   - 测试批量添加到文件夹")
    print()
    print("4. 🔄 测试文件夹选择：")
    print("   - 点击导航面板中的自定义文件夹")
    print("   - 验证文件视图是否正确筛选显示该文件夹的文件")
    print()
    print("5. ✨ 测试新建文件夹并添加文件：")
    print("   - 右键点击文件，选择'添加到文件夹' > '+ 新建文件夹'")
    print("   - 输入新文件夹名称")
    print("   - 验证文件夹创建并文件添加成功")
    print()
    print("测试完成后，请关闭窗口退出程序。")
    print("=" * 50)
    
    # 运行应用程序
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
