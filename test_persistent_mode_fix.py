import sys
import os
sys.path.append('.')

try:
    from PySide6.QtWidgets import QApplication
    from smartvault.ui.widgets.clipboard_floating_widget import ClipboardFloatingWidget
    
    print("测试持续显示模式修复...")
    
    # 创建Qt应用
    app = QApplication(sys.argv)
    
    # 创建浮动窗口
    widget = ClipboardFloatingWidget()
    
    print("=== 测试持续显示模式行为 ===")
    
    # 设置为持续显示模式
    widget.set_persistent_mode(True)
    widget.set_monitoring_enabled(True)
    print("✅ 设置为持续显示模式")
    print(f"持续显示模式: {widget.is_persistent_mode}")
    print(f"监控启用状态: {widget.monitoring_enabled}")
    
    # 测试监控状态显示
    print("\n--- 测试监控状态显示 ---")
    widget.show_monitoring_status()
    print(f"标题: {widget.title_label.text()}")
    print(f"内容: {widget.content_label.text()}")
    print(f"查看按钮可见: {widget.view_button.isVisible()}")
    print(f"关闭按钮可见: {widget.close_button.isVisible()}")
    
    # 测试显示重复信息
    print("\n--- 测试显示重复信息 ---")
    duplicate_info = {
        'type': 'file',
        'source_name': 'test.txt',
        'duplicates': [{
            'id': '1',
            'name': '测试文件.txt'
        }]
    }
    
    widget.show_duplicate(duplicate_info)
    print(f"标题: {widget.title_label.text()}")
    print(f"内容: {widget.content_label.text()}")
    print(f"查看按钮可见: {widget.view_button.isVisible()}")
    print(f"关闭按钮可见: {widget.close_button.isVisible()}")
    print(f"当前重复信息: {widget.current_duplicate_info is not None}")
    
    # 测试查看按钮点击
    print("\n--- 测试查看按钮点击 ---")
    print("模拟点击查看按钮...")
    widget.on_view_clicked()
    print(f"点击后标题: {widget.title_label.text()}")
    print(f"点击后内容: {widget.content_label.text()}")
    print(f"点击后查看按钮可见: {widget.view_button.isVisible()}")
    print(f"点击后关闭按钮可见: {widget.close_button.isVisible()}")
    print(f"点击后当前重复信息: {widget.current_duplicate_info is not None}")
    print(f"窗口是否可见: {widget.isVisible()}")
    
    # 再次显示重复信息
    print("\n--- 再次显示重复信息 ---")
    widget.show_duplicate(duplicate_info)
    print(f"标题: {widget.title_label.text()}")
    print(f"内容: {widget.content_label.text()}")
    
    # 测试关闭按钮点击
    print("\n--- 测试关闭按钮点击 ---")
    print("模拟点击关闭按钮...")
    widget.on_close_clicked()
    print(f"点击后标题: {widget.title_label.text()}")
    print(f"点击后内容: {widget.content_label.text()}")
    print(f"点击后查看按钮可见: {widget.view_button.isVisible()}")
    print(f"点击后关闭按钮可见: {widget.close_button.isVisible()}")
    print(f"点击后当前重复信息: {widget.current_duplicate_info is not None}")
    print(f"窗口是否可见: {widget.isVisible()}")
    
    # 对比按需弹出模式
    print("\n=== 对比按需弹出模式 ===")
    widget.set_persistent_mode(False)
    print("✅ 设置为按需弹出模式")
    
    widget.show_duplicate(duplicate_info)
    print(f"显示重复信息后窗口可见: {widget.isVisible()}")
    
    print("模拟点击查看按钮...")
    widget.on_view_clicked()
    print(f"点击查看按钮后窗口可见: {widget.isVisible()}")
    
    print("\n=== 修复总结 ===")
    print("✅ 1. 持续显示模式下点击查看按钮不再隐藏窗口")
    print("   - 点击查看按钮后自动回到监控状态")
    print("   - 窗口保持可见并置于最前")
    print()
    print("✅ 2. 持续显示模式下点击关闭按钮回到监控状态")
    print("   - 关闭按钮不隐藏窗口，而是回到监控状态")
    print("   - 窗口始终保持可见")
    print()
    print("✅ 3. 鼠标离开事件优化")
    print("   - 持续显示模式下不启动自动隐藏定时器")
    print("   - 按需弹出模式下正常启动自动隐藏")
    print()
    print("✅ 4. 窗口保持最前")
    print("   - 使用 activateWindow() 确保窗口保持最前")
    print("   - 监控状态显示时自动置顶")
    
    # 不启动事件循环，只测试功能
    widget.close()
    
except Exception as e:
    print(f"测试失败: {e}")
    import traceback
    traceback.print_exc()
