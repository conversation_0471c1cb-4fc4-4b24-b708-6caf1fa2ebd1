#!/usr/bin/env python3
"""
调试导入问题
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("开始调试导入问题...")

try:
    print("1. 导入标准库...")
    import os
    import re
    from typing import List, Dict, Any, Optional
    from dataclasses import dataclass, field
    from enum import Enum
    print("✅ 标准库导入成功")
    
    print("2. 导入枚举...")
    from smartvault.services.auto_tag_service import ConditionType
    print("✅ ConditionType 导入成功")
    
    print("3. 导入逻辑操作符...")
    from smartvault.services.auto_tag_service import LogicOperator
    print("✅ LogicOperator 导入成功")
    
    print("4. 导入 Condition 类...")
    from smartvault.services.auto_tag_service import Condition
    print("✅ Condition 导入成功")
    
    print("5. 测试创建 Condition 实例...")
    condition = Condition(type=ConditionType.FILE_EXTENSION, value="pdf")
    print("✅ Condition 实例创建成功")
    
    print("6. 导入 ConditionGroup 类...")
    from smartvault.services.auto_tag_service import ConditionGroup
    print("✅ ConditionGroup 导入成功")
    
    print("7. 测试创建 ConditionGroup 实例...")
    group = ConditionGroup(operator=LogicOperator.AND)
    print("✅ ConditionGroup 实例创建成功")
    
    print("8. 测试添加条件到组...")
    group.conditions.append(condition)
    print("✅ 条件添加成功")
    
    print("9. 导入 AutoTagRule 类...")
    from smartvault.services.auto_tag_service import AutoTagRule
    print("✅ AutoTagRule 导入成功")
    
    print("10. 测试创建 AutoTagRule 实例...")
    rule = AutoTagRule(
        id="test",
        name="测试规则",
        tag_names=["测试"],
        condition_group=group
    )
    print("✅ AutoTagRule 实例创建成功")
    
    print("11. 测试规则描述...")
    description = rule.get_description()
    print(f"✅ 规则描述: {description}")
    
    print("12. 测试规则匹配...")
    file_info = {"name": "test.pdf", "size": 1024}
    result = rule.matches(file_info)
    print(f"✅ 规则匹配结果: {result}")
    
    print("🎉 所有测试通过！")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
    
    # 尝试获取更多信息
    print("\n🔍 详细错误信息:")
    print(f"错误类型: {type(e).__name__}")
    print(f"错误消息: {str(e)}")
    
    # 检查模块状态
    print("\n📋 模块状态:")
    if 'smartvault.services.auto_tag_service' in sys.modules:
        module = sys.modules['smartvault.services.auto_tag_service']
        print(f"模块已加载: {module}")
        print(f"模块属性: {dir(module)}")
    else:
        print("模块未加载")
