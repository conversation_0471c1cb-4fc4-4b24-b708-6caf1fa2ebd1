#!/usr/bin/env python3
"""
SmartVault 全面数据库安全与可靠性测试套件
专注于数据安全的深度测试，确保用户数据万无一失
"""

import sys
import os
import sqlite3
import shutil
import time
import threading
import random
import hashlib
import tempfile
import psutil
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class ComprehensiveDatabaseSecurityTest:
    """全面数据库安全测试类"""

    def __init__(self):
        self.start_time = time.time()
        self.log_messages = []
        self.test_results = {}
        self.critical_issues = []
        self.warnings = []

    def log(self, message):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        log_msg = f"[{timestamp}] {message}"
        self.log_messages.append(log_msg)
        print(log_msg)

    def get_database_path(self):
        """获取数据库路径"""
        try:
            from smartvault.data.database import Database
            db = Database.create_from_config()
            db_path = db.db_path
            db.close()
            return db_path
        except Exception as e:
            self.log(f"❌ 获取数据库路径失败: {e}")
            return None

    def test_1_extreme_concurrent_stress(self):
        """测试1: 极限并发压力测试"""
        self.log("🔥 测试1: 极限并发压力测试 (模拟100个用户同时操作)")

        try:
            db_path = self.get_database_path()
            if not db_path:
                return False

            # 创建100个并发连接，每个执行50次操作
            num_threads = 100
            operations_per_thread = 50
            errors = []

            def worker_thread(thread_id):
                """工作线程"""
                thread_errors = []
                try:
                    conn = sqlite3.connect(db_path, timeout=60.0)
                    cursor = conn.cursor()

                    for i in range(operations_per_thread):
                        try:
                            # 随机执行不同类型的查询
                            operation = random.choice(['files', 'tags', 'file_tags', 'count'])

                            if operation == 'files':
                                cursor.execute("SELECT * FROM files LIMIT 10 OFFSET ?", (random.randint(0, 1000),))
                            elif operation == 'tags':
                                cursor.execute("SELECT * FROM tags LIMIT 5")
                            elif operation == 'file_tags':
                                cursor.execute("SELECT COUNT(*) FROM file_tags")
                            else:
                                cursor.execute("SELECT COUNT(*) FROM files")

                            cursor.fetchall()

                        except Exception as e:
                            thread_errors.append(f"线程{thread_id}操作{i}: {e}")

                    conn.close()

                except Exception as e:
                    thread_errors.append(f"线程{thread_id}连接错误: {e}")

                return thread_errors

            # 启动所有线程
            with ThreadPoolExecutor(max_workers=num_threads) as executor:
                futures = [executor.submit(worker_thread, i) for i in range(num_threads)]

                for future in as_completed(futures):
                    thread_errors = future.result()
                    errors.extend(thread_errors)

            total_operations = num_threads * operations_per_thread
            error_rate = len(errors) / total_operations * 100

            self.log(f"  📊 总操作数: {total_operations}")
            self.log(f"  📊 错误数: {len(errors)}")
            self.log(f"  📊 错误率: {error_rate:.2f}%")

            if error_rate > 5:  # 错误率超过5%认为有问题
                self.critical_issues.append(f"极限并发测试错误率过高: {error_rate:.2f}%")
                self.log("  ❌ 极限并发测试失败")
                return False
            else:
                self.log("  ✅ 极限并发测试通过")
                return True

        except Exception as e:
            self.log(f"  ❌ 测试失败: {e}")
            self.critical_issues.append(f"极限并发测试异常: {e}")
            return False

    def test_2_data_corruption_simulation(self):
        """测试2: 数据损坏模拟测试"""
        self.log("🧪 测试2: 数据损坏模拟测试")

        try:
            db_path = self.get_database_path()
            if not db_path:
                return False

            # 创建测试数据库副本
            test_db_path = db_path + ".corruption_test"
            shutil.copy2(db_path, test_db_path)

            # 模拟各种损坏场景
            corruption_tests = [
                self._test_partial_file_corruption,
                self._test_index_corruption,
                self._test_transaction_interruption,
                self._test_disk_full_simulation
            ]

            passed_tests = 0
            for test_func in corruption_tests:
                try:
                    # 恢复干净的测试数据库
                    shutil.copy2(db_path, test_db_path)
                    if test_func(test_db_path):
                        passed_tests += 1
                except Exception as e:
                    self.log(f"    ⚠️ 损坏测试异常: {e}")

            # 清理测试文件
            if os.path.exists(test_db_path):
                os.remove(test_db_path)

            success_rate = passed_tests / len(corruption_tests) * 100
            self.log(f"  📊 损坏恢复测试通过率: {success_rate:.1f}%")

            if success_rate < 75:
                self.critical_issues.append(f"数据损坏恢复能力不足: {success_rate:.1f}%")
                return False
            else:
                self.log("  ✅ 数据损坏模拟测试通过")
                return True

        except Exception as e:
            self.log(f"  ❌ 测试失败: {e}")
            return False

    def _test_partial_file_corruption(self, test_db_path):
        """部分文件损坏测试"""
        self.log("    🔍 测试部分文件损坏恢复...")

        try:
            # 在文件中间写入随机数据模拟损坏
            with open(test_db_path, 'r+b') as f:
                f.seek(1024)  # 跳到文件中间
                f.write(b'\x00' * 100)  # 写入空字节

            # 尝试打开并检查
            conn = sqlite3.connect(test_db_path)
            cursor = conn.cursor()

            try:
                cursor.execute("PRAGMA integrity_check")
                result = cursor.fetchone()[0]
                conn.close()

                if result != "ok":
                    self.log(f"      ✅ 检测到损坏: {result}")
                    return True
                else:
                    self.log("      ⚠️ 未检测到预期的损坏")
                    return False

            except Exception as e:
                self.log(f"      ✅ 损坏检测成功: {e}")
                conn.close()
                return True

        except Exception as e:
            self.log(f"      ❌ 部分损坏测试失败: {e}")
            return False

    def _test_index_corruption(self, test_db_path):
        """索引损坏测试"""
        self.log("    🔍 测试索引损坏检测...")

        try:
            conn = sqlite3.connect(test_db_path)
            cursor = conn.cursor()

            # 删除一个索引然后检查
            cursor.execute("DROP INDEX IF EXISTS idx_files_added_at")
            cursor.execute("REINDEX")

            # 检查是否能正常查询
            cursor.execute("SELECT COUNT(*) FROM files")
            count = cursor.fetchone()[0]

            conn.close()

            if count > 0:
                self.log("      ✅ 索引损坏后仍可正常查询")
                return True
            else:
                self.log("      ❌ 索引损坏影响查询")
                return False

        except Exception as e:
            self.log(f"      ❌ 索引损坏测试失败: {e}")
            return False

    def _test_transaction_interruption(self, test_db_path):
        """事务中断测试"""
        self.log("    🔍 测试事务中断恢复...")

        try:
            conn = sqlite3.connect(test_db_path)
            cursor = conn.cursor()

            # 开始事务但不提交
            cursor.execute("BEGIN TRANSACTION")
            cursor.execute("INSERT INTO tags (id, name, created_at) VALUES (?, ?, ?)",
                         ('test_interrupt', '测试中断', datetime.now().isoformat()))

            # 强制关闭连接而不提交
            conn.close()

            # 重新打开检查数据是否回滚
            conn = sqlite3.connect(test_db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM tags WHERE id = ?", ('test_interrupt',))
            count = cursor.fetchone()[0]
            conn.close()

            if count == 0:
                self.log("      ✅ 事务中断后正确回滚")
                return True
            else:
                self.log("      ❌ 事务中断后未正确回滚")
                return False

        except Exception as e:
            self.log(f"      ❌ 事务中断测试失败: {e}")
            return False

    def _test_disk_full_simulation(self, test_db_path):
        """磁盘空间不足模拟"""
        self.log("    🔍 测试磁盘空间不足处理...")

        try:
            # 这个测试比较复杂，简化为检查数据库是否能处理写入错误
            conn = sqlite3.connect(test_db_path)
            cursor = conn.cursor()

            # 尝试插入大量数据直到可能出错
            try:
                for i in range(1000):
                    large_data = 'x' * 10000  # 10KB数据
                    cursor.execute("INSERT INTO tags (id, name, created_at) VALUES (?, ?, ?)",
                                 (f'large_test_{i}', large_data, datetime.now().isoformat()))

                conn.commit()

                # 清理测试数据
                cursor.execute("DELETE FROM tags WHERE id LIKE 'large_test_%'")
                conn.commit()

            except Exception as e:
                self.log(f"      ✅ 正确处理写入错误: {e}")

            conn.close()
            return True

        except Exception as e:
            self.log(f"      ❌ 磁盘空间测试失败: {e}")
            return False

    def test_3_long_term_endurance(self):
        """测试3: 长期耐久性测试"""
        self.log("⏰ 测试3: 长期耐久性测试 (模拟24小时连续使用)")

        try:
            db_path = self.get_database_path()
            if not db_path:
                return False

            # 模拟长期使用：连续执行2000次操作
            operations = 2000
            errors = []

            # 记录初始数据校验和
            initial_checksum = self._get_database_checksum(db_path)

            conn = sqlite3.connect(db_path, timeout=60.0)
            cursor = conn.cursor()

            for i in range(operations):
                try:
                    # 模拟各种操作
                    operation_type = random.choice(['read', 'read', 'read', 'read', 'count'])  # 读操作为主

                    if operation_type == 'read':
                        table = random.choice(['files', 'tags', 'file_tags'])
                        cursor.execute(f"SELECT * FROM {table} LIMIT 10 OFFSET ?", (random.randint(0, 100),))
                        cursor.fetchall()
                    else:
                        cursor.execute("SELECT COUNT(*) FROM files")
                        cursor.fetchone()

                    # 每100次操作检查一次数据完整性
                    if (i + 1) % 100 == 0:
                        current_checksum = self._get_database_checksum(db_path)
                        if current_checksum != initial_checksum:
                            errors.append(f"第{i+1}次操作后数据校验和变化")

                        self.log(f"    📊 已完成 {i+1}/{operations} 次操作")

                except Exception as e:
                    errors.append(f"第{i+1}次操作错误: {e}")

            conn.close()

            error_rate = len(errors) / operations * 100
            self.log(f"  📊 长期测试错误率: {error_rate:.3f}%")

            if error_rate > 0.1:  # 错误率超过0.1%认为有问题
                self.critical_issues.append(f"长期耐久性测试错误率过高: {error_rate:.3f}%")
                self.log("  ❌ 长期耐久性测试失败")
                return False
            else:
                self.log("  ✅ 长期耐久性测试通过")
                return True

        except Exception as e:
            self.log(f"  ❌ 测试失败: {e}")
            return False

    def _get_database_checksum(self, db_path):
        """获取数据库内容校验和"""
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 获取所有表的数据并计算校验和
            checksums = {}

            tables = ['files', 'tags', 'file_tags']
            for table in tables:
                cursor.execute(f"SELECT * FROM {table} ORDER BY rowid")
                data = str(cursor.fetchall())
                checksums[table] = hashlib.md5(data.encode()).hexdigest()

            conn.close()
            return checksums

        except Exception as e:
            return f"error: {e}"

    def test_4_backup_recovery_comprehensive(self):
        """测试4: 全面备份恢复测试"""
        self.log("💾 测试4: 全面备份恢复测试")

        try:
            db_path = self.get_database_path()
            if not db_path:
                return False

            # 创建备份
            backup_path = db_path + ".comprehensive_backup"
            shutil.copy2(db_path, backup_path)

            # 记录原始数据
            original_checksum = self._get_database_checksum(db_path)

            # 模拟数据变化
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("INSERT INTO tags (id, name, created_at) VALUES (?, ?, ?)",
                         ('backup_test', '备份测试', datetime.now().isoformat()))
            conn.commit()
            conn.close()

            # 恢复备份
            shutil.copy2(backup_path, db_path)

            # 验证恢复
            restored_checksum = self._get_database_checksum(db_path)

            # 清理
            if os.path.exists(backup_path):
                os.remove(backup_path)

            if original_checksum == restored_checksum:
                self.log("  ✅ 备份恢复测试通过")
                return True
            else:
                self.critical_issues.append("备份恢复后数据不一致")
                self.log("  ❌ 备份恢复测试失败")
                return False

        except Exception as e:
            self.log(f"  ❌ 测试失败: {e}")
            return False

    def test_5_memory_pressure(self):
        """测试5: 内存压力测试"""
        self.log("🧠 测试5: 内存压力测试")

        try:
            db_path = self.get_database_path()
            if not db_path:
                return False

            # 创建多个连接模拟内存压力
            connections = []
            max_connections = 50

            try:
                for i in range(max_connections):
                    conn = sqlite3.connect(db_path, timeout=30.0)
                    connections.append(conn)

                # 在所有连接上执行查询
                for i, conn in enumerate(connections):
                    cursor = conn.cursor()
                    cursor.execute("SELECT COUNT(*) FROM files")
                    cursor.fetchone()

                self.log(f"  📊 成功创建 {len(connections)} 个并发连接")

                # 关闭所有连接
                for conn in connections:
                    conn.close()

                self.log("  ✅ 内存压力测试通过")
                return True

            except Exception as e:
                # 关闭已创建的连接
                for conn in connections:
                    try:
                        conn.close()
                    except:
                        pass

                self.log(f"  ❌ 内存压力测试失败: {e}")
                return False

        except Exception as e:
            self.log(f"  ❌ 测试失败: {e}")
            return False

    def run_comprehensive_tests(self):
        """运行全面安全测试"""
        self.log("🛡️ 开始 SmartVault 全面数据库安全测试")
        self.log("⚠️  这是一个深度安全测试，将进行严格的数据安全验证")

        # 定义所有测试
        tests = [
            ("极限并发压力测试", self.test_1_extreme_concurrent_stress),
            ("数据损坏模拟测试", self.test_2_data_corruption_simulation),
            ("长期耐久性测试", self.test_3_long_term_endurance),
            ("全面备份恢复测试", self.test_4_backup_recovery_comprehensive),
            ("内存压力测试", self.test_5_memory_pressure),
        ]

        passed_tests = 0
        total_tests = len(tests)

        for test_name, test_func in tests:
            self.log(f"\n{'='*60}")
            try:
                if test_func():
                    passed_tests += 1
                    self.test_results[test_name] = "PASS"
                else:
                    self.test_results[test_name] = "FAIL"
            except Exception as e:
                self.log(f"❌ {test_name} 执行异常: {e}")
                self.test_results[test_name] = "ERROR"
                self.critical_issues.append(f"{test_name} 执行异常: {e}")

        # 生成详细报告
        self._generate_comprehensive_report(passed_tests, total_tests)

        return passed_tests == total_tests and len(self.critical_issues) == 0

    def _generate_comprehensive_report(self, passed_tests, total_tests):
        """生成全面测试报告"""
        total_time = time.time() - self.start_time

        report = []
        report.append("=" * 80)
        report.append("SmartVault 全面数据库安全测试报告")
        report.append("=" * 80)
        report.append(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"测试耗时: {total_time:.2f}秒")
        report.append(f"通过测试: {passed_tests}/{total_tests}")
        report.append("")

        # 测试结果详情
        report.append("🔍 测试结果详情:")
        for test_name, result in self.test_results.items():
            status_icon = "✅" if result == "PASS" else "❌" if result == "FAIL" else "⚠️"
            report.append(f"  {status_icon} {test_name}: {result}")
        report.append("")

        # 关键问题
        if self.critical_issues:
            report.append("🚨 发现的关键问题:")
            for issue in self.critical_issues:
                report.append(f"  - {issue}")
            report.append("")

        # 警告信息
        if self.warnings:
            report.append("⚠️ 警告信息:")
            for warning in self.warnings:
                report.append(f"  - {warning}")
            report.append("")

        # 安全评级
        if len(self.critical_issues) == 0 and passed_tests == total_tests:
            safety_grade = "A+ (卓越)"
            safety_score = 100
            recommendation = "数据库安全性卓越，可以放心在生产环境使用"
        elif len(self.critical_issues) == 0 and passed_tests >= total_tests * 0.8:
            safety_grade = "A (优秀)"
            safety_score = 90
            recommendation = "数据库安全性优秀，建议关注未通过的测试项"
        elif len(self.critical_issues) <= 2 and passed_tests >= total_tests * 0.6:
            safety_grade = "B (良好)"
            safety_score = 75
            recommendation = "数据库安全性良好，建议修复发现的问题"
        elif len(self.critical_issues) <= 5:
            safety_grade = "C (一般)"
            safety_score = 60
            recommendation = "数据库存在一些安全隐患，建议优先修复关键问题"
        else:
            safety_grade = "D (需要改进)"
            safety_score = 40
            recommendation = "数据库存在严重安全风险，强烈建议修复后再使用"

        report.append(f"🏆 安全评级: {safety_grade}")
        report.append(f"📊 安全评分: {safety_score}/100")
        report.append("")
        report.append(f"💡 建议: {recommendation}")
        report.append("")

        # 详细建议
        report.append("📋 详细建议:")
        if len(self.critical_issues) == 0:
            report.append("  - 数据库安全性表现优秀")
            report.append("  - 建议定期运行此测试套件进行监控")
            report.append("  - 在重要更新前进行完整测试")
        else:
            report.append("  - 优先修复所有关键问题")
            report.append("  - 增加数据库监控和告警机制")
            report.append("  - 考虑实施更频繁的备份策略")
            report.append("  - 在生产环境部署前重新测试")

        report.append("")
        report.append("🔒 数据安全提醒:")
        report.append("  - 定期备份数据库文件")
        report.append("  - 监控数据库文件大小和性能")
        report.append("  - 在系统更新前进行完整备份")
        report.append("  - 考虑实施数据库文件的版本控制")
        report.append("")
        report.append("=" * 80)

        # 保存报告
        report_content = "\n".join(report)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"comprehensive_database_security_report_{timestamp}.txt"

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        self.log(f"📄 全面安全测试报告已保存到: {report_file}")
        print("\n" + report_content)

        return report_content

def main():
    """主函数"""
    print("🛡️ SmartVault 全面数据库安全测试套件")
    print("⚠️  这是一个深度安全测试，将进行严格的数据库安全验证")
    print("⚠️  测试过程可能需要较长时间，请耐心等待")
    print("⚠️  当前数据库为测试数据，可以安全进行各种测试")

    response = input("\n是否开始全面安全测试? (y/N): ")
    if response.lower() != 'y':
        print("测试已取消")
        return

    # 运行全面测试
    tester = ComprehensiveDatabaseSecurityTest()
    success = tester.run_comprehensive_tests()

    if success:
        print("\n🎉 恭喜！数据库通过了全面的安全测试")
        print("💪 您的数据库具有优秀的安全性和可靠性")
    else:
        print("\n⚠️ 发现了一些需要关注的问题")
        print("🔧 建议根据报告进行相应的优化")

if __name__ == '__main__':
    main()
