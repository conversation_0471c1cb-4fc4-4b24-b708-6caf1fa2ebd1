#!/usr/bin/env python3
"""
AI设置开关修复验证

验证AI开关问题的修复效果
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from smartvault.utils.config import load_config, save_config, get_ai_status, save_ai_status


def verify_advanced_page_ai_switch():
    """验证高级设置页面的AI开关"""
    print("🔧 验证高级设置页面AI开关")
    print("=" * 50)
    
    try:
        # 检查高级设置页面代码
        advanced_page_path = "smartvault/ui/dialogs/settings/pages/advanced_page.py"
        
        if os.path.exists(advanced_page_path):
            with open(advanced_page_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否包含AI开关相关代码
            checks = [
                ("enable_ai_features_check", "AI开关控件"),
                ("启用AI功能", "AI开关文本"),
                ("enable_ai_features", "配置键名"),
                ("AI功能设置组", "AI设置组")
            ]
            
            all_passed = True
            for check_item, description in checks:
                if check_item in content:
                    print(f"✅ {description}: 已添加")
                else:
                    print(f"❌ {description}: 缺失")
                    all_passed = False
            
            return all_passed
        else:
            print(f"❌ 文件不存在: {advanced_page_path}")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


def verify_ai_config_manager():
    """验证AI配置管理器"""
    print("\n🛠️ 验证AI配置管理器")
    print("=" * 50)
    
    try:
        from smartvault.ui.dialogs.settings.pages.ai.utils.ai_config_helper import AIConfigManager
        
        config_manager = AIConfigManager()
        
        # 测试配置加载
        ai_config = config_manager.load_ai_config()
        print(f"✅ 配置加载: 成功 ({len(ai_config)} 项)")
        
        # 测试配置验证
        is_valid, error_msg = config_manager.validate_ai_config(ai_config)
        if is_valid:
            print("✅ 配置验证: 通过")
        else:
            print(f"❌ 配置验证: 失败 - {error_msg}")
            return False
        
        # 测试保存功能（检查是否还有TODO）
        config_helper_path = "smartvault/ui/dialogs/settings/pages/ai/utils/ai_config_helper.py"
        if os.path.exists(config_helper_path):
            with open(config_helper_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if "TODO: 实际保存到配置文件" in content:
                print("❌ 保存功能: 仍有TODO项")
                return False
            elif "save_ai_config(config)" in content:
                print("✅ 保存功能: 已实现")
            else:
                print("⚠️ 保存功能: 状态不明")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


def verify_switch_sync_manager():
    """验证开关同步管理器"""
    print("\n🔄 验证开关同步管理器")
    print("=" * 50)
    
    try:
        from smartvault.ui.dialogs.settings.pages.ai.utils.ai_switch_sync import ai_switch_sync_manager
        
        print("✅ 同步管理器: 导入成功")
        
        # 检查关键方法
        methods = [
            'register_switch',
            'sync_all_switches', 
            'get_master_status',
            'update_from_config'
        ]
        
        for method in methods:
            if hasattr(ai_switch_sync_manager, method):
                print(f"✅ 方法 {method}: 存在")
            else:
                print(f"❌ 方法 {method}: 缺失")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


def verify_config_consistency():
    """验证配置一致性"""
    print("\n📋 验证配置一致性")
    print("=" * 50)
    
    try:
        # 获取当前配置
        config = load_config()
        
        # 检查各个AI开关位置
        advanced_ai = config.get("advanced", {}).get("enable_ai_features", None)
        ai_enabled = config.get("ai", {}).get("enabled", None)
        auto_tags_ai = config.get("auto_tags", {}).get("enable_ai", None)
        
        print(f"高级设置.enable_ai_features: {advanced_ai}")
        print(f"AI配置.enabled: {ai_enabled}")
        print(f"自动标签.enable_ai: {auto_tags_ai}")
        
        # 检查get_ai_status函数
        ai_status = get_ai_status()
        print(f"get_ai_status(): {ai_status}")
        
        # 测试状态切换
        print("\n🧪 测试状态切换...")
        original_status = ai_status
        
        # 切换状态
        new_status = not original_status
        save_ai_status(new_status)
        
        # 验证切换结果
        updated_config = load_config()
        updated_status = get_ai_status()
        
        if updated_status == new_status:
            print(f"✅ 状态切换成功: {original_status} -> {new_status}")
            
            # 检查所有位置是否都更新了
            updated_advanced = updated_config.get("advanced", {}).get("enable_ai_features", False)
            updated_ai = updated_config.get("ai", {}).get("enabled", False)
            updated_auto_tags = updated_config.get("auto_tags", {}).get("enable_ai", False)
            
            if updated_advanced == updated_ai == updated_auto_tags == new_status:
                print("✅ 所有配置位置同步更新")
                consistency_ok = True
            else:
                print("❌ 配置位置更新不一致")
                print(f"   高级设置: {updated_advanced}")
                print(f"   AI配置: {updated_ai}")
                print(f"   自动标签: {updated_auto_tags}")
                consistency_ok = False
        else:
            print(f"❌ 状态切换失败: 期望 {new_status}, 实际 {updated_status}")
            consistency_ok = False
        
        # 恢复原始状态
        save_ai_status(original_status)
        print(f"🔄 已恢复原始状态: {original_status}")
        
        return consistency_ok
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


def verify_ui_components():
    """验证UI组件"""
    print("\n🖥️ 验证UI组件")
    print("=" * 50)
    
    try:
        # 检查是否可以创建UI组件（需要QApplication）
        from PySide6.QtWidgets import QApplication
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 测试高级设置页面
        from smartvault.ui.dialogs.settings.pages.advanced_page import AdvancedSettingsPage
        
        advanced_page = AdvancedSettingsPage()
        
        # 检查AI开关控件是否存在
        if hasattr(advanced_page, 'enable_ai_features_check'):
            print("✅ 高级设置页面: AI开关控件存在")
        else:
            print("❌ 高级设置页面: AI开关控件缺失")
            return False
        
        # 测试AI设置页面
        from smartvault.ui.dialogs.settings.pages.ai.ai_main_page import AISettingsPage
        
        ai_page = AISettingsPage()
        print("✅ AI设置页面: 创建成功")
        
        # 测试AI功能组件
        from smartvault.ui.dialogs.settings.pages.ai.components.ai_features_widget import AIFeaturesWidget
        
        features_widget = AIFeaturesWidget()
        if hasattr(features_widget, 'ai_enabled_checkbox'):
            print("✅ AI功能组件: AI总开关存在")
        else:
            print("❌ AI功能组件: AI总开关缺失")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ UI组件验证失败: {e}")
        return False


def main():
    """主验证函数"""
    print("🔍 AI设置开关修复验证")
    print("=" * 80)
    
    # 执行各项验证
    results = []
    
    results.append(("高级设置页面AI开关", verify_advanced_page_ai_switch()))
    results.append(("AI配置管理器", verify_ai_config_manager()))
    results.append(("开关同步管理器", verify_switch_sync_manager()))
    results.append(("配置一致性", verify_config_consistency()))
    results.append(("UI组件", verify_ui_components()))
    
    # 输出验证结果
    print("\n📊 验证结果汇总")
    print("=" * 80)
    
    passed_count = 0
    total_count = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed_count += 1
    
    print(f"\n🎯 总体结果: {passed_count}/{total_count} 项通过")
    
    if passed_count == total_count:
        print("\n🎉 恭喜！所有AI开关问题已修复！")
        print("📋 修复内容:")
        print("   ✅ 高级设置页面添加了AI总开关")
        print("   ✅ AI配置管理器完善了保存功能")
        print("   ✅ 创建了开关同步管理器")
        print("   ✅ 配置一致性得到保证")
        print("   ✅ UI组件正常工作")
        print("\n🚀 AI设置界面已准备就绪！")
    else:
        print(f"\n❌ 还有 {total_count - passed_count} 项需要修复")
    
    return passed_count == total_count


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
