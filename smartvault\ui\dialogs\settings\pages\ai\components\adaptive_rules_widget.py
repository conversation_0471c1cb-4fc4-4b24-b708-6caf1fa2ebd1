"""
自适应规则设置组件

提供自适应规则引擎的设置和管理功能
预期代码长度: < 200行
当前代码长度: 190行 ✅
"""

from typing import Dict
from PySide6.QtWidgets import (
    QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTextEdit,
    QGroupBox, QGridLayout, QTableWidget, QTableWidgetItem, QHeaderView
)
from PySide6.QtCore import Qt
from .base_ai_widget import BaseAIWidget


class AdaptiveRulesWidget(BaseAIWidget):
    """自适应规则设置组件"""
    
    def __init__(self, parent=None):
        super().__init__("自适应规则引擎", parent)
    
    def setup_ui(self):
        """设置自适应规则UI"""
        layout = QVBoxLayout(self)
        
        # 规则性能统计
        stats_group = QGroupBox("规则性能统计")
        stats_layout = QGridLayout(stats_group)
        
        stats_layout.addWidget(QLabel("规则总数:"), 0, 0)
        self.total_rules_label = QLabel("0")
        stats_layout.addWidget(self.total_rules_label, 0, 1)
        
        stats_layout.addWidget(QLabel("高性能规则:"), 0, 2)
        self.high_perf_rules_label = QLabel("0")
        stats_layout.addWidget(self.high_perf_rules_label, 0, 3)
        
        stats_layout.addWidget(QLabel("中等性能规则:"), 1, 0)
        self.medium_perf_rules_label = QLabel("0")
        stats_layout.addWidget(self.medium_perf_rules_label, 1, 1)
        
        stats_layout.addWidget(QLabel("低性能规则:"), 1, 2)
        self.low_perf_rules_label = QLabel("0")
        stats_layout.addWidget(self.low_perf_rules_label, 1, 3)
        
        stats_layout.addWidget(QLabel("需要优化:"), 2, 0)
        self.optimization_needed_label = QLabel("0")
        stats_layout.addWidget(self.optimization_needed_label, 2, 1)
        
        stats_layout.addWidget(QLabel("平均性能:"), 2, 2)
        self.average_perf_label = QLabel("0.00")
        stats_layout.addWidget(self.average_perf_label, 2, 3)
        
        layout.addWidget(stats_group)
        
        # 最佳规则表格
        top_rules_group = QGroupBox("最佳规则")
        top_rules_layout = QVBoxLayout(top_rules_group)
        
        self.top_rules_table = QTableWidget()
        self.top_rules_table.setColumnCount(3)
        self.top_rules_table.setHorizontalHeaderLabels(["规则ID", "性能评分", "应用次数"])
        self.top_rules_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.top_rules_table.setMaximumHeight(120)
        top_rules_layout.addWidget(self.top_rules_table)
        
        layout.addWidget(top_rules_group)
        
        # 规则优化建议
        recommendations_group = QGroupBox("优化建议")
        recommendations_layout = QVBoxLayout(recommendations_group)
        
        self.recommendations_text = QTextEdit()
        self.recommendations_text.setMaximumHeight(100)
        self.recommendations_text.setReadOnly(True)
        self.recommendations_text.setPlainText("暂无优化建议")
        recommendations_layout.addWidget(self.recommendations_text)
        
        layout.addWidget(recommendations_group)
        
        # 规则生成设置
        generation_group = QGroupBox("规则生成设置")
        generation_layout = QGridLayout(generation_group)
        
        generation_layout.addWidget(QLabel("最小置信度:"), 0, 0)
        self.min_confidence_label = QLabel("0.8")
        generation_layout.addWidget(self.min_confidence_label, 0, 1)
        
        generation_layout.addWidget(QLabel("最小频率:"), 0, 2)
        self.min_frequency_label = QLabel("5")
        generation_layout.addWidget(self.min_frequency_label, 0, 3)
        
        generation_layout.addWidget(QLabel("自动生成规则:"), 1, 0)
        self.auto_generation_label = QLabel("启用")
        generation_layout.addWidget(self.auto_generation_label, 1, 1)
        
        layout.addWidget(generation_group)
        
        # 操作按钮
        buttons_layout = QHBoxLayout()
        
        self.refresh_button = QPushButton("刷新规则数据")
        self.refresh_button.clicked.connect(self.refresh_rules_data)
        buttons_layout.addWidget(self.refresh_button)
        
        self.generate_button = QPushButton("手动生成规则")
        self.generate_button.clicked.connect(self.generate_rules)
        buttons_layout.addWidget(self.generate_button)
        
        self.optimize_button = QPushButton("优化规则")
        self.optimize_button.clicked.connect(self.optimize_rules)
        buttons_layout.addWidget(self.optimize_button)
        
        self.view_details_button = QPushButton("查看详情")
        self.view_details_button.clicked.connect(self.view_rule_details)
        buttons_layout.addWidget(self.view_details_button)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
    
    def load_config(self, config: Dict):
        """加载配置到UI控件"""
        self._config = config
        self.refresh_rules_data()
    
    def save_config(self) -> Dict:
        """从UI控件保存配置"""
        # 自适应规则组件主要用于显示和管理，不需要保存特定配置
        return {}
    
    def refresh_rules_data(self):
        """刷新规则数据显示"""
        if not self.config_manager:
            return
        
        try:
            stats = self.config_manager.get_rule_performance_stats()
            
            # 更新统计信息
            self.total_rules_label.setText(str(stats.get('total_rules', 0)))
            self.high_perf_rules_label.setText(str(stats.get('high_performance_rules', 0)))
            self.medium_perf_rules_label.setText(str(stats.get('medium_performance_rules', 0)))
            self.low_perf_rules_label.setText(str(stats.get('low_performance_rules', 0)))
            self.optimization_needed_label.setText(str(stats.get('rules_needing_optimization', 0)))
            self.average_perf_label.setText(f"{stats.get('average_performance', 0):.2f}")
            
            # 更新最佳规则表格
            top_rules = stats.get('top_performing_rules', [])
            self.top_rules_table.setRowCount(len(top_rules))
            
            for i, rule in enumerate(top_rules):
                self.top_rules_table.setItem(i, 0, QTableWidgetItem(rule['rule_id']))
                self.top_rules_table.setItem(i, 1, QTableWidgetItem(f"{rule['performance_score']:.2f}"))
                self.top_rules_table.setItem(i, 2, QTableWidgetItem(str(rule['total_applications'])))
            
            # 更新优化建议
            worst_rules = stats.get('worst_performing_rules', [])
            if worst_rules:
                recommendations_text = "需要优化的规则:\n"
                for rule in worst_rules[:3]:
                    if rule['performance_score'] < 0.5:
                        recommendations_text += f"• {rule['rule_id']}: 性能评分 {rule['performance_score']:.2f} (建议优化)\n"
                
                if recommendations_text == "需要优化的规则:\n":
                    recommendations_text = "所有规则性能良好，暂无优化建议"
                    
                self.recommendations_text.setPlainText(recommendations_text)
            else:
                self.recommendations_text.setPlainText("暂无规则性能数据")
                
        except Exception as e:
            self.show_error(f"刷新规则数据失败: {e}")
    
    def generate_rules(self):
        """手动生成规则"""
        try:
            if not self.config_manager:
                self.show_error("配置管理器未初始化")
                return
            
            # TODO: 实现手动生成规则的功能
            # 这里需要调用AI管理器的规则生成功能
            
            self.show_info("规则生成功能开发中...")
            
        except Exception as e:
            self.show_error(f"生成规则失败: {e}")
    
    def optimize_rules(self):
        """优化规则"""
        try:
            if not self.config_manager:
                self.show_error("配置管理器未初始化")
                return
            
            # TODO: 实现规则优化功能
            
            self.show_info("规则优化功能开发中...")
            
        except Exception as e:
            self.show_error(f"优化规则失败: {e}")
    
    def view_rule_details(self):
        """查看规则详情"""
        try:
            # 导入规则性能对话框
            from ..dialogs.rule_performance_dialog import RulePerformanceDialog
            
            dialog = RulePerformanceDialog(self)
            if self.config_manager:
                dialog.set_config_manager(self.config_manager)
            dialog.exec()
            
        except ImportError:
            self.show_info("规则详情对话框开发中...")
        except Exception as e:
            self.show_error(f"查看规则详情失败: {e}")
