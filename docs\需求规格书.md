# SmartVault 需求规格说明书（第二版）

__version__ = "2.0.0"
__author__ = "Mojianghu"
__release_date__ = "20250522"

## 🎯 **核心开发理念**

> **"功能优先，适度优化；能工作的代码比完美的架构更有价值。"**

## 文档说明

本文档是SmartVault项目的需求规格说明书（第二版），基于务实开发策略调整，明确定义系统功能需求、用户场景和验收标准。本文档专注于功能价值，不涉及具体技术实现，技术实现细节请参考《技术选型及架构设计》文档。

## 项目资源
- docs\需求规格书.md（本文档，定义系统功能需求，不涉及具体技术实现）
- docs\技术选型及架构设计.md（项目技术指导的唯一权威文档）
- docs\开发实施方案.md（开发计划和进度跟踪文档）
- icons\SmartVault.ico（应用图标）
- SmartVault_UI.png（UI界面参考图）

## 1. 执行摘要

SmartVault是一个全流程本地化的智能文件管理系统，旨在解决用户在数字时代面临的文件管理挑战。系统通过"智能文件库"概念，提供自动化分类、智能检索、查重、标注等功能，实现"用户干预极简化"的核心价值。

核心功能包括：
- 智能文件库管理：支持链接、复制和移动三种入库方式
- 多维度文件组织：突破传统文件夹结构限制，支持一个文件属于多个类别
- 智能搜索与检索：基于内容、标签、元数据的综合搜索
- 文件重复管理：自动检测重复文件，节省存储空间
- 特定文件类型管理：针对软件、视频、图片、文档等提供专门管理功能

本项目分为两个开发阶段：核心功能开发阶段和AI增强开发阶段。AI辅助采用轻量级免费开源的本地化机器学习模型，系统预留统一的AI辅助接口方法开关，各模块需要的AI能力统一由此接口方法提供。

## 2. 项目概述

### 2.1 背景与目标

在当今数字时代，用户面临着文件管理的诸多挑战：文件数量激增、分类繁琐、查找困难、重复文件占用空间等问题。SmartVault旨在通过智能化、自动化的方式解决这些问题，提供一个全面的文件管理解决方案。

### 2.2 目标用户群体

- **普通用户**：需要简单高效管理个人文件的用户
- **知识工作者**：需要管理大量文档和资料的专业人士
- **内容创作者**：需要管理图片、视频等媒体文件的创作者
- **IT专业人员**：需要管理软件、代码等技术文件的用户

### 2.3 系统定位

SmartVault定位为一个本地化的智能文件管理系统，不依赖云服务，保护用户隐私的同时提供智能化功能。系统适用于Windows平台，针对普通PC的性能进行优化，确保在中等配置的计算机上也能流畅运行。

## 3. 功能需求

### 3.1 核心功能（P0-必须）

#### 3.1.1 智能文件库管理

- **智能文件库概念**：
  - 定义：SmartVault作为一个文件管理库，只管理明确添加到库中的文件
  - 职责边界：数据库和索引仅覆盖已入库的文件，减少系统资源占用并保护用户隐私
  - 用户界面：导航栏和文件视图仅显示已入库的文件，使系统职责边界清晰

- **多种入库方式及入库选项**：
  - 支持手动添加、自动监控指定目录、拖拽添加、剪贴板检测多种方式将文件添加到智能文件库
  - 链接到智能文件库（默认）：保持文件在原位置，仅在库中创建引用
  - 复制到智能文件库：在库存储位置创建文件副本
  - 移动到智能文件库：将文件移动到库存储位置

- **文件操作**：
  - 文件列表的右键菜单的 删除文件 被点击时弹出删除确认对话框，对话框有 确定/取消 按钮和 "同时删除物理文件"复选框（选项持久化保存），软件的先判断用户选中的文件或者多个文件中的 物理文件是否存在 ，行为如下：
    1. 物理文件 存在/在线：将选中的文件移动到系统的回收站，并在数据库移除选中的文件信息
    2. 物理文件 不存在/离线 ：对话框显示具体信息“x个物理文件不存在/离线，点击 确定 将仅删除文件链接信息”，并且 "同时删除物理文件"复选框自动临时禁用，用户点击确定时则仅删除数据库的文件信息
  - 移动和重命名：自动同步物理文件和库信息，对于物理文件的相关操作逻辑与 文件删除逻辑 类似。
  - 操作反馈：通过简洁通知提供操作结果反馈

- **验收标准**：
  - 用户能够通过三种方式将文件添加到智能文件库
  - 系统能够正确跟踪和管理文件状态
  - 用户界面清晰显示文件的入库状态和位置

#### 3.1.2 文件监控与自动处理

- **监控文件夹**：
  - 功能：监控指定文件夹（如下载目录）中的新文件
  - 性能要求：使用低资源占用的监控方法，CPU占用不超过5%
  - 自动处理：根据用户设置自动将新文件添加到智能文件库

- **自动分类系统**：
  - 功能：根据文件内容、类型和创建日期自动归类
  - 实现方式：基于文件扩展名、元数据和内容特征进行分类
  - 用户控制：提供分类规则编辑界面，允许用户自定义分类规则

- **验收标准**：
  - 系统能够在1秒内检测到监控文件夹中的新文件
  - 自动分类准确率达到80%以上
  - 系统资源占用符合性能要求

#### 3.1.3 重复检测与管理

- **重复文件检测**：
  - 功能：检测智能文件库中的重复或相似文件
  - 实现方式：基于文件名特征、MD5值等方法进行检测
  - 性能要求：1000个文件的查重时间不超过30秒

- **重复处理建议**：
  - 功能：提供重复文件处理建议
  - 用户选项：保留所有、保留最新、保留最大、自定义选择

- **验收标准**：
  - 重复检测准确率达到95%以上
  - 系统能够正确识别并处理重复文件
  - 用户界面清晰显示重复文件信息和处理选项

#### 3.1.4 智能搜索

- **搜索功能**：
  - 基本搜索：支持文件名、标签、内容关键词搜索
  - 高级搜索：支持多条件组合搜索（如类型+日期+标签）
  - 搜索范围：默认只搜索智能文件库内文件，可选扩展到全盘搜索

- **搜索结果展示**：
  - 结果排序：按相关性、日期、大小等多种方式排序
  - 结果预览：提供文件缩略图和内容预览
  - 结果过滤：支持通过标签、类型等进一步过滤结果

- **验收标准**：
  - 基本搜索响应时间不超过1秒（对于10000个文件的库）
  - 搜索结果准确率达到90%以上
  - 用户界面支持直观的结果浏览和过滤

### 3.2 文件类型特定功能（P1-重要）

#### 3.2.1 软件管理

- **软件入库与分类**：
  - 功能：软件入库、分类、备注功能
  - 元数据记录：版本、安装源、功能描述等
  - 验收标准：系统能够正确识别和分类常见软件类型

#### 3.2.2 文档管理

- **文档组织**：
  - 功能：文档分类、关联和版本管理
  - 元数据提取：自动提取标题、作者等信息
  - 验收标准：系统能够正确组织和关联相关文档

#### 3.2.3 媒体文件管理

- **图片管理**：
  - 功能：图片分类、去重和组织
  - 特殊处理：相似图片分组，基于时间/地点组织
  - 验收标准：系统能够有效管理大量图片文件

- **视频管理**：
  - 功能：视频分类、片段管理
  - 特殊处理：视频内容索引，片段截取与标记
  - 验收标准：系统能够有效管理视频文件和片段

### 3.3 AI增强功能（P2-次要）

#### 3.3.1 AI辅助接口

- **统一AI接口**：
  - 设计：预留统一的AI辅助接口方法开关
  - 实现：各模块需要的AI能力统一由此接口方法提供
  - 降级处理：AI离线时自动切换到非AI模式

- **验收标准**：
  - 系统能够在有无AI的情况下均正常运行
  - AI功能可通过单一开关控制
  - 降级处理平滑，用户体验一致

#### 3.3.2 内容识别与分类

- **AI内容识别**：
  - 功能：识别图片、视频、文档内容
  - 实现：使用轻量级本地化机器学习模型
  - 性能要求：识别过程不影响系统主要功能的响应时间

- **智能分类建议**：
  - 功能：基于内容提供分类建议
  - 用户控制：用户可接受或拒绝建议
  - 验收标准：分类建议准确率达到70%以上

## 4. 非功能性需求

### 4.1 性能要求

- **响应时间**：
  - 基本操作（如文件添加、搜索）响应时间不超过1秒
  - 批量操作（如批量导入）进度可视，且不阻塞UI
  - AI功能在后台处理，不影响主要功能的响应时间

- **资源占用**：
  - 内存占用：正常使用不超过500MB
  - CPU占用：正常使用不超过10%，后台处理不超过30%
  - 存储占用：索引和数据库大小不超过原始文件的5%

- **验收标准**：
  - 在普通用户PC（8GB RAM，四核CPU）上能流畅运行
  - 处理10000个文件的库不出现明显卡顿
  - 系统启动时间不超过5秒

### 4.2 可靠性要求

- **数据安全**：
  - 自动备份：定期自动备份智能文件库数据
  - 恢复机制：提供数据恢复功能
  - 错误处理：优雅处理各类错误，不导致数据丢失

- **稳定性**：
  - 崩溃恢复：系统崩溃后能够恢复到上一个稳定状态
  - 长时间运行：支持长时间运行而不出现内存泄漏等问题
  - 异常处理：妥善处理文件系统异常、权限问题等

- **验收标准**：
  - 连续运行24小时不出现崩溃或性能下降
  - 模拟断电等异常情况后，数据能够完整恢复
  - 错误日志完整记录系统异常

### 4.3 兼容性要求

- **操作系统**：
  - 支持Windows 10及以上版本
  - 适配不同分辨率和DPI设置

- **文件系统**：
  - 支持NTFS、FAT32等常见文件系统
  - 正确处理长路径、特殊字符等情况

- **验收标准**：
  - 在不同Windows版本上功能一致
  - 正确处理各种文件系统特性和限制

### 4.4 安全性要求

- **隐私保护**：
  - 本地处理：所有数据处理在本地完成，不上传用户数据
  - 数据范围：只索引和处理用户明确添加到智能文件库的文件
  - 权限控制：遵循最小权限原则，不请求不必要的系统权限

- **验收标准**：
  - 无未授权的网络通信
  - 敏感数据（如索引、配置）存储时加密
  - 用户可完全控制系统访问的文件范围

## 5. 用户界面需求

### 5.1 设计原则

- **简洁直观**：界面简洁清晰，功能一目了然
- **一致性**：界面元素和交互模式保持一致
- **响应式**：适应不同屏幕尺寸和分辨率
- **用户干预极简化**：减少用户操作步骤，提高效率

### 5.2 主要界面

- **主界面**：
  - 文件浏览区：显示智能文件库中的文件
  - 导航栏：提供分类、标签等导航方式
  - 搜索框：支持快速搜索
  - 状态栏：显示智能文件库状态信息

![SmartVault主界面](SmartVault.png)

- **文件操作界面**：
  - 添加到智能文件库对话框：提供三种入库方式选择
  - 文件详情面板：显示文件元数据和标签
  - 批量操作界面：支持批量添加、分类等操作

- **设置界面**：
  - 监控文件夹设置
  - 智能文件库位置设置
  - AI功能开关
  - 自动化规则配置

### 5.3 交互设计

- **拖放支持**：支持文件拖放添加到智能文件库
- **右键菜单**：提供上下文相关的操作选项
- **键盘快捷键**：支持常用操作的快捷键
- **进度反馈**：长时间操作提供进度指示
- **通知机制**：操作完成后提供简洁通知

## 6. 用户场景

### 6.1 场景一：下载文件自动管理

**用户角色**：普通用户
**场景描述**：用户从网络下载各类文件
**用户目标**：无需手动整理，文件自动分类并易于查找
**步骤流程**：
1. 用户启动自动监控，选择要监控的下载文件夹
2. 用户设置监控文件夹的处理方式（自动链接/复制/询问）
3. 用户从网络下载文件到监控文件夹
4. 系统自动检测新文件并添加到智能文件库
5. 系统分析文件类型、内容和元数据
6. 系统自动为文件添加适当标签
7. 用户收到简洁通知："已添加到智能文件库：[文件名]"
8. 用户可以立即在系统中查找和使用该文件

**验收标准**：
- 系统能在文件下载完成后5秒内检测到新文件
- 自动分类准确率达到80%以上
- 用户能够通过搜索快速找到新添加的文件

### 6.2 场景二：查找历史文件

**用户角色**：知识工作者
**场景描述**：用户需要查找之前处理过的文档
**用户目标**：快速找到所需文件，无需记住确切位置
**步骤流程**：
1. 用户在主界面的搜索框中输入查询
2. 系统搜索智能文件库中的所有文件
3. 系统按相关性排序展示结果，并提供预览
4. 用户可通过智能过滤器快速缩小范围
5. 用户找到并打开目标文件
6. 系统记住搜索历史，下次可快速重复相同搜索

**验收标准**：
- 搜索响应时间不超过1秒
- 搜索结果准确率达到90%以上
- 用户能够通过不完整的关键词找到目标文件

### 6.3 场景三：整理已有文件库

**用户角色**：系统管理员/个人用户
**场景描述**：用户有大量未整理的历史文件
**用户目标**：批量整理现有文件，建立有序的文件结构
**步骤流程**：
1. 用户选择"批量添加到智能文件库"功能
2. 用户指定需要整理的文件夹
3. 用户选择处理方式（链接/复制/移动）
4. 系统扫描并分析所有文件
5. 系统提供自动分类方案预览
6. 用户确认或调整分类方案
7. 系统执行批量添加和整理
8. 系统生成整理报告，显示已添加文件数量和分类结果

**验收标准**：
- 系统能够处理至少10000个文件的批量添加
- 批量处理过程中UI保持响应
- 用户能够查看详细的处理进度和结果

## 7. 实施计划

### 7.1 开发阶段

本项目分为两个主要开发阶段：

1. **核心功能开发阶段**：
   - 智能文件库基础功能
   - 文件监控与自动处理
   - 重复检测与管理
   - 智能搜索
   - 基础UI实现

2. **AI增强开发阶段**：
   - AI辅助接口实现
   - 内容识别与分类
   - 智能标签建议
   - 高级搜索功能

### 7.2 优先级定义

- **P0（必须）**：核心功能，产品最小可用版本必须包含
- **P1（重要）**：重要功能，显著提升产品价值，应尽量包含
- **P2（次要）**：增强功能，提供额外价值，资源允许时实现

## 8. 附录

### 8.1 术语表

请参考 docs\用户术语表.md 文件，该文件定义了系统中使用的所有术语，确保界面、文档和代码中的术语使用一致。

### 8.2 标签系统设计
- 如果需要详细说明可参考 “标签系统.md”
#### 8.2.1 标签层次结构
采用三层标签体系，支持分类、属性和具体描述：
```
顶层标签 → 中层标签 → 底层标签
```
**示例：**
- **文档类型** → **报告** → **财务报告**
- **媒体类型** → **图片** → **风景**

#### 8.2.2 标签关系类型
| **类型**       | **说明**                                 | **示例**              |
|----------------|------------------------------------------|----------------------|
| 父子关系       | 层级继承（如"文档→报告→月度"）              | 文档 → 合同 → 客户A   |
| 关联关系       | 非层级关联（如"项目A"和"设计稿"）           | 项目A ↔ 设计稿        |
| 互斥关系       | 不可同时应用（如"个人"和"工作"）            | 个人 ⨉ 工作          |

#### 8.2.3 标签继承机制
- **属性继承**：子标签自动继承父标签的存储位置、预览方式等规则。
- **搜索继承**：搜索父标签时，结果包含所有子标签的文件。
- **权重继承**：子标签在父标签权重基础上调整（如"工作=10" → "项目=8"）。

#### 8.2.4 标签应用规则
- **多标签支持**：一个文件可跨类别打标（如"工作+紧急"）。
- **自动推断**：根据已有标签推荐关联标签（如"财务+月度" → "报告"）。
- **标签传播**：文件移动至文件夹时自动继承其标签（如移至"项目A"文件夹 → 获得"项目A"标签）。

### 8.3 文件命名与文件夹结构
- 如果需要详细说明可参考“文件夹结构规则.md”
#### 8.3.1 文件命名规则
**命名模板**
```
[类别]-[日期]-[名称]-[版本].[扩展名]
```
**示例：**
- `DOC-20230615-财务报告-v2.docx`
- `IMG-20230620-团队活动.jpg`

**自动重命名逻辑**
- **基于内容**：提取文档标题/图片内容生成名称。
- **基于标签**：将主标签融入文件名（如"财务-月度报告.docx"）。
- **冲突处理**：重复时添加序号（如`报告(1).docx`）。

#### 8.3.2 文件夹结构模式
**可选结构**
| **模式**       | **适用场景**      | **示例路径**                     |
|----------------|------------------|----------------------------------|
| 类型优先       | 按文件类型组织     | `/文档/报告/财务`                |
| 项目优先       | 按项目组织         | `/项目A/文档/需求`               |
| 时间优先       | 按时间归档         | `/2023/Q2/会议记录`              |

**动态文件夹**
- **虚拟分类**：根据标签或条件自动生成（如"最近修改"、"重要文件"）。
- **元数据支持**：文件夹可附加描述、预览图、继承标签。

## 9. 数据需求

### 9.1 数据模型

#### 9.1.1 文件元数据
- **基本信息**：文件名、路径、大小、创建/修改日期
- **入库信息**：入库方式（链接/复制/移动）、入库日期、原始路径
- **分类信息**：标签、分类、用户备注
- **状态信息**：可用性（在线/离线）、设备信息

#### 9.1.2 标签数据
- **标签层次**：父标签、子标签关系
- **标签属性**：名称、颜色、优先级、使用频率
- **标签关系**：关联标签、互斥标签

#### 9.1.3 设备数据
- **设备信息**：名称、ID、上次连接时间
- **设备文件**：设备上的文件记录、状态

### 9.2 数据存储

#### 9.2.1 本地数据库
- **文件索引**：存储文件元数据和索引信息
- **标签系统**：存储标签层次和关系
- **用户设置**：存储用户偏好和配置

#### 9.2.2 文件存储
- **智能文件库存储**：用于存储复制或移动到库的文件
- **临时存储**：用于缓存和处理文件
- **备份存储**：用于存储系统备份

### 9.3 数据安全

#### 9.3.1 备份与恢复
- **自动备份**：定期自动备份数据库和配置
- **手动备份**：支持用户手动创建备份
- **恢复机制**：支持从备份恢复系统

#### 9.3.2 数据完整性
- **事务处理**：确保数据操作的原子性
- **错误恢复**：系统崩溃后能恢复数据一致性
- **冲突解决**：处理文件移动、重命名等冲突情况

## 10. 系统集成

### 10.1 操作系统集成

#### 10.1.1 文件资源管理器集成
- **右键菜单**：添加到智能文件库、查看文件信息等选项
- **拖放支持**：支持拖放文件到应用窗口
- **缩略图集成**：在文件资源管理器中显示标签信息

#### 10.1.2 系统通知集成
- **操作通知**：文件添加、处理完成等通知
- **状态通知**：设备连接、同步状态等通知

### 10.2 第三方软件集成

#### 10.2.1 媒体处理集成
- **视频播放器**：支持打开外部视频播放器
- **图片编辑器**：支持打开外部图片编辑器
- **文档编辑器**：支持打开外部文档编辑器

#### 10.2.2 扩展接口
- **插件系统**：支持第三方插件扩展功能
- **命令行接口**：支持通过命令行操作系统
- **自动化接口**：支持与自动化工具集成

## 11. 风险与限制

### 11.1 技术风险

- **性能风险**：处理大量文件时可能出现性能问题
  - 缓解措施：实现增量索引、分批处理、后台处理等优化

- **兼容性风险**：不同Windows版本和文件系统可能存在兼容性问题
  - 缓解措施：全面测试不同环境，实现优雅降级

- **AI依赖风险**：AI模型可能不稳定或资源占用过高
  - 缓解措施：实现AI/非AI双模式，确保核心功能不依赖AI

### 11.2 功能限制

- **文件类型限制**：某些特殊文件类型可能无法完全支持
  - 处理方案：明确支持的文件类型列表，其他类型提供基本支持

- **规模限制**：系统设计针对个人和小型团队使用，不适用于企业级大规模文件管理
  - 处理方案：明确说明适用范围，提供性能优化建议

- **AI功能限制**：本地AI模型的能力有限，不如云服务强大
  - 处理方案：明确AI功能边界，设置合理期望

## 12. 结论

SmartVault智能文件管理系统通过"智能文件库"概念和自动化功能，为用户提供一个高效、智能的文件管理解决方案。系统设计注重"用户干预极简化"原则，同时保持功能强大和灵活。

本需求规格书明确定义了系统功能、性能要求和用户场景，为开发团队提供清晰的指导。通过分阶段开发和明确的优先级，确保系统能够按计划交付核心价值，并在后续迭代中不断增强功能。
