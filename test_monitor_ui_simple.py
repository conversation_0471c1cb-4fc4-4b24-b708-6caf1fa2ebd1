#!/usr/bin/env python3
"""
简单的文件监控UI测试
验证工具栏监控开关和基本功能
"""

import sys
import os
import tempfile
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import QTimer

from smartvault.ui.main_window import MainWindow


def test_monitor_ui_simple():
    """简单的监控UI测试"""
    print("🧪 开始简单文件监控UI测试...")
    
    app = QApplication(sys.argv)
    
    try:
        # 创建主窗口
        print("\n1️⃣ 创建主窗口...")
        main_window = MainWindow()
        main_window.show()
        
        # 等待UI初始化完成
        app.processEvents()
        time.sleep(1)
        
        # 检查监控服务
        print("\n2️⃣ 检查监控服务...")
        assert hasattr(main_window, 'monitor_service'), "监控服务应该存在"
        print("   ✅ 监控服务已初始化")
        
        # 检查工具栏监控按钮
        print("\n3️⃣ 检查工具栏监控按钮...")
        toolbar = main_window.toolbar_manager
        assert hasattr(toolbar, 'monitor_toggle_button'), "监控按钮应该存在"
        button = toolbar.monitor_toggle_button
        print(f"   ✅ 监控按钮已创建: {button.text()}")
        
        # 获取监控统计
        print("\n4️⃣ 获取监控统计...")
        stats = main_window.monitor_service.get_monitor_statistics()
        print(f"   📊 监控统计: {stats}")
        
        # 测试按钮状态更新
        print("\n5️⃣ 测试按钮状态更新...")
        toolbar.update_monitor_status()
        app.processEvents()
        print(f"   📊 按钮状态: {button.text()}")
        print(f"   📊 按钮选中: {button.isChecked()}")
        print(f"   📊 工具提示: {button.toolTip()}")
        
        # 创建测试监控配置
        print("\n6️⃣ 创建测试监控配置...")
        test_dir = tempfile.mkdtemp(prefix="smartvault_simple_test_")
        print(f"   📁 测试目录: {test_dir}")
        
        monitor_id = main_window.monitor_service.add_monitor_folder(
            folder_path=test_dir,
            entry_mode="link",
            file_patterns=["*.txt"],
            auto_add=True,
            recursive=False
        )
        print(f"   ✅ 监控配置已添加: {monitor_id}")
        
        # 更新按钮状态
        toolbar.update_monitor_status()
        app.processEvents()
        print(f"   📊 更新后按钮状态: {button.text()}")
        
        # 测试监控开关功能
        print("\n7️⃣ 测试监控开关功能...")
        
        # 点击启动
        print("   🖱️ 点击启动监控...")
        button.click()
        app.processEvents()
        time.sleep(0.5)
        
        stats = main_window.monitor_service.get_monitor_statistics()
        print(f"   📊 启动后统计: {stats}")
        print(f"   📊 按钮状态: {button.text()}")
        
        # 点击停止
        print("   🖱️ 点击停止监控...")
        button.click()
        app.processEvents()
        time.sleep(0.5)
        
        stats = main_window.monitor_service.get_monitor_statistics()
        print(f"   📊 停止后统计: {stats}")
        print(f"   📊 按钮状态: {button.text()}")
        
        print("\n✅ 简单监控UI测试完成！")
        
        # 清理测试数据
        print("\n🧹 清理测试数据...")
        try:
            os.rmdir(test_dir)
            print("   ✅ 测试数据已清理")
        except Exception as e:
            print(f"   ⚠️ 清理测试数据失败: {e}")
        
        # 显示成功消息
        QMessageBox.information(
            main_window,
            "测试完成",
            "简单文件监控UI测试已完成！\n\n"
            "✅ 监控服务初始化正常\n"
            "✅ 工具栏监控按钮正常\n"
            "✅ 监控状态更新正常\n"
            "✅ 开关功能正常\n\n"
            "您可以继续手动测试其他功能。"
        )
        
        print("\n💡 窗口保持打开，您可以继续手动测试：")
        print("   - 工具栏监控按钮的开关功能")
        print("   - 设置界面的监控配置")
        print("   - 创建文件测试监控事件")
        print("   - 观察状态栏的统计信息")
        
        return app.exec()
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        
        QMessageBox.critical(
            None,
            "测试失败",
            f"简单监控UI测试失败：\n\n{e}"
        )
        return 1


if __name__ == "__main__":
    exit_code = test_monitor_ui_simple()
    sys.exit(exit_code)
