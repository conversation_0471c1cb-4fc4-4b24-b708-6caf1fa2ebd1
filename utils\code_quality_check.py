"""
代码质量检查工具
"""

import os
import ast
from collections import defaultdict


class CodeQualityChecker:
    """代码质量检查器"""

    def __init__(self):
        """初始化检查器"""
        self.results = {
            'file_sizes': {},
            'class_sizes': {},
            'function_sizes': {},
            'duplicate_imports': defaultdict(list),
            'large_files': [],
            'large_classes': [],
            'large_functions': [],
            'warnings': []
        }

    def check_file(self, file_path):
        """检查单个文件

        Args:
            file_path: 文件路径
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')

            # 检查文件大小
            line_count = len(lines)
            self.results['file_sizes'][file_path] = line_count

            # 检查是否为大文件
            if line_count > 500:
                self.results['large_files'].append((file_path, line_count))
                if line_count > 1000:
                    self.results['warnings'].append(
                        f"超大文件警告: {file_path} ({line_count} 行)"
                    )

            # 解析AST
            try:
                tree = ast.parse(content)
                self._analyze_ast(tree, file_path)
            except SyntaxError as e:
                self.results['warnings'].append(
                    f"语法错误: {file_path} - {e}"
                )

        except Exception as e:
            self.results['warnings'].append(
                f"文件读取错误: {file_path} - {e}"
            )

    def _analyze_ast(self, tree, file_path):
        """分析AST

        Args:
            tree: AST树
            file_path: 文件路径
        """
        for node in ast.walk(tree):
            # 检查类大小
            if isinstance(node, ast.ClassDef):
                class_lines = self._count_node_lines(node)
                class_name = f"{file_path}::{node.name}"
                self.results['class_sizes'][class_name] = class_lines

                if class_lines > 200:
                    self.results['large_classes'].append((class_name, class_lines))
                    if class_lines > 400:
                        self.results['warnings'].append(
                            f"超大类警告: {class_name} ({class_lines} 行)"
                        )

            # 检查函数大小
            elif isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                func_lines = self._count_node_lines(node)
                func_name = f"{file_path}::{node.name}"
                self.results['function_sizes'][func_name] = func_lines

                if func_lines > 50:
                    self.results['large_functions'].append((func_name, func_lines))
                    if func_lines > 100:
                        self.results['warnings'].append(
                            f"超大函数警告: {func_name} ({func_lines} 行)"
                        )

            # 检查导入语句
            elif isinstance(node, (ast.Import, ast.ImportFrom)):
                try:
                    # Python 3.9+ 支持 ast.unparse
                    if hasattr(ast, 'unparse'):
                        import_str = ast.unparse(node)
                    else:
                        # 兼容旧版本Python
                        import_str = self._node_to_string(node)
                    self.results['duplicate_imports'][import_str].append(file_path)
                except Exception:
                    # 忽略无法解析的导入语句
                    pass

    def _node_to_string(self, node):
        """将AST节点转换为字符串（兼容旧版本Python）

        Args:
            node: AST节点

        Returns:
            str: 节点的字符串表示
        """
        if isinstance(node, ast.Import):
            names = [alias.name for alias in node.names]
            return f"import {', '.join(names)}"
        elif isinstance(node, ast.ImportFrom):
            module = node.module or ""
            names = [alias.name for alias in node.names]
            return f"from {module} import {', '.join(names)}"
        return str(type(node).__name__)

    def _count_node_lines(self, node):
        """计算AST节点的行数

        Args:
            node: AST节点

        Returns:
            int: 行数
        """
        if hasattr(node, 'end_lineno') and hasattr(node, 'lineno'):
            return node.end_lineno - node.lineno + 1
        return 1

    def check_directory(self, directory):
        """检查目录

        Args:
            directory: 目录路径
        """
        for root, _, files in os.walk(directory):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    self.check_file(file_path)

    def generate_report(self):
        """生成报告

        Returns:
            str: 报告内容
        """
        report = []
        report.append("=" * 60)
        report.append("代码质量检查报告")
        report.append("=" * 60)

        # 文件统计
        total_files = len(self.results['file_sizes'])
        total_lines = sum(self.results['file_sizes'].values())
        avg_lines = total_lines / total_files if total_files > 0 else 0

        report.append(f"\n📊 总体统计:")
        report.append(f"  总文件数: {total_files}")
        report.append(f"  总行数: {total_lines}")
        report.append(f"  平均每文件行数: {avg_lines:.1f}")

        # 大文件警告
        if self.results['large_files']:
            report.append(f"\n⚠️  大文件 (>500行):")
            for file_path, lines in sorted(self.results['large_files'],
                                         key=lambda x: x[1], reverse=True):
                report.append(f"  {file_path}: {lines} 行")

        # 大类警告
        if self.results['large_classes']:
            report.append(f"\n⚠️  大类 (>200行):")
            for class_name, lines in sorted(self.results['large_classes'],
                                          key=lambda x: x[1], reverse=True):
                report.append(f"  {class_name}: {lines} 行")

        # 大函数警告
        if self.results['large_functions']:
            report.append(f"\n⚠️  大函数 (>50行):")
            for func_name, lines in sorted(self.results['large_functions'],
                                         key=lambda x: x[1], reverse=True):
                report.append(f"  {func_name}: {lines} 行")

        # 重复导入
        duplicates = {k: v for k, v in self.results['duplicate_imports'].items()
                     if len(v) > 1}
        if duplicates:
            report.append(f"\n🔄 重复导入:")
            for import_str, files in duplicates.items():
                report.append(f"  {import_str}")
                for file_path in files:
                    report.append(f"    - {file_path}")

        # 警告信息
        if self.results['warnings']:
            report.append(f"\n🚨 警告信息:")
            for warning in self.results['warnings']:
                report.append(f"  {warning}")

        # 建议
        report.append(f"\n💡 改进建议:")
        if self.results['large_files']:
            report.append("  - 考虑将大文件拆分为多个模块")
        if self.results['large_classes']:
            report.append("  - 考虑将大类拆分或使用组合模式")
        if self.results['large_functions']:
            report.append("  - 考虑将大函数拆分为多个小函数")
        if duplicates:
            report.append("  - 考虑创建通用导入模块减少重复")

        return "\n".join(report)


def main():
    """主函数"""
    checker = CodeQualityChecker()

    # 检查 smartvault 目录
    if os.path.exists('smartvault'):
        checker.check_directory('smartvault')

    # 生成并显示报告
    report = checker.generate_report()
    print(report)

    # 保存报告到文件
    with open('code_quality_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)

    print(f"\n报告已保存到: code_quality_report.txt")


if __name__ == "__main__":
    main()
