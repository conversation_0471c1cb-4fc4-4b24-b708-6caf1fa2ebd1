#!/usr/bin/env python3
"""
SmartVault 拆分功能验证测试
验证备份管理和剪贴板功能拆分后是否正常工作
"""

import sys
import os
import time
sys.path.append('.')

def test_clipboard_floating_widget_signal_connection():
    """测试剪贴板浮动窗口的信号连接"""
    print("=" * 60)
    print("🔍 测试1: 剪贴板浮动窗口信号连接")
    print("=" * 60)

    try:
        from PySide6.QtWidgets import QApplication
        from smartvault.ui.widgets.clipboard_floating_widget import ClipboardFloatingWidget
        from smartvault.ui.main_window.clipboard_handler import ClipboardHandler

        # 创建Qt应用
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        # 模拟主窗口
        class MockMainWindow:
            def __init__(self):
                self.clipboard_floating_widget = ClipboardFloatingWidget()
                self.clipboard_handler = ClipboardHandler(self)

                # 连接信号
                self.clipboard_floating_widget.open_file_requested.connect(
                    self.clipboard_handler.on_clipboard_open_file
                )

                # 模拟其他必要属性
                self.file_view = None

            def show_status_message(self, message, success=True):
                print(f"状态消息: {message} ({'成功' if success else '失败'})")

        # 创建模拟主窗口
        main_window = MockMainWindow()

        # 测试信号连接
        print("✅ 浮动窗口创建成功")
        print("✅ 剪贴板处理器创建成功")
        print("✅ 信号连接成功")

        # 测试信号发射
        print("\n📡 测试信号发射...")

        # 创建测试数据
        test_duplicate_info = {
            'type': 'test',
            'source_text': '测试文件.txt',
            'cleaned_name': '测试文件.txt',
            'duplicates': [
                {
                    'id': 'test-file-id-123',
                    'name': '测试文件.txt',
                    'original_path': '/test/path/测试文件.txt',
                    'library_path': 'test/测试文件.txt',
                    'entry_type': 'file'
                }
            ]
        }

        # 显示重复文件信息
        main_window.clipboard_floating_widget.show_duplicate(test_duplicate_info)
        print("✅ 重复文件信息显示成功")

        # 模拟点击查看按钮
        print("\n🖱️ 模拟点击查看按钮...")
        main_window.clipboard_floating_widget.on_view_clicked()
        print("✅ 查看按钮点击处理完成")

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_backup_manager_integration():
    """测试备份管理器集成"""
    print("\n" + "=" * 60)
    print("🔍 测试2: 备份管理器集成")
    print("=" * 60)

    try:
        from smartvault.ui.main_window.backup_manager import BackupManager

        # 模拟主窗口
        class MockMainWindow:
            def __init__(self):
                self.backup_service = None

            def show_status_message(self, message, success=True):
                print(f"状态消息: {message} ({'成功' if success else '失败'})")

            def statusBar(self):
                class MockStatusBar:
                    def addPermanentWidget(self, widget):
                        print(f"添加永久状态栏组件: {widget}")
                return MockStatusBar()

        # 创建模拟主窗口
        main_window = MockMainWindow()

        # 创建备份管理器
        backup_manager = BackupManager(main_window)
        print("✅ 备份管理器创建成功")

        # 测试方法存在性
        methods_to_test = [
            'start_backup_service',
            'stop_backup_service',
            'setup_backup_status_display',
            'on_backup_completed',
            'on_backup_failed',
            'get_backup_status',
            'update_backup_status_display'
        ]

        for method_name in methods_to_test:
            if hasattr(backup_manager, method_name):
                print(f"✅ 方法存在: {method_name}")
            else:
                print(f"❌ 方法缺失: {method_name}")
                return False

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_core_file_structure():
    """测试core.py文件结构"""
    print("\n" + "=" * 60)
    print("🔍 测试3: core.py文件结构")
    print("=" * 60)

    try:
        from smartvault.ui.main_window.core import MainWindowCore

        # 检查core.py中不应该存在的方法（已拆分的方法）
        removed_methods = [
            # 备份相关方法
            'start_backup_service',
            'stop_backup_service',
            'on_backup_completed',
            'on_backup_failed',
            'setup_backup_status_display',
            'get_backup_status_text',
            'get_backup_status_color',
            'update_backup_status_display',

            # 剪贴板相关方法
            'start_clipboard_monitor_if_enabled',
            'setup_clipboard_float_mode',
            'toggle_clipboard_monitor',
            'show_clipboard_demo',
            'on_clipboard_duplicate_found',
            'on_clipboard_open_file'
        ]

        core_methods = dir(MainWindowCore)

        for method_name in removed_methods:
            if method_name in core_methods:
                # 检查是否是委托方法
                if method_name in ['toggle_clipboard_monitor', 'show_clipboard_demo']:
                    print(f"ℹ️ 委托方法保留在core.py中: {method_name} (用于向后兼容)")
                else:
                    print(f"⚠️ 方法仍存在于core.py中: {method_name}")
            else:
                print(f"✅ 方法已成功移除: {method_name}")

        # 检查应该存在的核心方法
        core_methods_should_exist = [
            'init_ui',
            'load_initial_data',
            'on_file_activated',
            'on_file_selected',
            'show_status_message'
        ]

        for method_name in core_methods_should_exist:
            if method_name in core_methods:
                print(f"✅ 核心方法存在: {method_name}")
            else:
                print(f"❌ 核心方法缺失: {method_name}")
                return False

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_location_functionality():
    """测试文件定位功能"""
    print("\n" + "=" * 60)
    print("🔍 测试4: 文件定位功能")
    print("=" * 60)

    try:
        from smartvault.ui.main_window.clipboard_handler import ClipboardHandler

        # 模拟主窗口和文件视图
        class MockFileView:
            def get_current_model(self):
                return MockModel()

            def get_current_view(self):
                return MockView()

        class MockModel:
            def rowCount(self):
                return 5

            def index(self, row, col):
                return MockIndex(row, col)

            def data(self, index, role):
                if role == 256:  # Qt.UserRole
                    return f"file-id-{index.row}"
                return f"file-{index.row}"

        class MockView:
            def selectRow(self, row):
                print(f"选中行: {row}")

            def scrollTo(self, index):
                print(f"滚动到: {index.row}")

        class MockIndex:
            def __init__(self, row, col):
                self.row = row
                self.col = col

        class MockMainWindow:
            def __init__(self):
                self.file_view = MockFileView()

            def show_status_message(self, message, success=True):
                print(f"状态消息: {message} ({'成功' if success else '失败'})")

        # 创建剪贴板处理器
        main_window = MockMainWindow()
        clipboard_handler = ClipboardHandler(main_window)

        print("✅ 剪贴板处理器创建成功")

        # 测试文件定位方法
        if hasattr(clipboard_handler, '_locate_and_select_file'):
            print("✅ _locate_and_select_file 方法存在")

            # 测试文件定位
            print("\n🎯 测试文件定位...")
            clipboard_handler._locate_and_select_file("file-id-2")
            print("✅ 文件定位测试完成")

        else:
            print("❌ _locate_and_select_file 方法不存在")
            return False

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始SmartVault拆分功能验证测试")
    print("测试目标: 验证备份管理和剪贴板功能拆分后是否正常工作")

    test_results = []

    # 执行测试
    test_results.append(("剪贴板浮动窗口信号连接", test_clipboard_floating_widget_signal_connection()))
    test_results.append(("备份管理器集成", test_backup_manager_integration()))
    test_results.append(("core.py文件结构", test_core_file_structure()))
    test_results.append(("文件定位功能", test_file_location_functionality()))

    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)

    passed = 0
    failed = 0

    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1

    print(f"\n总计: {passed} 个测试通过, {failed} 个测试失败")

    if failed == 0:
        print("🎉 所有测试通过！拆分功能验证成功！")
        return True
    else:
        print("⚠️ 存在测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
