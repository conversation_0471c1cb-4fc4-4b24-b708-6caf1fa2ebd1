#!/usr/bin/env python3
"""
标签管理系统综合测试
包含所有标签相关对话框的修复验证
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QHBoxLayout
from PySide6.QtCore import Qt

from smartvault.ui.dialogs.file_tag_dialog import FileTagDialog
from smartvault.ui.dialogs.tag_management_dialog import TagManagementDialog
from smartvault.ui.themes import ThemeManager


class TagSystemTestWindow(QMainWindow):
    """文件标签测试窗口"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("文件标签对话框修复测试")
        self.setGeometry(100, 100, 500, 400)

        # 初始化主题管理器
        self.theme_manager = ThemeManager()

        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)

        # 标题
        title = QLabel("文件标签对话框修复测试")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 15px;")
        layout.addWidget(title)

        # 主题切换按钮
        theme_layout = QHBoxLayout()

        light_btn = QPushButton("浅色主题")
        light_btn.clicked.connect(lambda: self.switch_theme("light"))
        theme_layout.addWidget(light_btn)

        dark_btn = QPushButton("深色主题")
        dark_btn.clicked.connect(lambda: self.switch_theme("dark"))
        theme_layout.addWidget(dark_btn)

        blue_btn = QPushButton("蓝色主题")
        blue_btn.clicked.connect(lambda: self.switch_theme("blue"))
        theme_layout.addWidget(blue_btn)

        layout.addLayout(theme_layout)

        # 当前主题显示
        self.current_theme_label = QLabel(f"当前主题: {self.theme_manager.get_current_theme()}")
        self.current_theme_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.current_theme_label.setStyleSheet("font-size: 14px; margin: 10px;")
        layout.addWidget(self.current_theme_label)

        # 测试按钮
        file_tag_btn = QPushButton("打开文件标签对话框（添加标签）")
        file_tag_btn.clicked.connect(self.open_file_tag_dialog)
        file_tag_btn.setStyleSheet("font-size: 14px; padding: 8px; margin: 5px;")
        layout.addWidget(file_tag_btn)

        tag_mgmt_btn = QPushButton("打开标签管理对话框（导航栏）")
        tag_mgmt_btn.clicked.connect(self.open_tag_management_dialog)
        tag_mgmt_btn.setStyleSheet("font-size: 14px; padding: 8px; margin: 5px;")
        layout.addWidget(tag_mgmt_btn)

        # 修复说明
        fixes_label = QLabel("✅ 已修复的问题:")
        fixes_label.setStyleSheet("font-weight: bold; margin-top: 20px;")
        layout.addWidget(fixes_label)

        fixes_list = QLabel("""
1. 右键菜单文本从"管理标签"改为"添加标签"
2. 两个标签对话框在深色主题下都显示彩色标签
3. 标签显示方式与导航栏保持一致
4. 支持标签颜色继承和粗体显示
5. 清理了重复代码，使用统一的颜色应用工具
        """)
        fixes_list.setStyleSheet("margin-left: 20px; line-height: 1.5;")
        layout.addWidget(fixes_list)

        # 测试说明
        test_info = QLabel("🧪 测试步骤:")
        test_info.setStyleSheet("font-weight: bold; margin-top: 10px;")
        layout.addWidget(test_info)

        test_steps = QLabel("""
1. 切换到深色主题
2. 打开文件标签对话框
3. 检查左侧标签树是否显示彩色标签
4. 验证标签显示效果与导航栏一致
5. 测试标签选择功能是否正常
        """)
        test_steps.setStyleSheet("margin-left: 20px; line-height: 1.5;")
        layout.addWidget(test_steps)

        # 应用当前主题
        self.theme_manager.apply_theme(self.theme_manager.get_current_theme())

    def switch_theme(self, theme_name):
        """切换主题"""
        self.theme_manager.set_theme(theme_name)
        self.current_theme_label.setText(f"当前主题: {theme_name}")

    def open_file_tag_dialog(self):
        """打开文件标签对话框"""
        try:
            # 模拟文件ID列表
            file_ids = [1, 2, 3]  # 假设的文件ID
            dialog = FileTagDialog(file_ids, self)
            dialog.exec()
        except Exception as e:
            print(f"打开文件标签对话框失败: {e}")

    def open_tag_management_dialog(self):
        """打开标签管理对话框"""
        try:
            dialog = TagManagementDialog(self)
            dialog.exec()
        except Exception as e:
            print(f"打开标签管理对话框失败: {e}")


def main():
    """主函数"""
    app = QApplication(sys.argv)

    window = TagSystemTestWindow()
    window.show()

    print("文件标签对话框修复测试程序已启动")
    print("请按照窗口中的测试步骤进行验证")
    print("\n修复内容：")
    print("1. 右键菜单文本：'管理标签' → '添加标签'")
    print("2. 深色主题下显示彩色标签")
    print("3. 与导航栏标签显示方式保持一致")

    sys.exit(app.exec())


if __name__ == "__main__":
    main()
