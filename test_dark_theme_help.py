#!/usr/bin/env python3
"""
测试深色主题下的帮助对话框
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PySide6.QtGui import QPalette, QColor
from smartvault.ui.dialogs.help_dialog import HelpDialog


class TestWindow(QMainWindow):
    """测试窗口"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("深色主题帮助对话框测试")
        self.setGeometry(100, 100, 400, 300)

        # 设置深色主题
        self.set_dark_theme()

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建布局
        layout = QVBoxLayout(central_widget)

        # 创建按钮
        btn_light = QPushButton("测试浅色主题帮助")
        btn_light.clicked.connect(self.test_light_theme)
        layout.addWidget(btn_light)

        btn_dark = QPushButton("测试深色主题帮助")
        btn_dark.clicked.connect(self.test_dark_theme)
        layout.addWidget(btn_dark)

        btn_auto = QPushButton("自动检测主题帮助")
        btn_auto.clicked.connect(self.test_auto_theme)
        layout.addWidget(btn_auto)

    def set_dark_theme(self):
        """设置深色主题"""
        palette = QPalette()

        # 设置深色背景
        palette.setColor(QPalette.Window, QColor(43, 43, 43))
        palette.setColor(QPalette.WindowText, QColor(224, 224, 224))
        palette.setColor(QPalette.Base, QColor(60, 60, 60))
        palette.setColor(QPalette.AlternateBase, QColor(67, 67, 67))
        palette.setColor(QPalette.ToolTipBase, QColor(0, 0, 0))
        palette.setColor(QPalette.ToolTipText, QColor(224, 224, 224))
        palette.setColor(QPalette.Text, QColor(224, 224, 224))
        palette.setColor(QPalette.Button, QColor(53, 53, 53))
        palette.setColor(QPalette.ButtonText, QColor(224, 224, 224))
        palette.setColor(QPalette.BrightText, QColor(255, 0, 0))
        palette.setColor(QPalette.Link, QColor(42, 130, 218))
        palette.setColor(QPalette.Highlight, QColor(42, 130, 218))
        palette.setColor(QPalette.HighlightedText, QColor(0, 0, 0))

        self.setPalette(palette)

    def set_light_theme(self):
        """设置浅色主题"""
        palette = QPalette()

        # 设置浅色背景
        palette.setColor(QPalette.Window, QColor(240, 240, 240))
        palette.setColor(QPalette.WindowText, QColor(0, 0, 0))
        palette.setColor(QPalette.Base, QColor(255, 255, 255))
        palette.setColor(QPalette.AlternateBase, QColor(233, 233, 233))
        palette.setColor(QPalette.ToolTipBase, QColor(255, 255, 220))
        palette.setColor(QPalette.ToolTipText, QColor(0, 0, 0))
        palette.setColor(QPalette.Text, QColor(0, 0, 0))
        palette.setColor(QPalette.Button, QColor(240, 240, 240))
        palette.setColor(QPalette.ButtonText, QColor(0, 0, 0))
        palette.setColor(QPalette.BrightText, QColor(255, 255, 255))
        palette.setColor(QPalette.Link, QColor(0, 0, 255))
        palette.setColor(QPalette.Highlight, QColor(0, 120, 215))
        palette.setColor(QPalette.HighlightedText, QColor(255, 255, 255))

        self.setPalette(palette)

    def test_light_theme(self):
        """测试浅色主题"""
        print("🌞 切换到浅色主题...")
        self.set_light_theme()
        self.show_help_dialog()

    def test_dark_theme(self):
        """测试深色主题"""
        print("🌙 切换到深色主题...")
        self.set_dark_theme()
        self.show_help_dialog()

    def test_auto_theme(self):
        """测试自动检测主题"""
        print("🔄 使用当前主题...")
        self.show_help_dialog()

    def show_help_dialog(self):
        """显示帮助对话框"""
        try:
            print("📖 正在打开帮助对话框...")
            help_dialog = HelpDialog(self)
            help_dialog.exec()
            print("✅ 帮助对话框已关闭")
        except Exception as e:
            print(f"❌ 帮助对话框出错: {e}")


def main():
    """主函数"""
    print("🚀 启动深色主题帮助对话框测试...")

    app = QApplication(sys.argv)

    # 创建测试窗口
    window = TestWindow()
    window.show()

    print("✅ 测试窗口已显示")
    print("💡 测试说明:")
    print("   • 点击'测试浅色主题帮助'按钮测试浅色主题")
    print("   • 点击'测试深色主题帮助'按钮测试深色主题")
    print("   • 点击'自动检测主题帮助'按钮测试自动主题检测")
    print("   • 在帮助对话框中查看文字是否清晰可见")

    sys.exit(app.exec())


if __name__ == "__main__":
    main()
