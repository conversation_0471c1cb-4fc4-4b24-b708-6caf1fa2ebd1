#!/usr/bin/env python3
"""
SmartVault导航性能测试脚本

测试不同文件夹类型的切换性能，验证优化效果
"""

import sys
import os
import time
import statistics

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from smartvault.data.database import Database
from smartvault.services.file import FileService
from smartvault.services.tag_service import TagService


def measure_time(func, *args, **kwargs):
    """测量函数执行时间"""
    start_time = time.time()
    result = func(*args, **kwargs)
    end_time = time.time()
    return result, end_time - start_time


def test_navigation_performance():
    """测试导航性能"""
    print("🔧 SmartVault导航性能测试")
    print("=" * 50)

    try:
        # 初始化服务
        db = Database.create_from_config()
        file_service = FileService()
        tag_service = TagService()

        # 获取数据库统计信息
        cursor = db.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM files")
        total_files = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM files WHERE staging_status = 'staging'")
        staging_files = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM tags")
        total_tags = cursor.fetchone()[0]

        print(f"📊 数据库统计:")
        print(f"  - 总文件数: {total_files}")
        print(f"  - 中转文件数: {staging_files}")
        print(f"  - 总标签数: {total_tags}")
        print()

        # 测试次数
        test_rounds = 5

        # 1. 测试"全部文件"性能
        print("🔍 测试1: 全部文件加载性能")
        all_files_times = []
        for i in range(test_rounds):
            _, duration = measure_time(
                file_service.get_files,
                limit=100,
                offset=0,
                folder_filter_type=None,
                folder_filter_value=None
            )
            all_files_times.append(duration)
            print(f"  第{i+1}次: {duration:.3f}秒")

        avg_all_files = statistics.mean(all_files_times)
        print(f"  平均时间: {avg_all_files:.3f}秒")
        print()

        # 2. 测试"中转文件夹"性能
        print("🔍 测试2: 中转文件夹加载性能")
        staging_times = []
        for i in range(test_rounds):
            _, duration = measure_time(file_service.get_staging_display_items)
            staging_times.append(duration)
            print(f"  第{i+1}次: {duration:.3f}秒")

        avg_staging = statistics.mean(staging_times)
        print(f"  平均时间: {avg_staging:.3f}秒")
        print()

        # 3. 测试"移动设备"文件夹性能
        print("🔍 测试3: 移动设备文件夹加载性能")

        # 获取设备标签
        device_tags = tag_service.get_device_tags()
        if device_tags:
            device_tag_id = device_tags[0]['id']
            print(f"  测试设备标签: {device_tags[0]['name']}")

            device_times = []
            for i in range(test_rounds):
                _, duration = measure_time(
                    tag_service.get_files_by_tag_hierarchy,
                    device_tag_id,
                    limit=100,
                    offset=0
                )
                device_times.append(duration)
                print(f"  第{i+1}次: {duration:.3f}秒")

            avg_device = statistics.mean(device_times)
            print(f"  平均时间: {avg_device:.3f}秒")
        else:
            print("  ⚠️ 没有找到设备标签，跳过测试")
            avg_device = 0
        print()

        # 4. 测试"自定义文件夹"性能
        print("🔍 测试4: 自定义文件夹加载性能")

        # 获取自定义文件夹标签
        folder_tags = tag_service.get_folder_tags()
        if folder_tags:
            folder_tag_id = folder_tags[0]['id']
            print(f"  测试文件夹标签: {folder_tags[0]['name']}")

            folder_times = []
            for i in range(test_rounds):
                _, duration = measure_time(
                    tag_service.get_files_by_tag_hierarchy,
                    folder_tag_id,
                    limit=100,
                    offset=0
                )
                folder_times.append(duration)
                print(f"  第{i+1}次: {duration:.3f}秒")

            avg_folder = statistics.mean(folder_times)
            print(f"  平均时间: {avg_folder:.3f}秒")
        else:
            print("  ⚠️ 没有找到自定义文件夹标签，跳过测试")
            avg_folder = 0
        print()

        # 5. 性能对比分析
        print("📈 性能对比分析")
        print("-" * 30)
        print(f"全部文件:     {avg_all_files:.3f}秒 (基准)")

        # 避免除零错误
        if avg_all_files > 0.001:  # 大于1毫秒才计算倍数
            print(f"中转文件夹:   {avg_staging:.3f}秒 ({avg_staging/avg_all_files:.1f}x)")
            if avg_device > 0:
                print(f"移动设备:     {avg_device:.3f}秒 ({avg_device/avg_all_files:.1f}x)")
            if avg_folder > 0:
                print(f"自定义文件夹: {avg_folder:.3f}秒 ({avg_folder/avg_all_files:.1f}x)")
        else:
            print(f"中转文件夹:   {avg_staging:.3f}秒 (极快)")
            if avg_device > 0:
                print(f"移动设备:     {avg_device:.3f}秒 (极快)")
            if avg_folder > 0:
                print(f"自定义文件夹: {avg_folder:.3f}秒 (极快)")
        print()

        # 6. 性能评估
        print("🎯 性能评估")
        print("-" * 20)

        # 评估中转文件夹性能
        if avg_all_files > 0.001:
            if avg_staging <= avg_all_files * 1.5:
                print("✅ 中转文件夹性能: 良好 (≤1.5x基准)")
            elif avg_staging <= avg_all_files * 3:
                print("⚠️ 中转文件夹性能: 一般 (1.5-3x基准)")
            else:
                print("❌ 中转文件夹性能: 需要优化 (>3x基准)")
        else:
            print("✅ 中转文件夹性能: 极佳 (毫秒级响应)")

        # 评估标签文件夹性能
        max_tag_time = max(avg_device if avg_device > 0 else 0,
                          avg_folder if avg_folder > 0 else 0)
        if max_tag_time > 0:
            if avg_all_files > 0.001:
                if max_tag_time <= avg_all_files * 2:
                    print("✅ 标签文件夹性能: 良好 (≤2x基准)")
                elif max_tag_time <= avg_all_files * 4:
                    print("⚠️ 标签文件夹性能: 一般 (2-4x基准)")
                else:
                    print("❌ 标签文件夹性能: 需要优化 (>4x基准)")
            else:
                print("✅ 标签文件夹性能: 极佳 (毫秒级响应)")

        print()
        print("🔧 优化建议:")
        if avg_staging > avg_all_files * 2:
            print("- 中转文件夹查询较慢，建议检查staging_status索引")
        if max_tag_time > avg_all_files * 3:
            print("- 标签查询较慢，建议检查file_tags表索引")
        if avg_all_files > 0.5:
            print("- 基础查询较慢，建议检查数据库整体性能")

        db.close()

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_navigation_performance()
