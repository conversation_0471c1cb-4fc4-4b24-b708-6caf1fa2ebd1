"""
搜索设置页面
从原 settings_dialog.py 中的 create_search_tab 方法迁移而来
"""

from PySide6.QtWidgets import (
    QVBoxLayout, QHBoxLayout, QGroupBox, QLabel, QComboBox,
    QCheckBox, QSpinBox
)
from typing import Tuple
from ..base.base_page import BaseSettingsPage


class SearchSettingsPage(BaseSettingsPage):
    """搜索设置页面"""

    def setup_ui(self):
        """设置UI界面"""
        layout = QVBoxLayout(self)

        # 搜索行为设置组
        behavior_group = QGroupBox("搜索行为设置")
        behavior_layout = QVBoxLayout(behavior_group)

        # 默认搜索范围
        scope_layout = QHBoxLayout()
        scope_layout.addWidget(QLabel("默认搜索范围:"))

        self.search_scope_combo = QComboBox()
        self.search_scope_combo.addItems(["当前页面", "整个数据库"])
        scope_layout.addWidget(self.search_scope_combo)
        scope_layout.addStretch()

        behavior_layout.addLayout(scope_layout)

        # 最大搜索结果数
        max_results_layout = QHBoxLayout()
        max_results_layout.addWidget(QLabel("最大搜索结果数:"))

        self.max_results_spin = QSpinBox()
        self.max_results_spin.setRange(100, 10000)
        self.max_results_spin.setSingleStep(100)
        max_results_layout.addWidget(self.max_results_spin)
        max_results_layout.addStretch()

        behavior_layout.addLayout(max_results_layout)

        # 搜索选项
        self.case_sensitive_check = QCheckBox("区分大小写")
        behavior_layout.addWidget(self.case_sensitive_check)

        self.whole_word_check = QCheckBox("全词匹配")
        behavior_layout.addWidget(self.whole_word_check)

        layout.addWidget(behavior_group)
        layout.addStretch()

    def load_settings(self, config: dict):
        """从配置加载设置到UI控件

        Args:
            config: 配置字典
        """
        self.config = config
        search_config = config.get("search", {})

        # 默认搜索范围
        default_scope = search_config.get("default_scope", "database")
        scope_map = {"page": 0, "database": 1}
        self.search_scope_combo.setCurrentIndex(scope_map.get(default_scope, 1))

        # 最大搜索结果数
        self.max_results_spin.setValue(search_config.get("max_results", 1000))

        # 搜索选项
        self.case_sensitive_check.setChecked(search_config.get("case_sensitive", False))
        self.whole_word_check.setChecked(search_config.get("whole_word", False))

    def save_settings(self) -> dict:
        """从UI控件保存设置到配置

        Returns:
            dict: 搜索设置字典
        """
        scope_map = {0: "page", 1: "database"}

        return {
            "default_scope": scope_map[self.search_scope_combo.currentIndex()],
            "max_results": self.max_results_spin.value(),
            "case_sensitive": self.case_sensitive_check.isChecked(),
            "whole_word": self.whole_word_check.isChecked()
        }

    def validate_settings(self) -> Tuple[bool, str]:
        """验证设置有效性

        Returns:
            tuple: (是否有效, 错误信息)
        """
        # 验证最大搜索结果数
        if self.max_results_spin.value() < 100:
            return False, "最大搜索结果数不能少于100"

        return True, ""

    def reset_to_defaults(self):
        """重置为默认设置"""
        self.search_scope_combo.setCurrentIndex(1)  # 整个数据库
        self.max_results_spin.setValue(1000)
        self.case_sensitive_check.setChecked(False)
        self.whole_word_check.setChecked(False)

    def get_page_title(self) -> str:
        """获取页面标题

        Returns:
            str: 页面标题
        """
        return "搜索设置"
