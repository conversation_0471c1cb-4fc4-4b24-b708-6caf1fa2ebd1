#!/usr/bin/env python3
"""
B018集成测试：验证标签管理对话框的完整功能
"""

import sys
import os
import tempfile
import shutil
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from smartvault.services.tag_service import TagService
from smartvault.ui.dialogs.tag_management_dialog import TagManagementDialog


class B018IntegrationTester:
    """B018集成测试器"""

    def __init__(self):
        self.app = QApplication.instance()
        if self.app is None:
            self.app = QApplication(sys.argv)

        self.temp_dir = tempfile.mkdtemp()
        self.test_results = []

        # 创建测试数据
        self.setup_test_data()

        # 创建对话框
        self.dialog = TagManagementDialog()

    def setup_test_data(self):
        """创建测试数据"""
        tag_service = TagService()

        # 创建测试标签层次结构
        self.work_tag_id = tag_service.create_tag("工作", "#FF0000")
        self.personal_tag_id = tag_service.create_tag("个人", "#00FF00")

        # 中层标签
        self.project_tag_id = tag_service.create_tag("项目", "#FF8800", self.work_tag_id)
        self.document_tag_id = tag_service.create_tag("文档", "#0088FF", self.work_tag_id)

        # 底层标签
        self.urgent_tag_id = tag_service.create_tag("紧急", "#FF0088", self.project_tag_id)

        print("测试数据创建完成：")
        print("- 工作")
        print("  - 项目")
        print("    - 紧急")
        print("  - 文档")
        print("- 个人")

    def log_test(self, name, success, details=""):
        """记录测试结果"""
        result = {
            "name": name,
            "success": success,
            "details": details
        }
        self.test_results.append(result)

        status = "✅" if success else "❌"
        print(f"  {status} {name}")
        if details:
            print(f"    详情: {details}")

    def test_dialog_creation(self):
        """测试对话框创建"""
        print("\n=== 对话框创建测试 ===")

        try:
            # 测试对话框是否正确创建
            self.log_test("对话框创建", self.dialog is not None)

            # 测试标签加载
            self.dialog.load_tags()
            self.log_test("标签加载", True, "标签列表已加载")

            # 测试必要方法存在
            methods = ['add_child_tag', 'edit_tag', 'delete_tag_with_confirmation', 'show_context_menu']
            for method in methods:
                has_method = hasattr(self.dialog, method)
                self.log_test(f"方法 {method}", has_method)

        except Exception as e:
            self.log_test("对话框创建测试", False, str(e))

    def test_tag_service_integration(self):
        """测试TagService集成"""
        print("\n=== TagService集成测试 ===")

        try:
            tag_service = TagService()

            # 测试创建新标签
            new_tag_id = tag_service.create_tag("测试标签", "#FFFF00")
            self.log_test("创建新标签", new_tag_id is not None, f"标签ID: {new_tag_id}")

            # 测试更新标签
            success = tag_service.update_tag(new_tag_id, name="测试标签-更新", color="#FF00FF")
            self.log_test("更新标签", success)

            # 测试移动标签
            success = tag_service.move_tag(new_tag_id, self.work_tag_id)
            self.log_test("移动标签", success)

            # 测试循环依赖检查
            can_move = tag_service.can_move_tag(self.work_tag_id, new_tag_id)
            self.log_test("循环依赖检查", not can_move, "正确阻止了循环依赖")

            # 测试删除标签
            success = tag_service.delete_tag_cascade(new_tag_id, delete_children=False)
            self.log_test("删除标签", success)

        except Exception as e:
            self.log_test("TagService集成测试", False, str(e))

    def test_tag_hierarchy_operations(self):
        """测试标签层次操作"""
        print("\n=== 标签层次操作测试 ===")

        try:
            tag_service = TagService()

            # 测试获取标签树
            tag_tree = tag_service.get_tag_tree()
            self.log_test("获取标签树", len(tag_tree) > 0, f"获取到 {len(tag_tree)} 个顶级标签")

            # 测试子标签获取
            child_tags = tag_service.get_child_tags(self.work_tag_id)
            self.log_test("获取子标签", len(child_tags) >= 2, f"工作标签有 {len(child_tags)} 个子标签")

            # 测试层级文件获取
            files = tag_service.get_files_by_tag_hierarchy(self.work_tag_id)
            self.log_test("层级文件获取", isinstance(files, list), f"获取到 {len(files)} 个文件")

        except Exception as e:
            self.log_test("标签层次操作测试", False, str(e))

    def test_dialog_functionality(self):
        """测试对话框功能"""
        print("\n=== 对话框功能测试 ===")

        try:
            # 测试标签服务访问
            has_tag_service = hasattr(self.dialog, 'tag_service')
            self.log_test("标签服务访问", has_tag_service)

            # 测试标签列表
            has_tag_list = hasattr(self.dialog, 'tag_list')
            self.log_test("标签列表组件", has_tag_list)

            # 测试信号
            has_signal = hasattr(self.dialog, 'tags_changed')
            self.log_test("标签变化信号", has_signal)

            # 测试UI组件
            components = ['tag_list', 'tag_details', 'add_tag_btn', 'edit_tag_btn', 'delete_tag_btn']
            for component in components:
                has_component = hasattr(self.dialog, component)
                self.log_test(f"UI组件 {component}", has_component)

        except Exception as e:
            self.log_test("对话框功能测试", False, str(e))

    def run_all_tests(self):
        """运行所有集成测试"""
        print("🧪 B018标签管理对话框集成测试")
        print("=" * 50)

        try:
            self.test_dialog_creation()
            self.test_tag_service_integration()
            self.test_tag_hierarchy_operations()
            self.test_dialog_functionality()

        except Exception as e:
            print(f"测试过程中发生错误: {e}")
            return False

        # 输出测试结果
        print("\n" + "=" * 50)
        print("B018集成测试结果汇总")
        print("=" * 50)

        passed = sum(1 for result in self.test_results if result["success"])
        total = len(self.test_results)

        print(f"总测试数: {total}")
        print(f"通过数: {passed}")
        print(f"失败数: {total - passed}")
        print(f"通过率: {passed/total*100:.1f}%")

        if passed == total:
            print("\n🎉 所有B018集成测试通过！功能完整可用。")
            return True
        else:
            print("\n⚠️ 部分集成测试失败，需要进一步检查。")
            print("\n失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  ❌ {result['name']}: {result['details']}")
            return False

    def cleanup(self):
        """清理测试环境"""
        if hasattr(self, 'dialog'):
            self.dialog.close()
        if hasattr(self, 'temp_dir'):
            shutil.rmtree(self.temp_dir, ignore_errors=True)


def main():
    """主函数"""
    tester = B018IntegrationTester()

    try:
        success = tester.run_all_tests()

        # 自动退出
        QTimer.singleShot(1000, tester.app.quit)
        tester.app.exec()

        return 0 if success else 1

    except Exception as e:
        print(f"集成测试过程中发生错误: {e}")
        return 1
    finally:
        tester.cleanup()


if __name__ == "__main__":
    sys.exit(main())
