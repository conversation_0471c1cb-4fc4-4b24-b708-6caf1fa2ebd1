#!/usr/bin/env python3
"""
测试帮助对话框
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("帮助对话框测试")
        self.setGeometry(100, 100, 400, 200)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建测试按钮
        help_button = QPushButton("打开帮助对话框")
        help_button.clicked.connect(self.open_help_dialog)
        layout.addWidget(help_button)
        
        # F1快捷键测试按钮
        f1_button = QPushButton("测试F1快捷键")
        f1_button.setToolTip("点击后按F1键测试")
        layout.addWidget(f1_button)
    
    def open_help_dialog(self):
        """打开帮助对话框"""
        try:
            from smartvault.ui.dialogs.help_dialog import HelpDialog
            print("正在打开帮助对话框...")
            
            dialog = HelpDialog(self)
            print("帮助对话框创建成功")
            
            dialog.exec()
            print("帮助对话框已关闭")
            
        except Exception as e:
            print(f"打开帮助对话框失败: {e}")
            import traceback
            traceback.print_exc()
    
    def keyPressEvent(self, event):
        """处理键盘事件"""
        from PySide6.QtCore import Qt
        
        if event.key() == Qt.Key_F1:
            print("检测到F1按键，打开帮助对话框...")
            self.open_help_dialog()
        else:
            super().keyPressEvent(event)

def main():
    """主函数"""
    print("🚀 启动帮助对话框测试...")
    
    app = QApplication(sys.argv)
    app.setApplicationName("SmartVault-Help-Test")
    
    window = TestMainWindow()
    window.show()
    
    print("✅ 测试窗口已显示")
    print("💡 测试说明:")
    print("   • 点击'打开帮助对话框'按钮测试帮助功能")
    print("   • 按F1键测试快捷键功能")
    print("   • 在帮助对话框中测试导航和搜索功能")
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
