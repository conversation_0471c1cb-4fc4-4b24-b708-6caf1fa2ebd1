#!/usr/bin/env python3
"""
B018任务测试：完善标签管理对话框
测试三层标签编辑功能的完整性
"""

import sys
import os
import tempfile
import shutil
from PySide6.QtWidgets import QApplication, QMenu, QMessageBox
from PySide6.QtCore import Qt, QPoint
from PySide6.QtTest import QTest

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from smartvault.services.tag_service import TagService
from smartvault.ui.dialogs.tag_management_dialog import TagManagementDialog


class TestB018TagManagementEnhanced:
    """B018标签管理对话框增强功能测试"""

    def setup_test_environment(self):
        """设置测试环境"""
        self.app = QApplication.instance()
        if self.app is None:
            self.app = QApplication(sys.argv)

        # 创建临时数据库
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = os.path.join(self.temp_dir, "test.db")

        # 初始化服务
        self.tag_service = TagService()

        # 创建测试标签数据
        self.setup_test_tags()

    def cleanup_test_environment(self):
        """清理测试环境"""
        # 清理
        if hasattr(self.tag_service, 'db') and self.tag_service.db:
            self.tag_service.db.close()
        if hasattr(self, 'temp_dir'):
            shutil.rmtree(self.temp_dir, ignore_errors=True)

    def setup_test_tags(self):
        """创建测试标签数据"""
        # 顶层标签
        self.work_tag_id = self.tag_service.create_tag("工作", "#FF0000")
        self.personal_tag_id = self.tag_service.create_tag("个人", "#00FF00")

        # 中层标签
        self.project_tag_id = self.tag_service.create_tag("项目", "#FF8800", self.work_tag_id)
        self.document_tag_id = self.tag_service.create_tag("文档", "#0088FF", self.work_tag_id)

        # 底层标签
        self.urgent_tag_id = self.tag_service.create_tag("紧急", "#FF0088", self.project_tag_id)
        self.report_tag_id = self.tag_service.create_tag("报告", "#8800FF", self.document_tag_id)

    def test_tag_service_update_method(self):
        """测试TagService的update_tag方法"""
        # 测试更新标签名称
        success = self.tag_service.update_tag(
            self.work_tag_id,
            name="工作-更新",
            color="#FF00FF"
        )
        assert success, "标签更新应该成功"

        # 验证更新结果
        updated_tag = self.tag_service.get_tag_by_id(self.work_tag_id)
        assert updated_tag['name'] == "工作-更新", "标签名称应该被更新"
        assert updated_tag['color'] == "#FF00FF", "标签颜色应该被更新"

    def test_tag_service_move_method(self):
        """测试TagService的move_tag方法"""
        # 将紧急标签从项目移动到文档下
        success = self.tag_service.move_tag(self.urgent_tag_id, self.document_tag_id)
        assert success, "标签移动应该成功"

        # 验证移动结果
        moved_tag = self.tag_service.get_tag_by_id(self.urgent_tag_id)
        assert moved_tag['parent_id'] == self.document_tag_id, "父标签ID应该被更新"

    def test_prevent_circular_dependency(self):
        """测试防止循环依赖"""
        # 尝试将父标签移动到子标签下（应该失败）
        can_move = self.tag_service.can_move_tag(self.work_tag_id, self.project_tag_id)
        assert not can_move, "不应该允许创建循环依赖"

        # 尝试将标签移动到自己下面（应该失败）
        can_move = self.tag_service.can_move_tag(self.work_tag_id, self.work_tag_id)
        assert not can_move, "不应该允许将标签移动到自己下面"

    def test_delete_tag_cascade(self):
        """测试级联删除标签"""
        # 获取删除前的子标签
        child_tags_before = self.tag_service.get_child_tags(self.work_tag_id)
        assert len(child_tags_before) > 0, "工作标签应该有子标签"

        # 级联删除工作标签
        success = self.tag_service.delete_tag_cascade(self.work_tag_id, delete_children=True)
        assert success, "级联删除应该成功"

        # 验证标签已被删除
        deleted_tag = self.tag_service.get_tag_by_id(self.work_tag_id)
        assert deleted_tag is None, "标签应该被删除"

        # 验证子标签也被删除
        for child_tag in child_tags_before:
            child_deleted = self.tag_service.get_tag_by_id(child_tag['id'])
            assert child_deleted is None, "子标签应该被级联删除"

    def test_delete_tag_keep_children(self):
        """测试删除标签但保留子标签"""
        # 获取删除前的子标签
        child_tags_before = self.tag_service.get_child_tags(self.personal_tag_id)

        # 删除个人标签但保留子标签
        success = self.tag_service.delete_tag_cascade(self.personal_tag_id, delete_children=False)
        assert success, "删除标签应该成功"

        # 验证标签已被删除
        deleted_tag = self.tag_service.get_tag_by_id(self.personal_tag_id)
        assert deleted_tag is None, "标签应该被删除"

        # 验证子标签变成顶级标签
        for child_tag in child_tags_before:
            child_updated = self.tag_service.get_tag_by_id(child_tag['id'])
            assert child_updated is not None, "子标签应该保留"
            assert child_updated['parent_id'] is None, "子标签应该变成顶级标签"


class TestB018TagManagementDialogUI:
    """B018标签管理对话框UI测试"""

    def setup_ui_test(self):
        """设置UI测试环境"""
        self.app = QApplication.instance()
        if self.app is None:
            self.app = QApplication(sys.argv)

        # 创建临时数据库
        self.temp_dir = tempfile.mkdtemp()

        # 创建测试数据
        self.setup_test_data()

        # 创建对话框
        self.dialog = TagManagementDialog()

    def cleanup_ui_test(self):
        """清理UI测试环境"""
        # 清理
        if hasattr(self, 'dialog'):
            self.dialog.close()
        if hasattr(self, 'temp_dir'):
            shutil.rmtree(self.temp_dir, ignore_errors=True)

    def setup_test_data(self):
        """创建测试数据"""
        tag_service = TagService()

        # 创建测试标签
        self.parent_tag_id = tag_service.create_tag("父标签", "#FF0000")
        self.child_tag_id = tag_service.create_tag("子标签", "#00FF00", self.parent_tag_id)

    def test_context_menu_has_enhanced_options(self):
        """测试右键菜单包含增强选项"""
        # 加载标签
        self.dialog.load_tags()

        # 验证对话框有show_context_menu方法
        assert hasattr(self.dialog, 'show_context_menu'), "对话框应该有右键菜单方法"

        # 验证标签列表存在
        assert hasattr(self.dialog, 'tag_list'), "对话框应该有标签列表"

    def test_add_child_tag_method_exists(self):
        """测试添加子标签方法存在"""
        # 验证对话框有add_child_tag方法
        assert hasattr(self.dialog, 'add_child_tag'), "对话框应该有添加子标签方法"

    def test_edit_tag_method_enhanced(self):
        """测试标签编辑方法增强"""
        # 验证对话框有edit_tag方法
        assert hasattr(self.dialog, 'edit_tag'), "对话框应该有编辑标签方法"

    def test_delete_tag_with_confirmation_method(self):
        """测试带确认的删除方法"""
        # 验证对话框有delete_tag_with_confirmation方法
        assert hasattr(self.dialog, 'delete_tag_with_confirmation'), "对话框应该有确认删除方法"


def run_b018_tests():
    """运行B018测试套件"""
    print("🧪 开始B018标签管理增强功能测试")
    print("=" * 50)

    # 运行测试
    test_results = []

    try:
        # 创建测试实例
        service_tester = TestB018TagManagementEnhanced()
        ui_tester = TestB018TagManagementDialogUI()

        # 设置测试环境
        service_tester.setup_test_environment()
        ui_tester.setup_ui_test()

        # 运行服务层测试
        print("\n--- TagService增强功能测试 ---")

        tests = [
            ("标签更新功能", service_tester.test_tag_service_update_method),
            ("标签移动功能", service_tester.test_tag_service_move_method),
            ("循环依赖防护", service_tester.test_prevent_circular_dependency),
            ("级联删除功能", service_tester.test_delete_tag_cascade),
            ("保留子标签删除", service_tester.test_delete_tag_keep_children),
        ]

        for test_name, test_func in tests:
            try:
                test_func()
                print(f"  ✅ {test_name}: 通过")
                test_results.append((test_name, True, ""))
            except Exception as e:
                print(f"  ❌ {test_name}: 失败 - {e}")
                test_results.append((test_name, False, str(e)))

        # 运行UI测试
        print("\n--- UI增强功能测试 ---")

        ui_tests = [
            ("右键菜单增强", ui_tester.test_context_menu_has_enhanced_options),
            ("添加子标签方法", ui_tester.test_add_child_tag_method_exists),
            ("编辑标签方法", ui_tester.test_edit_tag_method_enhanced),
            ("确认删除方法", ui_tester.test_delete_tag_with_confirmation_method),
        ]

        for test_name, test_func in ui_tests:
            try:
                test_func()
                print(f"  ✅ {test_name}: 通过")
                test_results.append((test_name, True, ""))
            except Exception as e:
                print(f"  ❌ {test_name}: 失败 - {e}")
                test_results.append((test_name, False, str(e)))

    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        return False

    # 输出测试结果
    print("\n" + "=" * 50)
    print("B018测试结果汇总")
    print("=" * 50)

    passed = sum(1 for _, success, _ in test_results if success)
    total = len(test_results)

    print(f"总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")

    if passed == total:
        print("\n🎉 所有B018测试通过！可以开始功能实现。")
        return True
    else:
        print("\n⚠️ 部分测试失败，这是预期的，因为功能还未实现。")
        print("现在开始实现这些功能...")
        return False


if __name__ == "__main__":
    run_b018_tests()
