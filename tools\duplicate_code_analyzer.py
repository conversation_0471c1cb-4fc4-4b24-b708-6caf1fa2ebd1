#!/usr/bin/env python3
"""
SmartVault 重复代码分析工具
专门用于识别和分析重复方法，评估合并风险
"""

import os
import ast
import re
from pathlib import Path
from typing import Dict, List, Set, Tuple
from collections import defaultdict
import hashlib

class DuplicateCodeAnalyzer:
    """重复代码分析器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.python_files = []
        self.method_signatures = defaultdict(list)  # signature -> [method_info, ...]
        self.method_bodies = defaultdict(list)      # body_hash -> [method_info, ...]
        self.duplicate_groups = []
        
    def scan_smartvault_files(self):
        """扫描SmartVault项目文件"""
        print("🔍 扫描SmartVault项目文件...")
        
        smartvault_dir = self.project_root / "smartvault"
        if smartvault_dir.exists():
            for file_path in smartvault_dir.rglob("*.py"):
                if not any(skip in str(file_path) for skip in ['__pycache__', '.pyc']):
                    self.python_files.append(file_path)
        
        print(f"📊 找到 {len(self.python_files)} 个Python文件")
        
    def analyze_method_duplicates(self):
        """分析方法重复"""
        print("🔍 分析方法重复...")
        
        for file_path in self.python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    tree = ast.parse(content)
                    lines = content.split('\n')
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.FunctionDef):
                        method_info = self._extract_method_info(node, file_path, lines)
                        if method_info:
                            # 按方法签名分组
                            signature = method_info['signature']
                            self.method_signatures[signature].append(method_info)
                            
                            # 按方法体内容分组
                            body_hash = method_info['body_hash']
                            if body_hash:  # 只有非空方法体才考虑
                                self.method_bodies[body_hash].append(method_info)
            
            except Exception as e:
                print(f"⚠️ 解析文件失败: {file_path} - {e}")
        
        # 找出重复组
        self._identify_duplicate_groups()
        
        print(f"📊 发现 {len(self.duplicate_groups)} 组重复方法")
    
    def _extract_method_info(self, node: ast.FunctionDef, file_path: Path, lines: List[str]) -> Dict:
        """提取方法信息"""
        try:
            # 基本信息
            method_name = node.name
            start_line = node.lineno
            end_line = getattr(node, 'end_lineno', start_line + 10)
            
            # 方法签名（名称 + 参数）
            args = [arg.arg for arg in node.args.args]
            signature = f"{method_name}({', '.join(args)})"
            
            # 方法体内容（去除注释和空行）
            method_lines = lines[start_line-1:end_line]
            body_lines = []
            
            for line in method_lines[1:]:  # 跳过def行
                stripped = line.strip()
                if stripped and not stripped.startswith('#') and not stripped.startswith('"""') and not stripped.startswith("'''"):
                    # 标准化代码（移除多余空格）
                    normalized = re.sub(r'\s+', ' ', stripped)
                    body_lines.append(normalized)
            
            # 计算方法体哈希
            body_content = '\n'.join(body_lines)
            body_hash = hashlib.md5(body_content.encode()).hexdigest() if body_content.strip() else None
            
            # 分析方法特征
            is_private = method_name.startswith('_')
            is_property = any(isinstance(decorator, ast.Name) and decorator.id == 'property' 
                            for decorator in node.decorator_list)
            is_signal_slot = any(keyword in method_name.lower() 
                               for keyword in ['signal', 'slot', 'connect', 'emit', 'on_'])
            
            # 计算复杂度
            complexity = self._calculate_complexity(node)
            
            return {
                'name': method_name,
                'signature': signature,
                'file': str(file_path.relative_to(self.project_root)),
                'start_line': start_line,
                'end_line': end_line,
                'body_hash': body_hash,
                'body_content': body_content,
                'body_lines': len(body_lines),
                'is_private': is_private,
                'is_property': is_property,
                'is_signal_slot': is_signal_slot,
                'complexity': complexity,
                'args_count': len(args),
                'args': args
            }
            
        except Exception as e:
            print(f"⚠️ 提取方法信息失败: {node.name} - {e}")
            return None
    
    def _calculate_complexity(self, node: ast.FunctionDef) -> int:
        """计算方法复杂度（简化版）"""
        complexity = 1  # 基础复杂度
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.Try, ast.With)):
                complexity += 1
            elif isinstance(child, ast.BoolOp):
                complexity += len(child.values) - 1
        
        return complexity
    
    def _identify_duplicate_groups(self):
        """识别重复组"""
        # 1. 完全相同的方法体
        for body_hash, methods in self.method_bodies.items():
            if len(methods) >= 2:
                self.duplicate_groups.append({
                    'type': 'identical_body',
                    'methods': methods,
                    'risk_level': 'low',
                    'merge_strategy': 'extract_common_method'
                })
        
        # 2. 相同签名的方法
        for signature, methods in self.method_signatures.items():
            if len(methods) >= 2:
                # 检查是否已经在body重复中
                body_hashes = set(m['body_hash'] for m in methods if m['body_hash'])
                if len(body_hashes) > 1:  # 不同的方法体
                    self.duplicate_groups.append({
                        'type': 'same_signature',
                        'methods': methods,
                        'risk_level': 'medium',
                        'merge_strategy': 'review_manually'
                    })
    
    def assess_merge_risks(self):
        """评估合并风险"""
        print("🔍 评估合并风险...")
        
        for group in self.duplicate_groups:
            methods = group['methods']
            
            # 风险因素评估
            risk_factors = []
            
            # 1. 跨文件重复风险更高
            files = set(m['file'] for m in methods)
            if len(files) > 1:
                risk_factors.append("跨文件重复")
            
            # 2. 信号槽方法风险更高
            if any(m['is_signal_slot'] for m in methods):
                risk_factors.append("包含信号槽方法")
            
            # 3. 公共方法风险更高
            if any(not m['is_private'] for m in methods):
                risk_factors.append("包含公共方法")
            
            # 4. 高复杂度方法风险更高
            max_complexity = max(m['complexity'] for m in methods)
            if max_complexity > 5:
                risk_factors.append(f"高复杂度({max_complexity})")
            
            # 5. 方法体差异大风险更高
            if group['type'] == 'same_signature':
                body_lengths = [m['body_lines'] for m in methods]
                if max(body_lengths) - min(body_lengths) > 5:
                    risk_factors.append("方法体差异大")
            
            # 更新风险级别
            if len(risk_factors) == 0:
                group['risk_level'] = 'very_low'
            elif len(risk_factors) <= 2:
                group['risk_level'] = 'low'
            elif len(risk_factors) <= 3:
                group['risk_level'] = 'medium'
            else:
                group['risk_level'] = 'high'
            
            group['risk_factors'] = risk_factors
    
    def generate_merge_plan(self):
        """生成合并计划"""
        print("🔍 生成合并计划...")
        
        # 按风险级别分组
        very_low_risk = [g for g in self.duplicate_groups if g['risk_level'] == 'very_low']
        low_risk = [g for g in self.duplicate_groups if g['risk_level'] == 'low']
        medium_risk = [g for g in self.duplicate_groups if g['risk_level'] == 'medium']
        high_risk = [g for g in self.duplicate_groups if g['risk_level'] == 'high']
        
        return {
            'very_low_risk': very_low_risk,
            'low_risk': low_risk,
            'medium_risk': medium_risk,
            'high_risk': high_risk,
            'total_groups': len(self.duplicate_groups),
            'safe_to_merge': len(very_low_risk) + len(low_risk)
        }
    
    def generate_report(self) -> str:
        """生成重复代码分析报告"""
        merge_plan = self.generate_merge_plan()
        
        report = []
        report.append("# SmartVault 重复代码分析报告")
        report.append(f"分析时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"分析文件数: {len(self.python_files)}")
        report.append("")
        
        # 总体统计
        report.append("## 📊 重复代码统计")
        report.append(f"- 总重复组数: {merge_plan['total_groups']}")
        report.append(f"- 安全合并组数: {merge_plan['safe_to_merge']}")
        report.append(f"- 极低风险: {len(merge_plan['very_low_risk'])} 组")
        report.append(f"- 低风险: {len(merge_plan['low_risk'])} 组")
        report.append(f"- 中风险: {len(merge_plan['medium_risk'])} 组")
        report.append(f"- 高风险: {len(merge_plan['high_risk'])} 组")
        report.append("")
        
        # 极低风险组（建议立即合并）
        if merge_plan['very_low_risk']:
            report.append("## 🟢 极低风险组 (建议立即合并)")
            for i, group in enumerate(merge_plan['very_low_risk'][:5]):
                report.append(f"### 组 {i+1}: {group['type']}")
                for method in group['methods']:
                    report.append(f"- `{method['signature']}` in {method['file']}:{method['start_line']}")
                report.append(f"**合并策略**: {group['merge_strategy']}")
                report.append("")
        
        # 低风险组
        if merge_plan['low_risk']:
            report.append("## 🟡 低风险组 (谨慎合并)")
            for i, group in enumerate(merge_plan['low_risk'][:3]):
                report.append(f"### 组 {i+1}: {group['type']}")
                for method in group['methods']:
                    report.append(f"- `{method['signature']}` in {method['file']}:{method['start_line']}")
                report.append(f"**风险因素**: {', '.join(group['risk_factors'])}")
                report.append(f"**合并策略**: {group['merge_strategy']}")
                report.append("")
        
        # 合并建议
        report.append("## 💡 合并建议")
        if merge_plan['safe_to_merge'] > 0:
            report.append(f"### ✅ 可以安全合并: {merge_plan['safe_to_merge']} 组")
            report.append("- 这些组风险极低，可以立即进行合并")
            report.append("- 建议采用提取公共方法的策略")
            report.append("- 合并后进行完整的功能测试")
        else:
            report.append("### ⚠️ 暂不建议合并")
            report.append("- 所有重复组都存在一定风险")
            report.append("- 建议先完善测试覆盖再进行合并")
        
        return "\n".join(report)
    
    def run_analysis(self):
        """运行完整分析"""
        self.scan_smartvault_files()
        self.analyze_method_duplicates()
        self.assess_merge_risks()
        return self.generate_report()

def main():
    """主函数"""
    print("SmartVault 重复代码分析工具")
    print("=" * 50)
    
    analyzer = DuplicateCodeAnalyzer(".")
    report = analyzer.run_analysis()
    
    # 保存报告
    report_file = "duplicate_code_analysis_report.md"
    with open(report_file, "w", encoding="utf-8") as f:
        f.write(report)
    
    print(f"\n📄 分析报告已保存到 {report_file}")
    
    # 显示风险评估结果
    merge_plan = analyzer.generate_merge_plan()
    print("\n" + "="*50)
    print("风险评估结果:")
    print(f"- 总重复组: {merge_plan['total_groups']}")
    print(f"- 安全合并: {merge_plan['safe_to_merge']} 组")
    
    if merge_plan['safe_to_merge'] > 0:
        print("✅ 建议: 可以开始合并低风险组")
    else:
        print("⚠️ 建议: 暂时不进行合并，风险较高")
    
    return analyzer

if __name__ == "__main__":
    main()
