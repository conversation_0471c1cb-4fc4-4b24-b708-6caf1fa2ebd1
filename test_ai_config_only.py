#!/usr/bin/env python3
"""
AI配置测试（不依赖Qt）
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ai_config_manager():
    """测试AI配置管理器"""
    print("🔍 测试AI配置管理器...")
    
    try:
        from smartvault.ui.dialogs.settings.pages.ai.utils.ai_config_helper import AIConfigManager
        
        # 创建配置管理器
        manager = AIConfigManager()
        print("✅ AI配置管理器创建成功")
        
        # 测试加载配置
        config = manager.load_ai_config()
        print(f"✅ AI配置加载成功，配置项: {list(config.keys())}")
        
        # 测试配置验证
        is_valid, error_msg = manager.validate_ai_config(config)
        print(f"✅ 配置验证: {is_valid}, {error_msg}")
        
        # 测试保存配置
        success = manager.save_ai_config(config)
        print(f"✅ 配置保存: {success}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI配置管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_manager():
    """测试AI管理器"""
    print("\n🔍 测试AI管理器...")
    
    try:
        from smartvault.services.ai.ai_manager import AIManager
        from smartvault.utils.config import load_config
        
        # 创建AI管理器
        ai_manager = AIManager()
        print("✅ AI管理器创建成功")
        
        # 加载配置
        config = load_config()
        print("✅ 配置加载成功")
        
        # 初始化AI管理器
        success = ai_manager.initialize(config)
        print(f"✅ AI管理器初始化: {success}")
        
        # 获取状态
        status = ai_manager.get_status()
        print(f"✅ AI状态: {status['enabled']}, {status['status']}")
        
        # 测试标签建议
        test_file = {
            'name': 'test.py',
            'extension': '.py',
            'path': '/test/test.py'
        }
        suggestions = ai_manager.suggest_tags(test_file)
        print(f"✅ 标签建议: {suggestions}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_settings_dialog_structure():
    """测试设置对话框结构（不创建实例）"""
    print("\n🔍 测试设置对话框结构...")
    
    try:
        # 检查导入
        from smartvault.ui.dialogs.settings_dialog import SettingsDialog
        print("✅ 设置对话框导入成功")
        
        # 检查AI页面导入
        from smartvault.ui.dialogs.settings.pages.ai.ai_main_page import AISettingsPage
        print("✅ AI设置页面导入成功")
        
        # 检查页面注册
        from smartvault.ui.dialogs.settings.pages import AISettingsPage as ImportedAIPage
        print("✅ AI页面已在pages模块中注册")
        
        return True
        
    except Exception as e:
        print(f"❌ 设置对话框结构测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 AI配置测试（无Qt依赖）")
    print("=" * 50)
    
    # 测试1: AI配置管理器
    test1 = test_ai_config_manager()
    
    # 测试2: AI管理器
    test2 = test_ai_manager()
    
    # 测试3: 设置对话框结构
    test3 = test_settings_dialog_structure()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"AI配置管理器: {'✅' if test1 else '❌'}")
    print(f"AI管理器: {'✅' if test2 else '❌'}")
    print(f"设置对话框结构: {'✅' if test3 else '❌'}")
    
    if all([test1, test2, test3]):
        print("\n🎉 所有配置测试通过！")
        print("✅ AI功能基础架构已就绪")
        return 0
    else:
        print("\n⚠️ 部分测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
