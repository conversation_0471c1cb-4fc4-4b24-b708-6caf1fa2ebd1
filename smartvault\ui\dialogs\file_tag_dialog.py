"""
文件标签管理对话框
"""

import sys
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QTreeWidget, QTreeWidgetItem, QMessageBox,
    QWidget, QSplitter, QTextEdit
)
from PySide6.QtCore import Qt, Signal

from smartvault.services.tag_service import TagService
from smartvault.utils.tree_state_manager import tree_state_manager


class FileTagDialog(QDialog):
    """文件标签管理对话框"""

    # 信号：标签发生变化时发出
    tags_changed = Signal()

    def __init__(self, file_ids, parent=None):
        """初始化对话框

        Args:
            file_ids: 文件ID列表（支持单个或多个文件）
            parent: 父窗口
        """
        super().__init__(parent)
        self.file_ids = file_ids if isinstance(file_ids, list) else [file_ids]
        self.tag_service = TagService()

        # 当前文件的标签状态
        self.current_file_tags = {}  # {file_id: [tag_ids]}
        self.selected_tags = set()   # 当前选中的标签ID集合

        self.init_ui()
        self.load_data()

    def init_ui(self):
        """初始化用户界面"""
        file_count = len(self.file_ids)
        title = f"管理标签 - {file_count} 个文件" if file_count > 1 else "管理标签"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(600, 500)

        # 主布局
        layout = QVBoxLayout(self)

        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(splitter)

        # 左侧：标签树
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)

        # 标签树标题
        left_layout.addWidget(QLabel("选择标签"))

        # 标签树
        self.tag_tree = QTreeWidget()
        self.tag_tree.setObjectName("fileTagManagementTree")  # 设置对象名称用于主题处理
        self.tag_tree.setHeaderHidden(True)
        self.tag_tree.setRootIsDecorated(True)
        self.tag_tree.itemChanged.connect(self.on_tag_item_changed)
        left_layout.addWidget(self.tag_tree)

        splitter.addWidget(left_widget)

        # 右侧：文件信息和当前标签
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)

        # 文件信息
        right_layout.addWidget(QLabel("文件信息"))
        self.file_info = QTextEdit()
        self.file_info.setMaximumHeight(150)
        self.file_info.setReadOnly(True)
        right_layout.addWidget(self.file_info)

        # 当前标签状态
        right_layout.addWidget(QLabel("标签状态"))
        self.tag_status = QTextEdit()
        self.tag_status.setReadOnly(True)
        right_layout.addWidget(self.tag_status)

        splitter.addWidget(right_widget)

        # 设置分割器比例
        splitter.setSizes([350, 250])

        # 对话框按钮
        buttons_layout = QHBoxLayout()

        self.apply_btn = QPushButton("应用")
        self.apply_btn.clicked.connect(self.apply_changes)
        buttons_layout.addWidget(self.apply_btn)

        buttons_layout.addStretch()

        self.ok_btn = QPushButton("确定")
        self.ok_btn.clicked.connect(self.accept_changes)
        buttons_layout.addWidget(self.ok_btn)

        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)

        layout.addLayout(buttons_layout)

    def load_data(self):
        """加载数据"""
        try:
            # 加载文件信息
            self.load_file_info()

            # 加载当前文件的标签
            self.load_current_tags()

            # 加载标签树
            self.load_tag_tree()

            # 更新标签状态显示
            self.update_tag_status()

        except Exception as e:
            QMessageBox.warning(self, "错误", f"加载数据失败: {e}")

    def load_file_info(self):
        """加载文件信息"""
        try:
            from smartvault.services.file import FileService
            file_service = FileService()

            file_info_text = f"选中文件数量: {len(self.file_ids)}\n\n"

            for i, file_id in enumerate(self.file_ids[:5]):  # 最多显示5个文件
                file_info = file_service.get_file_by_id(file_id)
                if file_info:
                    file_info_text += f"{i+1}. {file_info['name']}\n"

            if len(self.file_ids) > 5:
                file_info_text += f"... 还有 {len(self.file_ids) - 5} 个文件"

            self.file_info.setText(file_info_text)

        except Exception as e:
            self.file_info.setText(f"加载文件信息失败: {e}")

    def load_current_tags(self):
        """加载当前文件的标签"""
        try:
            self.current_file_tags = {}

            for file_id in self.file_ids:
                tags = self.tag_service.get_file_tags(file_id)
                self.current_file_tags[file_id] = [tag['id'] for tag in tags]

            # 计算共同标签（所有文件都有的标签）
            if self.file_ids:
                common_tags = set(self.current_file_tags[self.file_ids[0]])
                for file_id in self.file_ids[1:]:
                    common_tags &= set(self.current_file_tags[file_id])

                self.selected_tags = common_tags.copy()

        except Exception as e:
            print(f"加载当前标签失败: {e}")

    def load_tag_tree(self):
        """加载标签树"""
        try:
            # 清空树
            self.tag_tree.clear()

            # 获取标签树结构
            tag_tree = self.tag_service.get_tag_tree()

            # 构建树控件
            self.build_tree_items(tag_tree, self.tag_tree)

            # 🔧 使用状态管理器恢复展开状态，而不是强制展开所有
            tree_state_manager.restore_tree_state(self.tag_tree, "file_tag_dialog_tree")

        except Exception as e:
            QMessageBox.warning(self, "错误", f"加载标签树失败: {e}")

    def accept(self):
        """接受对话框前保存树状态"""
        try:
            # 🔧 保存树展开状态
            tree_state_manager.save_tree_state(self.tag_tree, "file_tag_dialog_tree")
        except Exception as e:
            print(f"保存文件标签对话框树状态失败: {e}")
        finally:
            super().accept()

    def reject(self):
        """拒绝对话框前保存树状态"""
        try:
            # 🔧 保存树展开状态
            tree_state_manager.save_tree_state(self.tag_tree, "file_tag_dialog_tree")
        except Exception as e:
            print(f"保存文件标签对话框树状态失败: {e}")
        finally:
            super().reject()

    def build_tree_items(self, tag_nodes, parent_widget, depth=0):
        """构建树项（参考导航栏的显示方式）

        Args:
            tag_nodes: 标签节点列表
            parent_widget: 父组件
            depth: 当前深度（用于颜色继承）
        """
        for tag_node in tag_nodes:
            # 创建树项
            item = QTreeWidgetItem(parent_widget)

            # 设置显示文本
            display_text = f"{tag_node['name']}"
            if tag_node['total_file_count'] > 0:
                display_text += f" ({tag_node['total_file_count']})"
            item.setText(0, display_text)

            # 存储标签数据
            item.setData(0, Qt.ItemDataRole.UserRole, tag_node)

            # 设置复选框
            item.setFlags(item.flags() | Qt.ItemFlag.ItemIsUserCheckable)

            # 设置初始选中状态
            if tag_node['id'] in self.selected_tags:
                item.setCheckState(0, Qt.CheckState.Checked)
            else:
                item.setCheckState(0, Qt.CheckState.Unchecked)

            # 设置颜色（参考导航栏的方式）
            self._apply_tag_color(item, tag_node, depth)

            # 递归添加子项
            if tag_node['children']:
                self.build_tree_items(tag_node['children'], item, depth + 1)

    def _apply_tag_color(self, item, tag_data, depth=0):
        """应用标签颜色（使用统一工具类）"""
        from smartvault.ui.utils.tag_color_utils import TagColorApplier
        TagColorApplier.apply_tag_color(item, tag_data, self.tag_service, depth)

    def on_tag_item_changed(self, item, column):
        """标签项状态改变时的处理"""
        if column != 0:
            return

        tag_data = item.data(0, Qt.ItemDataRole.UserRole)
        if not tag_data:
            return

        tag_id = tag_data['id']
        is_checked = item.checkState(0) == Qt.CheckState.Checked

        if is_checked:
            self.selected_tags.add(tag_id)
        else:
            self.selected_tags.discard(tag_id)

        # 更新标签状态显示
        self.update_tag_status()

    def update_tag_status(self):
        """更新标签状态显示"""
        try:
            status_text = ""

            if len(self.file_ids) == 1:
                # 单文件模式
                file_id = self.file_ids[0]
                current_tags = self.current_file_tags.get(file_id, [])

                status_text += f"当前标签 ({len(current_tags)} 个):\n"
                for tag_id in current_tags:
                    tag = self.tag_service.get_tag_by_id(tag_id)
                    if tag:
                        status_text += f"• {tag['name']}\n"

                status_text += f"\n选中标签 ({len(self.selected_tags)} 个):\n"
                for tag_id in self.selected_tags:
                    tag = self.tag_service.get_tag_by_id(tag_id)
                    if tag:
                        status_text += f"• {tag['name']}\n"
            else:
                # 多文件模式
                # 计算共同标签
                common_tags = set(self.current_file_tags[self.file_ids[0]])
                for file_id in self.file_ids[1:]:
                    common_tags &= set(self.current_file_tags[file_id])

                status_text += f"共同标签 ({len(common_tags)} 个):\n"
                for tag_id in common_tags:
                    tag = self.tag_service.get_tag_by_id(tag_id)
                    if tag:
                        status_text += f"• {tag['name']}\n"

                status_text += f"\n将应用标签 ({len(self.selected_tags)} 个):\n"
                for tag_id in self.selected_tags:
                    tag = self.tag_service.get_tag_by_id(tag_id)
                    if tag:
                        status_text += f"• {tag['name']}\n"

            self.tag_status.setText(status_text)

        except Exception as e:
            self.tag_status.setText(f"更新状态失败: {e}")

    def apply_changes(self):
        """应用更改"""
        try:
            changes_made = False

            for file_id in self.file_ids:
                current_tags = set(self.current_file_tags.get(file_id, []))
                new_tags = self.selected_tags.copy()

                # 添加新标签
                tags_to_add = new_tags - current_tags
                for tag_id in tags_to_add:
                    self.tag_service.add_tag_to_file(file_id, tag_id)
                    changes_made = True

                # 移除标签
                tags_to_remove = current_tags - new_tags
                for tag_id in tags_to_remove:
                    self.tag_service.remove_tag_from_file(file_id, tag_id)
                    changes_made = True

                # 更新当前标签状态
                self.current_file_tags[file_id] = list(new_tags)

            if changes_made:
                self.tags_changed.emit()
                # 移除不必要的成功提示，符合"用户干预极简化"原则
                self.update_tag_status()
            # 移除"没有更改"的提示，减少用户干预

        except Exception as e:
            QMessageBox.warning(self, "错误", f"应用更改失败: {e}")

    def accept_changes(self):
        """确定并关闭对话框"""
        self.apply_changes()
        self.accept()


if __name__ == "__main__":
    from PySide6.QtWidgets import QApplication

    app = QApplication(sys.argv)

    # 测试对话框
    dialog = FileTagDialog(["test_file_id"])
    dialog.show()

    sys.exit(app.exec())
