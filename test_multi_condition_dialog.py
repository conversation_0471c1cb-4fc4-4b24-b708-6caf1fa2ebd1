#!/usr/bin/env python3
"""
测试多条件自动标签对话框
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QPushButton, QVBoxLayout, QWidget, QMessageBox

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from smartvault.ui.dialogs.multi_condition_auto_tag_dialog import MultiConditionAutoTagDialog
from smartvault.services.auto_tag_service import (
    AutoTagRule, Condition, ConditionGroup, ConditionType, LogicOperator
)


class TestWindow(QWidget):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("多条件自动标签对话框测试")
        self.resize(300, 200)
        
        layout = QVBoxLayout(self)
        
        # 新建规则按钮
        new_rule_btn = QPushButton("新建多条件规则")
        new_rule_btn.clicked.connect(self.new_rule)
        layout.addWidget(new_rule_btn)
        
        # 编辑规则按钮
        edit_rule_btn = QPushButton("编辑示例规则")
        edit_rule_btn.clicked.connect(self.edit_rule)
        layout.addWidget(edit_rule_btn)
        
        # 测试复杂规则按钮
        complex_rule_btn = QPushButton("测试复杂嵌套规则")
        complex_rule_btn.clicked.connect(self.test_complex_rule)
        layout.addWidget(complex_rule_btn)
    
    def new_rule(self):
        """新建规则"""
        dialog = MultiConditionAutoTagDialog(self)
        if dialog.exec_() == dialog.Accepted:
            rule = dialog.get_rule()
            if rule:
                QMessageBox.information(
                    self, "成功", 
                    f"创建规则成功！\n\n"
                    f"规则名称: {rule.name}\n"
                    f"标签: {', '.join(rule.tag_names)}\n"
                    f"条件描述: {rule.get_description()}"
                )
    
    def edit_rule(self):
        """编辑示例规则"""
        # 创建一个示例规则
        condition_group = ConditionGroup(
            operator=LogicOperator.AND,
            conditions=[
                Condition(type=ConditionType.FILE_EXTENSION, value="pdf,doc"),
                Condition(type=ConditionType.FILE_SIZE, value=">1MB")
            ]
        )
        
        sample_rule = AutoTagRule(
            id="sample_rule",
            name="大文档规则",
            tag_names=["文档", "大文件"],
            enabled=True,
            priority=5,
            condition_group=condition_group
        )
        
        dialog = MultiConditionAutoTagDialog(self, sample_rule)
        if dialog.exec_() == dialog.Accepted:
            rule = dialog.get_rule()
            if rule:
                QMessageBox.information(
                    self, "成功", 
                    f"编辑规则成功！\n\n"
                    f"规则名称: {rule.name}\n"
                    f"标签: {', '.join(rule.tag_names)}\n"
                    f"条件描述: {rule.get_description()}"
                )
    
    def test_complex_rule(self):
        """测试复杂嵌套规则"""
        # 创建复杂的嵌套条件：(PDF OR DOC) AND (大文件 OR 重要文件)
        file_type_group = ConditionGroup(
            operator=LogicOperator.OR,
            conditions=[
                Condition(type=ConditionType.FILE_EXTENSION, value="pdf"),
                Condition(type=ConditionType.FILE_EXTENSION, value="doc,docx")
            ]
        )
        
        importance_group = ConditionGroup(
            operator=LogicOperator.OR,
            conditions=[
                Condition(type=ConditionType.FILE_SIZE, value=">5MB"),
                Condition(type=ConditionType.FILE_NAME_PATTERN, value="重要|urgent|important")
            ]
        )
        
        main_group = ConditionGroup(
            operator=LogicOperator.AND,
            conditions=[file_type_group, importance_group]
        )
        
        complex_rule = AutoTagRule(
            id="complex_rule",
            name="重要文档自动标签规则",
            tag_names=["重要文档", "需关注"],
            enabled=True,
            priority=10,
            condition_group=main_group
        )
        
        dialog = MultiConditionAutoTagDialog(self, complex_rule)
        if dialog.exec_() == dialog.Accepted:
            rule = dialog.get_rule()
            if rule:
                QMessageBox.information(
                    self, "成功", 
                    f"复杂规则编辑成功！\n\n"
                    f"规则名称: {rule.name}\n"
                    f"标签: {', '.join(rule.tag_names)}\n"
                    f"条件描述: {rule.get_description()}"
                )


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
