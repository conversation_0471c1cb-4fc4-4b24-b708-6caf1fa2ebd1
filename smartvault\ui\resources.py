"""
资源管理模块
"""

from PySide6.QtCore import QFile
from PySide6.QtGui import QIcon, QPixmap
from PySide6.QtWidgets import QStyle, QApplication


def get_icon_path(name):
    """获取图标文件路径

    Args:
        name: 图标名称

    Returns:
        str: 图标文件路径
    """
    return f"icons/{name}.png"


def get_icon(name):
    """获取图标

    Args:
        name: 图标名称

    Returns:
        QIcon: 图标对象
    """
    # 首先尝试从文件加载
    icon_path = f"icons/{name}.png"
    if QFile.exists(icon_path):
        return QIcon(icon_path)

    # 如果文件不存在，则使用内置图标
    app = QApplication.instance()
    style = app.style()

    # 工具栏图标
    if name == "add_file":
        return style.standardIcon(QStyle.SP_FileIcon)
    elif name == "add_folder":
        return style.standardIcon(QStyle.SP_DirIcon)
    elif name == "search":
        return style.standardIcon(QStyle.SP_FileDialogContentsView)
    elif name == "app":
        return QIcon("icons/SmartVault.ico")
    elif name == "help":
        return style.standardIcon(QStyle.SP_DialogHelpButton)
    elif name == "exit":
        return style.standardIcon(QStyle.SP_DialogCloseButton)

    # 分页按钮图标
    elif name == "first_page":
        return style.standardIcon(QStyle.SP_MediaSkipBackward)
    elif name == "prev_page":
        return style.standardIcon(QStyle.SP_MediaSeekBackward)
    elif name == "next_page":
        return style.standardIcon(QStyle.SP_MediaSeekForward)
    elif name == "last_page":
        return style.standardIcon(QStyle.SP_MediaSkipForward)

    # 视图模式图标
    elif name == "table_view":
        return style.standardIcon(QStyle.SP_FileDialogDetailedView)
    elif name == "grid_view":
        return style.standardIcon(QStyle.SP_FileDialogContentsView)
    elif name == "details_view":
        return style.standardIcon(QStyle.SP_FileDialogListView)

    # 文件类型图标
    elif name == "file":
        return style.standardIcon(QStyle.SP_FileIcon)
    elif name == "folder":
        return style.standardIcon(QStyle.SP_DirIcon)
    elif name == "text":
        return style.standardIcon(QStyle.SP_FileIcon)
    elif name == "pdf":
        return style.standardIcon(QStyle.SP_FileLinkIcon)
    elif name == "word":
        return style.standardIcon(QStyle.SP_FileDialogDetailedView)
    elif name == "excel":
        return style.standardIcon(QStyle.SP_FileDialogDetailedView)
    elif name == "powerpoint":
        return style.standardIcon(QStyle.SP_FileDialogDetailedView)
    elif name == "image":
        return style.standardIcon(QStyle.SP_DriveCDIcon)
    elif name == "audio":
        return style.standardIcon(QStyle.SP_MediaVolume)
    elif name == "video":
        return style.standardIcon(QStyle.SP_MediaPlay)
    elif name == "archive":
        return style.standardIcon(QStyle.SP_DirClosedIcon)

    # 网格视图需要的文件类型图标
    elif name == "text_file":
        return style.standardIcon(QStyle.SP_FileIcon)
    elif name == "word_file":
        return style.standardIcon(QStyle.SP_FileDialogDetailedView)
    elif name == "pdf_file":
        return style.standardIcon(QStyle.SP_FileLinkIcon)
    elif name == "image_file":
        return style.standardIcon(QStyle.SP_DriveCDIcon)
    elif name == "audio_file":
        return style.standardIcon(QStyle.SP_MediaVolume)
    elif name == "video_file":
        return style.standardIcon(QStyle.SP_MediaPlay)
    elif name == "archive_file":
        return style.standardIcon(QStyle.SP_DirClosedIcon)
    elif name == "default_file":
        return style.standardIcon(QStyle.SP_FileIcon)
    else:
        # 返回默认图标
        return QIcon()


def get_pixmap(name):
    """获取图像

    Args:
        name: 图像名称

    Returns:
        QPixmap: 图像对象
    """
    # 首先尝试从文件加载
    icon_path = f"icons/{name}.png"
    if QFile.exists(icon_path):
        return QPixmap(icon_path)

    # 如果文件不存在，则使用内置图标
    icon = get_icon(name)
    return icon.pixmap(32, 32)
