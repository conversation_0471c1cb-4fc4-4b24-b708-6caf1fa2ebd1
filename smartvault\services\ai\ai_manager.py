"""
AI功能统一管理器

负责协调所有AI功能，提供统一的接口给其他服务使用
"""

import os
import json
from typing import Dict, List, Optional, Any
from datetime import datetime

from .fallback_service import FallbackService
from .smart_rule_engine import SmartRuleEngine
from .adaptive_rule_engine import AdaptiveRuleEngine


class AIManager:
    """AI功能统一管理器"""

    def __init__(self):
        self.enabled = False
        self.current_stage = "rule_based"  # rule_based, ml_basic, deep_learning
        self.smart_rule_engine = None
        self.adaptive_rule_engine = None
        self.ml_engine = None
        self.fallback_service = FallbackService()

        # 与现有服务的集成
        self.tag_service = None
        self.auto_tag_service = None
        self.db = None  # 数据库连接

        # AI状态
        self.initialization_status = "not_initialized"  # not_initialized, initializing, ready, error
        self.last_error = None

    def initialize(self, config: Dict, tag_service=None, auto_tag_service=None, db=None) -> bool:
        """初始化AI功能

        Args:
            config: 配置字典
            tag_service: 标签服务实例
            auto_tag_service: 自动标签服务实例
            db: 数据库连接

        Returns:
            bool: 初始化是否成功
        """
        try:
            self.initialization_status = "initializing"

            # 读取AI配置 - 只检查AI设置页面的开关
            self.enabled = config.get("ai", {}).get("enabled", False)
            ai_config = config.get("ai", {})

            # 设置服务引用
            self.tag_service = tag_service
            self.auto_tag_service = auto_tag_service
            self.db = db

            if not self.enabled:
                self.initialization_status = "ready"
                return True

            # 初始化智能规则引擎（阶段一）
            self.smart_rule_engine = SmartRuleEngine()
            self.smart_rule_engine.initialize(config, tag_service, db)

            # 初始化自适应规则引擎（第二阶段）
            self.adaptive_rule_engine = AdaptiveRuleEngine()
            self.adaptive_rule_engine.initialize(db)

            # 根据配置决定AI能力级别
            if ai_config.get("enable_ml", False):
                self.current_stage = "ml_basic"
                # TODO: 初始化ML引擎

            elif ai_config.get("enable_deep_learning", False):
                self.current_stage = "deep_learning"
                # TODO: 初始化深度学习引擎

            self.initialization_status = "ready"
            return True

        except Exception as e:
            self.last_error = str(e)
            self.initialization_status = "error"
            print(f"AI管理器初始化失败: {e}")
            return False

    def is_available(self) -> bool:
        """检查AI功能是否可用"""
        return self.enabled and self.initialization_status == "ready"

    def get_status(self) -> Dict[str, Any]:
        """获取AI功能状态"""
        return {
            "enabled": self.enabled,
            "stage": self.current_stage,
            "status": self.initialization_status,
            "last_error": self.last_error,
            "features": {
                "smart_rules": self.smart_rule_engine is not None,
                "adaptive_rules": self.adaptive_rule_engine is not None,
                "ml_engine": self.ml_engine is not None,
                "deep_learning": False  # TODO: 实现后更新
            }
        }

    def suggest_tags(self, file_info: Dict) -> List[str]:
        """智能标签建议

        Args:
            file_info: 文件信息字典，包含name, path, extension等

        Returns:
            List[str]: 建议的标签列表
        """
        # 如果AI功能未启用，直接返回空列表
        if not self.enabled:
            return []

        # 如果AI功能启用但不可用，使用降级服务
        if not self.is_available():
            return self.fallback_service.suggest_tags_fallback(file_info)

        suggestions = []

        try:
            # 阶段一：智能规则引擎
            if self.smart_rule_engine:
                rule_suggestions = self.smart_rule_engine.suggest_tags(file_info)
                suggestions.extend(rule_suggestions)

            # 阶段二：机器学习增强（TODO）
            if self.current_stage in ["ml_basic", "deep_learning"] and self.ml_engine:
                ml_suggestions = self.ml_engine.predict_tags(file_info)
                suggestions.extend(ml_suggestions)

            # 阶段三：深度学习增强（TODO）
            if self.current_stage == "deep_learning":
                # TODO: 实现深度学习模型调用
                pass

        except Exception as e:
            print(f"AI标签建议失败，使用降级方案: {e}")
            return self.fallback_service.suggest_tags_fallback(file_info)

        # 去重保持顺序
        return list(dict.fromkeys(suggestions))

    def detect_project_files(self, folder_path: str) -> Dict:
        """检测项目文件

        Args:
            folder_path: 文件夹路径

        Returns:
            Dict: 项目检测结果
        """
        if not self.is_available() or not self.smart_rule_engine:
            return {}

        try:
            return self.smart_rule_engine.detect_project_files(folder_path)
        except Exception as e:
            print(f"项目文件检测失败: {e}")
            return {}

    def detect_file_series(self, files: List[Dict]) -> List[Dict]:
        """检测文件系列

        Args:
            files: 文件列表

        Returns:
            List[Dict]: 系列检测结果
        """
        if not self.is_available() or not self.smart_rule_engine:
            return []

        try:
            return self.smart_rule_engine.detect_file_series(files)
        except Exception as e:
            print(f"文件系列检测失败: {e}")
            return []

    def learn_from_user_action(self, action_data: Dict):
        """从用户行为中学习

        Args:
            action_data: 用户行为数据
        """
        if not self.is_available():
            return

        try:
            # 智能规则引擎学习
            if self.smart_rule_engine:
                self.smart_rule_engine.learn_from_user_action(action_data)

            # 自适应规则引擎学习（如果有规则反馈）
            if self.adaptive_rule_engine and action_data.get('rule_feedback'):
                rule_id = action_data.get('rule_id')
                feedback = action_data.get('feedback')
                context = action_data.get('context', {})

                if rule_id and feedback:
                    self.adaptive_rule_engine.update_rule_performance(rule_id, feedback, context)

        except Exception as e:
            print(f"用户行为学习失败: {e}")

    def generate_adaptive_rules(self, user_behavior_data: List[Dict]) -> List[Dict]:
        """生成自适应规则

        Args:
            user_behavior_data: 用户行为数据列表

        Returns:
            List[Dict]: 新生成的规则列表
        """
        if not self.is_available() or not self.adaptive_rule_engine:
            return []

        try:
            return self.adaptive_rule_engine.generate_adaptive_rules(user_behavior_data)
        except Exception as e:
            print(f"生成自适应规则失败: {e}")
            return []

    def get_rule_performance_stats(self) -> Dict:
        """获取规则性能统计

        Returns:
            Dict: 规则性能统计信息
        """
        if not self.is_available() or not self.adaptive_rule_engine:
            return {}

        try:
            return self.adaptive_rule_engine.get_performance_statistics()
        except Exception as e:
            print(f"获取规则性能统计失败: {e}")
            return {}

    def get_learning_statistics(self) -> Dict:
        """获取学习统计信息

        Returns:
            Dict: 学习统计信息
        """
        stats = {}

        try:
            # 智能规则引擎统计
            if self.smart_rule_engine:
                stats['behavior_learning'] = self.smart_rule_engine.get_learning_statistics()

            # 自适应规则引擎统计
            if self.adaptive_rule_engine:
                stats['rule_performance'] = self.adaptive_rule_engine.get_performance_statistics()

        except Exception as e:
            print(f"获取学习统计失败: {e}")

        return stats

    def get_ai_suggestions_for_file(self, file_info: Dict) -> Dict[str, Any]:
        """获取文件的完整AI建议

        Args:
            file_info: 文件信息

        Returns:
            Dict: 包含各种AI建议的字典
        """
        if not self.is_available():
            return {}

        suggestions = {
            "tags": self.suggest_tags(file_info),
            "project_info": {},
            "series_info": [],
            "confidence_scores": {}
        }

        # 如果是文件夹中的文件，检测项目信息
        if file_info.get("folder_path"):
            project_info = self.detect_project_files(file_info["folder_path"])
            if project_info:
                suggestions["project_info"] = project_info
                suggestions["confidence_scores"]["project"] = project_info.get("confidence", 0.0)

        return suggestions
