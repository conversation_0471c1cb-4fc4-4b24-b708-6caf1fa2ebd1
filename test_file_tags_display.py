#!/usr/bin/env python3
"""
测试文件列表中的标签显示功能
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import unittest
from unittest.mock import Mock, patch
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from smartvault.ui.models.file_table_model import FileTableModel
from smartvault.services.tag_service import TagService

class TestFileTagsDisplay(unittest.TestCase):
    """测试文件标签显示功能"""

    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        if not QApplication.instance():
            cls.app = QApplication(sys.argv)
        else:
            cls.app = QApplication.instance()

    def setUp(self):
        """每个测试前的设置"""
        # 创建文件表格模型
        self.model = FileTableModel()

        # 模拟标签服务
        self.mock_tag_service = Mock()

        # 模拟文件数据
        self.mock_files = [
            {
                "id": "file1",
                "name": "test1.txt",
                "original_path": "/path/to/test1.txt",
                "size": 1024,
                "added_at": "2024-01-01T10:00:00",
                "entry_type": "link"
            },
            {
                "id": "file2",
                "name": "test2.pdf",
                "original_path": "/path/to/test2.pdf",
                "size": 2048,
                "added_at": "2024-01-02T10:00:00",
                "entry_type": "copy"
            }
        ]

        # 模拟标签数据
        self.mock_tags_data = {
            "file1": [
                {"id": "tag1", "name": "工作", "color": "#FF5722"},
                {"id": "tag2", "name": "重要", "color": "#2196F3"}
            ],
            "file2": [
                {"id": "tag3", "name": "文档", "color": "#4CAF50"},
                {"id": "tag4", "name": "PDF", "color": "#9C27B0"},
                {"id": "tag5", "name": "归档", "color": "#FFC107"},
                {"id": "tag6", "name": "备份", "color": "#607D8B"}
            ]
        }

        # 设置模拟返回值
        self.mock_tag_service.get_file_tags.side_effect = lambda file_id: self.mock_tags_data.get(file_id, [])
        self.mock_tag_service.get_files_tags_batch.return_value = self.mock_tags_data

        # 设置标签服务到模型
        self.model.set_tag_service(self.mock_tag_service)

    def test_tags_column_exists(self):
        """测试标签列是否存在"""
        # 验证列数
        self.assertEqual(self.model.columnCount(), 6, "应该有6列（包括标签列）")

        # 验证标签列索引
        self.assertEqual(self.model.TAGS_COLUMN, 5, "标签列应该是第6列（索引5）")

        # 验证列名
        self.assertEqual(self.model.COLUMNS[self.model.TAGS_COLUMN], "标签", "标签列名应该是'标签'")

    def test_tags_display_single_file(self):
        """测试单个文件的标签显示"""
        # 设置文件数据
        self.model.setFiles([self.mock_files[0]])

        # 获取标签列的显示数据
        index = self.model.index(0, self.model.TAGS_COLUMN)
        tags_display = self.model.data(index, Qt.DisplayRole)

        # 验证标签显示
        self.assertEqual(tags_display, "工作 • 重要", "应该显示文件的所有标签")

    def test_tags_display_multiple_tags(self):
        """测试多标签文件的显示（超过3个标签）"""
        # 设置文件数据
        self.model.setFiles([self.mock_files[1]])

        # 获取标签列的显示数据
        index = self.model.index(0, self.model.TAGS_COLUMN)
        tags_display = self.model.data(index, Qt.DisplayRole)

        # 验证标签显示（应该显示前3个标签加省略号）
        self.assertEqual(tags_display, "文档 • PDF • 归档 • ...", "超过3个标签时应该显示省略号")

    def test_tags_display_no_tags(self):
        """测试没有标签的文件"""
        # 创建没有标签的文件
        file_without_tags = {
            "id": "file3",
            "name": "test3.txt",
            "original_path": "/path/to/test3.txt",
            "size": 512,
            "added_at": "2024-01-03T10:00:00",
            "entry_type": "link"
        }

        # 设置文件数据
        self.model.setFiles([file_without_tags])

        # 获取标签列的显示数据
        index = self.model.index(0, self.model.TAGS_COLUMN)
        tags_display = self.model.data(index, Qt.DisplayRole)

        # 验证标签显示
        self.assertEqual(tags_display, "", "没有标签的文件应该显示空字符串")

    def test_batch_tags_loading(self):
        """测试批量标签加载"""
        # 设置文件数据
        self.model.setFiles(self.mock_files)

        # 验证批量加载方法被调用
        self.mock_tag_service.get_files_tags_batch.assert_called_once()

        # 验证传入的文件ID
        called_args = self.mock_tag_service.get_files_tags_batch.call_args[0][0]
        expected_file_ids = ["file1", "file2"]
        self.assertEqual(set(called_args), set(expected_file_ids), "应该批量加载所有文件的标签")

    def test_tags_cache_functionality(self):
        """测试标签缓存功能"""
        # 设置文件数据
        self.model.setFiles([self.mock_files[0]])

        # 第一次获取标签（应该从服务获取）
        index = self.model.index(0, self.model.TAGS_COLUMN)
        tags_display1 = self.model.data(index, Qt.DisplayRole)

        # 第二次获取标签（应该从缓存获取）
        tags_display2 = self.model.data(index, Qt.DisplayRole)

        # 验证结果一致
        self.assertEqual(tags_display1, tags_display2, "缓存的标签显示应该一致")

        # 验证缓存中有数据
        self.assertIn("file1", self.model.file_tags_cache, "标签应该被缓存")

    def test_refresh_tags_functionality(self):
        """测试刷新标签功能"""
        # 设置文件数据
        self.model.setFiles([self.mock_files[0]])

        # 验证缓存中有数据
        self.assertIn("file1", self.model.file_tags_cache)

        # 刷新特定文件的标签
        self.model.refresh_file_tags(["file1"])

        # 验证缓存被清除
        self.assertNotIn("file1", self.model.file_tags_cache)

    def test_tags_display_style(self):
        """测试标签显示样式优化"""
        # 设置文件数据
        self.model.setFiles([self.mock_files[0]])

        # 获取标签显示
        index = self.model.index(0, self.model.TAGS_COLUMN)
        tags_display = self.model.data(index, Qt.DisplayRole)

        # 验证使用圆点分隔符
        self.assertIn(" • ", tags_display, "标签应该使用圆点分隔符")

        # 验证标签颜色
        foreground = self.model.data(index, Qt.ForegroundRole)
        self.assertIsNotNone(foreground, "标签列应该有特殊的前景色")

    def test_tags_tooltip(self):
        """测试标签工具提示"""
        # 设置文件数据
        self.model.setFiles([self.mock_files[0]])

        # 获取标签工具提示
        index = self.model.index(0, self.model.TAGS_COLUMN)
        tooltip = self.model.data(index, Qt.ToolTipRole)

        # 验证工具提示内容
        self.assertIn("文件标签:", tooltip, "工具提示应该包含标签信息")
        self.assertIn("工作", tooltip, "工具提示应该包含标签名称")

    def test_tags_cache_performance(self):
        """测试标签缓存性能优化"""
        # 设置缓存大小限制为较小值以便测试
        self.model.tags_cache_size_limit = 5

        # 创建多个文件来测试缓存清理
        many_files = []
        for i in range(10):
            file_data = {
                "id": f"file{i}",
                "name": f"test{i}.txt",
                "original_path": f"/path/to/test{i}.txt",
                "size": 1024,
                "added_at": "2023-01-01T00:00:00",
                "entry_type": "link"
            }
            many_files.append(file_data)

        # 设置文件数据
        self.model.setFiles(many_files)

        # 模拟访问所有文件的标签，触发缓存
        for i in range(10):
            index = self.model.index(i, self.model.TAGS_COLUMN)
            self.model.data(index, Qt.DisplayRole)

        # 验证缓存大小被限制
        self.assertLessEqual(len(self.model.file_tags_cache),
                           self.model.tags_cache_size_limit,
                           "缓存大小应该被限制")

    def test_batch_tags_loading(self):
        """测试批量标签加载"""
        # 创建多个文件
        many_files = []
        for i in range(5):
            file_data = {
                "id": f"batch_file{i}",
                "name": f"batch{i}.txt",
                "original_path": f"/path/to/batch{i}.txt",
                "size": 1024,
                "added_at": "2023-01-01T00:00:00",
                "entry_type": "link"
            }
            many_files.append(file_data)

        # 设置文件数据（应该触发批量加载）
        self.model.setFiles(many_files)

        # 验证批量加载被调用
        self.assertTrue(self.mock_tag_service.get_files_tags_batch.called,
                       "应该调用批量标签加载方法")

    def test_clear_tags_functionality(self):
        """测试清除标签功能"""
        # 设置文件数据
        self.model.setFiles([self.mock_files[0]])

        # 验证文件有标签
        index = self.model.index(0, self.model.TAGS_COLUMN)
        tags_display = self.model.data(index, Qt.DisplayRole)
        self.assertNotEqual(tags_display, "", "文件应该有标签")

        # 直接清空缓存并设置新的返回值
        self.model.file_tags_cache.clear()

        # 重新设置mock服务
        new_mock_service = Mock()
        new_mock_service.get_file_tags.return_value = []
        self.model.set_tag_service(new_mock_service)

        # 重新获取标签显示（这会触发新的标签获取）
        updated_tags_display = self.model.data(index, Qt.DisplayRole)
        self.assertEqual(updated_tags_display, "", "标签应该被清除")

def run_tests():
    """运行测试"""
    print("开始测试文件标签显示功能...")
    print("=" * 50)

    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestFileTagsDisplay)

    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    print("=" * 50)

    # 输出结果
    if result.wasSuccessful():
        print("🎉 所有测试通过！文件标签显示功能正常")
        print("\n✨ B019-8 任务完成:")
        print("  • 文件表格模型中成功添加标签列")
        print("  • 标签显示格式正确（最多3个标签+省略号）")
        print("  • 批量标签加载功能正常")
        print("  • 标签缓存机制工作正常")
        print("  • 标签刷新功能正常")
    else:
        print(f"❌ 测试失败：{len(result.failures)} 个失败，{len(result.errors)} 个错误")

        for test, traceback in result.failures:
            print(f"\n失败测试: {test}")
            print(f"错误信息: {traceback}")

        for test, traceback in result.errors:
            print(f"\n错误测试: {test}")
            print(f"错误信息: {traceback}")

    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
