#!/usr/bin/env python3
"""
测试备注自动刷新功能
"""

import sys
import os
sys.path.append('.')

def test_file_table_model_refresh():
    """测试文件表格模型的刷新功能"""
    print("=" * 60)
    print("🔍 测试文件表格模型刷新功能")
    print("=" * 60)
    
    try:
        from smartvault.services.file import FileService
        from smartvault.services.tag_service import TagService
        from smartvault.ui.models.file_table_model import FileTableModel
        import tempfile
        
        file_service = FileService()
        tag_service = TagService()
        
        # 创建测试文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("测试文件内容")
            test_file_path = f.name
        
        try:
            print("1. 添加测试文件...")
            file_id = file_service.add_file(test_file_path, "link")
            print(f"   ✅ 文件已添加，ID: {file_id}")
            
            print("2. 创建文件表格模型...")
            model = FileTableModel()
            model.set_tag_service(tag_service)
            
            # 加载文件数据
            files = file_service.get_files(limit=10)
            model.setFiles(files)
            print(f"   ✅ 加载了 {len(files)} 个文件")
            
            print("3. 测试初始状态...")
            display_text = model._get_file_tags_and_note_display(file_id)
            print(f"   📝 初始显示: '{display_text}'")
            
            print("4. 添加备注...")
            file_service.update_file_note(file_id, "这是测试备注")
            
            # 测试刷新前的显示
            display_text_before = model._get_file_tags_and_note_display(file_id)
            print(f"   📝 刷新前显示: '{display_text_before}'")
            
            print("5. 调用刷新方法...")
            model.refresh_file_notes([file_id])
            
            # 测试刷新后的显示
            display_text_after = model._get_file_tags_and_note_display(file_id)
            print(f"   📝 刷新后显示: '{display_text_after}'")
            
            if "📝这是测试备注" in display_text_after:
                print("   ✅ 备注刷新成功")
            else:
                print("   ❌ 备注刷新失败")
                return False
            
            print("6. 测试更新备注...")
            file_service.update_file_note(file_id, "更新后的备注")
            model.refresh_file_notes([file_id])
            
            display_text_updated = model._get_file_tags_and_note_display(file_id)
            print(f"   📝 更新后显示: '{display_text_updated}'")
            
            if "📝更新后的备注" in display_text_updated:
                print("   ✅ 备注更新刷新成功")
            else:
                print("   ❌ 备注更新刷新失败")
                return False
            
            print("7. 测试清除备注...")
            file_service.update_file_note(file_id, "")
            model.refresh_file_notes([file_id])
            
            display_text_cleared = model._get_file_tags_and_note_display(file_id)
            print(f"   📝 清除后显示: '{display_text_cleared}'")
            
            if "📝" not in display_text_cleared:
                print("   ✅ 备注清除刷新成功")
            else:
                print("   ❌ 备注清除刷新失败")
                return False
            
            return True
            
        finally:
            # 清理测试文件
            try:
                os.unlink(test_file_path)
            except:
                pass
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_refresh_file_data_method():
    """测试综合刷新方法"""
    print("\n" + "=" * 60)
    print("🔍 测试综合刷新方法")
    print("=" * 60)
    
    try:
        from smartvault.services.file import FileService
        from smartvault.services.tag_service import TagService
        from smartvault.ui.models.file_table_model import FileTableModel
        import tempfile
        
        file_service = FileService()
        tag_service = TagService()
        
        # 创建测试文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("测试文件内容")
            test_file_path = f.name
        
        try:
            print("1. 添加测试文件...")
            file_id = file_service.add_file(test_file_path, "link")
            print(f"   ✅ 文件已添加，ID: {file_id}")
            
            print("2. 添加标签和备注...")
            tag_id = tag_service.create_tag("测试标签", "#FF0000")
            tag_service.add_tag_to_file(file_id, tag_id)
            file_service.update_file_note(file_id, "测试备注内容")
            
            print("3. 创建文件表格模型...")
            model = FileTableModel()
            model.set_tag_service(tag_service)
            
            # 加载文件数据
            files = file_service.get_files(limit=10)
            model.setFiles(files)
            
            print("4. 测试综合刷新...")
            model.refresh_file_data([file_id])
            
            display_text = model._get_file_tags_and_note_display(file_id)
            print(f"   📝 刷新后显示: '{display_text}'")
            
            # 验证标签和备注都存在
            if "测试标签" in display_text and "📝测试备注内容" in display_text:
                print("   ✅ 标签和备注综合刷新成功")
                return True
            else:
                print("   ❌ 标签和备注综合刷新失败")
                return False
            
        finally:
            # 清理测试文件
            try:
                os.unlink(test_file_path)
            except:
                pass
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_components():
    """测试UI组件导入"""
    print("\n" + "=" * 60)
    print("🔍 测试UI组件导入")
    print("=" * 60)
    
    try:
        print("1. 测试文件表格视图...")
        from smartvault.ui.views.file_table_view import FileTableView
        print("   ✅ 文件表格视图导入成功")
        
        print("2. 测试文件网格视图...")
        from smartvault.ui.views.file_grid_view import FileGridView
        print("   ✅ 文件网格视图导入成功")
        
        print("3. 测试备注菜单...")
        from smartvault.ui.components.note_menu import NoteMenu
        print("   ✅ 备注菜单导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ UI组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始备注自动刷新测试")
    print("目标: 验证编辑备注后视图能自动刷新显示")
    
    test_results = []
    
    # 执行测试
    test_results.append(("文件表格模型刷新", test_file_table_model_refresh()))
    test_results.append(("综合刷新方法", test_refresh_file_data_method()))
    test_results.append(("UI组件导入", test_ui_components()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n总计: {passed} 个通过, {failed} 个失败")
    
    if failed == 0:
        print("\n🎉 所有测试通过！备注自动刷新功能实现成功！")
        print("\n📋 修复内容：")
        print("1. ✅ 在FileTableModel中添加了refresh_file_notes方法")
        print("2. ✅ 在FileTableModel中添加了refresh_file_data综合刷新方法")
        print("3. ✅ 修改了文件视图的_on_tags_changed方法，支持备注刷新")
        print("4. ✅ 修改了主窗口的on_tags_changed方法，支持备注刷新")
        print("5. ✅ 备注对话框保存后会自动触发视图刷新")
        
        print("\n💡 用户体验改进：")
        print("- 编辑备注后，文件视图立即显示最新的备注内容")
        print("- 无需手动刷新或重新加载文件列表")
        print("- 标签和备注的变化都能实时反映在界面上")
        print("- 保持了界面的响应性和流畅性")
        
        print("\n🔄 刷新机制：")
        print("1. 用户保存备注 → TagNoteDialog发出notes_changed信号")
        print("2. NoteMenu接收信号 → 转发给tags_changed信号")
        print("3. 文件视图接收信号 → 调用model.refresh_file_data()")
        print("4. 模型刷新数据 → 发出dataChanged信号")
        print("5. 视图接收信号 → 重新绘制显示内容")
    else:
        print("\n❌ 部分测试失败，请检查问题")
    
    return 0 if failed == 0 else 1

if __name__ == "__main__":
    sys.exit(main())
