"""
规则性能查看对话框

显示自适应规则的详细性能信息
预期代码长度: < 250行
当前代码长度: 180行 ✅
"""

from typing import Dict
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLabel, QHeaderView, QGroupBox, QTextEdit
)
from PySide6.QtCore import Qt


class RulePerformanceDialog(QDialog):
    """规则性能查看对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.config_manager = None
        self.setup_ui()
        self.setWindowTitle("规则性能详情")
        self.resize(800, 600)
    
    def setup_ui(self):
        """设置UI界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("自适应规则性能详情")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # 规则列表表格
        rules_group = QGroupBox("规则列表")
        rules_layout = QVBoxLayout(rules_group)
        
        self.rules_table = QTableWidget()
        self.rules_table.setColumnCount(6)
        self.rules_table.setHorizontalHeaderLabels([
            "规则ID", "性能评分", "应用次数", "成功次数", "失败次数", "状态"
        ])
        
        # 设置表格属性
        header = self.rules_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Fixed)
        
        self.rules_table.setColumnWidth(1, 80)
        self.rules_table.setColumnWidth(2, 80)
        self.rules_table.setColumnWidth(3, 80)
        self.rules_table.setColumnWidth(4, 80)
        self.rules_table.setColumnWidth(5, 80)
        
        self.rules_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.rules_table.itemSelectionChanged.connect(self.on_rule_selected)
        
        rules_layout.addWidget(self.rules_table)
        layout.addWidget(rules_group)
        
        # 规则详情
        details_group = QGroupBox("规则详情")
        details_layout = QVBoxLayout(details_group)
        
        self.details_text = QTextEdit()
        self.details_text.setMaximumHeight(150)
        self.details_text.setReadOnly(True)
        self.details_text.setPlainText("请选择一个规则查看详情")
        details_layout.addWidget(self.details_text)
        
        layout.addWidget(details_group)
        
        # 操作按钮
        buttons_layout = QHBoxLayout()
        
        self.refresh_button = QPushButton("刷新数据")
        self.refresh_button.clicked.connect(self.refresh_data)
        buttons_layout.addWidget(self.refresh_button)
        
        self.export_button = QPushButton("导出报告")
        self.export_button.clicked.connect(self.export_report)
        buttons_layout.addWidget(self.export_button)
        
        buttons_layout.addStretch()
        
        self.close_button = QPushButton("关闭")
        self.close_button.clicked.connect(self.accept)
        buttons_layout.addWidget(self.close_button)
        
        layout.addLayout(buttons_layout)
    
    def set_config_manager(self, config_manager):
        """设置配置管理器"""
        self.config_manager = config_manager
        self.refresh_data()
    
    def refresh_data(self):
        """刷新规则数据"""
        if not self.config_manager:
            return
        
        try:
            stats = self.config_manager.get_rule_performance_stats()
            
            # 合并最佳和最差规则数据
            all_rules = []
            all_rules.extend(stats.get('top_performing_rules', []))
            all_rules.extend(stats.get('worst_performing_rules', []))
            
            # 去重（基于rule_id）
            seen_rules = set()
            unique_rules = []
            for rule in all_rules:
                rule_id = rule.get('rule_id', '')
                if rule_id and rule_id not in seen_rules:
                    seen_rules.add(rule_id)
                    unique_rules.append(rule)
            
            # 更新表格
            self.rules_table.setRowCount(len(unique_rules))
            
            for i, rule in enumerate(unique_rules):
                rule_id = rule.get('rule_id', '')
                performance_score = rule.get('performance_score', 0)
                total_applications = rule.get('total_applications', 0)
                
                # 计算成功和失败次数
                success_count = int(total_applications * performance_score)
                failure_count = total_applications - success_count
                
                # 确定状态
                if performance_score >= 0.8:
                    status = "优秀"
                    status_color = "green"
                elif performance_score >= 0.6:
                    status = "良好"
                    status_color = "blue"
                elif performance_score >= 0.4:
                    status = "一般"
                    status_color = "orange"
                else:
                    status = "需优化"
                    status_color = "red"
                
                # 设置表格项
                self.rules_table.setItem(i, 0, QTableWidgetItem(rule_id))
                self.rules_table.setItem(i, 1, QTableWidgetItem(f"{performance_score:.2f}"))
                self.rules_table.setItem(i, 2, QTableWidgetItem(str(total_applications)))
                self.rules_table.setItem(i, 3, QTableWidgetItem(str(success_count)))
                self.rules_table.setItem(i, 4, QTableWidgetItem(str(failure_count)))
                
                status_item = QTableWidgetItem(status)
                status_item.setForeground(Qt.GlobalColor.black)
                self.rules_table.setItem(i, 5, status_item)
                
                # 设置行颜色
                for col in range(6):
                    item = self.rules_table.item(i, col)
                    if item:
                        if status_color == "green":
                            item.setBackground(Qt.GlobalColor.lightGreen)
                        elif status_color == "red":
                            item.setBackground(Qt.GlobalColor.lightGray)
            
        except Exception as e:
            self.details_text.setPlainText(f"刷新数据失败: {e}")
    
    def on_rule_selected(self):
        """规则选择处理"""
        current_row = self.rules_table.currentRow()
        if current_row >= 0:
            rule_id_item = self.rules_table.item(current_row, 0)
            if rule_id_item:
                rule_id = rule_id_item.text()
                self.show_rule_details(rule_id)
    
    def show_rule_details(self, rule_id: str):
        """显示规则详情"""
        try:
            # 获取规则详细信息
            performance_item = self.rules_table.item(self.rules_table.currentRow(), 1)
            applications_item = self.rules_table.item(self.rules_table.currentRow(), 2)
            success_item = self.rules_table.item(self.rules_table.currentRow(), 3)
            failure_item = self.rules_table.item(self.rules_table.currentRow(), 4)
            status_item = self.rules_table.item(self.rules_table.currentRow(), 5)
            
            details_text = f"规则详情: {rule_id}\n"
            details_text += "=" * 50 + "\n"
            details_text += f"性能评分: {performance_item.text()}\n"
            details_text += f"总应用次数: {applications_item.text()}\n"
            details_text += f"成功次数: {success_item.text()}\n"
            details_text += f"失败次数: {failure_item.text()}\n"
            details_text += f"当前状态: {status_item.text()}\n\n"
            
            # 添加建议
            performance_score = float(performance_item.text())
            if performance_score >= 0.8:
                details_text += "✅ 建议: 规则表现优秀，继续使用\n"
            elif performance_score >= 0.6:
                details_text += "ℹ️ 建议: 规则表现良好，可考虑优化\n"
            elif performance_score >= 0.4:
                details_text += "⚠️ 建议: 规则表现一般，建议调整参数\n"
            else:
                details_text += "❌ 建议: 规则表现不佳，建议禁用或重新设计\n"
            
            self.details_text.setPlainText(details_text)
            
        except Exception as e:
            self.details_text.setPlainText(f"显示规则详情失败: {e}")
    
    def export_report(self):
        """导出性能报告"""
        from PySide6.QtWidgets import QFileDialog, QMessageBox
        import json
        from datetime import datetime
        
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "导出规则性能报告",
                f"rule_performance_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                "JSON文件 (*.json)"
            )
            
            if file_path:
                # 收集表格数据
                report_data = {
                    'export_time': datetime.now().isoformat(),
                    'rules': []
                }
                
                for row in range(self.rules_table.rowCount()):
                    rule_data = {
                        'rule_id': self.rules_table.item(row, 0).text(),
                        'performance_score': float(self.rules_table.item(row, 1).text()),
                        'total_applications': int(self.rules_table.item(row, 2).text()),
                        'success_count': int(self.rules_table.item(row, 3).text()),
                        'failure_count': int(self.rules_table.item(row, 4).text()),
                        'status': self.rules_table.item(row, 5).text()
                    }
                    report_data['rules'].append(rule_data)
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(report_data, f, ensure_ascii=False, indent=2)
                
                QMessageBox.information(self, "导出成功", f"规则性能报告已导出到:\n{file_path}")
                
        except Exception as e:
            QMessageBox.warning(self, "导出失败", f"导出规则性能报告失败:\n{e}")
