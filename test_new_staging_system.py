#!/usr/bin/env python3
"""
测试新的中转文件夹系统
"""

import sys
import os
import tempfile
import shutil
sys.path.insert(0, os.path.abspath('.'))

from smartvault.services.file import FileService

def test_new_staging_system():
    """测试新的中转文件夹系统"""
    print("🧪 测试新的中转文件夹系统...")
    
    try:
        # 初始化文件服务
        file_service = FileService()
        
        # 1. 测试单个文件添加
        print("\n📄 测试单个文件添加...")
        temp_dir = tempfile.mkdtemp(prefix="smartvault_test_")
        test_file_path = os.path.join(temp_dir, "test_single_file.txt")
        
        with open(test_file_path, 'w', encoding='utf-8') as f:
            f.write("这是一个测试单个文件")
        
        file_id = file_service.add_file(test_file_path, mode="link")
        file_info = file_service.get_file_by_id(file_id)
        
        print(f"✅ 单个文件添加成功")
        print(f"   文件ID: {file_id}")
        print(f"   中转状态: {file_info.get('staging_status')}")
        print(f"   文件夹组: {file_info.get('folder_group_id')}")
        
        # 2. 测试文件夹添加
        print("\n📁 测试文件夹添加...")
        folder_dir = tempfile.mkdtemp(prefix="smartvault_test_folder_")
        
        # 创建几个测试文件
        for i in range(3):
            test_file = os.path.join(folder_dir, f"folder_file_{i}.txt")
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(f"这是文件夹中的文件 {i}")
        
        folder_group_id = file_service.add_file(folder_dir, mode="link")
        
        print(f"✅ 文件夹添加成功")
        print(f"   文件夹组ID: {folder_group_id}")
        
        # 3. 测试中转文件夹显示项目
        print("\n📥 测试中转文件夹显示...")
        
        # 获取文件夹组
        folder_groups = file_service.get_folder_groups_in_staging()
        print(f"文件夹组数量: {len(folder_groups)}")
        for group in folder_groups:
            print(f"  📁 {group['folder_name']} (包含 {group['file_count']} 个文件)")
        
        # 获取单独文件
        individual_files = file_service.get_individual_staging_files()
        print(f"单独文件数量: {len(individual_files)}")
        for file_info in individual_files:
            print(f"  📄 {file_info['name']}")
        
        # 获取显示项目
        display_items = file_service.get_staging_display_items()
        print(f"中转文件夹显示项目总数: {len(display_items)}")
        
        for item in display_items:
            if item.get('type') == 'folder_group':
                print(f"  📁 [文件夹] {item['folder_name']} (包含 {item['file_count']} 个文件)")
            elif item.get('type') == 'individual_file':
                print(f"  📄 [文件] {item['name']}")
        
        # 4. 测试文件夹组中的文件
        if folder_groups:
            print(f"\n🔍 测试文件夹组内容...")
            first_group = folder_groups[0]
            group_files = file_service.get_files_in_folder_group(first_group['folder_group_id'])
            print(f"文件夹组 '{first_group['folder_name']}' 包含文件:")
            for file_info in group_files:
                print(f"  - {file_info['name']} (状态: {file_info.get('staging_status')})")
        
        # 5. 测试"最近添加"筛选（应该只显示正式文件）
        print(f"\n📅 测试'最近添加'筛选...")
        recent_files = file_service.get_files(folder_filter_type="recent")
        print(f"最近添加的正式文件数量: {len(recent_files)}")
        
        # 将一个文件移出中转状态
        if individual_files:
            test_file_id = individual_files[0]['id']
            file_service.move_from_staging(test_file_id)
            print(f"已将文件 {individual_files[0]['name']} 移出中转状态")
            
            # 再次测试"最近添加"
            recent_files_after = file_service.get_files(folder_filter_type="recent")
            print(f"移出后，最近添加的正式文件数量: {len(recent_files_after)}")
        
        print("\n🎉 新的中转文件夹系统测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理临时文件
        try:
            if 'temp_dir' in locals():
                shutil.rmtree(temp_dir, ignore_errors=True)
            if 'folder_dir' in locals():
                shutil.rmtree(folder_dir, ignore_errors=True)
        except:
            pass

if __name__ == "__main__":
    success = test_new_staging_system()
    sys.exit(0 if success else 1)
