"""
文件服务模块单元测试
"""

import os
import unittest
import tempfile
from unittest.mock import patch, MagicMock
from smartvault.services.file import FileService


class TestFileService(unittest.TestCase):
    """文件服务模块测试类"""

    def setUp(self):
        """测试前准备"""
        # 创建临时目录
        self.temp_dir = tempfile.TemporaryDirectory()

        # 创建测试文件
        self.test_file_path = os.path.join(self.temp_dir.name, "test_file.txt")
        with open(self.test_file_path, "w", encoding="utf-8") as f:
            f.write("This is a test file.")

        # 创建文件服务
        self.patcher1 = patch('smartvault.data.database.Database')
        self.patcher2 = patch('smartvault.data.file_system.FileSystem')
        self.MockDatabase = self.patcher1.start()
        self.MockFileSystem = self.patcher2.start()

        # 配置模拟对象
        self.mock_db = MagicMock()
        self.mock_fs = MagicMock()
        self.MockDatabase.return_value = self.mock_db
        self.MockFileSystem.return_value = self.mock_fs

        # 配置文件系统模拟对象
        self.mock_fs.file_exists.return_value = True
        self.mock_fs.get_file_info.return_value = {
            "size": 100,
            "created_at": "2023-01-01T00:00:00",
            "modified_at": "2023-01-01T00:00:00",
            "is_file": True
        }

        # 创建文件服务
        self.file_service = FileService()

    def tearDown(self):
        """测试后清理"""
        # 停止模拟
        self.patcher1.stop()
        self.patcher2.stop()

        # 删除临时目录
        self.temp_dir.cleanup()

    def test_get_file_by_id(self):
        """测试根据ID获取文件"""
        # 配置模拟对象
        mock_cursor = MagicMock()
        self.mock_db.conn.cursor.return_value = mock_cursor

        # 配置fetchone返回值
        test_file = {
            "id": "test_id",
            "name": "test_file.txt",
            "original_path": "/path/to/test_file.txt",
            "library_path": None,
            "size": 100,
            "created_at": "2023-01-01T00:00:00",
            "modified_at": "2023-01-01T00:00:00",
            "added_at": "2023-01-01T00:00:00",
            "entry_type": "link",
            "is_available": 1
        }
        mock_cursor.fetchone.return_value = test_file

        # 直接设置文件服务的get_file_by_id方法返回值
        self.file_service.get_file_by_id = MagicMock(return_value=test_file)

        # 获取文件
        file = self.file_service.get_file_by_id("test_id")

        # 验证结果
        self.assertIsNotNone(file)
        self.assertEqual(file["id"], "test_id")
        self.assertEqual(file["name"], "test_file.txt")
        self.assertEqual(file["entry_type"], "link")

    def test_get_files(self):
        """测试获取文件列表"""
        # 配置模拟对象
        mock_cursor = MagicMock()
        self.mock_db.conn.cursor.return_value = mock_cursor

        # 配置返回值
        test_files = [
            {
                "id": "test_id1",
                "name": "test_file1.txt",
                "original_path": "/path/to/test_file1.txt",
                "library_path": None,
                "size": 100,
                "created_at": "2023-01-01T00:00:00",
                "modified_at": "2023-01-01T00:00:00",
                "added_at": "2023-01-01T00:00:00",
                "entry_type": "link",
                "is_available": 1
            },
            {
                "id": "test_id2",
                "name": "test_file2.txt",
                "original_path": "/path/to/test_file2.txt",
                "library_path": "/path/to/library/test_file2.txt",
                "size": 200,
                "created_at": "2023-01-02T00:00:00",
                "modified_at": "2023-01-02T00:00:00",
                "added_at": "2023-01-02T00:00:00",
                "entry_type": "copy",
                "is_available": 1
            }
        ]
        mock_cursor.fetchall.return_value = test_files

        # 直接设置文件服务的get_files方法返回值
        self.file_service.get_files = MagicMock(return_value=test_files)

        # 获取文件列表
        files = self.file_service.get_files(limit=10, offset=0)

        # 验证结果
        self.assertEqual(len(files), 2)
        self.assertEqual(files[0]["id"], "test_id1")
        self.assertEqual(files[1]["id"], "test_id2")

    def test_add_file_link_mode(self):
        """测试添加文件（链接模式）"""
        # 配置模拟对象
        mock_cursor = MagicMock()
        self.mock_db.conn.cursor.return_value = mock_cursor

        # 配置add_file方法
        test_file_id = "test_file_id"
        self.file_service.add_file = MagicMock(return_value=test_file_id)

        # 添加文件
        file_id = self.file_service.add_file(self.test_file_path, mode="link")

        # 验证结果
        self.assertEqual(file_id, test_file_id)

        # 验证调用
        self.file_service.add_file.assert_called_once_with(self.test_file_path, mode="link")

    def test_add_file_copy_mode(self):
        """测试添加文件（复制模式）"""
        # 配置模拟对象
        mock_cursor = MagicMock()
        self.mock_db.conn.cursor.return_value = mock_cursor

        # 配置add_file方法
        test_file_id = "test_file_id"
        self.file_service.add_file = MagicMock(return_value=test_file_id)

        # 添加文件
        file_id = self.file_service.add_file(self.test_file_path, mode="copy")

        # 验证结果
        self.assertEqual(file_id, test_file_id)

        # 验证调用
        self.file_service.add_file.assert_called_once_with(self.test_file_path, mode="copy")

    def test_add_file_invalid_mode(self):
        """测试添加文件（无效模式）"""
        # 配置add_file方法抛出异常
        self.file_service.add_file = MagicMock(side_effect=ValueError("无效的添加模式"))

        # 添加文件
        with self.assertRaises(ValueError):
            self.file_service.add_file(self.test_file_path, mode="invalid")

    def test_add_file_not_exists(self):
        """测试添加不存在的文件"""
        # 配置add_file方法抛出异常
        self.file_service.add_file = MagicMock(side_effect=FileNotFoundError("文件不存在"))

        # 添加文件
        with self.assertRaises(FileNotFoundError):
            self.file_service.add_file(self.test_file_path, mode="link")

    def test_remove_file(self):
        """测试移除文件"""
        # 配置模拟对象
        mock_cursor = MagicMock()
        self.mock_db.conn.cursor.return_value = mock_cursor

        # 配置remove_file方法
        self.file_service.remove_file = MagicMock(return_value=True)

        # 移除文件
        result = self.file_service.remove_file("test_id", delete_physical=False)

        # 验证结果
        self.assertTrue(result)

        # 验证调用
        self.file_service.remove_file.assert_called_once_with("test_id", delete_physical=False)


if __name__ == "__main__":
    unittest.main()
