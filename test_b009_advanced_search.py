#!/usr/bin/env python3
"""
测试B009高级搜索功能
"""

import sys
import os
import tempfile
import shutil
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication
from smartvault.ui.dialogs.advanced_search_dialog import AdvancedSearchDialog
from smartvault.services.search_service import SearchService
from smartvault.services.file import FileService
from smartvault.services.tag_service import TagService


def test_b009_advanced_search():
    """测试B009高级搜索功能"""
    print("🚀 测试B009高级搜索功能")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    try:
        # 创建QApplication实例
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        # 创建服务实例
        search_service = SearchService()
        file_service = FileService()
        tag_service = TagService()

        print("\n🧪 测试1: SearchService扩展功能")

        # 测试基本搜索
        basic_results = search_service.search_files("test")
        print(f"   ✅ 基本搜索: 找到 {len(basic_results)} 个结果")

        # 测试文件类型过滤
        type_filters = {"file_types": "*.txt,*.pdf"}
        type_results = search_service.search_files("", type_filters)
        print(f"   ✅ 文件类型过滤: 找到 {len(type_results)} 个结果")

        # 测试文件大小过滤
        size_filters = {"min_size": 1024, "max_size": 1024*1024}  # 1KB - 1MB
        size_results = search_service.search_files("", size_filters)
        print(f"   ✅ 文件大小过滤: 找到 {len(size_results)} 个结果")

        # 测试日期范围过滤
        date_from = (datetime.now() - timedelta(days=7)).isoformat()
        date_to = datetime.now().isoformat()
        date_filters = {"date_from": date_from, "date_to": date_to}
        date_results = search_service.search_files("", date_filters)
        print(f"   ✅ 日期范围过滤: 找到 {len(date_results)} 个结果")

        # 测试入库方式过滤
        entry_filters = {"entry_type": "link"}
        entry_results = search_service.search_files("", entry_filters)
        print(f"   ✅ 入库方式过滤: 找到 {len(entry_results)} 个结果")

        print("\n🧪 测试2: 高级搜索对话框UI")

        # 创建高级搜索对话框
        dialog = AdvancedSearchDialog()

        # 验证UI组件存在
        assert hasattr(dialog, 'name_edit')
        assert hasattr(dialog, 'type_combo')
        assert hasattr(dialog, 'size_min_spin')
        assert hasattr(dialog, 'size_max_spin')
        assert hasattr(dialog, 'date_from')
        assert hasattr(dialog, 'date_to')
        assert hasattr(dialog, 'entry_combo')
        assert hasattr(dialog, 'tag_combo')
        assert hasattr(dialog, 'result_table')
        print("   ✅ UI组件创建成功")

        # 测试清除功能
        dialog.name_edit.setText("test")
        dialog.clear_conditions()
        assert dialog.name_edit.text() == ""
        print("   ✅ 清除功能正常")

        print("\n🧪 测试3: 搜索条件构建")

        # 设置搜索条件
        dialog.name_edit.setText("*.txt")
        dialog.type_combo.setCurrentText("文档 (*.doc, *.pdf, *.txt)")
        dialog.size_min_spin.setValue(10)  # 10KB
        dialog.size_max_spin.setValue(1000)  # 1000KB
        dialog.entry_combo.setCurrentText("链接")

        # 构建搜索过滤条件
        filters = dialog.build_search_filters()

        # 验证过滤条件
        assert "name" in filters
        assert "file_types" in filters
        assert "min_size" in filters
        assert "max_size" in filters
        assert "entry_type" in filters

        print("   ✅ 搜索条件构建正确")
        print(f"   📋 过滤条件: {filters}")

        print("\n🧪 测试4: 搜索预览功能")

        # 执行搜索预览
        dialog.preview_search()

        # 检查结果表格
        row_count = dialog.result_table.rowCount()
        print(f"   ✅ 搜索预览: 显示 {row_count} 个结果")

        # 检查结果计数标签
        count_text = dialog.result_count_label.text()
        print(f"   ✅ 结果计数: {count_text}")

        print("\n🧪 测试5: 复杂搜索条件")

        # 清除之前的条件
        dialog.clear_conditions()

        # 设置复杂搜索条件
        dialog.name_edit.setText("test*")
        dialog.case_sensitive_check.setChecked(True)

        # 设置自定义文件类型
        dialog.type_combo.setCurrentText("自定义...")
        dialog.custom_type_edit.setText("*.txt,*.doc,*.pdf")

        # 设置日期范围（设置为非默认值）
        from PySide6.QtCore import QDate
        dialog.date_from.setDate(QDate.currentDate().addDays(-60))  # 不同于默认的-30天
        dialog.date_to.setDate(QDate.currentDate().addDays(-1))     # 不同于默认的今天

        # 构建复杂过滤条件
        complex_filters = dialog.build_search_filters()

        # 验证复杂条件
        assert complex_filters["case_sensitive"] is True
        assert "*.txt" in complex_filters["file_types"]
        assert "date_from" in complex_filters
        assert "date_to" in complex_filters

        print("   ✅ 复杂搜索条件构建正确")

        # 执行复杂搜索
        complex_results = search_service.search_files("", complex_filters)
        print(f"   ✅ 复杂搜索: 找到 {len(complex_results)} 个结果")

        print("\n🧪 测试6: 标签搜索功能")

        # 获取所有标签
        tags = tag_service.get_all_tags()
        if tags:
            # 选择第一个标签进行测试
            first_tag = tags[0]
            tag_filters = {"tag_id": first_tag["id"]}
            tag_results = search_service.search_files("", tag_filters)
            print(f"   ✅ 标签搜索: 标签'{first_tag['name']}'找到 {len(tag_results)} 个结果")
        else:
            print("   ⚠️ 没有标签可供测试")

        print("\n🧪 测试7: 文件大小格式化")

        # 测试文件大小格式化
        test_sizes = [512, 1024, 1024*1024, 1024*1024*1024]
        for size in test_sizes:
            formatted = dialog.format_size(size)
            print(f"   📏 {size} 字节 -> {formatted}")

        print("   ✅ 文件大小格式化正常")

        print("\n🎉 B009高级搜索功能测试全部通过！")
        print("✅ SearchService扩展功能正常")
        print("✅ 高级搜索对话框UI完整")
        print("✅ 搜索条件构建正确")
        print("✅ 搜索预览功能正常")
        print("✅ 复杂搜索条件支持")
        print("✅ 标签搜索功能正常")
        print("✅ 文件大小格式化正常")

        return True

    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_search_performance():
    """测试搜索性能"""
    print("\n🚀 测试搜索性能...")

    try:
        search_service = SearchService()

        # 测试大量结果的搜索性能
        start_time = datetime.now()

        # 执行一个可能返回大量结果的搜索
        results = search_service.search_files("")  # 搜索所有文件

        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        print(f"   📊 搜索 {len(results)} 个文件耗时: {duration:.3f} 秒")

        if duration < 1.0:
            print("   ✅ 搜索性能良好")
        elif duration < 3.0:
            print("   ⚠️ 搜索性能一般")
        else:
            print("   ❌ 搜索性能较差，需要优化")

        return True

    except Exception as e:
        print(f"   ❌ 性能测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试B009高级搜索功能...")

    success1 = test_b009_advanced_search()
    success2 = test_search_performance()

    if success1 and success2:
        print("\n🎉 所有测试通过！B009高级搜索功能开发完成！")
        return True
    else:
        print("\n❌ 部分测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
