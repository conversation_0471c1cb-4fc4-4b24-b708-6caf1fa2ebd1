import sys
import os
sys.path.append('.')

try:
    from PySide6.QtWidgets import QApplication
    from smartvault.ui.widgets.clipboard_floating_widget import ClipboardFloatingWidget
    
    print("测试文件定位功能修复...")
    
    # 创建Qt应用
    app = QApplication(sys.argv)
    
    # 创建浮动窗口
    widget = ClipboardFloatingWidget()
    
    print("=== 测试文件定位功能 ===")
    
    # 模拟主窗口的文件定位逻辑
    class MockFileView:
        """模拟文件视图容器"""
        def __init__(self):
            self.current_model = MockModel()
            self.current_view = MockView()
        
        def get_current_model(self):
            return self.current_model
        
        def get_current_view(self):
            return self.current_view
    
    class MockModel:
        """模拟文件模型"""
        def __init__(self):
            self.files = [
                {'id': '12345', 'name': '测试文件.txt'},
                {'id': '67890', 'name': '其他文件.pdf'},
                {'id': '5f5c7338', 'name': 'file - 生成图标图像 (5).png'}  # 匹配实际的文件ID
            ]
            self.FileIdRole = 1001
        
        def rowCount(self):
            return len(self.files)
        
        def index(self, row, column):
            return MockIndex(row, column)
        
        def data(self, index, role):
            if role == self.FileIdRole and 0 <= index.row < len(self.files):
                return self.files[index.row]['id']
            return None
    
    class MockView:
        """模拟视图"""
        def __init__(self):
            self.selected_row = None
            self.scrolled_to = None
        
        def selectRow(self, row):
            self.selected_row = row
            print(f"  选中行: {row}")
        
        def scrollTo(self, index):
            self.scrolled_to = index.row
            print(f"  滚动到行: {index.row}")
    
    class MockIndex:
        """模拟索引"""
        def __init__(self, row, column):
            self.row = row
            self.column = column
    
    class MockMainWindow:
        """模拟主窗口"""
        def __init__(self):
            self.file_view = MockFileView()
            self.main_tab_widget = MockTabWidget()
            self.status_message = ""
        
        def show_status_message(self, message, success):
            self.status_message = message
            print(f"  状态消息: {message} ({'成功' if success else '失败'})")
        
        def _locate_and_select_file(self, file_id):
            """文件定位逻辑（从主窗口复制）"""
            try:
                print(f"开始定位文件: {file_id}")
                
                # 确保切换到文件视图
                if hasattr(self, 'main_tab_widget'):
                    # 找到文件视图标签页并切换
                    for i in range(self.main_tab_widget.count()):
                        if self.main_tab_widget.tabText(i) == "文件":
                            self.main_tab_widget.setCurrentIndex(i)
                            print(f"  切换到文件视图标签页")
                            break

                # 在当前文件视图中定位文件
                if hasattr(self, 'file_view') and self.file_view:
                    # 获取当前活动的模型（通过容器）
                    model = self.file_view.get_current_model()
                    current_view = self.file_view.get_current_view()
                    
                    if model and current_view:
                        # 在模型中查找文件
                        for row in range(model.rowCount()):
                            index = model.index(row, 0)
                            current_file_id = model.data(index, model.FileIdRole)
                            if current_file_id == file_id:
                                # 找到文件，选中并滚动到可见位置
                                if hasattr(current_view, 'selectRow'):
                                    # 表格视图
                                    current_view.selectRow(row)
                                    current_view.scrollTo(index)
                                elif hasattr(current_view, 'setCurrentIndex'):
                                    # 网格视图或其他视图
                                    current_view.setCurrentIndex(index)
                                    current_view.scrollTo(index)
                                
                                print(f"✅ 已定位到文件: {file_id}")
                                return

                        # 如果在当前页面没找到，可能需要加载更多数据或搜索
                        print(f"⚠️ 在当前视图中未找到文件: {file_id}")
                        self.show_status_message("文件可能在其他页面，请尝试搜索", False)
                    else:
                        print("⚠️ 无法获取当前视图或模型")
                        self.show_status_message("无法访问文件视图", False)

            except Exception as e:
                print(f"定位文件失败: {e}")
                raise
        
        def on_clipboard_open_file(self, file_id):
            """处理剪贴板浮动窗的打开文件请求"""
            try:
                # 在文件视图中定位并选中文件
                self._locate_and_select_file(file_id)
                self.show_status_message(f"已定位到重复文件", True)
            except Exception as e:
                print(f"定位剪贴板文件失败: {e}")
                self.show_status_message("定位文件失败", False)
    
    class MockTabWidget:
        """模拟标签页组件"""
        def __init__(self):
            self.tabs = ["文件", "标签", "设置"]
            self.current_index = 0
        
        def count(self):
            return len(self.tabs)
        
        def tabText(self, index):
            return self.tabs[index] if 0 <= index < len(self.tabs) else ""
        
        def setCurrentIndex(self, index):
            self.current_index = index
    
    # 创建模拟主窗口
    main_window = MockMainWindow()
    
    # 连接信号
    widget.open_file_requested.connect(main_window.on_clipboard_open_file)
    
    # 测试文件定位功能
    print("\n--- 测试1: 定位存在的文件 ---")
    test_file_id = "5f5c7338"  # 匹配实际的文件ID
    main_window.on_clipboard_open_file(test_file_id)
    
    print("\n--- 测试2: 定位不存在的文件 ---")
    test_file_id = "nonexistent"
    main_window.on_clipboard_open_file(test_file_id)
    
    print("\n--- 测试3: 通过浮动窗口查看按钮 ---")
    duplicate_info = {
        'type': 'file',
        'source_name': 'test.png',
        'duplicates': [{
            'id': '5f5c7338',
            'name': 'file - 生成图标图像 (5).png'
        }]
    }
    
    widget.show_duplicate(duplicate_info)
    print(f"浮动窗口显示: {widget.title_label.text()}")
    print(f"内容: {widget.content_label.text()}")
    print(f"查看按钮可见: {widget.view_button.isVisible()}")
    
    # 模拟点击查看按钮
    print("\n模拟点击查看按钮...")
    widget.on_view_clicked()
    
    print("\n=== 修复总结 ===")
    print("✅ 1. 修复了文件视图容器访问问题")
    print("   - 使用 get_current_model() 获取当前模型")
    print("   - 使用 get_current_view() 获取当前视图")
    print("   - 支持不同类型的视图（表格、网格、详情）")
    print()
    print("✅ 2. 改进了文件选中逻辑")
    print("   - 表格视图使用 selectRow()")
    print("   - 其他视图使用 setCurrentIndex()")
    print("   - 统一使用 scrollTo() 滚动到可见位置")
    print()
    print("✅ 3. 增强了错误处理")
    print("   - 检查模型和视图的有效性")
    print("   - 提供详细的错误信息")
    print("   - 友好的用户反馈")
    
    # 不启动事件循环，只测试功能
    widget.close()
    
except Exception as e:
    print(f"测试失败: {e}")
    import traceback
    traceback.print_exc()
