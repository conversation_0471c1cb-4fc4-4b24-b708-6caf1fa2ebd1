"""
文件网格视图模型
"""

from PySide6.QtCore import QAbstractListModel, Qt, QModelIndex, QSize
from PySide6.QtGui import QIcon, QPixmap, QPainter, QFont, QFontMetrics
from smartvault.ui.resources import get_icon
import os


class FileGridModel(QAbstractListModel):
    """文件网格视图模型"""

    # 自定义角色
    FileIdRole = Qt.UserRole + 1
    FilePathRole = Qt.UserRole + 2
    FileTypeRole = Qt.UserRole + 3
    FileSizeRole = Qt.UserRole + 4

    def __init__(self, parent=None):
        """初始化模型

        Args:
            parent: 父对象
        """
        super().__init__(parent)
        self.files = []
        self.filtered_files = []  # 过滤后的文件列表
        self.visible_files = []   # 当前可见的文件列表

        # 分页相关
        self.page_size = 100      # 每页显示的文件数量
        self.current_page = 0     # 当前页码
        self.total_pages = 0      # 总页数
        self.total_files = 0      # 数据库中的总文件数

        # 过滤相关
        self.filter_text = ""     # 过滤文本
        self.filter_column = 0    # 过滤列

        # 缓存
        self.icon_cache = {}      # 图标缓存
        self.path_exists_cache = {}  # 路径存在缓存

        # 数据加载回调
        self.data_loader_callback = None
        self.search_total_count_callback = None

        # 网格视图特有属性
        self.item_size = QSize(120, 100)  # 网格项大小

        # 标签相关
        self.file_tags_cache = {}  # 文件标签缓存 {file_id: [tag_dict, ...]}
        self.tag_service = None    # 标签服务实例

    def rowCount(self, parent=QModelIndex()):
        """返回行数

        Args:
            parent: 父索引

        Returns:
            int: 行数
        """
        return len(self.visible_files)

    def data(self, index, role=Qt.DisplayRole):
        """获取数据

        Args:
            index: 模型索引
            role: 数据角色

        Returns:
            数据值
        """
        if not index.isValid() or index.row() >= len(self.visible_files):
            return None

        file = self.visible_files[index.row()]

        # 显示角色
        if role == Qt.DisplayRole:
            return file["name"]

        # 装饰角色（图标）
        elif role == Qt.DecorationRole:
            return self._get_file_icon(file)

        # 工具提示
        elif role == Qt.ToolTipRole:
            return self._get_file_tooltip(file)

        # 自定义角色
        elif role == self.FileIdRole:
            return file["id"]
        elif role == self.FilePathRole:
            if file["entry_type"] in ["copy", "move"]:
                return file["library_path"]
            else:
                return file["original_path"]
        elif role == self.FileTypeRole:
            return file.get("file_type", "")
        elif role == self.FileSizeRole:
            return file.get("size", 0)

        return None

    def _get_file_icon(self, file):
        """获取文件图标

        Args:
            file: 文件信息字典

        Returns:
            QIcon: 文件图标
        """
        file_path = file["original_path"] if file["entry_type"] == "link" else file["library_path"]

        # 检查缓存
        if file_path in self.icon_cache:
            return self.icon_cache[file_path]

        # 根据文件扩展名获取图标
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()

        # 常见文件类型图标映射
        icon_map = {
            '.txt': 'text_file',
            '.doc': 'word_file',
            '.docx': 'word_file',
            '.pdf': 'pdf_file',
            '.jpg': 'image_file',
            '.jpeg': 'image_file',
            '.png': 'image_file',
            '.gif': 'image_file',
            '.mp3': 'audio_file',
            '.mp4': 'video_file',
            '.avi': 'video_file',
            '.zip': 'archive_file',
            '.rar': 'archive_file',
        }

        icon_name = icon_map.get(ext, 'default_file')
        icon = get_icon(icon_name)

        # 缓存图标
        self.icon_cache[file_path] = icon

        return icon

    def _get_file_tooltip(self, file):
        """获取文件工具提示

        Args:
            file: 文件信息字典

        Returns:
            str: 工具提示文本
        """
        tooltip_parts = [
            f"名称: {file['name']}",
            f"类型: {file.get('file_type', '未知')}",
            f"大小: {self._format_file_size(file.get('size', 0))}",
        ]

        # 添加位置信息
        staging_status = file.get("staging_status", "normal")
        if staging_status == "staging":
            tooltip_parts.append("位置: 中转")
        elif file["entry_type"] == "link":
            tooltip_parts.append("位置: 库外")
            tooltip_parts.append(f"原始路径: {file['original_path']}")
        else:
            tooltip_parts.append("位置: 库内")
            tooltip_parts.append(f"库路径: {file['library_path']}")

        return "\n".join(tooltip_parts)

    def _format_file_size(self, size):
        """格式化文件大小

        Args:
            size: 文件大小（字节）

        Returns:
            str: 格式化后的大小
        """
        if size < 1024:
            return f"{size} B"
        elif size < 1024 * 1024:
            return f"{size / 1024:.1f} KB"
        elif size < 1024 * 1024 * 1024:
            return f"{size / (1024 * 1024):.1f} MB"
        else:
            return f"{size / (1024 * 1024 * 1024):.1f} GB"

    def setFiles(self, files):
        """设置文件列表 - 完全模仿表格视图模型

        Args:
            files: 文件信息字典列表（已经是当前页的数据）
        """
        # 清除缓存
        self.icon_cache = {}
        self.path_exists_cache = {}

        # 保存文件列表
        self.beginResetModel()
        self.files = files

        # 在数据库分页模式下，直接设置为当前页数据
        self.filtered_files = files.copy()
        self.visible_files = files.copy()

        # 计算总页数（优先使用数据库中的总文件数）
        if hasattr(self, 'total_files') and self.total_files > 0:
            if self.page_size >= 999999:  # "全部"选项
                self.total_pages = 1
            else:
                self.total_pages = (self.total_files + self.page_size - 1) // self.page_size
        else:
            self.total_pages = (len(self.filtered_files) + self.page_size - 1) // self.page_size if self.page_size > 0 else 1

        # 确保当前页码有效
        if self.current_page >= self.total_pages:
            self.current_page = max(0, self.total_pages - 1)

        self.endResetModel()

    def setFilesForTagFilter(self, files):
        """为标签筛选设置文件列表（不触发数据库重新加载）

        Args:
            files: 文件信息字典列表
        """
        # 清除缓存
        self.icon_cache = {}
        self.path_exists_cache = {}

        # 保存文件列表
        self.beginResetModel()
        self.files = files
        self.filtered_files = files.copy()
        self.visible_files = files.copy()

        # 🔧 修复：不要重置total_files，保持主窗口设置的值
        # 这样分页信息就能正确显示标签筛选的总文件数
        # self.total_files = len(files)  # 注释掉这行

        # 重新计算总页数（基于已设置的total_files）
        if hasattr(self, 'total_files') and self.total_files > 0:
            if self.page_size >= 999999:  # "全部"选项
                self.total_pages = 1
            else:
                self.total_pages = (self.total_files + self.page_size - 1) // self.page_size
        else:
            self.total_pages = 1

        self.current_page = 0

        self.endResetModel()

    def addFile(self, file):
        """添加文件

        Args:
            file: 文件信息字典
        """
        self.beginInsertRows(QModelIndex(), len(self.visible_files), len(self.visible_files))
        self.files.append(file)
        self.filtered_files.append(file)
        self.visible_files.append(file)
        self.endInsertRows()

    def removeFile(self, file_id):
        """移除文件

        Args:
            file_id: 文件ID

        Returns:
            bool: 是否成功移除
        """
        removed = False

        # 从所有文件列表中移除
        self.files = [f for f in self.files if f["id"] != file_id]
        self.filtered_files = [f for f in self.filtered_files if f["id"] != file_id]

        # 从可见文件列表中移除并通知视图
        for i, file in enumerate(self.visible_files):
            if file["id"] == file_id:
                self.beginRemoveRows(QModelIndex(), i, i)
                self.visible_files.pop(i)
                self.endRemoveRows()
                removed = True
                break

        return removed

    def clear_all_state(self):
        """清除所有状态"""
        self.beginResetModel()
        self.files.clear()
        self.filtered_files.clear()
        self.visible_files.clear()
        self.current_page = 0
        self.total_pages = 0
        self.total_files = 0
        self.filter_text = ""
        self.filter_column = 0
        self.icon_cache.clear()
        self.path_exists_cache.clear()
        self.file_tags_cache.clear()
        self.endResetModel()

    def set_tag_service(self, tag_service):
        """设置标签服务

        Args:
            tag_service: TagService实例
        """
        self.tag_service = tag_service

    def applyFilter(self, filter_text, filter_column=-1):
        """应用过滤器 - 完全模仿表格视图模型

        Args:
            filter_text: 过滤文本
            filter_column: 过滤列（-1表示全部列）
        """
        self.filter_text = filter_text
        self.filter_column = filter_column

        # 数据库分页模式：重置到第一页并重新加载数据
        self.current_page = 0
        self._reload_current_page_data()

    def updateVisibleFiles(self):
        """更新可见文件列表 - 简化版本，仅用于兼容性"""
        # 在数据库分页模式下，visible_files就是当前加载的文件
        self.visible_files = self.files.copy()

        # 通知视图数据已更改
        self.beginResetModel()
        self.endResetModel()

    def setPageSize(self, page_size):
        """设置页面大小

        Args:
            page_size: 页面大小
        """
        old_page_size = self.page_size
        self.page_size = page_size

        # 确保当前页码有效
        total_pages = self.getTotalPages()
        if self.current_page >= total_pages:
            self.current_page = max(0, total_pages - 1)

        # 如果页面大小改变了，需要重新加载数据
        if old_page_size != page_size and self.data_loader_callback:
            self._reload_current_page_data()
        else:
            self.updateVisibleFiles()

    def setCurrentPage(self, page):
        """设置当前页

        Args:
            page: 页码
        """
        self.current_page = page
        if self.data_loader_callback:
            self._reload_current_page_data()
        else:
            self.updateVisibleFiles()

    def set_data_loader_callback(self, callback):
        """设置数据加载回调

        Args:
            callback: 回调函数
        """
        self.data_loader_callback = callback

    def set_search_total_count_callback(self, callback):
        """设置搜索总数回调

        Args:
            callback: 回调函数
        """
        self.search_total_count_callback = callback

    def set_total_files(self, total_files):
        """设置数据库中的总文件数

        Args:
            total_files: 总文件数
        """
        self.total_files = total_files
        # 重新计算总页数
        if self.page_size >= 999999:  # "全部"选项
            self.total_pages = 1
        else:
            self.total_pages = (total_files + self.page_size - 1) // self.page_size if total_files > 0 else 1

    def getCurrentPage(self):
        """获取当前页码

        Returns:
            int: 当前页码
        """
        return self.current_page

    def getTotalPages(self):
        """获取总页数

        Returns:
            int: 总页数
        """
        if self.page_size <= 0:
            return 1

        # 优先使用数据库中的总文件数
        if hasattr(self, 'total_files') and self.total_files > 0:
            if self.page_size >= 999999:  # "全部"选项
                return 1
            else:
                return max(1, (self.total_files + self.page_size - 1) // self.page_size)
        else:
            # 回退到使用过滤后的文件数
            return max(1, (len(self.filtered_files) + self.page_size - 1) // self.page_size)

    def goToPage(self, page):
        """跳转到指定页

        Args:
            page: 页码
        """
        total_pages = self.getTotalPages()
        if 0 <= page < total_pages:
            self.current_page = page
            self._reload_current_page_data()

    def nextPage(self):
        """下一页"""
        if self.current_page < self.getTotalPages() - 1:
            self.current_page += 1
            self._reload_current_page_data()

    def previousPage(self):
        """上一页"""
        if self.current_page > 0:
            self.current_page -= 1
            self._reload_current_page_data()

    def _reload_current_page_data(self):
        """重新加载当前页数据 - 完全模仿表格视图模型"""
        if self.data_loader_callback:
            try:
                offset = self.current_page * self.page_size
                print(f"网格视图重新加载第 {self.current_page + 1} 页数据，offset={offset}, limit={self.page_size}")

                # 获取搜索条件
                search_keyword = self.filter_text if self.filter_text else None
                search_column = self.filter_column if search_keyword else None

                # 从数据库加载当前页的数据（支持搜索条件）
                new_files = self.data_loader_callback(
                    self.page_size,
                    offset,
                    search_keyword=search_keyword,
                    search_column=search_column
                )

                # 直接设置为当前页数据，不使用内存分页逻辑
                self.beginResetModel()
                self.files = new_files
                self.filtered_files = new_files.copy()
                self.visible_files = new_files.copy()  # 直接设置可见文件
                self.endResetModel()

                print(f"网格视图成功加载 {len(new_files)} 个文件")
            except Exception as e:
                print(f"网格视图重新加载数据失败: {e}")
        else:
            # 回退到内存分页（兼容性保留）
            self.updateVisibleFiles()
