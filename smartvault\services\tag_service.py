"""
标签服务模块
"""

import uuid
from datetime import datetime
from smartvault.data.database import Database


class TagService:
    """标签服务类"""

    def __init__(self, db=None):
        """初始化标签服务

        Args:
            db: 数据库实例，用于测试注入，默认为None
        """
        self._db = db

    @property
    def db(self):
        """获取数据库实例，支持延迟初始化和测试注入"""
        if self._db is None:
            self._db = Database.create_from_config()
        return self._db

    def create_tag(self, name, color=None, parent_id=None, weight=5):
        """创建标签

        Args:
            name: 标签名称
            color: 标签颜色
            parent_id: 父标签ID
            weight: 标签权重 (1-10, 默认5)

        Returns:
            str: 标签ID
        """
        # 检查标签是否已存在
        cursor = self.db.conn.cursor()
        cursor.execute("SELECT * FROM tags WHERE name = ?", (name,))
        existing_tag = cursor.fetchone()

        if existing_tag:
            return dict(existing_tag)["id"]

        # 创建标签
        tag_id = str(uuid.uuid4())
        cursor.execute(
            """
            INSERT INTO tags (id, name, color, parent_id, weight, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
            """,
            (
                tag_id,
                name,
                color,
                parent_id,
                weight,
                datetime.now().isoformat()
            )
        )
        self.db.conn.commit()

        return tag_id

    def get_tag_by_id(self, tag_id):
        """根据ID获取标签

        Args:
            tag_id: 标签ID

        Returns:
            dict: 标签信息字典，如果标签不存在则返回None
        """
        cursor = self.db.conn.cursor()
        cursor.execute("SELECT * FROM tags WHERE id = ?", (tag_id,))
        row = cursor.fetchone()

        if row:
            return dict(row)

        return None

    def get_tags(self, parent_id=None):
        """获取标签列表

        Args:
            parent_id: 父标签ID，如果为None则获取所有顶级标签

        Returns:
            list: 标签信息字典列表
        """
        cursor = self.db.conn.cursor()

        if parent_id is None:
            cursor.execute(
                "SELECT * FROM tags WHERE parent_id IS NULL ORDER BY name"
            )
        else:
            cursor.execute(
                "SELECT * FROM tags WHERE parent_id = ? ORDER BY name",
                (parent_id,)
            )

        return [dict(row) for row in cursor.fetchall()]

    def add_tag_to_file(self, file_id, tag_id, note=None):
        """为文件添加标签

        Args:
            file_id: 文件ID
            tag_id: 标签ID
            note: 备注信息（可选）

        Returns:
            bool: 是否成功

        Raises:
            ValueError: 文件或标签不存在
        """
        # 检查文件是否存在
        cursor = self.db.conn.cursor()
        cursor.execute("SELECT * FROM files WHERE id = ?", (file_id,))
        file = cursor.fetchone()

        if not file:
            raise ValueError(f"文件不存在: {file_id}")

        # 检查标签是否存在
        cursor.execute("SELECT * FROM tags WHERE id = ?", (tag_id,))
        tag = cursor.fetchone()

        if not tag:
            raise ValueError(f"标签不存在: {tag_id}")

        # 检查关联是否已存在
        cursor.execute(
            "SELECT * FROM file_tags WHERE file_id = ? AND tag_id = ?",
            (file_id, tag_id)
        )
        existing = cursor.fetchone()

        if existing:
            # 如果关联已存在且提供了备注，更新备注
            if note is not None:
                cursor.execute(
                    "UPDATE file_tags SET note = ? WHERE file_id = ? AND tag_id = ?",
                    (note, file_id, tag_id)
                )
                self.db.conn.commit()
            return True

        # 添加关联
        cursor.execute(
            """
            INSERT INTO file_tags (file_id, tag_id, added_at, note)
            VALUES (?, ?, ?, ?)
            """,
            (
                file_id,
                tag_id,
                datetime.now().isoformat(),
                note
            )
        )
        self.db.conn.commit()

        return True

    def remove_tag_from_file(self, file_id, tag_id):
        """从文件移除标签

        Args:
            file_id: 文件ID
            tag_id: 标签ID

        Returns:
            bool: 是否成功
        """
        cursor = self.db.conn.cursor()
        cursor.execute(
            "DELETE FROM file_tags WHERE file_id = ? AND tag_id = ?",
            (file_id, tag_id)
        )
        self.db.conn.commit()

        return cursor.rowcount > 0

    def get_file_tags(self, file_id):
        """获取文件的标签

        Args:
            file_id: 文件ID

        Returns:
            list: 标签信息字典列表，包含备注信息
        """
        cursor = self.db.conn.cursor()
        cursor.execute(
            """
            SELECT t.*, ft.note, ft.added_at as tag_added_at
            FROM tags t
            JOIN file_tags ft ON t.id = ft.tag_id
            WHERE ft.file_id = ?
            ORDER BY t.name
            """,
            (file_id,)
        )

        return [dict(row) for row in cursor.fetchall()]

    def update_tag_note(self, file_id, tag_id, note):
        """更新文件标签的备注

        Args:
            file_id: 文件ID
            tag_id: 标签ID
            note: 备注内容

        Returns:
            bool: 是否成功
        """
        cursor = self.db.conn.cursor()
        cursor.execute(
            "UPDATE file_tags SET note = ? WHERE file_id = ? AND tag_id = ?",
            (note, file_id, tag_id)
        )
        self.db.conn.commit()
        return cursor.rowcount > 0

    def get_tag_note(self, file_id, tag_id):
        """获取文件标签的备注

        Args:
            file_id: 文件ID
            tag_id: 标签ID

        Returns:
            str: 备注内容，如果没有则返回空字符串
        """
        cursor = self.db.conn.cursor()
        cursor.execute(
            "SELECT note FROM file_tags WHERE file_id = ? AND tag_id = ?",
            (file_id, tag_id)
        )
        result = cursor.fetchone()
        return result['note'] if result and result['note'] else ""

    def get_files_tags_batch(self, file_ids):
        """批量获取多个文件的标签

        Args:
            file_ids: 文件ID列表

        Returns:
            dict: {file_id: [tag_dict, ...]} 格式的字典
        """
        if not file_ids:
            return {}

        cursor = self.db.conn.cursor()

        # 构建IN查询的占位符
        placeholders = ",".join(["?" for _ in file_ids])

        cursor.execute(
            f"""
            SELECT ft.file_id, t.id, t.name, t.color, t.parent_id
            FROM file_tags ft
            JOIN tags t ON ft.tag_id = t.id
            WHERE ft.file_id IN ({placeholders})
            ORDER BY ft.file_id, t.name
            """,
            file_ids
        )

        # 组织结果
        result = {file_id: [] for file_id in file_ids}
        for row in cursor.fetchall():
            row_dict = dict(row)
            file_id = row_dict.pop('file_id')
            result[file_id].append(row_dict)

        return result

    def get_files_by_tag(self, tag_id):
        """获取带有指定标签的文件

        Args:
            tag_id: 标签ID

        Returns:
            list: 文件信息字典列表
        """
        cursor = self.db.conn.cursor()
        cursor.execute(
            """
            SELECT f.* FROM files f
            JOIN file_tags ft ON f.id = ft.file_id
            WHERE ft.tag_id = ?
            ORDER BY f.name
            """,
            (tag_id,)
        )

        return [dict(row) for row in cursor.fetchall()]

    def get_child_tags(self, parent_id):
        """获取子标签列表

        Args:
            parent_id: 父标签ID

        Returns:
            list: 子标签信息字典列表
        """
        cursor = self.db.conn.cursor()
        cursor.execute(
            "SELECT * FROM tags WHERE parent_id = ? ORDER BY name",
            (parent_id,)
        )

        return [dict(row) for row in cursor.fetchall()]

    def delete_tag(self, tag_id):
        """删除标签

        Args:
            tag_id: 标签ID

        Returns:
            bool: 是否成功删除
        """
        cursor = self.db.conn.cursor()

        try:
            # 先删除所有文件关联
            cursor.execute("DELETE FROM file_tags WHERE tag_id = ?", (tag_id,))

            # 删除标签本身
            cursor.execute("DELETE FROM tags WHERE id = ?", (tag_id,))

            self.db.conn.commit()

            return cursor.rowcount > 0

        except Exception as e:
            self.db.conn.rollback()
            print(f"删除标签失败: {e}")
            return False

    def get_all_tags(self):
        """获取所有标签

        Returns:
            list: 所有标签信息字典列表
        """
        cursor = self.db.conn.cursor()
        cursor.execute("SELECT * FROM tags ORDER BY created_at DESC")

        return [dict(row) for row in cursor.fetchall()]

    def get_tag_statistics(self):
        """获取标签使用统计

        Returns:
            list: 标签统计信息列表，包含tag_id, name, file_count
        """
        cursor = self.db.conn.cursor()
        cursor.execute(
            """
            SELECT t.id as tag_id, t.name, t.color,
                   COUNT(ft.file_id) as file_count
            FROM tags t
            LEFT JOIN file_tags ft ON t.id = ft.tag_id
            GROUP BY t.id, t.name, t.color
            ORDER BY file_count DESC, t.name
            """
        )

        return [dict(row) for row in cursor.fetchall()]

    def get_tag_tree(self):
        """获取标签树结构

        Returns:
            list: 标签树结构，包含层级关系和文件统计
        """
        # 获取所有标签
        all_tags = self.get_all_tags()

        # 获取标签统计
        tag_stats = self.get_tag_statistics()
        stats_dict = {stat["tag_id"]: stat for stat in tag_stats}

        # 构建标签字典
        tag_dict = {}
        for tag in all_tags:
            tag_id = tag["id"]
            tag_info = {
                "id": tag_id,
                "name": tag["name"],
                "color": tag.get("color"),
                "parent_id": tag.get("parent_id"),
                "children": [],
                "file_count": stats_dict.get(tag_id, {}).get("file_count", 0),
                "total_file_count": 0  # 将在后面计算
            }
            tag_dict[tag_id] = tag_info

        # 构建树结构
        root_tags = []
        for tag_id, tag_info in tag_dict.items():
            parent_id = tag_info["parent_id"]
            if parent_id and parent_id in tag_dict:
                # 添加到父标签的children中
                tag_dict[parent_id]["children"].append(tag_info)
            else:
                # 根标签
                root_tags.append(tag_info)

        # 计算总文件数（包含子标签）
        def calculate_total_file_count(tag_info):
            total = tag_info["file_count"]
            for child in tag_info["children"]:
                total += calculate_total_file_count(child)
            tag_info["total_file_count"] = total
            return total

        for root_tag in root_tags:
            calculate_total_file_count(root_tag)

        return root_tags

    def get_files_by_tag_hierarchy(self, tag_id, limit=None, offset=0):
        """按标签层级获取文件（包含子标签的文件）- 高性能版本

        Args:
            tag_id: 标签ID
            limit: 返回的最大文件数，None表示返回所有
            offset: 起始偏移量

        Returns:
            list: 文件信息字典列表
        """
        cursor = self.db.conn.cursor()

        # 🔧 性能优化：首先检查是否有子标签
        cursor.execute("SELECT COUNT(*) FROM tags WHERE parent_id = ?", (tag_id,))
        has_children = cursor.fetchone()[0] > 0

        if not has_children:
            # 🔧 简单情况：没有子标签，直接查询（使用优化索引）
            sql = """
                SELECT f.* FROM files f
                JOIN file_tags ft ON f.id = ft.file_id
                WHERE ft.tag_id = ?
                ORDER BY f.name
            """
            params = [tag_id]
        else:
            # 🔧 复杂情况：有子标签，使用优化的递归查询
            # 先获取所有子标签ID（一次性查询，避免递归）
            all_tag_ids = self._get_all_descendant_tag_ids(tag_id)

            # 使用IN查询（已有优化索引）
            placeholders = ",".join(["?" for _ in all_tag_ids])
            sql = f"""
                SELECT DISTINCT f.* FROM files f
                JOIN file_tags ft ON f.id = ft.file_id
                WHERE ft.tag_id IN ({placeholders})
                ORDER BY f.name
            """
            params = all_tag_ids

        # 添加分页支持
        if limit is not None:
            sql += " LIMIT ? OFFSET ?"
            params.extend([limit, offset])

        cursor.execute(sql, params)
        return [dict(row) for row in cursor.fetchall()]

    def _get_all_descendant_tag_ids(self, tag_id):
        """获取标签的所有后代标签ID（包括自己）- 优化版本

        Args:
            tag_id: 标签ID

        Returns:
            list: 所有后代标签ID列表
        """
        cursor = self.db.conn.cursor()

        # 🔧 使用CTE递归查询，一次性获取所有后代标签
        cursor.execute("""
            WITH RECURSIVE tag_descendants AS (
                -- 基础情况：当前标签
                SELECT id FROM tags WHERE id = ?

                UNION ALL

                -- 递归情况：子标签
                SELECT t.id FROM tags t
                JOIN tag_descendants td ON t.parent_id = td.id
            )
            SELECT id FROM tag_descendants
        """, (tag_id,))

        return [row[0] for row in cursor.fetchall()]

    def get_files_count_by_tag_hierarchy(self, tag_id):
        """获取标签层级文件总数

        Args:
            tag_id: 标签ID

        Returns:
            int: 文件总数
        """
        # 获取当前标签的所有子标签ID
        def get_all_child_tag_ids(parent_id):
            child_ids = [parent_id]  # 包含自己
            children = self.get_child_tags(parent_id)
            for child in children:
                child_ids.extend(get_all_child_tag_ids(child["id"]))
            return child_ids

        all_tag_ids = get_all_child_tag_ids(tag_id)

        # 构建SQL查询
        cursor = self.db.conn.cursor()
        placeholders = ",".join(["?" for _ in all_tag_ids])

        cursor.execute(
            f"""
            SELECT COUNT(DISTINCT f.id) FROM files f
            JOIN file_tags ft ON f.id = ft.file_id
            WHERE ft.tag_id IN ({placeholders})
            """,
            all_tag_ids
        )

        return cursor.fetchone()[0]

    def update_tag(self, tag_id, name=None, color=None, parent_id=None, weight=None):
        """更新标签信息

        Args:
            tag_id: 标签ID
            name: 新的标签名称
            color: 新的标签颜色
            parent_id: 新的父标签ID
            weight: 新的标签权重

        Returns:
            bool: 更新是否成功
        """
        try:
            cursor = self.db.conn.cursor()

            # 构建更新语句
            update_fields = []
            update_values = []

            if name is not None:
                update_fields.append("name = ?")
                update_values.append(name)

            if color is not None:
                update_fields.append("color = ?")
                update_values.append(color)

            if parent_id is not None:
                # 检查是否会造成循环依赖
                if not self.can_move_tag(tag_id, parent_id):
                    return False
                update_fields.append("parent_id = ?")
                update_values.append(parent_id)

            if weight is not None:
                # 验证权重范围
                if not (1 <= weight <= 10):
                    return False
                update_fields.append("weight = ?")
                update_values.append(weight)

            if not update_fields:
                return True  # 没有需要更新的字段

            # 执行更新
            update_values.append(tag_id)
            sql = f"UPDATE tags SET {', '.join(update_fields)} WHERE id = ?"
            cursor.execute(sql, update_values)
            self.db.conn.commit()

            return cursor.rowcount > 0

        except Exception as e:
            print(f"更新标签失败: {e}")
            return False

    def move_tag(self, tag_id, new_parent_id):
        """移动标签到新父级

        Args:
            tag_id: 要移动的标签ID
            new_parent_id: 新父标签ID，None表示移动到顶级

        Returns:
            bool: 移动是否成功
        """
        return self.update_tag(tag_id, parent_id=new_parent_id)

    def can_move_tag(self, tag_id, new_parent_id):
        """检查是否可以移动标签（防止循环依赖）

        Args:
            tag_id: 要移动的标签ID
            new_parent_id: 新父标签ID

        Returns:
            bool: 是否可以移动
        """
        if new_parent_id is None:
            return True  # 移动到顶级总是允许的

        if tag_id == new_parent_id:
            return False  # 不能移动到自己

        # 检查new_parent_id是否是tag_id的子孙标签
        def is_descendant(parent_id, child_id):
            if parent_id == child_id:
                return True

            children = self.get_child_tags(parent_id)
            for child in children:
                if is_descendant(child['id'], child_id):
                    return True
            return False

        return not is_descendant(tag_id, new_parent_id)

    def delete_tag_cascade(self, tag_id, delete_children=False):
        """级联删除标签

        Args:
            tag_id: 要删除的标签ID
            delete_children: 是否同时删除子标签

        Returns:
            bool: 删除是否成功
        """
        try:
            cursor = self.db.conn.cursor()

            if delete_children:
                # 递归删除所有子标签
                child_tags = self.get_child_tags(tag_id)
                for child_tag in child_tags:
                    self.delete_tag_cascade(child_tag['id'], delete_children=True)
            else:
                # 将子标签的parent_id设为None（变成顶级标签）
                cursor.execute(
                    "UPDATE tags SET parent_id = NULL WHERE parent_id = ?",
                    (tag_id,)
                )

            # 删除标签与文件的关联
            cursor.execute("DELETE FROM file_tags WHERE tag_id = ?", (tag_id,))

            # 删除标签本身
            cursor.execute("DELETE FROM tags WHERE id = ?", (tag_id,))

            self.db.conn.commit()
            return cursor.rowcount > 0

        except Exception as e:
            print(f"删除标签失败: {e}")
            return False

    # ==================== B021: 标签权重系统 ====================

    def get_tags_sorted_by_weight(self, parent_id=None):
        """按权重排序获取标签列表

        Args:
            parent_id: 父标签ID，如果为None则获取所有顶级标签

        Returns:
            list: 按权重降序排列的标签信息字典列表
        """
        cursor = self.db.conn.cursor()

        if parent_id is None:
            cursor.execute(
                "SELECT * FROM tags WHERE parent_id IS NULL ORDER BY weight DESC, name"
            )
        else:
            cursor.execute(
                "SELECT * FROM tags WHERE parent_id = ? ORDER BY weight DESC, name",
                (parent_id,)
            )

        return [dict(row) for row in cursor.fetchall()]

    def update_tag_weight(self, tag_id, weight):
        """更新标签权重

        Args:
            tag_id: 标签ID
            weight: 新权重值 (1-10)

        Returns:
            bool: 更新是否成功
        """
        if not (1 <= weight <= 10):
            return False

        try:
            cursor = self.db.conn.cursor()
            cursor.execute(
                "UPDATE tags SET weight = ? WHERE id = ?",
                (weight, tag_id)
            )
            self.db.conn.commit()
            return cursor.rowcount > 0
        except Exception as e:
            print(f"更新标签权重失败: {e}")
            return False

    # ==================== B021: 标签关联关系系统 ====================

    def add_tag_relation(self, tag1_id, tag2_id, relation_type="related", strength=0.5):
        """添加标签关联关系

        Args:
            tag1_id: 第一个标签ID
            tag2_id: 第二个标签ID
            relation_type: 关联类型 (related, similar, opposite等)
            strength: 关联强度 (0.0-1.0)

        Returns:
            str: 关联关系ID，失败返回None
        """
        if tag1_id == tag2_id:
            return None  # 不能关联自己

        try:
            cursor = self.db.conn.cursor()
            relation_id = str(uuid.uuid4())

            cursor.execute(
                """
                INSERT OR REPLACE INTO tag_relations
                (id, tag1_id, tag2_id, relation_type, strength, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
                """,
                (
                    relation_id,
                    tag1_id,
                    tag2_id,
                    relation_type,
                    strength,
                    datetime.now().isoformat()
                )
            )

            # 添加反向关联（双向关系）
            reverse_relation_id = str(uuid.uuid4())
            cursor.execute(
                """
                INSERT OR REPLACE INTO tag_relations
                (id, tag1_id, tag2_id, relation_type, strength, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
                """,
                (
                    reverse_relation_id,
                    tag2_id,
                    tag1_id,
                    relation_type,
                    strength,
                    datetime.now().isoformat()
                )
            )

            self.db.conn.commit()
            return relation_id

        except Exception as e:
            print(f"添加标签关联失败: {e}")
            return None

    def get_related_tags(self, tag_id, limit=10):
        """获取相关标签

        Args:
            tag_id: 标签ID
            limit: 返回数量限制

        Returns:
            list: 相关标签列表，按关联强度降序排列
        """
        cursor = self.db.conn.cursor()
        cursor.execute(
            """
            SELECT t.*, tr.strength, tr.relation_type
            FROM tags t
            JOIN tag_relations tr ON t.id = tr.tag2_id
            WHERE tr.tag1_id = ?
            ORDER BY tr.strength DESC, t.name
            LIMIT ?
            """,
            (tag_id, limit)
        )

        return [dict(row) for row in cursor.fetchall()]

    def get_tag_recommendations(self, tag_id, limit=5):
        """获取标签推荐

        Args:
            tag_id: 基础标签ID
            limit: 推荐数量限制

        Returns:
            list: 推荐标签列表
        """
        # 基于关联关系和使用频率的推荐算法
        cursor = self.db.conn.cursor()

        # 获取与当前标签相关的标签，以及它们的使用频率
        cursor.execute(
            """
            SELECT t.*, tr.strength,
                   COUNT(ft.file_id) as usage_count,
                   (tr.strength * 0.7 + (COUNT(ft.file_id) * 0.1)) as recommendation_score
            FROM tags t
            JOIN tag_relations tr ON t.id = tr.tag2_id
            LEFT JOIN file_tags ft ON t.id = ft.tag_id
            WHERE tr.tag1_id = ? AND t.id != ?
            GROUP BY t.id
            ORDER BY recommendation_score DESC, t.name
            LIMIT ?
            """,
            (tag_id, tag_id, limit)
        )

        return [dict(row) for row in cursor.fetchall()]

    # ==================== B021: 属性继承机制 ====================

    def get_inherited_attributes(self, tag_id):
        """获取标签的继承属性

        Args:
            tag_id: 标签ID

        Returns:
            dict: 继承的属性字典
        """
        tag = self.get_tag_by_id(tag_id)
        if not tag:
            return {}

        inherited_attrs = {
            "color": tag.get("color"),
            "weight": tag.get("weight", 5)
        }

        # 如果当前标签没有设置颜色或权重，从父标签继承
        if not inherited_attrs["color"] or inherited_attrs["weight"] == 5:
            parent_id = tag.get("parent_id")
            if parent_id:
                parent_attrs = self.get_inherited_attributes(parent_id)
                if not inherited_attrs["color"]:
                    inherited_attrs["color"] = parent_attrs.get("color")
                if inherited_attrs["weight"] == 5:
                    inherited_attrs["weight"] = parent_attrs.get("weight", 5)

        return inherited_attrs

    # ==================== 文件夹导航功能扩展 ====================

    def get_or_create_folder_category(self):
        """获取或创建"自定义文件夹"分类

        Returns:
            str: 分类标签ID
        """
        category = self.get_tag_by_name("自定义文件夹")
        if not category:
            category_id = self.create_tag(
                name="自定义文件夹",
                color="#4CAF50",  # 绿色
                weight=8
            )
            return category_id
        return category['id']

    def get_or_create_device_category(self):
        """获取或创建"移动设备"分类

        Returns:
            str: 分类标签ID
        """
        category = self.get_tag_by_name("移动设备")
        if not category:
            category_id = self.create_tag(
                name="移动设备",
                color="#FF9800",  # 橙色
                weight=7
            )
            return category_id
        return category['id']

    def create_folder_tag(self, folder_name):
        """创建文件夹标签 - 复用现有标签系统

        Args:
            folder_name: 文件夹名称

        Returns:
            str: 标签ID
        """
        folder_category_id = self.get_or_create_folder_category()
        return self.create_tag(
            name=f"📁{folder_name}",
            parent_id=folder_category_id,
            color="#4CAF50",  # 继承绿色
            weight=6
        )

    def create_device_folder_tag(self, device_name):
        """创建设备文件夹标签

        Args:
            device_name: 设备名称

        Returns:
            str: 标签ID
        """
        device_category_id = self.get_or_create_device_category()
        return self.create_tag(
            name=f"💾{device_name}",
            parent_id=device_category_id,
            color="#FF9800",  # 继承橙色
            weight=6
        )

    def get_folder_tags(self):
        """获取所有文件夹标签

        Returns:
            list: 文件夹标签列表
        """
        try:
            folder_category = self.get_tag_by_name("自定义文件夹")
            if not folder_category:
                return []

            return self.get_tags(parent_id=folder_category['id'])
        except Exception as e:
            print(f"获取文件夹标签失败: {e}")
            return []

    def get_device_tags(self):
        """获取所有设备标签

        Returns:
            list: 设备标签列表
        """
        try:
            device_category = self.get_tag_by_name("移动设备")
            if not device_category:
                return []

            return self.get_tags(parent_id=device_category['id'])
        except Exception as e:
            print(f"获取设备标签失败: {e}")
            return []

    def get_tag_by_name(self, name):
        """根据名称获取标签

        Args:
            name: 标签名称

        Returns:
            dict: 标签信息，如果不存在则返回None
        """
        cursor = self.db.conn.cursor()
        cursor.execute("SELECT * FROM tags WHERE name = ?", (name,))
        row = cursor.fetchone()

        if row:
            return dict(row)
        return None

    def get_inheritance_chain(self, tag_id):
        """获取标签的继承链

        Args:
            tag_id: 标签ID

        Returns:
            list: 从根标签到当前标签的继承链
        """
        chain = []
        current_id = tag_id

        while current_id:
            tag = self.get_tag_by_id(current_id)
            if not tag:
                break
            chain.insert(0, tag)  # 插入到开头，保持从根到叶的顺序
            current_id = tag.get("parent_id")

        return chain

    def get_effective_weight(self, tag_id):
        """获取标签的有效权重（考虑继承）

        Args:
            tag_id: 标签ID

        Returns:
            int: 有效权重值
        """
        inherited_attrs = self.get_inherited_attributes(tag_id)
        return inherited_attrs.get("weight", 5)

    def get_effective_color(self, tag_id):
        """获取标签的有效颜色（考虑继承）

        Args:
            tag_id: 标签ID

        Returns:
            str: 有效颜色值
        """
        inherited_attrs = self.get_inherited_attributes(tag_id)
        return inherited_attrs.get("color")

    # ==================== B021: 搜索优化逻辑 ====================

    def search_files_with_weight_sorting(self, tag_ids):
        """基于权重排序的文件搜索

        Args:
            tag_ids: 标签ID列表

        Returns:
            list: 按权重排序的文件列表
        """
        if not tag_ids:
            return []

        cursor = self.db.conn.cursor()
        placeholders = ",".join(["?" for _ in tag_ids])

        # 计算每个文件的权重分数
        cursor.execute(
            f"""
            SELECT f.*,
                   SUM(t.weight) as total_weight,
                   COUNT(ft.tag_id) as tag_count
            FROM files f
            JOIN file_tags ft ON f.id = ft.file_id
            JOIN tags t ON ft.tag_id = t.id
            WHERE ft.tag_id IN ({placeholders})
            GROUP BY f.id
            ORDER BY total_weight DESC, tag_count DESC, f.name
            """,
            tag_ids
        )

        return [dict(row) for row in cursor.fetchall()]

    def filter_files_by_tags_and(self, tag_ids):
        """AND组合筛选：文件必须包含所有指定标签

        Args:
            tag_ids: 标签ID列表

        Returns:
            list: 符合条件的文件列表
        """
        if not tag_ids:
            return []

        cursor = self.db.conn.cursor()
        placeholders = ",".join(["?" for _ in tag_ids])

        cursor.execute(
            f"""
            SELECT f.*
            FROM files f
            WHERE (
                SELECT COUNT(DISTINCT ft.tag_id)
                FROM file_tags ft
                WHERE ft.file_id = f.id AND ft.tag_id IN ({placeholders})
            ) = ?
            ORDER BY f.name
            """,
            tag_ids + [len(tag_ids)]
        )

        return [dict(row) for row in cursor.fetchall()]

    def filter_files_by_tags_or(self, tag_ids):
        """OR组合筛选：文件包含任一指定标签

        Args:
            tag_ids: 标签ID列表

        Returns:
            list: 符合条件的文件列表
        """
        if not tag_ids:
            return []

        cursor = self.db.conn.cursor()
        placeholders = ",".join(["?" for _ in tag_ids])

        cursor.execute(
            f"""
            SELECT DISTINCT f.*
            FROM files f
            JOIN file_tags ft ON f.id = ft.file_id
            WHERE ft.tag_id IN ({placeholders})
            ORDER BY f.name
            """,
            tag_ids
        )

        return [dict(row) for row in cursor.fetchall()]

    def filter_files_by_tags_not(self, include_tag_ids, exclude_tag_ids):
        """NOT组合筛选：包含指定标签但不包含排除标签

        Args:
            include_tag_ids: 必须包含的标签ID列表
            exclude_tag_ids: 必须排除的标签ID列表

        Returns:
            list: 符合条件的文件列表
        """
        if not include_tag_ids:
            return []

        cursor = self.db.conn.cursor()
        include_placeholders = ",".join(["?" for _ in include_tag_ids])
        exclude_placeholders = ",".join(["?" for _ in exclude_tag_ids]) if exclude_tag_ids else ""

        if exclude_tag_ids:
            cursor.execute(
                f"""
                SELECT DISTINCT f.*
                FROM files f
                JOIN file_tags ft ON f.id = ft.file_id
                WHERE ft.tag_id IN ({include_placeholders})
                AND f.id NOT IN (
                    SELECT DISTINCT ft2.file_id
                    FROM file_tags ft2
                    WHERE ft2.tag_id IN ({exclude_placeholders})
                )
                ORDER BY f.name
                """,
                include_tag_ids + exclude_tag_ids
            )
        else:
            # 如果没有排除标签，等同于OR筛选
            return self.filter_files_by_tags_or(include_tag_ids)

        return [dict(row) for row in cursor.fetchall()]

    def search_with_inherited_weights(self, query):
        """基于继承权重的搜索

        Args:
            query: 搜索关键词

        Returns:
            list: 按继承权重排序的搜索结果
        """
        cursor = self.db.conn.cursor()

        # 搜索匹配的标签
        cursor.execute(
            "SELECT * FROM tags WHERE name LIKE ? ORDER BY weight DESC",
            (f"%{query}%",)
        )

        matching_tags = [dict(row) for row in cursor.fetchall()]

        # 为每个标签计算有效权重并重新排序
        for tag in matching_tags:
            tag["effective_weight"] = self.get_effective_weight(tag["id"])

        # 按有效权重排序
        matching_tags.sort(key=lambda x: x["effective_weight"], reverse=True)

        return matching_tags

    # ==================== B021: 统计分析功能 ====================

    def get_tag_usage_statistics(self):
        """获取标签使用统计

        Returns:
            dict: 标签使用统计信息
        """
        cursor = self.db.conn.cursor()

        # 总标签数
        cursor.execute("SELECT COUNT(*) as total_tags FROM tags")
        total_tags = cursor.fetchone()[0]

        # 有文件的标签数
        cursor.execute("""
            SELECT COUNT(DISTINCT t.id) as tags_with_files
            FROM tags t
            JOIN file_tags ft ON t.id = ft.tag_id
        """)
        tags_with_files = cursor.fetchone()[0]

        # 平均每个文件的标签数
        cursor.execute("""
            SELECT AVG(tag_count) as avg_tags_per_file
            FROM (
                SELECT COUNT(ft.tag_id) as tag_count
                FROM files f
                LEFT JOIN file_tags ft ON f.id = ft.file_id
                GROUP BY f.id
            )
        """)
        avg_tags_per_file = cursor.fetchone()[0] or 0

        # 平均每个标签的文件数
        cursor.execute("""
            SELECT AVG(file_count) as avg_files_per_tag
            FROM (
                SELECT COUNT(ft.file_id) as file_count
                FROM tags t
                LEFT JOIN file_tags ft ON t.id = ft.tag_id
                GROUP BY t.id
            )
        """)
        avg_files_per_tag = cursor.fetchone()[0] or 0

        return {
            "total_tags": total_tags,
            "tags_with_files": tags_with_files,
            "unused_tags": total_tags - tags_with_files,
            "average_tags_per_file": round(avg_tags_per_file, 2),
            "average_files_per_tag": round(avg_files_per_tag, 2)
        }

    def get_tag_hierarchy_statistics(self):
        """获取标签层级统计

        Returns:
            dict: 标签层级统计信息
        """
        cursor = self.db.conn.cursor()

        # 计算最大深度
        def get_tag_depth(tag_id, visited=None):
            if visited is None:
                visited = set()
            if tag_id in visited:
                return 0  # 防止循环
            visited.add(tag_id)

            children = self.get_child_tags(tag_id)
            if not children:
                return 1

            max_child_depth = max(get_tag_depth(child["id"], visited.copy()) for child in children)
            return 1 + max_child_depth

        # 获取所有根标签
        root_tags = self.get_tags(parent_id=None)
        max_depth = 0
        if root_tags:
            max_depth = max(get_tag_depth(tag["id"]) for tag in root_tags)

        # 父标签数（有子标签的标签）
        cursor.execute("""
            SELECT COUNT(DISTINCT parent_id) as parent_tags
            FROM tags
            WHERE parent_id IS NOT NULL
        """)
        parent_tags = cursor.fetchone()[0]

        # 叶子标签数（没有子标签的标签）
        cursor.execute("""
            SELECT COUNT(*) as leaf_tags
            FROM tags t1
            WHERE NOT EXISTS (
                SELECT 1 FROM tags t2 WHERE t2.parent_id = t1.id
            )
        """)
        leaf_tags = cursor.fetchone()[0]

        # 根标签数
        cursor.execute("SELECT COUNT(*) as root_tags FROM tags WHERE parent_id IS NULL")
        root_tag_count = cursor.fetchone()[0]

        return {
            "max_depth": max_depth,
            "total_parent_tags": parent_tags,
            "total_leaf_tags": leaf_tags,
            "total_root_tags": root_tag_count
        }

    def get_popular_tags(self, limit=10):
        """获取热门标签

        Args:
            limit: 返回数量限制

        Returns:
            list: 按使用频率排序的热门标签列表
        """
        cursor = self.db.conn.cursor()
        cursor.execute(
            """
            SELECT t.*, COUNT(ft.file_id) as usage_count
            FROM tags t
            LEFT JOIN file_tags ft ON t.id = ft.tag_id
            GROUP BY t.id
            ORDER BY usage_count DESC, t.name
            LIMIT ?
            """,
            (limit,)
        )

        return [dict(row) for row in cursor.fetchall()]

    def get_tag_weight_distribution(self):
        """获取标签权重分布

        Returns:
            dict: 权重分布统计
        """
        cursor = self.db.conn.cursor()
        cursor.execute("""
            SELECT weight, COUNT(*) as count
            FROM tags
            GROUP BY weight
            ORDER BY weight
        """)

        distribution = {}
        for row in cursor.fetchall():
            distribution[row[0]] = row[1]

        return distribution