#!/usr/bin/env python3
"""
第二阶段快速功能验证脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from smartvault.ui.main_window import MainWindow


def test_folder_menu_creation():
    """测试文件夹菜单创建功能"""
    print("🔧 测试文件夹菜单创建...")
    
    app = QApplication.instance() or QApplication(sys.argv)
    main_window = MainWindow()
    
    # 获取文件视图
    file_view = main_window.file_view.table_view
    
    # 测试创建"移动到文件夹"菜单
    rows = {0}  # 模拟选中第一行
    move_menu = file_view._create_move_to_folder_menu(None, rows)
    
    if move_menu:
        print("✅ '移动到文件夹'菜单创建成功")
        print(f"   菜单项数量: {len(move_menu.actions())}")
    else:
        print("⚠️ '移动到文件夹'菜单创建失败（可能没有文件夹）")
    
    # 测试创建"添加到文件夹"菜单
    add_menu = file_view._create_add_to_folder_menu(None, rows)
    
    if add_menu:
        print("✅ '添加到文件夹'菜单创建成功")
        print(f"   菜单项数量: {len(add_menu.actions())}")
    else:
        print("⚠️ '添加到文件夹'菜单创建失败（可能没有文件夹）")
    
    # 测试创建新文件夹功能
    try:
        tag_service = main_window.tag_service
        test_folder_name = "测试文件夹_验证"
        
        # 创建测试文件夹
        folder_tag_id = tag_service.create_folder_tag(test_folder_name)
        print(f"✅ 测试文件夹创建成功: {test_folder_name} (ID: {folder_tag_id})")
        
        # 验证文件夹是否存在
        folder_tags = tag_service.get_folder_tags()
        test_folder_exists = any(f['name'] == f"📁{test_folder_name}" for f in folder_tags)
        
        if test_folder_exists:
            print("✅ 文件夹在数据库中验证成功")
        else:
            print("❌ 文件夹在数据库中验证失败")
        
        # 清理测试文件夹
        tag_service.delete_tag(folder_tag_id)
        print("🧹 测试文件夹已清理")
        
    except Exception as e:
        print(f"❌ 测试文件夹创建失败: {e}")
    
    app.quit()
    return True


def test_navigation_panel_functions():
    """测试导航面板功能"""
    print("\n🧭 测试导航面板功能...")
    
    app = QApplication.instance() or QApplication(sys.argv)
    main_window = MainWindow()
    
    navigation_panel = main_window.navigation_panel
    
    # 测试刷新功能
    try:
        navigation_panel.refresh_folder_tree()
        print("✅ 导航面板刷新功能正常")
    except Exception as e:
        print(f"❌ 导航面板刷新失败: {e}")
    
    # 测试文件夹选择功能
    try:
        # 获取现有文件夹
        folder_tags = main_window.tag_service.get_folder_tags()
        if folder_tags:
            first_folder = folder_tags[0]
            navigation_panel.select_folder_by_tag_id(first_folder['id'])
            print(f"✅ 文件夹选择功能正常: {first_folder['name']}")
        else:
            print("⚠️ 没有文件夹可供测试选择功能")
    except Exception as e:
        print(f"❌ 文件夹选择功能失败: {e}")
    
    app.quit()
    return True


def main():
    """主函数"""
    print("🚀 第二阶段快速功能验证")
    print("=" * 40)
    
    try:
        # 测试文件夹菜单创建
        test_folder_menu_creation()
        
        # 测试导航面板功能
        test_navigation_panel_functions()
        
        print("\n" + "=" * 40)
        print("✅ 快速验证完成！")
        print("\n💡 建议进行手动测试以验证完整功能：")
        print("   python test_stage2_manual.py")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ 验证过程中发生错误: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
