#!/usr/bin/env python3
"""
检查第二阶段基础完善任务的完成情况
"""

import os
import sys
import traceback

def check_task_b023():
    """检查B023 - 拖拽添加文件功能"""
    print("\n=== B023 - 拖拽添加文件功能 ===")

    try:
        from smartvault.ui.main_window.core import MainWindowCore

        # 检查拖拽相关方法
        required_methods = ['dragEnterEvent', 'dropEvent', '_process_dropped_files']
        missing_methods = []

        for method in required_methods:
            if hasattr(MainWindowCore, method):
                print(f"   ✅ {method} - 已实现")
            else:
                print(f"   ❌ {method} - 缺失")
                missing_methods.append(method)

        # 检查setAcceptDrops设置
        # 通过查看源码确认
        import inspect
        source = inspect.getsource(MainWindowCore.__init__)
        if 'setAcceptDrops(True)' in source:
            print("   ✅ setAcceptDrops(True) - 已设置")
        else:
            print("   ❌ setAcceptDrops(True) - 未设置")

        status = "✅ 已完成" if not missing_methods else "❌ 未完成"
        print(f"   状态: {status}")
        return not missing_methods

    except Exception as e:
        print(f"   ❌ 检查失败: {e}")
        return False

def check_task_b024():
    """检查B024 - CRC32文件指纹算法"""
    print("\n=== B024 - CRC32文件指纹算法 ===")

    try:
        from smartvault.services.file.import_ops import FileImportMixin

        # 检查_calculate_file_hash方法
        if hasattr(FileImportMixin, '_calculate_file_hash'):
            print("   ✅ _calculate_file_hash方法 - 已实现")

            # 检查是否使用CRC32
            import inspect
            source = inspect.getsource(FileImportMixin._calculate_file_hash)
            if 'zlib.crc32' in source:
                print("   ✅ CRC32算法 - 已实现")
                if 'hashlib.md5' in source:
                    print("   ✅ MD5备选方案 - 已保留")
                print("   状态: ✅ 已完成")
                return True
            else:
                print("   ❌ CRC32算法 - 未实现")
                print("   状态: ❌ 未完成")
                return False
        else:
            print("   ❌ _calculate_file_hash方法 - 缺失")
            print("   状态: ❌ 未完成")
            return False

    except Exception as e:
        print(f"   ❌ 检查失败: {e}")
        return False

def check_task_b025():
    """检查B025 - 统一文件处理入口测试"""
    print("\n=== B025 - 统一文件处理入口测试 ===")

    try:
        from smartvault.services.file import FileService

        # 检查统一入口方法
        if hasattr(FileService, 'add_file'):
            print("   ✅ FileService.add_file统一入口 - 已实现")

            # 检查方法签名
            import inspect
            sig = inspect.signature(FileService.add_file)
            params = list(sig.parameters.keys())

            expected_params = ['self', 'path', 'mode', 'smart_duplicate_handling']
            if all(param in params for param in expected_params):
                print("   ✅ 方法参数 - 符合预期")
                print("   ⚠️  测试用例 - 需要编写专门的测试")
                print("   状态: 🔄 部分完成")
                return True
            else:
                print(f"   ❌ 方法参数不匹配: {params}")
                print("   状态: ❌ 未完成")
                return False
        else:
            print("   ❌ FileService.add_file方法 - 缺失")
            print("   状态: ❌ 未完成")
            return False

    except Exception as e:
        print(f"   ❌ 检查失败: {e}")
        return False

def check_task_b026():
    """检查B026 - 基础功能综合测试和优化"""
    print("\n=== B026 - 基础功能综合测试和优化 ===")

    # 这个任务需要手动测试，检查是否有相关测试文件
    test_files = [
        'test_b017_feature_validation.py',
        'test_features.py',
        'test_monitor_fixes.py',
        'test_monitor_final_stable.py'
    ]

    existing_tests = []
    for test_file in test_files:
        if os.path.exists(test_file):
            existing_tests.append(test_file)
            print(f"   ✅ 测试文件: {test_file}")

    if existing_tests:
        print(f"   ✅ 发现 {len(existing_tests)} 个测试文件")
        print("   ⚠️  需要运行综合测试验证功能")
        print("   状态: 🔄 部分完成")
        return True
    else:
        print("   ❌ 未发现测试文件")
        print("   状态: ❌ 未完成")
        return False

def check_task_b027():
    """检查B027 - settings_dialog.py模块化重构"""
    print("\n=== B027 - settings_dialog.py模块化重构 ===")

    try:
        # 检查主设置对话框文件大小
        settings_dialog_path = 'smartvault/ui/dialogs/settings_dialog.py'
        if os.path.exists(settings_dialog_path):
            with open(settings_dialog_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            line_count = len(lines)
            print(f"   ✅ 主对话框文件: {line_count} 行")

            if line_count < 300:
                print("   ✅ 文件大小 - 符合重构目标")
            else:
                print("   ⚠️  文件大小 - 仍然较大")

        # 检查模块化结构
        settings_dir = 'smartvault/ui/dialogs/settings'
        if os.path.exists(settings_dir):
            print("   ✅ settings模块目录 - 已创建")

            # 检查页面文件
            pages_dir = os.path.join(settings_dir, 'pages')
            if os.path.exists(pages_dir):
                page_files = [f for f in os.listdir(pages_dir) if f.endswith('.py') and f != '__init__.py']
                print(f"   ✅ 设置页面文件: {len(page_files)} 个")
                for page_file in page_files:
                    print(f"      - {page_file}")

                if len(page_files) >= 6:
                    print("   ✅ 模块化重构 - 已完成")
                    print("   状态: ✅ 已完成")
                    return True
                else:
                    print("   ⚠️  页面文件数量不足")
                    print("   状态: 🔄 部分完成")
                    return True
            else:
                print("   ❌ pages目录 - 缺失")
                print("   状态: ❌ 未完成")
                return False
        else:
            print("   ❌ settings模块目录 - 缺失")
            print("   状态: ❌ 未完成")
            return False

    except Exception as e:
        print(f"   ❌ 检查失败: {e}")
        return False

def main():
    """主检查函数"""
    print("🔍 检查第二阶段基础完善任务完成情况")
    print("=" * 60)

    tasks = [
        ("B023", "拖拽添加文件功能", check_task_b023),
        ("B024", "CRC32文件指纹算法", check_task_b024),
        ("B025", "统一文件处理入口测试", check_task_b025),
        ("B026", "基础功能综合测试和优化", check_task_b026),
        ("B027", "settings_dialog.py模块化重构", check_task_b027),
    ]

    results = {}

    for task_id, task_name, check_func in tasks:
        try:
            results[task_id] = check_func()
        except Exception as e:
            print(f"\n❌ {task_id} 检查异常: {e}")
            traceback.print_exc()
            results[task_id] = False

    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 第二阶段基础完善任务完成情况汇总")
    print("=" * 60)

    completed = 0
    total = len(tasks)

    for task_id, task_name, _ in tasks:
        status = results.get(task_id, False)
        if status:
            print(f"✅ {task_id} - {task_name}")
            completed += 1
        else:
            print(f"❌ {task_id} - {task_name}")

    print(f"\n📈 完成进度: {completed}/{total} ({completed/total*100:.1f}%)")

    if completed == total:
        print("🎉 第二阶段基础完善任务全部完成！")
    elif completed >= total * 0.8:
        print("🔄 第二阶段基础完善任务基本完成，还有少量工作需要完善")
    else:
        print("⚠️  第二阶段基础完善任务还有较多工作需要完成")

if __name__ == "__main__":
    main()
