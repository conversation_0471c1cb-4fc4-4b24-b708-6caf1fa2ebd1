#!/usr/bin/env python3
"""
AI主程序集成测试
测试AI管理器是否能正确集成到主程序中
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_main_window_ai_integration():
    """测试主窗口AI集成"""
    print("🔍 测试主窗口AI集成...")
    
    try:
        # 测试主窗口核心类导入
        from smartvault.ui.main_window.core import MainWindowCore
        print("✅ 主窗口核心类导入成功")
        
        # 检查AI管理器是否已添加到主窗口
        import inspect
        source = inspect.getsource(MainWindowCore.__init__)
        
        if 'ai_manager' in source and 'AIManager' in source:
            print("✅ AI管理器已添加到主窗口初始化代码")
        else:
            print("❌ AI管理器未添加到主窗口")
            return False
        
        # 检查AI初始化方法是否存在
        if hasattr(MainWindowCore, '_initialize_ai_manager'):
            print("✅ AI初始化方法已添加")
        else:
            print("❌ AI初始化方法未添加")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 主窗口AI集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_manager_initialization():
    """测试AI管理器初始化"""
    print("\n🔍 测试AI管理器初始化...")
    
    try:
        from smartvault.services.ai.ai_manager import AIManager
        from smartvault.services.tag_service import TagService
        from smartvault.services.file import FileService
        from smartvault.utils.config import load_config
        
        # 创建必要的服务
        tag_service = TagService()
        file_service = FileService()
        config = load_config()
        
        print("✅ 服务创建成功")
        
        # 创建AI管理器
        ai_manager = AIManager()
        print("✅ AI管理器创建成功")
        
        # 初始化AI管理器
        success = ai_manager.initialize(
            config=config,
            tag_service=tag_service,
            auto_tag_service=None,
            db=file_service.db if hasattr(file_service, 'db') else None
        )
        
        print(f"✅ AI管理器初始化: {success}")
        
        # 获取状态
        status = ai_manager.get_status()
        print(f"✅ AI状态: 启用={status['enabled']}, 阶段={status['stage']}, 状态={status['status']}")
        
        # 测试标签建议功能
        test_file = {
            'name': 'test_project.py',
            'extension': '.py',
            'path': '/test/test_project.py'
        }
        suggestions = ai_manager.suggest_tags(test_file)
        print(f"✅ 标签建议功能: {suggestions}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI管理器初始化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_config_integration():
    """测试AI配置集成"""
    print("\n🔍 测试AI配置集成...")
    
    try:
        from smartvault.utils.config import load_config, get_ai_config, get_ai_status
        
        # 测试配置加载
        config = load_config()
        print("✅ 主配置加载成功")
        
        # 测试AI配置
        ai_config = get_ai_config()
        print(f"✅ AI配置加载成功: {list(ai_config.keys())}")
        
        # 测试AI状态
        ai_status = get_ai_status()
        print(f"✅ AI状态获取成功: {ai_status}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI配置集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_settings_dialog_ai_page():
    """测试设置对话框AI页面"""
    print("\n🔍 测试设置对话框AI页面...")
    
    try:
        from smartvault.ui.dialogs.settings_dialog import SettingsDialog
        from smartvault.ui.dialogs.settings.pages.ai.ai_main_page import AISettingsPage
        
        # 检查AI页面是否在导入中
        import inspect
        source = inspect.getsource(SettingsDialog)
        
        if 'AISettingsPage' in source:
            print("✅ AI设置页面已导入到设置对话框")
        else:
            print("❌ AI设置页面未导入到设置对话框")
            return False
        
        # 检查create_pages方法
        create_pages_source = inspect.getsource(SettingsDialog.create_pages)
        if "'ai'" in create_pages_source and "AISettingsPage" in create_pages_source:
            print("✅ AI页面已添加到页面创建逻辑")
        else:
            print("❌ AI页面未添加到页面创建逻辑")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 设置对话框AI页面测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 AI主程序集成测试")
    print("=" * 60)
    
    # 测试1: 主窗口AI集成
    test1 = test_main_window_ai_integration()
    
    # 测试2: AI管理器初始化
    test2 = test_ai_manager_initialization()
    
    # 测试3: AI配置集成
    test3 = test_ai_config_integration()
    
    # 测试4: 设置对话框AI页面
    test4 = test_settings_dialog_ai_page()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"主窗口AI集成: {'✅ 通过' if test1 else '❌ 失败'}")
    print(f"AI管理器初始化: {'✅ 通过' if test2 else '❌ 失败'}")
    print(f"AI配置集成: {'✅ 通过' if test3 else '❌ 失败'}")
    print(f"设置对话框AI页面: {'✅ 通过' if test4 else '❌ 失败'}")
    
    if all([test1, test2, test3, test4]):
        print("\n🎉 AI主程序集成测试全部通过！")
        print("✅ AI功能已成功集成到主程序")
        return 0
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查")
        return 1

if __name__ == "__main__":
    sys.exit(main())
