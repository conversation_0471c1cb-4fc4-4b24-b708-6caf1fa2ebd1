#!/usr/bin/env python3
"""
SmartVault 性能测试
测试启动时间、翻页响应时间、内存使用等性能指标
"""

import sys
import os
import time
import psutil
import uuid
import datetime
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from smartvault.services.file import FileService
from smartvault.ui.models.file_table_model import FileTableModel


class PerformanceTester:
    """性能测试器"""

    def __init__(self):
        self.test_results = []
        self.process = psutil.Process()

    def log_test(self, test_name, success, details=""):
        """记录测试结果"""
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{status} {test_name}")
        if details:
            print(f"   详情: {details}")
        self.test_results.append({
            "name": test_name,
            "success": success,
            "details": details
        })

    def get_memory_usage(self):
        """获取当前内存使用量（MB）"""
        return self.process.memory_info().rss / 1024 / 1024

    def prepare_large_dataset(self, target_count=1000):
        """准备大数据集"""
        print(f"📝 准备大数据集（目标：{target_count}个文件）...")

        file_service = FileService()
        current_count = file_service.get_file_count()

        if current_count < target_count:
            needed = target_count - current_count
            print(f"   需要添加 {needed} 个测试文件")

            cursor = file_service.db.conn.cursor()
            batch_size = 100

            for batch_start in range(0, needed, batch_size):
                batch_end = min(batch_start + batch_size, needed)
                batch_data = []

                for i in range(batch_start, batch_end):
                    file_id = str(uuid.uuid4())
                    now = datetime.datetime.now().isoformat()

                    batch_data.append((
                        file_id,
                        f"性能测试文件_{i+1:04d}.txt",
                        f"C:\\perf_test\\性能测试文件_{i+1:04d}.txt",
                        None,
                        1024 * (i + 1),
                        now,
                        now,
                        now,
                        "link",
                        1
                    ))

                cursor.executemany(
                    """
                    INSERT INTO files (
                        id, name, original_path, library_path, size,
                        created_at, modified_at, added_at, entry_type, is_available
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """,
                    batch_data
                )

                if (batch_end - batch_start) % 100 == 0:
                    print(f"   已添加 {batch_end} / {needed} 个文件")

            file_service.db.conn.commit()
            print(f"   数据准备完成")

        final_count = file_service.get_file_count()
        file_service.db.close()
        print(f"   数据集大小: {final_count} 个文件")
        return final_count

    def test_startup_time(self):
        """测试启动时间"""
        print("\n🔍 测试启动时间...")

        # 测试文件服务初始化时间
        start_time = time.time()
        file_service = FileService()
        service_init_time = time.time() - start_time

        # 测试数据库连接时间
        start_time = time.time()
        total_files = file_service.get_file_count()
        db_connect_time = time.time() - start_time

        # 测试第一页数据加载时间
        start_time = time.time()
        first_page = file_service.get_files(limit=100, offset=0)
        first_page_time = time.time() - start_time

        file_service.db.close()

        # 总启动时间
        total_startup_time = service_init_time + db_connect_time + first_page_time

        # 验收标准：启动时间 < 3秒
        startup_ok = total_startup_time < 3.0
        self.log_test(
            "启动时间 < 3秒",
            startup_ok,
            f"总时间: {total_startup_time:.3f}s (服务: {service_init_time:.3f}s, 数据库: {db_connect_time:.3f}s, 首页: {first_page_time:.3f}s)"
        )

        return total_startup_time

    def test_pagination_response_time(self):
        """测试翻页响应时间"""
        print("\n🔍 测试翻页响应时间...")

        file_service = FileService()
        model = FileTableModel()

        # 设置回调
        model.set_data_loader_callback(
            lambda limit, offset, search_keyword=None, search_column=None:
            file_service.get_files(limit=limit, offset=offset, search_keyword=search_keyword, search_column=search_column)
        )

        # 设置分页参数
        total_files = file_service.get_file_count()
        model.set_total_files(total_files)
        model.setPageSize(100)

        # 测试多次翻页的平均响应时间
        page_times = []
        test_pages = min(10, model.getTotalPages())  # 测试最多10页

        for page in range(test_pages):
            start_time = time.time()
            model.goToPage(page)
            page_time = time.time() - start_time
            page_times.append(page_time)

        avg_page_time = sum(page_times) / len(page_times)
        max_page_time = max(page_times)

        file_service.db.close()

        # 验收标准：翻页响应时间 < 1秒
        response_ok = avg_page_time < 1.0 and max_page_time < 1.0
        self.log_test(
            "翻页响应时间 < 1秒",
            response_ok,
            f"平均: {avg_page_time:.3f}s, 最大: {max_page_time:.3f}s (测试了 {test_pages} 页)"
        )

        return avg_page_time, max_page_time

    def test_memory_usage(self):
        """测试内存使用"""
        print("\n🔍 测试内存使用...")

        # 记录初始内存
        initial_memory = self.get_memory_usage()

        file_service = FileService()

        # 测试加载不同数量文件时的内存使用
        memory_measurements = []

        for page_size in [100, 500, 1000]:
            # 加载数据
            files = file_service.get_files(limit=page_size, offset=0)
            current_memory = self.get_memory_usage()
            memory_increase = current_memory - initial_memory

            memory_measurements.append({
                "page_size": page_size,
                "files_loaded": len(files),
                "memory_mb": current_memory,
                "memory_increase_mb": memory_increase
            })

            print(f"   加载 {len(files)} 个文件: {current_memory:.1f}MB (+{memory_increase:.1f}MB)")

        file_service.db.close()

        # 检查内存使用是否合理（不应该线性增长太多）
        max_increase = max(m["memory_increase_mb"] for m in memory_measurements)
        memory_ok = max_increase < 100  # 内存增长不超过100MB

        self.log_test(
            "内存使用合理",
            memory_ok,
            f"最大内存增长: {max_increase:.1f}MB"
        )

        return memory_measurements

    def test_search_performance(self):
        """测试搜索性能"""
        print("\n🔍 测试搜索性能...")

        file_service = FileService()

        # 测试不同搜索条件的响应时间
        search_tests = [
            ("性能", "按名称搜索"),
            ("测试", "按名称搜索"),
            ("txt", "按扩展名搜索"),
        ]

        search_times = []

        for keyword, description in search_tests:
            # 测试搜索总数查询时间
            start_time = time.time()
            count = file_service.get_file_count(search_keyword=keyword, search_column=None)
            count_time = time.time() - start_time

            # 测试搜索结果加载时间
            start_time = time.time()
            results = file_service.get_files(limit=100, offset=0, search_keyword=keyword, search_column=None)
            search_time = time.time() - start_time

            total_time = count_time + search_time
            search_times.append(total_time)

            print(f"   {description} '{keyword}': {total_time:.3f}s (找到 {count} 个结果)")

        file_service.db.close()

        avg_search_time = sum(search_times) / len(search_times)

        # 搜索响应时间应该 < 2秒
        search_ok = avg_search_time < 2.0
        self.log_test(
            "搜索响应时间 < 2秒",
            search_ok,
            f"平均搜索时间: {avg_search_time:.3f}s"
        )

        return avg_search_time

    def run_all_tests(self):
        """运行所有性能测试"""
        print("开始性能测试")
        print("=" * 60)

        # 准备大数据集
        total_files = self.prepare_large_dataset(1000)

        # 运行各项测试
        startup_time = self.test_startup_time()
        avg_page_time, max_page_time = self.test_pagination_response_time()
        memory_measurements = self.test_memory_usage()
        avg_search_time = self.test_search_performance()

        # 输出测试结果
        print("\n" + "=" * 60)
        print("性能测试结果汇总")
        print("=" * 60)

        passed = sum(1 for result in self.test_results if result["success"])
        total = len(self.test_results)

        print(f"总测试数: {total}")
        print(f"通过数: {passed}")
        print(f"失败数: {total - passed}")
        print(f"通过率: {passed/total*100:.1f}%")

        print(f"\n📈 性能指标:")
        print(f"• 启动时间: {startup_time:.3f}s")
        print(f"• 翻页时间: 平均 {avg_page_time:.3f}s, 最大 {max_page_time:.3f}s")
        print(f"• 搜索时间: 平均 {avg_search_time:.3f}s")
        print(f"• 数据集大小: {total_files} 个文件")

        if passed == total:
            print("\n所有性能测试通过！系统性能符合要求。")
            return True
        else:
            print("\n部分性能测试失败，需要优化。")
            print("\n失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  失败 {result['name']}: {result['details']}")
            return False


def main():
    """主函数"""
    app = QApplication(sys.argv)

    try:
        tester = PerformanceTester()
        success = tester.run_all_tests()

        # 自动退出
        QTimer.singleShot(1000, app.quit)
        app.exec()

        return 0 if success else 1

    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
