"""
添加文件对话框
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGroupBox, QRadioButton,
    QPushButton, QListWidget, QDialogButtonBox, QFileDialog, QLabel,
    QFrame, QButtonGroup, QComboBox
)
from PySide6.QtCore import Qt
from smartvault.services.file import FileService
from smartvault.utils.config import load_config, save_config


class AddFileDialog(QDialog):
    """添加文件对话框"""

    def __init__(self, parent=None):
        """初始化对话框

        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        self.setWindowTitle("添加到智能文件库")
        self.resize(600, 450)

        # 文件列表
        self.file_list = []

        # 加载配置
        self.config = load_config()

        # 获取文件库统计信息
        self.file_service = FileService()
        try:
            self.library_stats = self.file_service.get_library_stats()
        except Exception as e:
            print(f"获取文件库统计信息失败: {e}")
            # 提供默认值
            self.library_stats = {
                "library_path": self.file_service.file_system.library_path,
                "total_files": 0,
                "total_size": 0,
                "entry_type_stats": {}
            }

        # 初始化UI
        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)

        # 智能文件库信息部分
        info_group = QGroupBox("智能文件库信息")
        info_layout = QVBoxLayout(info_group)

        # 文件库路径
        path_layout = QHBoxLayout()
        path_label = QLabel("路径:")
        path_layout.addWidget(path_label)

        # 使用标准化路径
        from smartvault.utils.config import normalize_path
        normalized_path = normalize_path(self.library_stats["library_path"])
        path_value = QLabel(normalized_path)
        path_value.setStyleSheet("font-weight: bold;")
        path_layout.addWidget(path_value)
        path_layout.addStretch()

        info_layout.addLayout(path_layout)

        # 文件库统计信息
        stats_layout = QHBoxLayout()

        # 文件总数
        total_files_label = QLabel(f"文件总数: {self.library_stats['total_files']}")
        stats_layout.addWidget(total_files_label)

        # 总大小
        total_size = self._format_size(self.library_stats["total_size"])
        total_size_label = QLabel(f"总大小: {total_size}")
        stats_layout.addWidget(total_size_label)

        # 入库方式统计
        entry_stats = self.library_stats.get("entry_type_stats", {})
        link_count = entry_stats.get("link", 0)
        copy_count = entry_stats.get("copy", 0)
        move_count = entry_stats.get("move", 0)

        entry_stats_label = QLabel(
            f"入库方式: 链接({link_count}) / 复制({copy_count}) / 移动({move_count})"
        )
        stats_layout.addWidget(entry_stats_label)

        stats_layout.addStretch()
        info_layout.addLayout(stats_layout)

        # 添加分隔线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        info_layout.addWidget(line)

        layout.addWidget(info_group)

        # 文件选择部分
        file_group = QGroupBox("选择文件")
        file_layout = QVBoxLayout(file_group)

        self.file_list_widget = QListWidget()
        file_layout.addWidget(self.file_list_widget)

        file_buttons = QHBoxLayout()
        self.add_file_button = QPushButton("添加文件")
        self.add_file_button.clicked.connect(self.on_add_files)
        file_buttons.addWidget(self.add_file_button)

        self.add_folder_button = QPushButton("添加文件夹")
        self.add_folder_button.clicked.connect(self.on_add_folder)
        file_buttons.addWidget(self.add_folder_button)

        file_layout.addLayout(file_buttons)
        layout.addWidget(file_group)

        # 入库方式选择
        mode_group = QGroupBox("入库方式")
        mode_layout = QVBoxLayout(mode_group)

        # 创建单选按钮组
        self.entry_type_group = QButtonGroup(self)

        # 链接选项
        self.link_radio = QRadioButton("链接到智能文件库")
        self.link_radio.setToolTip("在智能文件库中创建对原始文件的链接，不复制或移动文件")
        mode_layout.addWidget(self.link_radio)
        self.entry_type_group.addButton(self.link_radio, 1)

        # 复制选项
        self.copy_radio = QRadioButton("复制到智能文件库")
        self.copy_radio.setToolTip("将文件复制到智能文件库中，保留原始文件")
        mode_layout.addWidget(self.copy_radio)
        self.entry_type_group.addButton(self.copy_radio, 2)

        # 移动选项
        self.move_radio = QRadioButton("移动到智能文件库")
        self.move_radio.setToolTip("将文件移动到智能文件库中，删除原始文件")
        mode_layout.addWidget(self.move_radio)
        self.entry_type_group.addButton(self.move_radio, 3)

        # 加载保存的设置
        entry_type = self.config.get("entry_type", "link")
        if entry_type == "link":
            self.link_radio.setChecked(True)
        elif entry_type == "copy":
            self.copy_radio.setChecked(True)
        elif entry_type == "move":
            self.move_radio.setChecked(True)
        else:
            # 默认为链接模式
            self.link_radio.setChecked(True)

        # 设置选择变化事件
        self.entry_type_group.buttonClicked.connect(self.on_entry_type_changed)

        # 添加说明
        note_label = QLabel("选择文件添加到智能文件库的方式")
        note_label.setStyleSheet("color: #666;")
        mode_layout.addWidget(note_label)

        layout.addWidget(mode_group)

        # 设备来源选择
        device_group = QGroupBox("设备来源 (可选)")
        device_layout = QVBoxLayout(device_group)

        # 设备选择下拉框
        self.device_combo = QComboBox()
        self.device_combo.addItem("无设备来源", None)

        # 加载设备列表
        self._load_device_list()

        device_layout.addWidget(QLabel("选择文件来源设备:"))
        device_layout.addWidget(self.device_combo)

        # 添加说明
        device_note_label = QLabel("选择文件来源的移动设备，便于后续分类管理")
        device_note_label.setStyleSheet("color: #666;")
        device_layout.addWidget(device_note_label)

        layout.addWidget(device_group)

        # 对话框按钮
        buttons = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)

    def on_add_files(self):
        """添加文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self, "选择要添加的文件", "", "所有文件 (*.*)"
        )

        if files:
            for file in files:
                if file not in self.file_list:
                    self.file_list.append(file)
                    self.file_list_widget.addItem(file)

    def on_add_folder(self):
        """添加文件夹"""
        folder = QFileDialog.getExistingDirectory(
            self, "选择要添加的文件夹"
        )

        if folder:
            self.file_list.append(folder)
            self.file_list_widget.addItem(f"文件夹: {folder}")

    def on_entry_type_changed(self, button):
        """入库方式选择变化处理

        Args:
            button: 被点击的按钮
        """
        # 确定选择的入库方式
        if button == self.link_radio:
            entry_type = "link"
        elif button == self.copy_radio:
            entry_type = "copy"
        elif button == self.move_radio:
            entry_type = "move"
        else:
            return

        # 保存设置
        self.config["entry_type"] = entry_type
        save_config(self.config)

    def _load_device_list(self):
        """加载设备文件夹列表"""
        try:
            from smartvault.services.tag import TagService

            tag_service = TagService()

            # 获取所有设备标签
            device_tags = tag_service.get_device_tags()

            for tag in device_tags:
                # 格式: "卷标名称"
                display_name = tag['name']
                self.device_combo.addItem(display_name, tag['id'])

        except Exception as e:
            print(f"加载设备列表失败: {e}")

    def get_selected_device_tag_id(self):
        """获取选择的设备标签ID

        Returns:
            Optional[int]: 设备标签ID，如果未选择则返回None
        """
        return self.device_combo.currentData()

    def get_entry_type(self):
        """获取入库方式

        Returns:
            str: 入库方式（link/copy/move）
        """
        if self.link_radio.isChecked():
            return "link"
        elif self.copy_radio.isChecked():
            return "copy"
        elif self.move_radio.isChecked():
            return "move"
        else:
            return "link"  # 默认为链接模式

    def _format_size(self, size):
        """格式化文件大小

        Args:
            size: 文件大小（字节）

        Returns:
            str: 格式化后的文件大小
        """
        if size < 1024:
            return f"{size} B"
        elif size < 1024 * 1024:
            return f"{size / 1024:.2f} KB"
        elif size < 1024 * 1024 * 1024:
            return f"{size / (1024 * 1024):.2f} MB"
        else:
            return f"{size / (1024 * 1024 * 1024):.2f} GB"
