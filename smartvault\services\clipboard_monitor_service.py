"""
剪贴板监控服务
监控剪贴板中的文件和文件名，自动进行查重检测
"""

import os
import re
from typing import List, Dict, Optional
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QObject, Signal, QTimer
from smartvault.data.database import Database
from smartvault.utils.config import load_config


class ClipboardMonitorService(QObject):
    """剪贴板监控服务"""

    # 信号定义
    duplicate_found = Signal(dict)  # 发现重复文件时发出信号

    def __init__(self):
        """初始化剪贴板监控服务"""
        super().__init__()

        # 延迟初始化数据库连接
        self.db = None
        self.config = load_config()

        # 剪贴板对象
        self.clipboard = QApplication.clipboard()

        # 监控状态
        self.is_monitoring = False

        # 从配置加载文件名清洗规则
        self.load_cleaning_rules()

        # 上次检查的剪贴板内容（避免重复检查）
        self.last_clipboard_content = ""

        # 防抖定时器
        self.debounce_timer = QTimer()
        self.debounce_timer.setSingleShot(True)
        self.debounce_timer.timeout.connect(self._process_clipboard_change)

    def load_cleaning_rules(self):
        """从配置文件加载清洗规则"""
        try:
            # 确保config属性存在
            if not hasattr(self, 'config'):
                self.config = load_config()

            clipboard_config = self.config.get("clipboard", {})
            cleaning_config = clipboard_config.get("filename_cleaning", {})

            # 检查清洗功能是否启用
            cleaning_enabled = cleaning_config.get("enabled", True)

            if not cleaning_enabled:
                # 清洗功能被禁用，不加载任何规则
                self.cleaning_rules = []
                print("📋 文件名清洗功能已禁用，不应用清洗规则")
                return

            # 获取规则列表
            rules = cleaning_config.get("rules", [])

            # 只保留启用的规则
            self.cleaning_rules = [rule for rule in rules if rule.get("enabled", True)]

            # 如果没有规则，使用默认规则
            if not self.cleaning_rules:
                self.cleaning_rules = self._get_default_rules()

            print(f"📋 已加载 {len(self.cleaning_rules)} 个清洗规则")

        except Exception as e:
            print(f"加载清洗规则失败: {e}")
            self.cleaning_rules = self._get_default_rules()

    def _get_default_rules(self):
        """获取默认清洗规则"""
        return [
            {"name": "移除下载后缀", "pattern": r" - 下载.*$", "replace": "", "enabled": True},
            {"name": "移除文件大小", "pattern": r" \([0-9.]+[KMGT]?B\)", "replace": "", "enabled": True},
            {"name": "移除网站标记", "pattern": r"^\[.*?\]\s*", "replace": "", "enabled": True},
            {"name": "移除副本标记", "pattern": r" - 副本(\(\d+\))?", "replace": "", "enabled": True},
            {"name": "移除时间戳", "pattern": r"_\d{8}_", "replace": "_", "enabled": True},
            {"name": "移除括号数字", "pattern": r" \(\d+\)", "replace": "", "enabled": True},
        ]

    def reload_config(self):
        """重新加载配置"""
        try:
            self.config = load_config()
            self.load_cleaning_rules()
            print("✅ 剪贴板监控配置已重新加载")
        except Exception as e:
            print(f"重新加载配置失败: {e}")

    def get_detection_delay(self) -> int:
        """获取检测延迟（毫秒）"""
        try:
            if not hasattr(self, 'config'):
                self.config = load_config()
            clipboard_config = self.config.get("clipboard", {})
            return clipboard_config.get("detection_delay_ms", 500)
        except Exception:
            return 500  # 默认500ms

    def _ensure_database_connection(self) -> bool:
        """确保数据库连接存在"""
        try:
            if self.db is None:
                from smartvault.data.database import Database
                library_path = self.config.get("library_path", "")

                if not library_path:
                    print("⚠️ 文件库路径未配置，剪贴板查重功能将仅支持文件名清洗")
                    return False

                # 检查路径是否存在
                if not os.path.exists(library_path):
                    print(f"⚠️ 文件库路径不存在: {library_path}")
                    return False

                # 构建正确的数据库文件路径
                db_path = os.path.join(library_path, "data", "smartvault.db")

                # 检查数据库文件是否存在
                if not os.path.exists(db_path):
                    print(f"⚠️ 数据库文件不存在: {db_path}")
                    return False

                self.db = Database(db_path)
                print("✅ 剪贴板服务数据库连接已建立")

            return True

        except Exception as e:
            print(f"⚠️ 建立数据库连接失败: {e}")
            return False

    def start_monitoring(self):
        """开始监控剪贴板"""
        if not self.is_monitoring:
            self.clipboard.dataChanged.connect(self._on_clipboard_changed)
            self.is_monitoring = True
            print("✅ 剪贴板监控已启动")

    def stop_monitoring(self):
        """停止监控剪贴板"""
        if self.is_monitoring:
            self.clipboard.dataChanged.disconnect(self._on_clipboard_changed)
            self.is_monitoring = False
            print("⏹️ 剪贴板监控已停止")

    def _on_clipboard_changed(self):
        """剪贴板内容变化处理（防抖）"""
        # 使用防抖机制，避免频繁触发
        self.debounce_timer.stop()
        delay = self.get_detection_delay()
        self.debounce_timer.start(delay)

    def _process_clipboard_change(self):
        """处理剪贴板变化"""
        try:
            # 只有在监控启用时才处理剪贴板变化
            if not self.is_monitoring:
                print("📋 剪贴板变化检测到，但监控未启用")
                return

            print("📋 处理剪贴板变化...")
            mime_data = self.clipboard.mimeData()

            # 检查是否有文件
            if mime_data.hasUrls():
                print("📁 检测到剪贴板中有文件")
                self._check_clipboard_files(mime_data)

            # 检查是否有文本（可能是文件名）
            elif mime_data.hasText():
                text = mime_data.text().strip()
                print(f"📝 检测到剪贴板中有文本: {text[:50]}...")
                self._check_clipboard_text(mime_data)
            else:
                print("📋 剪贴板内容不是文件或文本")

        except Exception as e:
            print(f"处理剪贴板变化失败: {e}")
            import traceback
            traceback.print_exc()

    def _check_clipboard_files(self, mime_data):
        """检查剪贴板中的文件"""
        try:
            file_urls = mime_data.urls()
            print(f"📁 剪贴板中有 {len(file_urls)} 个文件URL")

            for url in file_urls:
                file_path = url.toLocalFile()
                print(f"📄 检查文件: {file_path}")

                if os.path.isfile(file_path):
                    # 计算文件CRC32哈希
                    print("🔢 计算文件哈希...")
                    file_hash = self._calculate_file_hash(file_path)

                    if file_hash:
                        print(f"🔢 文件哈希: {file_hash}")

                        # 检查数据库连接
                        if not self._ensure_database_connection():
                            print("❌ 数据库连接失败，无法进行文件查重检查")
                            # 显示数据库连接失败的提示
                            self._emit_database_error({
                                'type': 'database_error',
                                'source_path': file_path,
                                'source_name': os.path.basename(file_path),
                                'error_message': '数据库连接失败，请检查文件库路径配置'
                            })
                            continue

                        # 在数据库中查找相同哈希的文件
                        duplicate_files = self._find_files_by_hash(file_hash)

                        if duplicate_files:
                            print(f"🔍 发现 {len(duplicate_files)} 个重复文件")
                            # 发现重复文件
                            self._emit_duplicate_found({
                                'type': 'file',
                                'source_path': file_path,
                                'source_name': os.path.basename(file_path),
                                'hash': file_hash,
                                'duplicates': duplicate_files
                            })
                        else:
                            print("✅ 未发现重复文件")
                            # 显示未发现重复的信息
                            self._emit_no_duplicate_found({
                                'type': 'no_duplicate',
                                'source_path': file_path,
                                'source_name': os.path.basename(file_path),
                                'message': f'文件 "{os.path.basename(file_path)}" 在文件库中未发现重复'
                            })
                    else:
                        print("❌ 文件哈希计算失败")
                else:
                    print(f"⚠️ 路径不是文件: {file_path}")

        except Exception as e:
            print(f"检查剪贴板文件失败: {e}")
            import traceback
            traceback.print_exc()

    def _check_clipboard_text(self, mime_data):
        """检查剪贴板中的文本（可能是文件名）"""
        try:
            text = mime_data.text().strip()
            print(f"📝 剪贴板文本: '{text}'")

            # 避免重复检查相同的剪贴板内容
            if text == self.last_clipboard_content or not text:
                print("⏭️ 跳过重复的剪贴板内容（避免重复检查）")
                return

            self.last_clipboard_content = text

            # 清洗文件名
            print("🧹 清洗文件名...")
            cleaned_filename = self._clean_filename(text)

            if cleaned_filename:
                print(f"🧹 清洗后文件名: '{cleaned_filename}'")

                # 检查数据库连接
                if not self._ensure_database_connection():
                    print("❌ 数据库连接失败，无法进行查重检查")
                    # 显示数据库连接失败的提示
                    self._emit_database_error({
                        'type': 'database_error',
                        'source_text': text,
                        'cleaned_name': cleaned_filename,
                        'error_message': '数据库连接失败，请检查文件库路径配置'
                    })
                    return

                # 在数据库中查找相同文件名的文件
                duplicate_files = self._find_files_by_name(cleaned_filename)

                if duplicate_files:
                    print(f"🔍 发现 {len(duplicate_files)} 个相似文件名")
                    # 发现重复文件名
                    self._emit_duplicate_found({
                        'type': 'filename',
                        'source_text': text,
                        'cleaned_name': cleaned_filename,
                        'duplicates': duplicate_files
                    })
                else:
                    print("✅ 未发现相似文件名")
                    # 显示未发现重复的信息
                    self._emit_no_duplicate_found({
                        'type': 'no_duplicate',
                        'source_text': text,
                        'cleaned_name': cleaned_filename,
                        'message': f'"{cleaned_filename}" 在文件库中未发现重复'
                    })
            else:
                print("❌ 文本不像文件名，跳过检查")

        except Exception as e:
            print(f"检查剪贴板文本失败: {e}")
            import traceback
            traceback.print_exc()

    def _calculate_file_hash(self, file_path: str) -> str:
        """计算文件的CRC32哈希值"""
        try:
            import zlib

            crc32_hash = 0
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(65536), b""):  # 64KB chunks
                    crc32_hash = zlib.crc32(chunk, crc32_hash)

            return format(crc32_hash & 0xffffffff, '08x')

        except Exception as e:
            print(f"计算文件哈希失败: {e}")
            return ""

    def _clean_filename(self, raw_text: str) -> str:
        """清洗文件名"""
        try:
            cleaned = raw_text.strip()

            # 检查是否有启用的清洗规则
            enabled_rules = [rule for rule in self.cleaning_rules if rule.get('enabled', True)]

            if enabled_rules:
                # 有启用的清洗规则时，应用清洗规则
                for rule in enabled_rules:
                    try:
                        cleaned = re.sub(rule['pattern'], rule['replace'], cleaned)
                    except re.error as e:
                        print(f"清洗规则 '{rule.get('name', '未知')}' 执行失败: {e}")

            # 移除多余空格（无论是否有清洗规则都需要）
            cleaned = re.sub(r'\s+', ' ', cleaned).strip()

            # 基本长度检查，避免查询过短的文本
            if len(cleaned) >= 2:
                return cleaned
            else:
                return ""

        except Exception as e:
            print(f"清洗文件名失败: {e}")
            return ""

    def _find_files_by_hash(self, file_hash: str) -> List[Dict]:
        """根据哈希值查找文件"""
        try:
            # 确保数据库连接存在
            if not self._ensure_database_connection():
                return []

            cursor = self.db.conn.cursor()
            cursor.execute(
                "SELECT id, name, original_path, library_path, entry_type FROM files WHERE file_hash = ?",
                (file_hash,)
            )

            results = cursor.fetchall()
            files = []

            for row in results:
                files.append({
                    'id': row[0],
                    'name': row[1],
                    'original_path': row[2],
                    'library_path': row[3],
                    'entry_type': row[4]
                })

            return files

        except Exception as e:
            print(f"根据哈希查找文件失败: {e}")
            return []

    def _find_files_by_name(self, filename: str) -> List[Dict]:
        """根据文件名查找文件（智能比较策略）"""
        try:
            # 确保数据库连接存在
            if not self._ensure_database_connection():
                return []

            cursor = self.db.conn.cursor()

            # 检查剪贴板文本是否包含扩展名
            has_extension = '.' in filename and not filename.endswith('.')

            if has_extension:
                # 剪贴板内容有扩展名：精确匹配完整文件名
                print(f"🔍 精确匹配文件名（含扩展名）: {filename}")
                cursor.execute(
                    "SELECT id, name, original_path, library_path, entry_type FROM files WHERE name LIKE ?",
                    (f"%{filename}%",)
                )
            else:
                # 剪贴板内容无扩展名：只匹配文件名部分（不含扩展名）
                print(f"🔍 匹配文件名主体（不含扩展名）: {filename}")
                # 使用 LIKE 查询，匹配文件名开头部分
                cursor.execute(
                    "SELECT id, name, original_path, library_path, entry_type FROM files WHERE name LIKE ? OR name LIKE ?",
                    (f"{filename}.%", f"%{filename}.%")
                )

            results = cursor.fetchall()
            files = []

            for row in results:
                files.append({
                    'id': row[0],
                    'name': row[1],
                    'original_path': row[2],
                    'library_path': row[3],
                    'entry_type': row[4]
                })

            print(f"🔍 找到 {len(files)} 个匹配文件")
            return files

        except Exception as e:
            print(f"根据文件名查找文件失败: {e}")
            return []

    def _emit_duplicate_found(self, duplicate_info: Dict):
        """发出重复文件发现信号"""
        self.duplicate_found.emit(duplicate_info)
        print(f"🔍 发现重复: {duplicate_info['type']} - {duplicate_info.get('source_name', duplicate_info.get('cleaned_name', ''))}")

    def _emit_database_error(self, error_info: Dict):
        """发出数据库错误信号"""
        self.duplicate_found.emit(error_info)
        print(f"❌ 数据库错误: {error_info.get('error_message', '未知错误')}")

    def _emit_no_duplicate_found(self, info: Dict):
        """发出未发现重复信号"""
        self.duplicate_found.emit(info)
        print(f"✅ 未发现重复: {info.get('cleaned_name', '')}")

    def add_cleaning_rule(self, name: str, pattern: str, replace: str):
        """添加自定义清洗规则"""
        self.cleaning_rules.append({
            'name': name,
            'pattern': pattern,
            'replace': replace
        })

    def get_cleaning_rules(self) -> List[Dict]:
        """获取所有清洗规则"""
        return self.cleaning_rules.copy()

    def set_cleaning_rules(self, rules: List[Dict]):
        """设置清洗规则"""
        self.cleaning_rules = rules.copy()
