"""
基础设置页面类
"""

from PySide6.QtWidgets import QWidget
from abc import abstractmethod
from typing import Tuple


class BaseSettingsPage(QWidget):
    """设置页面基类"""

    def __init__(self, parent=None):
        """初始化基础页面

        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        self.config = {}
        self.setup_ui()

    @abstractmethod
    def setup_ui(self):
        """设置UI界面（子类必须实现）"""
        pass

    @abstractmethod
    def load_settings(self, config: dict):
        """从配置加载设置到UI控件（子类必须实现）

        Args:
            config: 配置字典
        """
        pass

    @abstractmethod
    def save_settings(self) -> dict:
        """从UI控件保存设置到配置（子类必须实现）

        Returns:
            dict: 设置字典
        """
        pass

    def validate_settings(self) -> Tuple[bool, str]:
        """验证设置有效性

        Returns:
            tuple: (是否有效, 错误信息)
        """
        return True, ""

    def reset_to_defaults(self):
        """重置为默认设置"""
        pass

    def get_page_title(self) -> str:
        """获取页面标题

        Returns:
            str: 页面标题
        """
        return "设置页面"
