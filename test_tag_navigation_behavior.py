#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
标签导航功能行为测试 - 定义标签导航面板和文件筛选的预期行为

这个测试文件定义了标签导航系统应该具备的核心行为，
作为B004和B005任务的指导和验收标准。
"""

import os
import sys
import tempfile
import shutil
import pytest
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from smartvault.services.tag_service import TagService
from smartvault.services.file import FileService
from smartvault.data.database import Database


class TestTagNavigationBehavior:
    """标签导航功能行为测试类"""

    def setup_method(self):
        """测试前准备"""
        # 创建临时数据库
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = os.path.join(self.temp_dir, "test.db")
        self.db = Database(self.db_path)

        # 创建服务实例
        self.tag_service = TagService()
        self.tag_service._db = self.db  # 注入测试数据库

        self.file_service = FileService()
        self.file_service._db = self.db  # 注入测试数据库

        # 创建测试文件
        self.test_files = []
        for i in range(3):
            test_file = os.path.join(self.temp_dir, f"test{i+1}.txt")
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(f"测试文件{i+1}内容")
            self.test_files.append(test_file)

    def teardown_method(self):
        """测试后清理"""
        if hasattr(self, 'db') and self.db:
            self.db.close()
        if hasattr(self, 'temp_dir') and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

    def test_get_tag_tree_structure(self):
        """测试获取标签树结构

        行为期望：
        1. 可以获取层级化的标签树结构
        2. 父标签包含子标签列表
        3. 标签显示使用统计（文件数量）
        """
        # 创建层级标签
        work_tag_id = self.tag_service.create_tag("工作", "#FF9800")
        project_a_id = self.tag_service.create_tag("项目A", "#2196F3", work_tag_id)
        project_b_id = self.tag_service.create_tag("项目B", "#4CAF50", work_tag_id)
        personal_tag_id = self.tag_service.create_tag("个人", "#9C27B0")

        # 添加文件并关联标签
        file1_id = self.file_service.add_file(self.test_files[0], "link")
        file2_id = self.file_service.add_file(self.test_files[1], "link")
        file3_id = self.file_service.add_file(self.test_files[2], "link")

        self.tag_service.add_tag_to_file(file1_id, work_tag_id)
        self.tag_service.add_tag_to_file(file1_id, project_a_id)
        self.tag_service.add_tag_to_file(file2_id, project_b_id)
        self.tag_service.add_tag_to_file(file3_id, personal_tag_id)

        # 获取标签树结构
        tag_tree = self.tag_service.get_tag_tree()

        # 验证树结构
        assert len(tag_tree) == 2  # 两个根标签：工作、个人

        # 找到工作标签
        work_node = next((node for node in tag_tree if node["name"] == "工作"), None)
        assert work_node is not None
        assert work_node["file_count"] == 1  # 直接关联的文件数
        assert work_node["total_file_count"] == 3  # 包含子标签的总文件数：1(直接) + 1(项目A) + 1(项目B)
        assert len(work_node["children"]) == 2  # 两个子标签

        # 验证子标签
        project_a_node = next((child for child in work_node["children"] if child["name"] == "项目A"), None)
        assert project_a_node is not None
        assert project_a_node["file_count"] == 1

        # 找到个人标签
        personal_node = next((node for node in tag_tree if node["name"] == "个人"), None)
        assert personal_node is not None
        assert personal_node["file_count"] == 1
        assert len(personal_node["children"]) == 0

    def test_filter_files_by_tag(self):
        """测试按标签筛选文件

        行为期望：
        1. 选择标签后只显示有该标签的文件
        2. 支持层级标签筛选（父标签包含子标签的文件）
        3. 可以清除筛选条件显示所有文件
        """
        # 创建标签和文件
        work_tag_id = self.tag_service.create_tag("工作", "#FF9800")
        personal_tag_id = self.tag_service.create_tag("个人", "#9C27B0")

        file1_id = self.file_service.add_file(self.test_files[0], "link")
        file2_id = self.file_service.add_file(self.test_files[1], "link")
        file3_id = self.file_service.add_file(self.test_files[2], "link")

        # 为文件添加标签
        self.tag_service.add_tag_to_file(file1_id, work_tag_id)
        self.tag_service.add_tag_to_file(file2_id, work_tag_id)
        self.tag_service.add_tag_to_file(file3_id, personal_tag_id)

        # 按工作标签筛选
        work_files = self.tag_service.get_files_by_tag(work_tag_id)
        assert len(work_files) == 2
        work_file_ids = [f["id"] for f in work_files]
        assert file1_id in work_file_ids
        assert file2_id in work_file_ids
        assert file3_id not in work_file_ids

        # 按个人标签筛选
        personal_files = self.tag_service.get_files_by_tag(personal_tag_id)
        assert len(personal_files) == 1
        assert personal_files[0]["id"] == file3_id

        # 获取所有文件（无筛选）
        all_files = self.file_service.get_files()
        assert len(all_files) == 3

    def test_hierarchical_tag_filtering(self):
        """测试层级标签筛选

        行为期望：
        1. 选择父标签时显示所有子标签的文件
        2. 选择子标签时只显示该子标签的文件
        3. 标签统计正确反映层级关系
        """
        # 创建层级标签
        work_tag_id = self.tag_service.create_tag("工作", "#FF9800")
        project_a_id = self.tag_service.create_tag("项目A", "#2196F3", work_tag_id)
        project_b_id = self.tag_service.create_tag("项目B", "#4CAF50", work_tag_id)

        # 添加文件并关联标签
        file1_id = self.file_service.add_file(self.test_files[0], "link")
        file2_id = self.file_service.add_file(self.test_files[1], "link")
        file3_id = self.file_service.add_file(self.test_files[2], "link")

        # 文件1：工作 + 项目A
        self.tag_service.add_tag_to_file(file1_id, work_tag_id)
        self.tag_service.add_tag_to_file(file1_id, project_a_id)

        # 文件2：项目B（自动继承工作标签的关联）
        self.tag_service.add_tag_to_file(file2_id, project_b_id)

        # 文件3：只有工作标签
        self.tag_service.add_tag_to_file(file3_id, work_tag_id)

        # 测试层级筛选
        work_files = self.tag_service.get_files_by_tag_hierarchy(work_tag_id)
        assert len(work_files) == 3  # 所有与工作相关的文件

        project_a_files = self.tag_service.get_files_by_tag(project_a_id)
        assert len(project_a_files) == 1  # 只有项目A的文件
        assert project_a_files[0]["id"] == file1_id

        project_b_files = self.tag_service.get_files_by_tag(project_b_id)
        assert len(project_b_files) == 1  # 只有项目B的文件
        assert project_b_files[0]["id"] == file2_id

    def test_tag_search_and_filtering(self):
        """测试标签搜索和筛选组合

        行为期望：
        1. 可以在标签筛选的基础上进行文本搜索
        2. 搜索结果只在当前标签筛选的文件中进行
        3. 可以组合多个筛选条件
        """
        # 创建标签
        work_tag_id = self.tag_service.create_tag("工作", "#FF9800")

        # 创建不同名称的测试文件
        doc_file = os.path.join(self.temp_dir, "工作文档.txt")
        report_file = os.path.join(self.temp_dir, "工作报告.txt")
        other_file = os.path.join(self.temp_dir, "其他文件.txt")

        with open(doc_file, 'w', encoding='utf-8') as f:
            f.write("工作文档内容")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("工作报告内容")
        with open(other_file, 'w', encoding='utf-8') as f:
            f.write("其他文件内容")

        # 添加文件
        doc_id = self.file_service.add_file(doc_file, "link")
        report_id = self.file_service.add_file(report_file, "link")
        other_id = self.file_service.add_file(other_file, "link")

        # 为工作文件添加标签
        self.tag_service.add_tag_to_file(doc_id, work_tag_id)
        self.tag_service.add_tag_to_file(report_id, work_tag_id)
        # other_file 不添加工作标签

        # 测试组合筛选：工作标签 + 文档关键词
        work_files = self.tag_service.get_files_by_tag(work_tag_id)
        work_file_ids = [f["id"] for f in work_files]

        # 在工作文件中搜索包含"文档"的文件
        doc_files = [f for f in work_files if "文档" in f["name"]]
        assert len(doc_files) == 1
        assert doc_files[0]["id"] == doc_id

        # 验证筛选正确性
        assert doc_id in work_file_ids
        assert report_id in work_file_ids
        assert other_id not in work_file_ids  # 其他文件没有工作标签

    def test_tag_navigation_state_management(self):
        """测试标签导航状态管理

        行为期望：
        1. 记住当前选中的标签
        2. 支持标签选择的撤销/重做
        3. 标签筛选状态与搜索状态独立管理
        """
        # 创建标签
        tag1_id = self.tag_service.create_tag("标签1", "#FF5722")
        tag2_id = self.tag_service.create_tag("标签2", "#2196F3")

        # 添加文件
        file1_id = self.file_service.add_file(self.test_files[0], "link")
        file2_id = self.file_service.add_file(self.test_files[1], "link")

        self.tag_service.add_tag_to_file(file1_id, tag1_id)
        self.tag_service.add_tag_to_file(file2_id, tag2_id)

        # 模拟导航状态管理
        navigation_state = {
            "selected_tag_id": None,
            "search_keyword": None,
            "filter_history": []
        }

        # 选择标签1
        navigation_state["selected_tag_id"] = tag1_id
        navigation_state["filter_history"].append(("tag", tag1_id))

        filtered_files = self.tag_service.get_files_by_tag(navigation_state["selected_tag_id"])
        assert len(filtered_files) == 1
        assert filtered_files[0]["id"] == file1_id

        # 切换到标签2
        navigation_state["selected_tag_id"] = tag2_id
        navigation_state["filter_history"].append(("tag", tag2_id))

        filtered_files = self.tag_service.get_files_by_tag(navigation_state["selected_tag_id"])
        assert len(filtered_files) == 1
        assert filtered_files[0]["id"] == file2_id

        # 清除筛选
        navigation_state["selected_tag_id"] = None
        navigation_state["filter_history"].append(("clear", None))

        all_files = self.file_service.get_files()
        assert len(all_files) == 2

        # 验证历史记录
        assert len(navigation_state["filter_history"]) == 3
        assert navigation_state["filter_history"][0] == ("tag", tag1_id)
        assert navigation_state["filter_history"][1] == ("tag", tag2_id)
        assert navigation_state["filter_history"][2] == ("clear", None)


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
