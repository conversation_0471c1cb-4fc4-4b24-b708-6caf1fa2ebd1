#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
跨页面文件定位功能测试

测试重复文件悬浮窗的查看按钮能否正确定位到不在当前页面的文件
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from smartvault.services.file import FileService
from smartvault.ui.models.file_table_model import FileTableModel


def test_file_position_calculation():
    """测试文件位置计算功能"""
    print("=== 测试文件位置计算功能 ===")

    file_service = FileService()

    # 获取一些文件用于测试
    files = file_service.get_files(limit=10, offset=0)
    if not files:
        print("❌ 数据库中没有文件，无法测试")
        return

    print(f"📁 数据库中共有 {file_service.get_file_count()} 个文件")
    print(f"🔍 测试前 10 个文件的位置计算...")

    for i, file_info in enumerate(files):
        file_id = file_info['id']
        file_name = file_info['name']

        # 计算文件在列表中的位置
        position = file_service.get_file_position_in_list(file_id)

        print(f"  {i+1}. 文件: {file_name[:30]}...")
        print(f"     ID: {file_id}")
        print(f"     计算位置: {position}")
        print(f"     预期位置: {i} (因为按添加时间倒序排列)")

        if position == i:
            print(f"     ✅ 位置计算正确")
        else:
            print(f"     ⚠️ 位置计算可能有偏差")
        print()


def test_page_calculation():
    """测试页面计算功能"""
    print("=== 测试页面计算功能 ===")

    file_service = FileService()
    total_files = file_service.get_file_count()

    if total_files == 0:
        print("❌ 数据库中没有文件，无法测试")
        return

    print(f"📁 数据库中共有 {total_files} 个文件")

    # 测试不同页面大小的计算
    page_sizes = [50, 100, 200]

    for page_size in page_sizes:
        print(f"\n📄 页面大小: {page_size}")
        total_pages = (total_files + page_size - 1) // page_size
        print(f"   总页数: {total_pages}")

        # 测试几个文件的页面计算
        test_positions = [0, page_size//2, page_size-1, page_size, total_files-1]

        for pos in test_positions:
            if pos < total_files:
                target_page = pos // page_size
                row_in_page = pos % page_size
                print(f"   位置 {pos} -> 第 {target_page + 1} 页, 行 {row_in_page}")


def test_cross_page_location_simulation():
    """模拟跨页面定位功能"""
    print("=== 模拟跨页面定位功能 ===")

    file_service = FileService()

    # 获取一些文件用于测试
    files = file_service.get_files(limit=5, offset=50)  # 获取第51-55个文件
    if not files:
        print("❌ 数据库中文件不足，无法测试跨页面定位")
        return

    print(f"🎯 模拟定位第51-55个文件（通常不在第一页）")

    for file_info in files:
        file_id = file_info['id']
        file_name = file_info['name']

        print(f"\n📍 定位文件: {file_name[:40]}...")
        print(f"   文件ID: {file_id}")

        # 计算文件位置
        position = file_service.get_file_position_in_list(file_id)
        print(f"   文件位置: {position}")

        # 模拟不同页面大小的定位
        page_sizes = [50, 100]
        for page_size in page_sizes:
            if page_size >= 999999:  # "全部"选项
                target_page = 0
                row_in_page = position
            else:
                target_page = position // page_size
                row_in_page = position % page_size

            print(f"   页面大小 {page_size}: 第 {target_page + 1} 页, 行 {row_in_page}")

        print(f"   ✅ 定位计算完成")


def test_search_condition_impact():
    """测试搜索条件对文件位置的影响"""
    print("=== 测试搜索条件对文件位置的影响 ===")

    file_service = FileService()

    # 获取一个文件用于测试
    files = file_service.get_files(limit=1, offset=10)
    if not files:
        print("❌ 数据库中文件不足，无法测试")
        return

    test_file = files[0]
    file_id = test_file['id']
    file_name = test_file['name']

    print(f"🔍 测试文件: {file_name}")
    print(f"   文件ID: {file_id}")

    # 测试无搜索条件的位置
    position_no_search = file_service.get_file_position_in_list(file_id)
    print(f"   无搜索条件位置: {position_no_search}")

    # 测试文件名搜索的位置
    search_keyword = file_name[:3]  # 使用文件名前3个字符搜索
    position_with_search = file_service.get_file_position_in_list(
        file_id,
        search_keyword=search_keyword,
        search_column=0
    )
    print(f"   搜索'{search_keyword}'时位置: {position_with_search}")

    if position_with_search != -1:
        print(f"   ✅ 搜索条件下文件仍可定位")
    else:
        print(f"   ⚠️ 搜索条件下文件不可见")


def main():
    """主测试函数"""
    print("🚀 开始跨页面文件定位功能测试")
    print("=" * 60)

    try:
        # 测试1: 文件位置计算
        test_file_position_calculation()
        print("\n" + "=" * 60)

        # 测试2: 页面计算
        test_page_calculation()
        print("\n" + "=" * 60)

        # 测试3: 跨页面定位模拟
        test_cross_page_location_simulation()
        print("\n" + "=" * 60)

        # 测试4: 搜索条件影响
        test_search_condition_impact()
        print("\n" + "=" * 60)

        print("✅ 所有测试完成")

        print("\n📋 功能说明:")
        print("1. get_file_position_in_list() - 计算文件在排序列表中的位置")
        print("2. 支持搜索和筛选条件下的位置计算")
        print("3. 可以根据位置计算目标页面和页内行号")
        print("4. 主窗口的 _locate_file_across_pages() 使用这些功能实现跨页面定位")

        print("\n🎯 使用方法:")
        print("1. 点击剪贴板悬浮窗的'查看'按钮")
        print("2. 系统首先在当前页面查找文件")
        print("3. 如果没找到，自动计算文件所在页面")
        print("4. 跳转到目标页面并选中文件")

    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
