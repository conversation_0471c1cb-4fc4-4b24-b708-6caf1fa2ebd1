#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试剪贴板清洗规则启用/禁用功能修复

验证设置页面的"启用文件名清洗"复选框能够正确控制清洗规则的应用
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from smartvault.utils.config import load_config, save_config


def test_cleaning_rules_control():
    """测试清洗规则的启用/禁用控制"""
    print("=== 测试剪贴板清洗规则启用/禁用功能 ===")
    
    # 测试用例
    test_text = "测试文件 - 下载 (1.2MB) [网站标记].pdf"
    print(f"测试文本: '{test_text}'")
    
    # 保存原始配置
    original_config = load_config()
    
    try:
        # 测试1: 清洗功能启用时
        print("\n--- 测试1: 清洗功能启用时 ---")
        
        # 修改配置：启用清洗功能
        config = load_config()
        config["clipboard"]["filename_cleaning"]["enabled"] = True
        save_config(config)
        
        # 重新创建服务实例以加载新配置
        from smartvault.services.clipboard_monitor_service import ClipboardMonitorService
        service = ClipboardMonitorService()
        
        # 检查清洗规则是否被加载
        print(f"加载的清洗规则数量: {len(service.cleaning_rules)}")
        
        if service.cleaning_rules:
            print("启用的清洗规则:")
            for rule in service.cleaning_rules:
                print(f"  - {rule.get('name', '未知')}")
        
        # 测试清洗效果
        cleaned_text = service._clean_filename(test_text)
        print(f"清洗前: '{test_text}'")
        print(f"清洗后: '{cleaned_text}'")
        
        expected_cleaned = "测试文件.pdf"  # 预期清洗后的结果
        if cleaned_text == expected_cleaned:
            print("✅ 清洗功能正常工作")
        else:
            print(f"⚠️ 清洗结果与预期不符，预期: '{expected_cleaned}'")
        
        # 测试2: 清洗功能禁用时
        print("\n--- 测试2: 清洗功能禁用时 ---")
        
        # 修改配置：禁用清洗功能
        config["clipboard"]["filename_cleaning"]["enabled"] = False
        save_config(config)
        
        # 重新创建服务实例以加载新配置
        service = ClipboardMonitorService()
        
        # 检查清洗规则是否被清空
        print(f"加载的清洗规则数量: {len(service.cleaning_rules)}")
        
        if not service.cleaning_rules:
            print("✅ 清洗规则已被清空（功能禁用）")
        else:
            print("❌ 清洗规则仍然存在（功能应该被禁用）")
            for rule in service.cleaning_rules:
                print(f"  - {rule.get('name', '未知')}")
        
        # 测试清洗效果
        cleaned_text = service._clean_filename(test_text)
        print(f"清洗前: '{test_text}'")
        print(f"清洗后: '{cleaned_text}'")
        
        # 禁用清洗时，应该只做基本处理（去除多余空格，长度检查）
        expected_no_cleaning = test_text.strip()  # 预期不清洗的结果
        if cleaned_text == expected_no_cleaning:
            print("✅ 清洗功能已正确禁用")
        else:
            print(f"⚠️ 清洗功能可能仍在工作，预期: '{expected_no_cleaning}'")
        
        # 测试3: 部分规则禁用
        print("\n--- 测试3: 部分规则禁用 ---")
        
        # 修改配置：启用清洗功能，但禁用部分规则
        config["clipboard"]["filename_cleaning"]["enabled"] = True
        rules = config["clipboard"]["filename_cleaning"]["rules"]
        
        # 禁用"移除下载后缀"规则
        for rule in rules:
            if rule["name"] == "移除下载后缀":
                rule["enabled"] = False
                print(f"禁用规则: {rule['name']}")
        
        save_config(config)
        
        # 重新创建服务实例
        service = ClipboardMonitorService()
        
        print(f"加载的清洗规则数量: {len(service.cleaning_rules)}")
        print("启用的清洗规则:")
        for rule in service.cleaning_rules:
            print(f"  - {rule.get('name', '未知')}")
        
        # 测试清洗效果
        cleaned_text = service._clean_filename(test_text)
        print(f"清洗前: '{test_text}'")
        print(f"清洗后: '{cleaned_text}'")
        
        # 应该保留"- 下载"部分，但移除其他部分
        if "- 下载" in cleaned_text:
            print("✅ 部分规则禁用功能正常工作")
        else:
            print("⚠️ 部分规则禁用可能有问题")
        
    finally:
        # 恢复原始配置
        save_config(original_config)
        print("\n✅ 已恢复原始配置")


def test_config_reload():
    """测试配置重新加载功能"""
    print("\n=== 测试配置重新加载功能 ===")
    
    # 保存原始配置
    original_config = load_config()
    
    try:
        # 创建服务实例
        from smartvault.services.clipboard_monitor_service import ClipboardMonitorService
        service = ClipboardMonitorService()
        
        initial_rules_count = len(service.cleaning_rules)
        print(f"初始清洗规则数量: {initial_rules_count}")
        
        # 修改配置文件
        config = load_config()
        config["clipboard"]["filename_cleaning"]["enabled"] = False
        save_config(config)
        
        # 重新加载配置
        service.reload_config()
        
        reloaded_rules_count = len(service.cleaning_rules)
        print(f"重新加载后清洗规则数量: {reloaded_rules_count}")
        
        if reloaded_rules_count == 0:
            print("✅ 配置重新加载功能正常工作")
        else:
            print("❌ 配置重新加载功能可能有问题")
        
    finally:
        # 恢复原始配置
        save_config(original_config)
        print("✅ 已恢复原始配置")


def main():
    """主测试函数"""
    print("🚀 开始测试剪贴板清洗规则修复")
    print("=" * 60)
    
    try:
        # 测试1: 清洗规则控制
        test_cleaning_rules_control()
        print("\n" + "=" * 60)
        
        # 测试2: 配置重新加载
        test_config_reload()
        print("\n" + "=" * 60)
        
        print("✅ 所有测试完成")
        
        print("\n📋 修复总结:")
        print("1. ✅ 修复了清洗规则启用/禁用控制")
        print("   - 检查 filename_cleaning.enabled 配置项")
        print("   - 禁用时不加载任何清洗规则")
        print("   - 启用时只加载enabled=True的规则")
        
        print("\n2. ✅ 改进了配置重新加载功能")
        print("   - reload_config() 方法会重新检查清洗功能状态")
        print("   - 支持运行时动态启用/禁用清洗功能")
        
        print("\n🎯 使用方法:")
        print("1. 在设置页面取消'启用文件名清洗'复选框")
        print("2. 保存设置后，剪贴板查重将使用原始文本进行查询")
        print("3. 重新勾选复选框可恢复清洗功能")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
