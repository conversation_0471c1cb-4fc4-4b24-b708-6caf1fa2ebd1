#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
标签系统集成测试 - 测试标签管理功能在主界面中的集成
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer

from smartvault.ui.main_window import MainWindow
from smartvault.services.tag_service import TagService


def setup_test_data():
    """设置测试数据"""
    print("🏷️  设置测试标签数据...")
    
    tag_service = TagService()
    
    # 创建一些测试标签
    work_tag = tag_service.create_tag("工作文档", "#FF9800")
    personal_tag = tag_service.create_tag("个人文件", "#4CAF50")
    important_tag = tag_service.create_tag("重要", "#F44336")
    project_tag = tag_service.create_tag("项目A", "#2196F3")
    archive_tag = tag_service.create_tag("归档", "#9E9E9E")
    
    print(f"   ✅ 创建了 5 个测试标签")
    return [work_tag, personal_tag, important_tag, project_tag, archive_tag]


def test_tag_menu_integration():
    """测试标签管理菜单集成"""
    print("🖥️  测试标签管理菜单集成...")
    
    app = QApplication(sys.argv)
    
    # 设置测试数据
    setup_test_data()
    
    # 创建主窗口
    main_window = MainWindow()
    main_window.show()
    
    print("   💡 主窗口已显示，你可以测试以下功能:")
    print("      • 点击菜单 '工具' -> '标签管理'")
    print("      • 在标签管理对话框中查看、创建、删除标签")
    print("      • 测试标签统计和详情显示")
    
    # 设置一个定时器来自动打开标签管理对话框（演示用）
    def auto_open_tag_dialog():
        print("🤖 自动打开标签管理对话框...")
        try:
            main_window.on_tag_management()
        except Exception as e:
            print(f"❌ 自动打开失败: {e}")
    
    # 3秒后自动打开标签管理对话框
    QTimer.singleShot(3000, auto_open_tag_dialog)
    
    # 运行应用
    sys.exit(app.exec())


if __name__ == "__main__":
    test_tag_menu_integration()
