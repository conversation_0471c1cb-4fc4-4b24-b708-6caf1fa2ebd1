#!/usr/bin/env python3
"""
测试文件视图选中效果的改进
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QHBoxLayout, QPushButton
from PySide6.QtCore import Qt

from smartvault.ui.views.file_table_view import FileTableView, ViewMode
from smartvault.ui.themes import theme_manager


class TestWindow(QMainWindow):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("文件视图选中效果测试")
        self.setGeometry(100, 100, 1000, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建主题切换按钮
        theme_layout = QHBoxLayout()
        
        light_btn = QPushButton("浅色主题")
        light_btn.clicked.connect(lambda: self.switch_theme("light"))
        theme_layout.addWidget(light_btn)
        
        dark_btn = QPushButton("深色主题")
        dark_btn.clicked.connect(lambda: self.switch_theme("dark"))
        theme_layout.addWidget(dark_btn)
        
        blue_btn = QPushButton("蓝色主题")
        blue_btn.clicked.connect(lambda: self.switch_theme("blue"))
        theme_layout.addWidget(blue_btn)
        
        green_btn = QPushButton("绿色主题")
        green_btn.clicked.connect(lambda: self.switch_theme("green"))
        theme_layout.addWidget(green_btn)
        
        theme_layout.addStretch()
        layout.addLayout(theme_layout)
        
        # 创建视图切换按钮
        view_layout = QHBoxLayout()
        
        table_btn = QPushButton("表格视图")
        table_btn.clicked.connect(lambda: self.switch_view(ViewMode.TABLE))
        view_layout.addWidget(table_btn)
        
        grid_btn = QPushButton("网格视图")
        grid_btn.clicked.connect(lambda: self.switch_view(ViewMode.GRID))
        view_layout.addWidget(grid_btn)
        
        view_layout.addStretch()
        layout.addLayout(view_layout)
        
        # 创建文件视图
        self.file_view = FileTableView()
        layout.addWidget(self.file_view)
        
        # 加载测试数据
        self.load_test_data()
        
        # 应用默认主题
        theme_manager.apply_theme("light")
    
    def switch_theme(self, theme_name):
        """切换主题"""
        theme_manager.set_theme(theme_name)
        print(f"切换到{theme_name}主题")
    
    def switch_view(self, view_mode):
        """切换视图模式"""
        self.file_view.switch_view_mode(view_mode)
        print(f"切换到{view_mode}视图")
    
    def load_test_data(self):
        """加载测试数据"""
        # 创建一些测试文件数据
        test_files = []
        for i in range(20):
            test_files.append({
                'id': i + 1,
                'name': f'测试文件_{i+1:02d}.txt',
                'size': 1024 * (i + 1),
                'modified_time': '2024-01-01 12:00:00',
                'entry_type': 'copy',
                'library_path': f'/test/path/测试文件_{i+1:02d}.txt',
                'original_path': f'/original/path/测试文件_{i+1:02d}.txt',
                'staging_status': 'normal',
                'tags': []
            })
        
        # 设置测试数据
        if hasattr(self.file_view, 'load_files'):
            self.file_view.load_files(test_files)
        else:
            # 直接设置模型数据
            current_view = self.file_view.get_current_view()
            if hasattr(current_view, 'model') and hasattr(current_view.model, 'set_files'):
                current_view.model.set_files(test_files)


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("SmartVault Selection Test")
    app.setApplicationVersion("1.0")
    
    # 创建测试窗口
    window = TestWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
