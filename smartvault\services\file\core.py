"""
文件服务核心功能
"""

import os
from smartvault.data.database import Database
from smartvault.data.file_system import FileSystem


class FileServiceCore:
    """文件服务核心类"""

    def __init__(self):
        """初始化文件服务"""
        self._db = None
        self.file_system = FileSystem()

    @property
    def db(self):
        """获取数据库实例，支持延迟初始化"""
        if self._db is None:
            self._db = Database.create_from_config()
        return self._db

    def switch_library(self, new_library_path):
        """切换到新的文件库

        Args:
            new_library_path: 新文件库路径
        """
        print(f"切换文件库: {new_library_path}")

        # 构建新的数据库路径
        new_db_path = os.path.join(new_library_path, "data", "smartvault.db")

        if self._db:
            # 重新连接到新数据库
            self._db.reconnect(new_db_path)
        else:
            # 创建新的数据库连接
            self._db = Database(new_db_path)

        # 更新文件系统路径
        self.file_system.library_path = new_library_path

        print(f"文件库切换完成: {new_library_path}")

    def reload_database_connection(self):
        """重新加载数据库连接（从配置文件）"""
        if self._db:
            self._db.close()
            self._db = None
        # 下次访问db属性时会重新创建连接

    def get_file_by_id(self, file_id):
        """根据ID获取文件

        Args:
            file_id: 文件ID

        Returns:
            dict: 文件信息字典，如果文件不存在则返回None
        """
        cursor = self.db.conn.cursor()
        cursor.execute("SELECT * FROM files WHERE id = ?", (file_id,))
        row = cursor.fetchone()

        if row:
            return dict(row)

        return None

    def update_file_note(self, file_id, note):
        """更新文件备注

        Args:
            file_id: 文件ID
            note: 备注内容

        Returns:
            bool: 是否成功
        """
        cursor = self.db.conn.cursor()
        cursor.execute(
            "UPDATE files SET note = ? WHERE id = ?",
            (note, file_id)
        )
        self.db.conn.commit()
        return cursor.rowcount > 0

    def get_file_note(self, file_id):
        """获取文件备注

        Args:
            file_id: 文件ID

        Returns:
            str: 备注内容，如果没有则返回空字符串
        """
        cursor = self.db.conn.cursor()
        cursor.execute("SELECT note FROM files WHERE id = ?", (file_id,))
        result = cursor.fetchone()
        return result['note'] if result and result['note'] else ""

    def get_files(self, limit=100, offset=0, search_keyword=None, search_column=None,
                  folder_filter_type=None, folder_filter_value=None):
        """获取文件列表

        Args:
            limit: 返回的最大文件数
            offset: 起始偏移量
            search_keyword: 搜索关键词
            search_column: 搜索列（0=名称，1=类型，2=位置，-1=全部）
            folder_filter_type: 文件夹筛选类型 ("recent", "staging", "type", "entry", "time")
            folder_filter_value: 文件夹筛选值

        Returns:
            list: 文件信息字典列表
        """
        cursor = self.db.conn.cursor()

        # 构建基本查询
        sql = "SELECT * FROM files"
        params = []
        where_conditions = []

        # 添加文件夹筛选条件
        if folder_filter_type:
            if folder_filter_type == "recent":
                # 最近7天添加的正式文件（非中转状态）
                where_conditions.append("added_at >= datetime('now', '-7 days')")
                where_conditions.append("staging_status = 'normal'")
            elif folder_filter_type == "staging":
                # 中转文件夹中的文件
                where_conditions.append("staging_status = 'staging'")
            elif folder_filter_type == "type" and folder_filter_value:
                # 按文件类型筛选
                if folder_filter_value == "document":
                    where_conditions.append("(name LIKE '%.doc' OR name LIKE '%.docx' OR name LIKE '%.pdf' OR name LIKE '%.txt' OR name LIKE '%.rtf')")
                elif folder_filter_value == "image":
                    where_conditions.append("(name LIKE '%.jpg' OR name LIKE '%.jpeg' OR name LIKE '%.png' OR name LIKE '%.gif' OR name LIKE '%.bmp' OR name LIKE '%.svg')")
                elif folder_filter_value == "audio":
                    where_conditions.append("(name LIKE '%.mp3' OR name LIKE '%.wav' OR name LIKE '%.flac' OR name LIKE '%.aac' OR name LIKE '%.ogg')")
                elif folder_filter_value == "video":
                    where_conditions.append("(name LIKE '%.mp4' OR name LIKE '%.avi' OR name LIKE '%.mkv' OR name LIKE '%.mov' OR name LIKE '%.wmv')")
                elif folder_filter_value == "other":
                    where_conditions.append("NOT (name LIKE '%.doc' OR name LIKE '%.docx' OR name LIKE '%.pdf' OR name LIKE '%.txt' OR name LIKE '%.rtf' OR name LIKE '%.jpg' OR name LIKE '%.jpeg' OR name LIKE '%.png' OR name LIKE '%.gif' OR name LIKE '%.bmp' OR name LIKE '%.svg' OR name LIKE '%.mp3' OR name LIKE '%.wav' OR name LIKE '%.flac' OR name LIKE '%.aac' OR name LIKE '%.ogg' OR name LIKE '%.mp4' OR name LIKE '%.avi' OR name LIKE '%.mkv' OR name LIKE '%.mov' OR name LIKE '%.wmv')")
            elif folder_filter_type == "entry" and folder_filter_value:
                # 按入库方式筛选
                where_conditions.append("entry_type = ?")
                params.append(folder_filter_value)
            elif folder_filter_type == "time" and folder_filter_value:
                # 按时间筛选
                if folder_filter_value == "today":
                    where_conditions.append("added_at >= datetime('now', 'start of day')")
                elif folder_filter_value == "week":
                    where_conditions.append("added_at >= datetime('now', '-7 days')")
                elif folder_filter_value == "month":
                    where_conditions.append("added_at >= datetime('now', 'start of month')")
                elif folder_filter_value == "older":
                    where_conditions.append("added_at < datetime('now', 'start of month')")

        # 添加搜索条件
        if search_keyword:
            if search_column == 0:  # 名称
                where_conditions.append("name LIKE ?")
                params.append(f"%{search_keyword}%")
            elif search_column == 1:  # 类型（扩展名）
                where_conditions.append("name LIKE ?")
                params.append(f"%.{search_keyword}%")
            elif search_column == 2:  # 位置
                if search_keyword.lower() in "库内":
                    where_conditions.append("entry_type IN ('copy', 'move')")
                elif search_keyword.lower() in "库外":
                    where_conditions.append("entry_type = 'link'")
                else:
                    where_conditions.append("(original_path LIKE ? OR library_path LIKE ?)")
                    params.extend([f"%{search_keyword}%", f"%{search_keyword}%"])
            else:  # 全部列
                where_conditions.append("(name LIKE ? OR original_path LIKE ? OR library_path LIKE ?)")
                params.extend([f"%{search_keyword}%", f"%{search_keyword}%", f"%{search_keyword}%"])

        # 组合WHERE条件
        if where_conditions:
            sql += " WHERE " + " AND ".join(where_conditions)

        # 添加排序和分页
        sql += " ORDER BY added_at DESC LIMIT ? OFFSET ?"
        params.extend([limit, offset])

        cursor.execute(sql, params)
        return [dict(row) for row in cursor.fetchall()]

    def get_file_position_in_list(self, file_id, search_keyword=None, search_column=None,
                                  folder_filter_type=None, folder_filter_value=None):
        """获取文件在排序列表中的位置（用于分页定位）

        Args:
            file_id: 文件ID
            search_keyword: 搜索关键词
            search_column: 搜索列（0=名称，1=类型，2=位置，-1=全部）
            folder_filter_type: 文件夹筛选类型 ("recent", "staging", "type", "entry", "time")
            folder_filter_value: 文件夹筛选值

        Returns:
            int: 文件在列表中的位置（从0开始），如果文件不存在则返回-1
        """
        cursor = self.db.conn.cursor()

        # 首先获取目标文件的添加时间
        cursor.execute("SELECT added_at FROM files WHERE id = ?", (file_id,))
        result = cursor.fetchone()
        if not result:
            return -1

        target_added_at = result[0]

        # 构建查询条件（与get_files方法保持一致）
        where_conditions = []
        params = []

        # 文件夹筛选条件
        if folder_filter_type == "recent":
            # 最近添加的文件（7天内）
            where_conditions.append("added_at >= datetime('now', '-7 days')")
        elif folder_filter_type == "staging":
            # 中转文件夹
            where_conditions.append("staging_status = 'staging'")
        elif folder_filter_type == "type" and folder_filter_value:
            # 按文件类型筛选
            where_conditions.append("LOWER(SUBSTR(name, INSTR(name, '.') + 1)) = LOWER(?)")
            params.append(folder_filter_value)
        elif folder_filter_type == "entry" and folder_filter_value:
            # 按入库方式筛选
            where_conditions.append("entry_type = ?")
            params.append(folder_filter_value)
        elif folder_filter_type == "time" and folder_filter_value:
            # 按时间范围筛选
            if folder_filter_value == "today":
                where_conditions.append("DATE(added_at) = DATE('now')")
            elif folder_filter_value == "week":
                where_conditions.append("added_at >= datetime('now', '-7 days')")
            elif folder_filter_value == "month":
                where_conditions.append("added_at >= datetime('now', '-30 days')")

        # 搜索条件
        if search_keyword and search_column is not None:
            if search_column == 0:  # 名称
                where_conditions.append("name LIKE ?")
                params.append(f"%{search_keyword}%")
            elif search_column == 1:  # 类型
                where_conditions.append("LOWER(SUBSTR(name, INSTR(name, '.') + 1)) LIKE LOWER(?)")
                params.append(f"%{search_keyword}%")
            elif search_column == 2:  # 位置
                where_conditions.append("library_path LIKE ?")
                params.append(f"%{search_keyword}%")
            elif search_column == -1:  # 全部
                where_conditions.append("(name LIKE ? OR library_path LIKE ? OR LOWER(SUBSTR(name, INSTR(name, '.') + 1)) LIKE LOWER(?))")
                params.extend([f"%{search_keyword}%", f"%{search_keyword}%", f"%{search_keyword}%"])

        # 构建SQL查询 - 计算有多少文件的added_at大于目标文件
        sql = "SELECT COUNT(*) FROM files"
        if where_conditions:
            sql += " WHERE " + " AND ".join(where_conditions)
            sql += " AND added_at > ?"
        else:
            sql += " WHERE added_at > ?"

        params.append(target_added_at)

        cursor.execute(sql, params)
        position = cursor.fetchone()[0]

        return position

    def get_file_count(self, search_keyword=None, search_column=None,
                       folder_filter_type=None, folder_filter_value=None):
        """获取文件总数

        Args:
            search_keyword: 搜索关键词
            search_column: 搜索列（0=名称，1=类型，2=位置，-1=全部）
            folder_filter_type: 文件夹筛选类型 ("recent", "staging", "type", "entry", "time")
            folder_filter_value: 文件夹筛选值

        Returns:
            int: 文件总数
        """
        cursor = self.db.conn.cursor()

        try:
            # 构建基本查询
            sql = "SELECT COUNT(*) FROM files"
            params = []
            where_conditions = []

            # 添加文件夹筛选条件（与get_files方法保持一致）
            if folder_filter_type:
                if folder_filter_type == "recent":
                    where_conditions.append("added_at >= datetime('now', '-7 days')")
                    where_conditions.append("staging_status = 'normal'")
                elif folder_filter_type == "staging":
                    where_conditions.append("staging_status = 'staging'")
                elif folder_filter_type == "type" and folder_filter_value:
                    if folder_filter_value == "document":
                        where_conditions.append("(name LIKE '%.doc' OR name LIKE '%.docx' OR name LIKE '%.pdf' OR name LIKE '%.txt' OR name LIKE '%.rtf')")
                    elif folder_filter_value == "image":
                        where_conditions.append("(name LIKE '%.jpg' OR name LIKE '%.jpeg' OR name LIKE '%.png' OR name LIKE '%.gif' OR name LIKE '%.bmp' OR name LIKE '%.svg')")
                    elif folder_filter_value == "audio":
                        where_conditions.append("(name LIKE '%.mp3' OR name LIKE '%.wav' OR name LIKE '%.flac' OR name LIKE '%.aac' OR name LIKE '%.ogg')")
                    elif folder_filter_value == "video":
                        where_conditions.append("(name LIKE '%.mp4' OR name LIKE '%.avi' OR name LIKE '%.mkv' OR name LIKE '%.mov' OR name LIKE '%.wmv')")
                    elif folder_filter_value == "other":
                        where_conditions.append("NOT (name LIKE '%.doc' OR name LIKE '%.docx' OR name LIKE '%.pdf' OR name LIKE '%.txt' OR name LIKE '%.rtf' OR name LIKE '%.jpg' OR name LIKE '%.jpeg' OR name LIKE '%.png' OR name LIKE '%.gif' OR name LIKE '%.bmp' OR name LIKE '%.svg' OR name LIKE '%.mp3' OR name LIKE '%.wav' OR name LIKE '%.flac' OR name LIKE '%.aac' OR name LIKE '%.ogg' OR name LIKE '%.mp4' OR name LIKE '%.avi' OR name LIKE '%.mkv' OR name LIKE '%.mov' OR name LIKE '%.wmv')")
                elif folder_filter_type == "entry" and folder_filter_value:
                    where_conditions.append("entry_type = ?")
                    params.append(folder_filter_value)
                elif folder_filter_type == "time" and folder_filter_value:
                    if folder_filter_value == "today":
                        where_conditions.append("added_at >= datetime('now', 'start of day')")
                    elif folder_filter_value == "week":
                        where_conditions.append("added_at >= datetime('now', '-7 days')")
                    elif folder_filter_value == "month":
                        where_conditions.append("added_at >= datetime('now', 'start of month')")
                    elif folder_filter_value == "older":
                        where_conditions.append("added_at < datetime('now', 'start of month')")

            # 添加搜索条件
            if search_keyword:
                if search_column == 0:  # 名称
                    where_conditions.append("name LIKE ?")
                    params.append(f"%{search_keyword}%")
                elif search_column == 1:  # 类型（扩展名）
                    where_conditions.append("name LIKE ?")
                    params.append(f"%.{search_keyword}%")
                elif search_column == 2:  # 位置
                    if search_keyword.lower() in "库内":
                        where_conditions.append("entry_type IN ('copy', 'move')")
                    elif search_keyword.lower() in "库外":
                        where_conditions.append("entry_type = 'link'")
                    else:
                        where_conditions.append("(original_path LIKE ? OR library_path LIKE ?)")
                        params.extend([f"%{search_keyword}%", f"%{search_keyword}%"])
                else:  # 全部列
                    where_conditions.append("(name LIKE ? OR original_path LIKE ? OR library_path LIKE ?)")
                    params.extend([f"%{search_keyword}%", f"%{search_keyword}%", f"%{search_keyword}%"])

            # 组合WHERE条件
            if where_conditions:
                sql += " WHERE " + " AND ".join(where_conditions)

            cursor.execute(sql, params)
            total_files = cursor.fetchone()[0]
            return total_files
        except Exception as e:
            print(f"获取文件总数失败: {e}")
            return 0

    def get_library_stats(self):
        """获取智能文件库统计信息

        Returns:
            dict: 统计信息字典，包含文件数量、总大小等
        """
        cursor = self.db.conn.cursor()

        try:
            # 获取文件总数
            cursor.execute("SELECT COUNT(*) FROM files")
            total_files = cursor.fetchone()[0]

            # 获取总大小
            cursor.execute("SELECT SUM(size) FROM files")
            total_size = cursor.fetchone()[0] or 0

            # 按入库方式统计
            cursor.execute("SELECT entry_type, COUNT(*) FROM files GROUP BY entry_type")
            entry_type_stats = {}
            for row in cursor.fetchall():
                entry_type_stats[row[0]] = row[1]
        except Exception as e:
            print(f"获取统计信息失败: {e}")
            total_files = 0
            total_size = 0
            entry_type_stats = {}

        # 获取文件库路径
        library_path = self.file_system.library_path

        return {
            "total_files": total_files,
            "total_size": total_size,
            "entry_type_stats": entry_type_stats,
            "library_path": library_path
        }

    def get_folder_groups_in_staging(self):
        """获取中转文件夹中的文件夹组

        Returns:
            list: 文件夹组信息列表，每个组包含组ID、文件夹名称、文件数量等
        """
        cursor = self.db.conn.cursor()
        cursor.execute(
            """
            SELECT
                folder_group_id,
                folder_name,
                COUNT(*) as file_count,
                MIN(added_at) as first_added_at,
                MAX(added_at) as last_added_at
            FROM files
            WHERE staging_status = 'staging'
                AND folder_group_id IS NOT NULL
                AND folder_name IS NOT NULL
            GROUP BY folder_group_id, folder_name
            ORDER BY first_added_at DESC
            """
        )

        folder_groups = []
        for row in cursor.fetchall():
            folder_groups.append({
                'folder_group_id': row[0],
                'folder_name': row[1],
                'file_count': row[2],
                'first_added_at': row[3],
                'last_added_at': row[4],
                'type': 'folder_group'  # 标识这是一个文件夹组
            })

        return folder_groups

    def get_files_in_folder_group(self, folder_group_id):
        """获取指定文件夹组中的文件

        Args:
            folder_group_id: 文件夹组ID

        Returns:
            list: 文件信息字典列表
        """
        cursor = self.db.conn.cursor()
        cursor.execute(
            "SELECT * FROM files WHERE folder_group_id = ? ORDER BY name",
            (folder_group_id,)
        )
        return [dict(row) for row in cursor.fetchall()]

    def get_individual_staging_files(self):
        """获取中转文件夹中的单独文件（不属于任何文件夹组）

        Returns:
            list: 文件信息字典列表
        """
        cursor = self.db.conn.cursor()
        cursor.execute(
            """
            SELECT * FROM files
            WHERE staging_status = 'staging'
                AND (folder_group_id IS NULL OR folder_name IS NULL)
            ORDER BY added_at DESC
            """
        )
        return [dict(row) for row in cursor.fetchall()]

    def get_staging_display_items(self):
        """获取中转文件夹的显示项目（文件夹组 + 单独文件）- 简化高效版本

        Returns:
            list: 显示项目列表，包含文件夹组和单独文件
        """
        cursor = self.db.conn.cursor()

        # 🔧 最简优化：直接返回所有中转文件，让UI层处理分组显示
        # 这样避免了复杂的SQL GROUP BY操作，性能最佳
        cursor.execute(
            """
            SELECT
                id,
                name,
                size,
                entry_type,
                original_path,
                library_path,
                created_at,
                modified_at,
                added_at,
                is_available,
                'individual_file' as type,
                folder_group_id,
                folder_name,
                0 as file_count,
                added_at as first_added_at,
                added_at as last_added_at,
                staging_status
            FROM files
            WHERE staging_status = 'staging'
            ORDER BY added_at DESC
            LIMIT 100
            """
        )

        return [dict(row) for row in cursor.fetchall()]
