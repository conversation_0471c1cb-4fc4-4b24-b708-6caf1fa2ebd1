"""
快速标签菜单组件
用于在文件右键菜单中提供快速标签操作
"""

from PySide6.QtWidgets import QMenu, QMessageBox
from PySide6.QtCore import QObject, Signal
from PySide6.QtGui import QAction, QIcon, QPixmap, QPainter, QBrush, QColor
from smartvault.ui.localization import question, warning
from smartvault.services.tag_service import TagService


class QuickTagMenu(QObject):
    """快速标签菜单类"""

    # 信号：标签发生变化时发出
    tags_changed = Signal()

    def __init__(self, parent=None):
        """初始化快速标签菜单

        Args:
            parent: 父对象
        """
        super().__init__(parent)
        self.tag_service = TagService()
        self.file_ids = []
        self.file_tags_cache = {}  # 缓存文件标签信息

    def create_menu(self, parent_menu, file_ids):
        """创建快速标签菜单

        Args:
            parent_menu: 父菜单
            file_ids: 文件ID列表

        Returns:
            QMenu: 创建的标签菜单
        """
        self.file_ids = file_ids if isinstance(file_ids, list) else [file_ids]

        # 创建快速标签子菜单
        tag_menu = QMenu("快速标签", parent_menu)

        # 加载文件当前标签
        self._load_file_tags()

        # 获取所有标签并构建菜单
        try:
            # 获取顶级标签
            top_level_tags = self.tag_service.get_tags(parent_id=None)

            if not top_level_tags:
                # 没有标签时显示提示
                no_tags_action = QAction("暂无标签", tag_menu)
                no_tags_action.setEnabled(False)
                tag_menu.addAction(no_tags_action)

                tag_menu.addSeparator()

                # 添加管理标签选项
                manage_action = QAction("管理标签...", tag_menu)
                manage_action.triggered.connect(self._open_tag_management)
                tag_menu.addAction(manage_action)
            else:
                # 添加标签项
                self._add_tag_items(tag_menu, top_level_tags)

                tag_menu.addSeparator()

                # 添加管理选项
                manage_action = QAction("管理标签...", tag_menu)
                manage_action.triggered.connect(self._open_tag_management)
                tag_menu.addAction(manage_action)

                # 添加清除所有标签选项
                if self._has_any_tags():
                    clear_action = QAction("清除所有标签", tag_menu)
                    clear_action.triggered.connect(self._clear_all_tags)
                    tag_menu.addAction(clear_action)

        except Exception as e:
            # 错误处理
            error_action = QAction(f"加载标签失败: {e}", tag_menu)
            error_action.setEnabled(False)
            tag_menu.addAction(error_action)

        return tag_menu

    def _load_file_tags(self):
        """加载文件当前标签"""
        self.file_tags_cache = {}

        try:
            for file_id in self.file_ids:
                tags = self.tag_service.get_file_tags(file_id)
                self.file_tags_cache[file_id] = {tag['id']: tag for tag in tags}
        except Exception as e:
            print(f"加载文件标签失败: {e}")

    def _add_tag_items(self, menu, tags, level=0):
        """递归添加标签项到菜单

        Args:
            menu: 目标菜单
            tags: 标签列表
            level: 层级深度
        """
        for tag in tags:
            # 检查标签状态
            tag_status = self._get_tag_status(tag['id'])

            # 创建标签动作
            action_text = tag['name']
            if tag_status == 'all':
                action_text = f"✓ {action_text}"
            elif tag_status == 'partial':
                action_text = f"◐ {action_text}"

            action = QAction(action_text, menu)

            # 设置标签颜色图标
            if tag.get('color'):
                icon = self._create_color_icon(tag['color'])
                action.setIcon(icon)

            # 连接信号 - 使用functools.partial避免lambda闭包问题
            from functools import partial
            action.triggered.connect(
                partial(self._toggle_tag, tag['id'])
            )

            menu.addAction(action)

            # 添加子标签（最多支持3层）
            if level < 2:
                child_tags = self.tag_service.get_child_tags(tag['id'])
                if child_tags:
                    # 创建子菜单
                    child_menu = QMenu(tag['name'], menu)

                    # 在子菜单顶部添加父标签本身的操作
                    parent_action_text = f"应用标签: {tag['name']}"
                    if tag_status == 'all':
                        parent_action_text = f"✓ 应用标签: {tag['name']}"
                    elif tag_status == 'partial':
                        parent_action_text = f"◐ 应用标签: {tag['name']}"

                    parent_action = QAction(parent_action_text, child_menu)
                    if tag.get('color'):
                        parent_action.setIcon(self._create_color_icon(tag['color']))

                    parent_action.triggered.connect(
                        partial(self._toggle_tag, tag['id'])
                    )
                    child_menu.addAction(parent_action)
                    child_menu.addSeparator()

                    # 添加子标签
                    self._add_tag_items(child_menu, child_tags, level + 1)

                    # 替换原来的动作为子菜单
                    menu.removeAction(action)
                    menu.addMenu(child_menu)

    def _get_tag_status(self, tag_id):
        """获取标签在选中文件中的状态

        Args:
            tag_id: 标签ID

        Returns:
            str: 'none' - 无文件有此标签
                 'partial' - 部分文件有此标签
                 'all' - 所有文件都有此标签
        """
        if not self.file_ids:
            return 'none'

        has_tag_count = 0
        for file_id in self.file_ids:
            file_tags = self.file_tags_cache.get(file_id, {})
            if tag_id in file_tags:
                has_tag_count += 1

        if has_tag_count == 0:
            return 'none'
        elif has_tag_count == len(self.file_ids):
            return 'all'
        else:
            return 'partial'

    def _has_any_tags(self):
        """检查选中文件是否有任何标签"""
        for file_id in self.file_ids:
            if self.file_tags_cache.get(file_id):
                return True
        return False

    def _create_color_icon(self, color):
        """创建颜色图标

        Args:
            color: 颜色值

        Returns:
            QIcon: 颜色图标
        """
        pixmap = QPixmap(16, 16)
        pixmap.fill(QColor(color))

        # 添加边框
        painter = QPainter(pixmap)
        painter.setPen(QColor("#cccccc"))
        painter.drawRect(0, 0, 15, 15)
        painter.end()

        return QIcon(pixmap)

    def _toggle_tag(self, tag_id):
        """切换标签状态

        Args:
            tag_id: 标签ID
        """
        try:
            tag_status = self._get_tag_status(tag_id)

            if tag_status == 'all':
                # 所有文件都有此标签，移除标签
                self._remove_tag_from_files(tag_id)
            else:
                # 部分或无文件有此标签，添加标签
                self._add_tag_to_files(tag_id)

            # 发出标签变化信号
            self.tags_changed.emit()

        except Exception as e:
            warning(
                None,
                "标签操作失败",
                f"操作标签时发生错误: {e}"
            )

    def _add_tag_to_files(self, tag_id):
        """为文件添加标签

        Args:
            tag_id: 标签ID
        """
        for file_id in self.file_ids:
            try:
                self.tag_service.add_tag_to_file(file_id, tag_id)
                # 更新缓存
                if file_id not in self.file_tags_cache:
                    self.file_tags_cache[file_id] = {}
                tag = self.tag_service.get_tag_by_id(tag_id)
                if tag:
                    self.file_tags_cache[file_id][tag_id] = tag
            except ValueError as e:
                if "文件不存在" in str(e):
                    # 对于不存在的文件，直接添加到关联表（用于演示和测试）
                    try:
                        self._add_tag_to_file_direct(file_id, tag_id)
                        # 更新缓存
                        if file_id not in self.file_tags_cache:
                            self.file_tags_cache[file_id] = {}
                        tag = self.tag_service.get_tag_by_id(tag_id)
                        if tag:
                            self.file_tags_cache[file_id][tag_id] = tag
                    except Exception as e2:
                        print(f"为文件 {file_id} 直接添加标签失败: {e2}")
                else:
                    print(f"为文件 {file_id} 添加标签失败: {e}")
            except Exception as e:
                print(f"为文件 {file_id} 添加标签失败: {e}")

    def _add_tag_to_file_direct(self, file_id, tag_id):
        """直接为文件添加标签（跳过文件存在检查）

        Args:
            file_id: 文件ID
            tag_id: 标签ID
        """
        from datetime import datetime

        # 检查标签是否存在
        cursor = self.tag_service.db.conn.cursor()
        cursor.execute("SELECT * FROM tags WHERE id = ?", (tag_id,))
        tag = cursor.fetchone()

        if not tag:
            raise ValueError(f"标签不存在: {tag_id}")

        # 检查关联是否已存在
        cursor.execute(
            "SELECT * FROM file_tags WHERE file_id = ? AND tag_id = ?",
            (file_id, tag_id)
        )
        if cursor.fetchone():
            return True

        # 直接添加关联
        cursor.execute(
            """
            INSERT INTO file_tags (file_id, tag_id, added_at, note)
            VALUES (?, ?, ?, ?)
            """,
            (
                file_id,
                tag_id,
                datetime.now().isoformat(),
                None
            )
        )
        self.tag_service.db.conn.commit()
        return True

    def _remove_tag_from_files(self, tag_id):
        """从文件移除标签

        Args:
            tag_id: 标签ID
        """
        for file_id in self.file_ids:
            try:
                self.tag_service.remove_tag_from_file(file_id, tag_id)
                # 更新缓存
                if file_id in self.file_tags_cache:
                    self.file_tags_cache[file_id].pop(tag_id, None)
            except Exception as e:
                print(f"从文件 {file_id} 移除标签失败: {e}")

    def _clear_all_tags(self):
        """清除所有文件的所有标签"""
        try:
            # 确认对话框
            reply = question(
                None,
                "确认清除",
                f"确定要清除选中的 {len(self.file_ids)} 个文件的所有标签吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                for file_id in self.file_ids:
                    file_tags = self.file_tags_cache.get(file_id, {})
                    for tag_id in list(file_tags.keys()):
                        self.tag_service.remove_tag_from_file(file_id, tag_id)

                # 清除缓存
                for file_id in self.file_ids:
                    self.file_tags_cache[file_id] = {}

                # 发出标签变化信号
                self.tags_changed.emit()

        except Exception as e:
            warning(
                None,
                "清除标签失败",
                f"清除标签时发生错误: {e}"
            )



    def _open_tag_management(self):
        """打开标签管理对话框"""
        try:
            from smartvault.ui.dialogs.tag_management_dialog import TagManagementDialog

            dialog = TagManagementDialog()
            dialog.tags_changed.connect(self.tags_changed.emit)
            dialog.exec()

        except Exception as e:
            warning(
                None,
                "打开标签管理失败",
                f"无法打开标签管理对话框: {e}"
            )
