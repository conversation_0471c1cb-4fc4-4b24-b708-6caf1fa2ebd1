"""
标签导航面板组件
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTreeWidget, QTreeWidgetItem,
    QPushButton, QLabel, QLineEdit, QMenu, QMessageBox
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QColor, QAction

from smartvault.services.tag_service import TagService
from smartvault.utils.tree_state_manager import tree_state_manager


class TagNavigationPanel(QWidget):
    """标签导航面板"""

    # 信号：标签选择变化
    tag_selected = Signal(str)  # tag_id
    tag_filter_cleared = Signal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.tag_service = TagService()
        self.current_selected_tag_id = None
        self.init_ui()
        self.load_tags()

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)

        # 标题和操作按钮
        header_layout = QHBoxLayout()

        title_label = QLabel("标签导航")
        title_label.setStyleSheet("font-weight: bold; font-size: 12px;")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # 管理标签按钮
        self.manage_btn = QPushButton("管理")
        self.manage_btn.setMaximumWidth(50)
        self.manage_btn.clicked.connect(self.open_tag_management)
        header_layout.addWidget(self.manage_btn)

        layout.addLayout(header_layout)

        # 搜索框
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("搜索标签...")
        self.search_edit.textChanged.connect(self.filter_tags)
        layout.addWidget(self.search_edit)

        # 标签树
        self.tag_tree = QTreeWidget()
        self.tag_tree.setObjectName("tagNavigationTree")  # 设置对象名称用于CSS样式
        self.tag_tree.setHeaderHidden(True)
        self.tag_tree.setRootIsDecorated(True)
        self.tag_tree.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.tag_tree.customContextMenuRequested.connect(self.show_context_menu)
        self.tag_tree.itemClicked.connect(self.on_tag_clicked)
        self.tag_tree.itemDoubleClicked.connect(self.on_tag_double_clicked)
        layout.addWidget(self.tag_tree)

        # 底部操作按钮
        bottom_layout = QHBoxLayout()

        self.clear_filter_btn = QPushButton("清除筛选")
        self.clear_filter_btn.clicked.connect(self.clear_filter)
        self.clear_filter_btn.setEnabled(False)
        bottom_layout.addWidget(self.clear_filter_btn)

        bottom_layout.addStretch()

        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.load_tags)
        bottom_layout.addWidget(self.refresh_btn)

        layout.addLayout(bottom_layout)

    def load_tags(self):
        """加载标签树"""
        try:
            # 清空树
            self.tag_tree.clear()

            # 获取标签树结构
            tag_tree = self.tag_service.get_tag_tree()

            # 构建树控件
            self.build_tree_items(tag_tree, self.tag_tree)

            # 🔧 使用状态管理器恢复展开状态，而不是强制展开所有
            tree_state_manager.restore_tree_state(self.tag_tree, "navigation_tag_tree")

        except Exception as e:
            print(f"加载标签失败: {e}")

    def build_tree_items(self, tag_nodes, parent_widget, depth=0):
        """构建树项

        Args:
            tag_nodes: 标签节点列表
            parent_widget: 父组件
            depth: 当前深度（用于颜色继承）
        """
        for tag_node in tag_nodes:
            # 创建树项
            item = QTreeWidgetItem(parent_widget)

            # 设置显示文本
            display_text = f"{tag_node['name']}"
            if tag_node['total_file_count'] > 0:
                display_text += f" ({tag_node['total_file_count']})"
            item.setText(0, display_text)

            # 存储标签数据
            item.setData(0, Qt.ItemDataRole.UserRole, tag_node)

            # 设置颜色（支持继承和深度递减）
            self._apply_tag_color(item, tag_node, depth)

            # 递归添加子项
            if tag_node['children']:
                self.build_tree_items(tag_node['children'], item, depth + 1)

    def _apply_tag_color(self, item, tag_node, depth):
        """应用标签颜色（支持继承和深度递减）

        Args:
            item: 树项
            tag_node: 标签数据
            depth: 深度级别
        """
        # 获取有效颜色（考虑继承）
        effective_color = None
        if tag_node.get('color'):
            effective_color = tag_node['color']
        else:
            # 如果没有颜色，尝试从TagService获取继承颜色
            try:
                inherited_attrs = self.tag_service.get_inherited_attributes(tag_node['id'])
                effective_color = inherited_attrs.get('color')
            except:
                pass

        if effective_color:
            from smartvault.ui.utils.color_utils import get_inherited_color

            # 根据深度调整颜色（参考文件视图的成功经验）
            display_color = get_inherited_color(effective_color, depth)

            # 设置文字颜色（使用QBrush，参考文件视图）
            from PySide6.QtGui import QBrush
            item.setForeground(0, QBrush(display_color))

            # 可选：为根级标签设置轻微的背景色
            if depth == 0 and effective_color:
                from smartvault.ui.utils.color_utils import lighten_color
                bg_color = lighten_color(effective_color, 0.8)  # 非常浅的背景
                item.setBackground(0, bg_color)

    def filter_tags(self, text):
        """筛选标签"""
        # 简单的文本筛选实现
        # TODO: 可以改进为更智能的筛选
        self.filter_tree_items(self.tag_tree.invisibleRootItem(), text.lower())

    def filter_tree_items(self, parent_item, filter_text):
        """递归筛选树项"""
        for i in range(parent_item.childCount()):
            item = parent_item.child(i)
            tag_data = item.data(0, Qt.ItemDataRole.UserRole)

            # 检查当前项是否匹配
            matches = not filter_text or filter_text in tag_data['name'].lower()

            # 检查子项是否有匹配
            has_matching_child = self.filter_tree_items(item, filter_text)

            # 显示/隐藏项
            item.setHidden(not (matches or has_matching_child))

        # 返回是否有匹配的子项
        return any(not parent_item.child(i).isHidden() for i in range(parent_item.childCount()))

    def on_tag_clicked(self, item, column):
        """标签点击事件"""
        tag_data = item.data(0, Qt.ItemDataRole.UserRole)
        if tag_data:
            self.current_selected_tag_id = tag_data['id']
            self.clear_filter_btn.setEnabled(True)

            print(f"🏷️  标签导航：点击标签 '{tag_data['name']}' (ID: {tag_data['id']})")

            # 发出标签选择信号
            self.tag_selected.emit(tag_data['id'])
            print(f"🚀 标签导航：已发出标签选择信号")

            # 更新选中状态显示
            self.update_selection_display(item)

    def on_tag_double_clicked(self, item, column):
        """标签双击事件"""
        # 双击时展开/折叠
        item.setExpanded(not item.isExpanded())

    def update_selection_display(self, selected_item):
        """更新选中状态显示"""
        # 清除所有项的选中状态
        self.clear_selection_display(self.tag_tree.invisibleRootItem())

        # 设置当前项为选中状态
        if selected_item:
            font = selected_item.font(0)
            font.setBold(True)
            selected_item.setFont(0, font)

    def clear_selection_display(self, parent_item):
        """清除选中状态显示"""
        for i in range(parent_item.childCount()):
            item = parent_item.child(i)
            font = item.font(0)
            font.setBold(False)
            item.setFont(0, font)
            self.clear_selection_display(item)

    def clear_filter(self):
        """清除标签筛选"""
        self.current_selected_tag_id = None
        self.clear_filter_btn.setEnabled(False)

        # 清除选中状态
        self.clear_selection_display(self.tag_tree.invisibleRootItem())

        # 发出清除筛选信号
        self.tag_filter_cleared.emit()

    def show_context_menu(self, position):
        """显示右键菜单"""
        item = self.tag_tree.itemAt(position)
        if not item:
            return

        tag_data = item.data(0, Qt.ItemDataRole.UserRole)
        if not tag_data:
            return

        menu = QMenu(self)

        # 筛选此标签
        filter_action = QAction(f"筛选 '{tag_data['name']}'", self)
        # 使用functools.partial避免lambda闭包问题
        from functools import partial
        filter_action.triggered.connect(partial(self.on_tag_clicked, item, 0))
        menu.addAction(filter_action)

        menu.addSeparator()

        # 查看标签详情
        details_action = QAction("查看详情", self)
        details_action.triggered.connect(partial(self.show_tag_details, tag_data))
        menu.addAction(details_action)

        # 管理标签
        manage_action = QAction("管理标签", self)
        manage_action.triggered.connect(self.open_tag_management)
        menu.addAction(manage_action)

        menu.exec(self.tag_tree.mapToGlobal(position))

    def show_tag_details(self, tag_data):
        """显示标签详情"""
        try:
            # 获取使用该标签的文件
            files = self.tag_service.get_files_by_tag(tag_data['id'])

            details = f"标签: {tag_data['name']}\n"
            details += f"颜色: {tag_data.get('color', '未设置')}\n"
            details += f"直接文件数: {tag_data['file_count']}\n"
            details += f"总文件数: {tag_data['total_file_count']}\n"

            if tag_data['children']:
                child_names = [child['name'] for child in tag_data['children']]
                details += f"子标签: {', '.join(child_names)}\n"

            QMessageBox.information(self, "标签详情", details)

        except Exception as e:
            QMessageBox.warning(self, "错误", f"获取标签详情失败: {e}")

    def open_tag_management(self):
        """打开标签管理对话框"""
        try:
            from smartvault.ui.dialogs.tag_management_dialog import TagManagementDialog

            dialog = TagManagementDialog(self)
            dialog.tags_changed.connect(self.load_tags)  # 标签变化时刷新
            dialog.exec()

        except Exception as e:
            QMessageBox.warning(self, "错误", f"打开标签管理失败: {e}")

    def get_current_tag_filter(self):
        """获取当前标签筛选"""
        return self.current_selected_tag_id

    def set_tag_filter(self, tag_id):
        """设置标签筛选"""
        if not tag_id:
            self.clear_filter()
            return

        # 在树中找到对应的项并选中
        def find_and_select_item(parent_item, target_tag_id):
            for i in range(parent_item.childCount()):
                item = parent_item.child(i)
                tag_data = item.data(0, Qt.ItemDataRole.UserRole)
                if tag_data and tag_data['id'] == target_tag_id:
                    self.on_tag_clicked(item, 0)
                    return True
                if find_and_select_item(item, target_tag_id):
                    return True
            return False

        find_and_select_item(self.tag_tree.invisibleRootItem(), tag_id)

    def refresh_tags(self):
        """刷新标签（外部调用）"""
        # 🔧 在刷新前保存当前展开状态
        tree_state_manager.save_tree_state(self.tag_tree, "navigation_tag_tree")
        self.load_tags()

    def save_tree_state(self):
        """保存树控件的展开状态"""
        try:
            tree_state_manager.save_tree_state(self.tag_tree, "navigation_tag_tree")
        except Exception as e:
            print(f"保存标签导航树状态失败: {e}")
