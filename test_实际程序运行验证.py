#!/usr/bin/env python3
"""
SmartVault 实际程序运行验证
验证拆分后的程序是否能正常启动和运行
"""

import sys
import os
import time
import subprocess
sys.path.append('.')

def test_program_startup():
    """测试程序启动"""
    print("=" * 60)
    print("🚀 程序启动测试")
    print("=" * 60)

    try:
        # 检查主程序文件是否存在
        if not os.path.exists('run.py'):
            print("❌ run.py 文件不存在")
            return False

        print("✅ run.py 文件存在")

        # 尝试导入主要模块
        try:
            from smartvault.ui.main_window import MainWindow
            print("✅ 主窗口模块导入成功")
        except Exception as e:
            print(f"❌ 主窗口模块导入失败: {e}")
            return False

        try:
            from smartvault.ui.main_window.clipboard_handler import ClipboardHandler
            print("✅ 剪贴板处理器模块导入成功")
        except Exception as e:
            print(f"❌ 剪贴板处理器模块导入失败: {e}")
            return False

        try:
            from smartvault.ui.main_window.backup_manager import BackupManager
            print("✅ 备份管理器模块导入成功")
        except Exception as e:
            print(f"❌ 备份管理器模块导入失败: {e}")
            return False

        return True

    except Exception as e:
        print(f"❌ 程序启动测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_window_creation():
    """测试主窗口创建"""
    print("\n" + "=" * 60)
    print("🪟 主窗口创建测试")
    print("=" * 60)

    try:
        from PySide6.QtWidgets import QApplication
        from smartvault.ui.main_window import MainWindow

        # 创建Qt应用
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        print("✅ Qt应用创建成功")

        # 创建主窗口
        main_window = MainWindow()
        print("✅ 主窗口创建成功")

        # 检查关键组件是否存在
        if hasattr(main_window, 'clipboard_handler'):
            print("✅ 剪贴板处理器已初始化")
        else:
            print("❌ 剪贴板处理器未初始化")
            return False

        if hasattr(main_window, 'backup_manager'):
            print("✅ 备份管理器已初始化")
        else:
            print("❌ 备份管理器未初始化")
            return False

        if hasattr(main_window, 'clipboard_floating_widget'):
            print("✅ 剪贴板浮动窗口已初始化")
        else:
            print("❌ 剪贴板浮动窗口未初始化")
            return False

        # 检查信号连接
        try:
            # 检查剪贴板浮动窗口的信号连接
            signal_count = len(main_window.clipboard_floating_widget.open_file_requested.receivers())
            if signal_count > 0:
                print(f"✅ 剪贴板浮动窗口信号已连接 ({signal_count} 个接收器)")
            else:
                print("❌ 剪贴板浮动窗口信号未连接")
                return False
        except Exception as e:
            print(f"⚠️ 信号连接检查失败: {e}")

        # 检查委托方法
        if hasattr(main_window, 'toggle_clipboard_monitor'):
            print("✅ 剪贴板监控切换委托方法存在")
        else:
            print("❌ 剪贴板监控切换委托方法不存在")
            return False

        if hasattr(main_window, 'show_clipboard_demo'):
            print("✅ 剪贴板演示委托方法存在")
        else:
            print("❌ 剪贴板演示委托方法不存在")
            return False

        return True

    except Exception as e:
        print(f"❌ 主窗口创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_clipboard_functionality():
    """测试剪贴板功能"""
    print("\n" + "=" * 60)
    print("📋 剪贴板功能测试")
    print("=" * 60)

    try:
        from PySide6.QtWidgets import QApplication
        from smartvault.ui.main_window import MainWindow

        # 创建Qt应用
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        # 创建主窗口
        main_window = MainWindow()

        # 测试剪贴板处理器方法
        clipboard_handler = main_window.clipboard_handler

        # 测试演示功能
        print("🎭 测试剪贴板演示功能...")
        try:
            clipboard_handler.show_clipboard_demo()
            print("✅ 剪贴板演示功能正常")
        except Exception as e:
            print(f"❌ 剪贴板演示功能失败: {e}")
            return False

        # 测试委托方法
        print("🔄 测试委托方法...")
        try:
            # 这些方法应该不会抛出异常
            main_window.show_clipboard_demo()
            print("✅ 剪贴板演示委托方法正常")
        except Exception as e:
            print(f"❌ 剪贴板演示委托方法失败: {e}")
            return False

        return True

    except Exception as e:
        print(f"❌ 剪贴板功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_backup_functionality():
    """测试备份功能"""
    print("\n" + "=" * 60)
    print("💾 备份功能测试")
    print("=" * 60)

    try:
        from PySide6.QtWidgets import QApplication
        from smartvault.ui.main_window import MainWindow

        # 创建Qt应用
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        # 创建主窗口
        main_window = MainWindow()

        # 测试备份管理器方法
        backup_manager = main_window.backup_manager

        # 测试获取备份状态
        print("📊 测试备份状态获取...")
        try:
            status = backup_manager.get_backup_status()
            print(f"✅ 备份状态获取成功: {status}")
        except Exception as e:
            print(f"❌ 备份状态获取失败: {e}")
            return False

        # 测试状态显示更新
        print("🔄 测试状态显示更新...")
        try:
            backup_manager.update_backup_status_display()
            print("✅ 备份状态显示更新成功")
        except Exception as e:
            print(f"❌ 备份状态显示更新失败: {e}")
            return False

        return True

    except Exception as e:
        print(f"❌ 备份功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_location_integration():
    """测试文件定位集成"""
    print("\n" + "=" * 60)
    print("🎯 文件定位集成测试")
    print("=" * 60)

    try:
        from PySide6.QtWidgets import QApplication
        from smartvault.ui.main_window import MainWindow
        from smartvault.services.file import FileService

        # 创建Qt应用
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        # 创建主窗口
        main_window = MainWindow()

        # 获取一些真实文件数据
        file_service = FileService()
        files = file_service.get_files(limit=1)

        if not files:
            print("⚠️ 数据库中没有文件，跳过文件定位测试")
            return True

        test_file = files[0]
        print(f"📄 使用测试文件: {test_file['name']} (ID: {test_file['id']})")

        # 测试文件定位功能
        print("🎯 测试文件定位...")
        try:
            clipboard_handler = main_window.clipboard_handler
            # 这个方法应该不会抛出异常，即使文件视图是模拟的
            clipboard_handler._locate_and_select_file(test_file['id'])
            print("✅ 文件定位功能调用成功")
        except Exception as e:
            print(f"❌ 文件定位功能失败: {e}")
            return False

        return True

    except Exception as e:
        print(f"❌ 文件定位集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始SmartVault实际程序运行验证")
    print("目标: 验证拆分后的程序能否正常启动和运行")

    test_results = []

    # 执行测试
    test_results.append(("程序启动", test_program_startup()))
    test_results.append(("主窗口创建", test_main_window_creation()))
    test_results.append(("剪贴板功能", test_clipboard_functionality()))
    test_results.append(("备份功能", test_backup_functionality()))
    test_results.append(("文件定位集成", test_file_location_integration()))

    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)

    passed = 0
    failed = 0

    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1

    print(f"\n总计: {passed} 个测试通过, {failed} 个测试失败")

    if failed == 0:
        print("🎉 SmartVault程序运行验证成功！拆分后的程序完全正常！")
        return True
    else:
        print("⚠️ 程序运行存在问题，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
