"""
SmartVault 应用程序入口点
"""

import sys
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import QSharedMemory
from smartvault.ui.main_window import MainWindow
from smartvault.ui.resources import get_icon


class SingleInstance:
    """单实例运行控制类"""

    def __init__(self, app_name="SmartVault"):
        self.app_name = app_name
        self.shared_memory = QSharedMemory(app_name)
        self.is_running = False

    def check_instance(self):
        """检查是否已有实例在运行"""
        # 尝试创建共享内存
        if self.shared_memory.create(1):
            # 成功创建，说明是第一个实例
            self.is_running = False
            return False
        else:
            # 创建失败，说明已有实例在运行
            self.is_running = True
            return True

    def cleanup(self):
        """清理资源"""
        if self.shared_memory.isAttached():
            self.shared_memory.detach()


def _pre_startup_check():
    """启动前检查"""
    return True


def main():
    """应用程序主入口函数"""
    print("启动 SmartVault 应用程序...")

    # 启动前检查
    if not _pre_startup_check():
        return 1

    try:
        # 设置应用程序属性
        app = QApplication(sys.argv)
        app.setApplicationName("SmartVault")
        app.setOrganizationName("SmartVault")
        app.setApplicationVersion("1.0.0")
        print("已创建应用程序实例")

        # 单实例检查
        single_instance = SingleInstance("SmartVault")
        if single_instance.check_instance():
            print("SmartVault 已在运行中")
            QMessageBox.information(
                None,
                "SmartVault",
                "SmartVault 已在运行中，请检查系统托盘或任务栏。"
            )
            return 0

        # 应用中文化设置
        from smartvault.ui.localization import apply_chinese_style
        apply_chinese_style(app)

        # 应用保存的主题
        from smartvault.ui.themes import theme_manager
        theme_manager.apply_theme(theme_manager.get_current_theme())

        # 设置应用图标
        try:
            app.setWindowIcon(get_icon("app"))
        except Exception as e:
            print(f"设置应用图标失败: {e}")

        # 直接创建主窗口，不使用复杂的线程机制
        print("正在创建主窗口...")
        window = MainWindow()
        print("主窗口已创建")

        window.show()
        print("主窗口已显示")

        # 运行应用程序事件循环
        print("进入应用程序事件循环")
        exit_code = app.exec()

        # 清理资源
        print("应用程序退出，清理资源...")

        # 确保窗口被正确销毁
        if window:
            window.close()
            window = None

        # 清理单实例资源
        single_instance.cleanup()

        # 退出应用程序
        app.quit()

        return exit_code

    except Exception as e:
        print(f"应用程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    main()
