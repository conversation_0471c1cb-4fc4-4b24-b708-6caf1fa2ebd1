#!/usr/bin/env python3
"""
AI功能最终验证测试
全面验证AI功能集成的完整性和安全性
"""

import sys
import os
import tempfile
import shutil
import uuid

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_complete_ai_workflow():
    """测试完整的AI工作流程"""
    print("🔍 测试完整的AI工作流程...")
    
    try:
        from smartvault.utils.config import save_ai_status, get_ai_status
        from smartvault.services.file import FileService
        from smartvault.services.tag_service import TagService
        
        # 确保AI功能关闭
        save_ai_status(False)
        
        # 创建测试文件
        test_dir = tempfile.mkdtemp(prefix="smartvault_final_test_")
        unique_id = str(uuid.uuid4())[:8]
        
        test_files = [
            (f"project_config_{unique_id}.json", '{"name": "SmartVault", "version": "3.0"}'),
            (f"main_app_{unique_id}.py", "#!/usr/bin/env python3\ndef main():\n    print('SmartVault AI Test')"),
            (f"user_manual_{unique_id}.md", "# SmartVault用户手册\n\n这是AI功能测试文档"),
            (f"database_settings_{unique_id}.conf", "[database]\nhost=localhost\nport=5432"),
            (f"backup_data_{unique_id}.bak", "备份数据内容"),
        ]
        
        created_files = []
        for filename, content in test_files:
            file_path = os.path.join(test_dir, filename)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            created_files.append(file_path)
        
        try:
            file_service = FileService()
            tag_service = TagService()
            
            # 阶段1: AI关闭时添加文件
            print("\n📋 阶段1: AI关闭时添加文件")
            ai_disabled_results = []
            
            for test_file in created_files[:2]:  # 只测试前两个文件
                filename = os.path.basename(test_file)
                print(f"添加文件: {filename}")
                
                file_id = file_service.add_file(test_file, mode="link")
                tags = tag_service.get_file_tags(file_id)
                tag_names = [tag['name'] for tag in tags]
                
                # 应该没有AI标签
                ai_tags = [tag for tag in tag_names if tag in ['配置文件', 'JSON配置', '代码', 'Python']]
                
                if not ai_tags:
                    print(f"✅ 正确：未应用AI标签")
                    ai_disabled_results.append(True)
                else:
                    print(f"❌ 错误：应用了AI标签: {ai_tags}")
                    ai_disabled_results.append(False)
            
            # 阶段2: 启用AI功能
            print("\n📋 阶段2: 启用AI功能")
            save_ai_status(True)
            ai_status = get_ai_status()
            print(f"AI状态: {ai_status}")
            
            # 阶段3: AI启用时添加文件
            print("\n📋 阶段3: AI启用时添加文件")
            ai_enabled_results = []
            
            for test_file in created_files[2:]:  # 测试剩余文件
                filename = os.path.basename(test_file)
                print(f"添加文件: {filename}")
                
                file_id = file_service.add_file(test_file, mode="link")
                tags = tag_service.get_file_tags(file_id)
                tag_names = [tag['name'] for tag in tags]
                
                print(f"应用的标签: {tag_names}")
                
                # 验证预期的AI标签
                expected_ai_tags = []
                if 'manual' in filename and '.md' in filename:
                    expected_ai_tags = ['文档', '说明文档']
                elif 'settings' in filename and '.conf' in filename:
                    expected_ai_tags = ['配置文件', '系统配置']
                elif 'backup' in filename and '.bak' in filename:
                    expected_ai_tags = ['备份']
                
                found_expected = any(tag in tag_names for tag in expected_ai_tags) if expected_ai_tags else True
                
                if found_expected:
                    print(f"✅ AI标签正确应用")
                    ai_enabled_results.append(True)
                else:
                    print(f"⚠️ AI标签未按预期应用，预期: {expected_ai_tags}")
                    ai_enabled_results.append(False)
            
            # 阶段4: 关闭AI功能
            print("\n📋 阶段4: 关闭AI功能")
            save_ai_status(False)
            final_ai_status = get_ai_status()
            print(f"最终AI状态: {final_ai_status}")
            
            # 总结结果
            all_disabled_correct = all(ai_disabled_results)
            all_enabled_correct = all(ai_enabled_results)
            
            print(f"\n📊 工作流程测试结果:")
            print(f"AI关闭时正确性: {all_disabled_correct} ({len([r for r in ai_disabled_results if r])}/{len(ai_disabled_results)})")
            print(f"AI启用时正确性: {all_enabled_correct} ({len([r for r in ai_enabled_results if r])}/{len(ai_enabled_results)})")
            print(f"AI状态管理: {final_ai_status == False}")
            
            return all_disabled_correct and all_enabled_correct and final_ai_status == False
            
        finally:
            # 清理测试文件
            shutil.rmtree(test_dir, ignore_errors=True)
            save_ai_status(False)
        
    except Exception as e:
        print(f"❌ 完整工作流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_settings_integration():
    """测试AI设置集成"""
    print("\n🔍 测试AI设置集成...")
    
    try:
        # 测试设置页面导入
        from smartvault.ui.dialogs.settings_dialog import SettingsDialog
        from smartvault.ui.dialogs.settings.pages.ai.ai_main_page import AISettingsPage
        
        # 测试AI配置管理
        from smartvault.ui.dialogs.settings.pages.ai.utils.ai_config_helper import AIConfigManager
        
        config_manager = AIConfigManager()
        config = config_manager.load_ai_config()
        
        # 测试配置验证
        is_valid, error_msg = config_manager.validate_ai_config(config)
        
        print(f"✅ 设置页面导入成功")
        print(f"✅ 配置管理器工作正常")
        print(f"✅ 配置验证: {is_valid}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI设置集成测试失败: {e}")
        return False

def test_ai_performance():
    """测试AI性能"""
    print("\n🔍 测试AI性能...")
    
    try:
        from smartvault.services.ai.ai_manager import AIManager
        from smartvault.utils.config import save_ai_status, load_config
        import time
        
        # 启用AI功能
        save_ai_status(True)
        
        ai_manager = AIManager()
        config = load_config()
        ai_manager.initialize(config)
        
        # 性能测试
        test_files = [
            {'name': f'test_{i}.json', 'extension': '.json', 'path': f'/test/test_{i}.json'}
            for i in range(10)
        ]
        
        start_time = time.time()
        
        for test_file in test_files:
            suggestions = ai_manager.suggest_tags(test_file)
        
        end_time = time.time()
        total_time = end_time - start_time
        avg_time = total_time / len(test_files)
        
        print(f"✅ 处理 {len(test_files)} 个文件")
        print(f"✅ 总时间: {total_time:.3f}秒")
        print(f"✅ 平均时间: {avg_time:.3f}秒/文件")
        
        # 恢复状态
        save_ai_status(False)
        
        # 性能标准：平均处理时间应该小于0.1秒
        return avg_time < 0.1
        
    except Exception as e:
        print(f"❌ AI性能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 AI功能最终验证测试")
    print("=" * 80)
    
    # 测试1: 完整AI工作流程
    test1 = test_complete_ai_workflow()
    
    # 测试2: AI设置集成
    test2 = test_ai_settings_integration()
    
    # 测试3: AI性能
    test3 = test_ai_performance()
    
    # 总结
    print("\n" + "=" * 80)
    print("📊 最终验证测试结果:")
    print(f"完整AI工作流程: {'✅ 通过' if test1 else '❌ 失败'}")
    print(f"AI设置集成: {'✅ 通过' if test2 else '❌ 失败'}")
    print(f"AI性能测试: {'✅ 通过' if test3 else '❌ 失败'}")
    
    if all([test1, test2, test3]):
        print("\n🎉 所有最终验证测试通过！")
        print("\n🏆 AI功能集成验证完成总结:")
        print("   ✅ AI设置页面完美集成到主设置对话框")
        print("   ✅ AI管理器成功集成到主程序核心")
        print("   ✅ AI标签建议功能正常工作")
        print("   ✅ 文件添加时自动应用AI标签")
        print("   ✅ AI开关安全可靠，不影响现有功能")
        print("   ✅ 智能规则引擎正确识别文件类型")
        print("   ✅ 配置管理和持久化完全正常")
        print("   ✅ 性能表现良好，响应迅速")
        print("\n🚀 SmartVault AI功能已准备就绪，可以投入生产使用！")
        return 0
    else:
        print("\n⚠️ 部分验证测试失败，需要进一步检查")
        return 1

if __name__ == "__main__":
    sys.exit(main())
