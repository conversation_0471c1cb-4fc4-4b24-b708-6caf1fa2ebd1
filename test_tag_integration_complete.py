#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
完整的标签系统集成测试
"""

import sys
import os
import tempfile

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer

from smartvault.ui.main_window import MainWindow
from smartvault.services.tag_service import TagService
from smartvault.services.file import FileService


def setup_test_data():
    """设置测试数据"""
    print("🏷️  设置完整的测试数据...")
    
    tag_service = TagService()
    file_service = FileService()
    
    # 创建层级标签
    work_tag = tag_service.create_tag("工作文档", "#FF9800")
    project_a = tag_service.create_tag("项目A", "#2196F3", work_tag)
    project_b = tag_service.create_tag("项目B", "#4CAF50", work_tag)
    
    personal_tag = tag_service.create_tag("个人文件", "#9C27B0")
    photos_tag = tag_service.create_tag("照片", "#E91E63", personal_tag)
    documents_tag = tag_service.create_tag("文档", "#795548", personal_tag)
    
    archive_tag = tag_service.create_tag("归档", "#9E9E9E")
    important_tag = tag_service.create_tag("重要", "#F44336")
    
    print(f"   ✅ 创建了层级标签结构")
    
    # 创建测试文件并关联标签
    temp_dir = tempfile.mkdtemp()
    test_files = []
    
    # 创建一些测试文件
    for i in range(5):
        test_file = os.path.join(temp_dir, f"测试文件{i+1}.txt")
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(f"这是测试文件{i+1}的内容")
        test_files.append(test_file)
    
    # 添加文件到库并关联标签
    try:
        # 文件1: 工作 + 项目A
        file1_id = file_service.add_file(test_files[0], "link")
        tag_service.add_tag_to_file(file1_id, work_tag)
        tag_service.add_tag_to_file(file1_id, project_a)
        
        # 文件2: 项目B + 重要
        file2_id = file_service.add_file(test_files[1], "link")
        tag_service.add_tag_to_file(file2_id, project_b)
        tag_service.add_tag_to_file(file2_id, important_tag)
        
        # 文件3: 个人 + 照片
        file3_id = file_service.add_file(test_files[2], "link")
        tag_service.add_tag_to_file(file3_id, personal_tag)
        tag_service.add_tag_to_file(file3_id, photos_tag)
        
        # 文件4: 个人 + 文档 + 重要
        file4_id = file_service.add_file(test_files[3], "link")
        tag_service.add_tag_to_file(file4_id, personal_tag)
        tag_service.add_tag_to_file(file4_id, documents_tag)
        tag_service.add_tag_to_file(file4_id, important_tag)
        
        # 文件5: 归档
        file5_id = file_service.add_file(test_files[4], "link")
        tag_service.add_tag_to_file(file5_id, archive_tag)
        
        print(f"   ✅ 创建了 5 个测试文件并关联标签")
        
    except Exception as e:
        print(f"   ❌ 创建测试文件失败: {e}")
    
    return {
        'work_tag': work_tag,
        'project_a': project_a,
        'project_b': project_b,
        'personal_tag': personal_tag,
        'photos_tag': photos_tag,
        'documents_tag': documents_tag,
        'archive_tag': archive_tag,
        'important_tag': important_tag
    }


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = MainWindow()
    main_window.show()
    
    print("🖥️  SmartVault 主窗口已显示")
    print("   💡 完整的标签导航功能测试:")
    print("      1. 点击左侧导航面板的'标签'选项卡")
    print("      2. 查看层级标签树结构")
    print("      3. 点击标签进行文件筛选")
    print("      4. 使用搜索框快速查找标签")
    print("      5. 右键标签查看更多选项")
    print("      6. 点击'管理'按钮打开标签管理对话框")
    print("      7. 在标签管理中创建、编辑、删除标签")
    print("      8. 点击'清除筛选'显示所有文件")
    print("      9. 通过菜单'工具'->'标签管理'也可以打开标签管理")
    
    # 设置测试数据
    def setup_data():
        try:
            tag_data = setup_test_data()
            
            # 刷新导航面板中的标签
            if hasattr(main_window, 'navigation_panel'):
                main_window.navigation_panel.refresh_tags()
            
            print("\n🎯 测试数据设置完成！现在可以测试以下功能:")
            print("   • 工作文档 (2个文件: 项目A + 项目B)")
            print("   • 个人文件 (2个文件: 照片 + 文档)")
            print("   • 重要 (2个文件: 跨越工作和个人)")
            print("   • 归档 (1个文件)")
            
        except Exception as e:
            print(f"❌ 设置测试数据失败: {e}")
    
    # 延迟设置测试数据，等待主窗口完全加载
    QTimer.singleShot(2000, setup_data)
    
    # 运行应用
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
