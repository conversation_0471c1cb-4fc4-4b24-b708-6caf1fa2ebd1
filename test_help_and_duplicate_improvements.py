#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试帮助系统内嵌和链接模式重复文件处理改进
"""

import sys
import os
import tempfile
import shutil
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QTextEdit
from PyQt5.QtCore import Qt

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_embedded_help_content():
    """测试内嵌帮助内容"""
    print("=" * 50)
    print("测试内嵌帮助内容")
    print("=" * 50)
    
    try:
        from smartvault.ui.dialogs.help_content import HELP_CONTENT
        
        print(f"✅ 帮助内容模块加载成功")
        print(f"📄 包含 {len(HELP_CONTENT)} 个帮助文档")
        
        # 检查关键文档是否存在
        required_docs = [
            "welcome",
            "用户帮助-新手指南.md",
            "用户帮助-基本概念.md",
            "用户帮助-文件管理.md"
        ]
        
        for doc in required_docs:
            if doc in HELP_CONTENT:
                content_length = len(HELP_CONTENT[doc])
                print(f"✅ {doc}: {content_length} 字符")
            else:
                print(f"❌ 缺失文档: {doc}")
        
        # 测试内容质量
        welcome_content = HELP_CONTENT.get("welcome", "")
        if "SmartVault" in welcome_content and len(welcome_content) > 100:
            print("✅ 欢迎页面内容质量良好")
        else:
            print("❌ 欢迎页面内容质量有问题")
            
        return True
        
    except Exception as e:
        print(f"❌ 帮助内容测试失败: {e}")
        return False

def test_help_dialog():
    """测试帮助对话框"""
    print("\n" + "=" * 50)
    print("测试帮助对话框")
    print("=" * 50)
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from smartvault.ui.dialogs.help_dialog import HelpDialog
        
        # 创建帮助对话框
        dialog = HelpDialog()
        
        # 检查内嵌内容是否加载
        if hasattr(dialog, 'embedded_help') and dialog.embedded_help:
            print("✅ 内嵌帮助内容已加载")
            print(f"📄 可用文档数量: {len(dialog.embedded_help)}")
        else:
            print("❌ 内嵌帮助内容未加载")
            return False
        
        # 测试加载欢迎页面
        dialog.load_welcome_page()
        print("✅ 欢迎页面加载成功")
        
        # 测试加载帮助文档
        dialog.load_help_document("用户帮助-新手指南.md")
        print("✅ 新手指南加载成功")
        
        dialog.close()
        return True
        
    except Exception as e:
        print(f"❌ 帮助对话框测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_link_mode_duplicate_handling():
    """测试链接模式重复文件处理"""
    print("\n" + "=" * 50)
    print("测试链接模式重复文件处理")
    print("=" * 50)
    
    try:
        # 创建临时测试文件
        temp_dir = tempfile.mkdtemp(prefix="smartvault_test_")
        test_file1 = os.path.join(temp_dir, "test_file.txt")
        test_file2 = os.path.join(temp_dir, "test_file_copy.txt")
        
        # 创建相同内容的文件
        test_content = "这是一个测试文件内容"
        with open(test_file1, 'w', encoding='utf-8') as f:
            f.write(test_content)
        with open(test_file2, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        print(f"✅ 创建测试文件: {test_file1}")
        print(f"✅ 创建测试文件: {test_file2}")
        
        # 模拟文件导入操作
        from smartvault.services.file.import_ops import FileImportMixin
        
        # 创建模拟的导入服务
        class MockImportService(FileImportMixin):
            def __init__(self):
                self.db = None
                self.file_system = None
            
            def _calculate_file_hash(self, file_path):
                import hashlib
                with open(file_path, 'rb') as f:
                    return hashlib.md5(f.read()).hexdigest()
        
        service = MockImportService()
        
        # 测试重复文件检测
        hash1 = service._calculate_file_hash(test_file1)
        hash2 = service._calculate_file_hash(test_file2)
        
        if hash1 == hash2:
            print("✅ 重复文件检测正常：文件哈希相同")
        else:
            print("❌ 重复文件检测异常：文件哈希不同")
            return False
        
        # 测试链接模式处理方法
        duplicate_info = {
            'new_file': test_file2,
            'existing_file': test_file1,
            'filename': 'test_file.txt',
            'suggestion': 'consider_delete_duplicate'
        }
        
        service._emit_duplicate_suggestion(duplicate_info)
        print("✅ 重复文件建议功能正常")
        
        # 清理测试文件
        shutil.rmtree(temp_dir, ignore_errors=True)
        print("✅ 测试文件清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 链接模式重复文件处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("SmartVault 帮助系统和重复文件处理测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建按钮
        help_btn = QPushButton("测试帮助对话框")
        help_btn.clicked.connect(self.test_help_dialog)
        layout.addWidget(help_btn)
        
        # 创建文本显示区域
        self.text_area = QTextEdit()
        self.text_area.setReadOnly(True)
        layout.addWidget(self.text_area)
        
        # 运行自动测试
        self.run_auto_tests()
    
    def test_help_dialog(self):
        """测试帮助对话框"""
        try:
            from smartvault.ui.dialogs.help_dialog import HelpDialog
            dialog = HelpDialog(self)
            dialog.exec()
        except Exception as e:
            self.text_area.append(f"❌ 帮助对话框测试失败: {e}")
    
    def run_auto_tests(self):
        """运行自动测试"""
        self.text_area.append("开始运行自动测试...\n")
        
        # 测试内嵌帮助内容
        if test_embedded_help_content():
            self.text_area.append("✅ 内嵌帮助内容测试通过")
        else:
            self.text_area.append("❌ 内嵌帮助内容测试失败")
        
        # 测试链接模式重复文件处理
        if test_link_mode_duplicate_handling():
            self.text_area.append("✅ 链接模式重复文件处理测试通过")
        else:
            self.text_area.append("❌ 链接模式重复文件处理测试失败")
        
        self.text_area.append("\n测试完成！点击按钮测试帮助对话框。")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 运行控制台测试
    print("SmartVault 帮助系统和重复文件处理改进测试")
    print("=" * 60)
    
    all_passed = True
    
    # 测试内嵌帮助内容
    if not test_embedded_help_content():
        all_passed = False
    
    # 测试帮助对话框
    if not test_help_dialog():
        all_passed = False
    
    # 测试链接模式重复文件处理
    if not test_link_mode_duplicate_handling():
        all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！")
    else:
        print("❌ 部分测试失败，请检查问题")
    
    # 显示测试窗口
    window = TestMainWindow()
    window.show()
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
