#!/usr/bin/env python3
"""
测试批量处理功能的脚本
"""

import sys
import os
import time
import shutil

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_files(test_dir, count=5):
    """创建测试文件"""
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    files = []
    for i in range(count):
        file_path = os.path.join(test_dir, f"test_file_{i+1}.txt")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(f"这是测试文件 {i+1}\n内容: {time.time()}")
        files.append(file_path)
        print(f"创建测试文件: {file_path}")
    
    return files

def test_batch_processing():
    """测试批量处理功能"""
    print("=== 测试批量处理功能 ===\n")
    
    # 测试目录
    test_dir = r"D:\temp_batch_test"
    
    try:
        # 清理测试目录
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
        
        print(f"1. 创建测试目录: {test_dir}")
        os.makedirs(test_dir)
        
        print("2. 启动应用程序（需要手动操作）")
        print("   - 请启动 SmartVault 应用程序")
        print("   - 添加监控文件夹: " + test_dir)
        print("   - 确保监控功能已启用")
        
        input("按回车键继续，确保应用程序已启动并配置好监控...")
        
        print("3. 创建批量测试文件")
        test_files = create_test_files(test_dir, 10)
        
        print(f"\n4. 已创建 {len(test_files)} 个测试文件")
        print("   请观察应用程序的反应：")
        print("   - 状态栏应显示 '正在批量处理监控文件...'")
        print("   - 进度条应显示处理进度")
        print("   - 处理完成后应显示批量统计信息")
        print("   - 文件视图应自动刷新")
        
        print("\n5. 等待处理完成...")
        time.sleep(5)
        
        print("6. 再次批量添加文件测试")
        more_files = create_test_files(test_dir, 5)
        
        print(f"\n7. 又创建了 {len(more_files)} 个测试文件")
        print("   再次观察应用程序的批量处理反应")
        
        print("\n测试完成！请检查：")
        print("✅ 状态栏是否显示了批量处理统计信息")
        print("✅ 进度条是否正常工作")
        print("✅ 文件视图是否自动刷新")
        print("✅ 没有出现频繁的状态栏闪烁")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        try:
            if os.path.exists(test_dir):
                print(f"\n清理测试目录: {test_dir}")
                shutil.rmtree(test_dir)
        except Exception as e:
            print(f"清理测试目录失败: {e}")

def main():
    """主函数"""
    test_batch_processing()

if __name__ == "__main__":
    main()
