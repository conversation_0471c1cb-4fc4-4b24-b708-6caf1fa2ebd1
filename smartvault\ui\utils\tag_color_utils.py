"""
标签颜色应用工具类
统一处理标签颜色的应用逻辑，避免重复代码
"""

from PySide6.QtGui import QColor, QBrush
from PySide6.QtWidgets import QTreeWidgetItem


class TagColorApplier:
    """标签颜色应用器 - 统一处理标签颜色应用逻辑"""
    
    @staticmethod
    def apply_tag_color(item: QTreeWidgetItem, tag_data: dict, tag_service, depth: int = 0):
        """应用标签颜色到树项
        
        Args:
            item: QTreeWidgetItem 树项
            tag_data: dict 标签数据
            tag_service: TagService 标签服务实例
            depth: int 当前深度（用于颜色继承）
        """
        try:
            # 获取标签的继承属性（包括颜色）
            inherited_attrs = tag_service.get_inherited_attributes(tag_data["id"])
            color = inherited_attrs.get('color')
            
            if color:
                # 设置文字颜色
                item.setForeground(0, QColor(color))
                
                # 为根级标签设置粗体
                if depth == 0:
                    font = item.font(0)
                    font.setBold(True)
                    item.setFont(0, font)
                    
        except Exception:
            # 如果获取继承属性失败，尝试直接使用标签的颜色
            color = tag_data.get('color')
            if color:
                item.setForeground(0, QColor(color))
                if depth == 0:
                    font = item.font(0)
                    font.setBold(True)
                    item.setFont(0, font)
    
    @staticmethod
    def apply_advanced_tag_color(item: QTreeWidgetItem, tag_data: dict, tag_service, depth: int = 0):
        """应用高级标签颜色（支持深度递减和背景色）
        
        Args:
            item: QTreeWidgetItem 树项
            tag_data: dict 标签数据
            tag_service: TagService 标签服务实例
            depth: int 当前深度（用于颜色继承）
        """
        # 获取有效颜色（考虑继承）
        effective_color = None
        if tag_data.get('color'):
            effective_color = tag_data['color']
        else:
            # 如果没有颜色，尝试从TagService获取继承颜色
            try:
                inherited_attrs = tag_service.get_inherited_attributes(tag_data['id'])
                effective_color = inherited_attrs.get('color')
            except:
                pass

        if effective_color:
            try:
                from smartvault.ui.utils.color_utils import get_inherited_color
                
                # 根据深度调整颜色
                display_color = get_inherited_color(effective_color, depth)
                
                # 设置文字颜色（使用QBrush）
                item.setForeground(0, QBrush(display_color))
                
                # 可选：为根级标签设置轻微的背景色
                if depth == 0 and effective_color:
                    from smartvault.ui.utils.color_utils import lighten_color
                    bg_color = lighten_color(effective_color, 0.8)  # 非常浅的背景
                    item.setBackground(0, bg_color)
                    
            except ImportError:
                # 如果color_utils不可用，使用简单版本
                TagColorApplier.apply_tag_color(item, tag_data, tag_service, depth)


class TagTreeBuilder:
    """标签树构建器 - 统一处理标签树构建逻辑"""
    
    @staticmethod
    def build_tree_items(tag_nodes, parent_widget, tag_service, depth=0, 
                        color_applier=None, item_callback=None):
        """构建标签树项
        
        Args:
            tag_nodes: list 标签节点列表
            parent_widget: QWidget 父组件
            tag_service: TagService 标签服务实例
            depth: int 当前深度
            color_applier: callable 颜色应用函数
            item_callback: callable 项目创建后的回调函数
        """
        if color_applier is None:
            color_applier = TagColorApplier.apply_tag_color
            
        for tag_node in tag_nodes:
            # 创建树项
            item = QTreeWidgetItem(parent_widget)
            
            # 设置显示文本
            display_text = f"{tag_node['name']}"
            if tag_node.get('total_file_count', 0) > 0:
                display_text += f" ({tag_node['total_file_count']})"
            item.setText(0, display_text)
            
            # 存储标签数据
            from PySide6.QtCore import Qt
            item.setData(0, Qt.ItemDataRole.UserRole, tag_node)
            
            # 应用颜色
            color_applier(item, tag_node, tag_service, depth)
            
            # 执行自定义回调（如设置复选框等）
            if item_callback:
                item_callback(item, tag_node, depth)
            
            # 递归添加子项
            if tag_node.get('children'):
                TagTreeBuilder.build_tree_items(
                    tag_node['children'], item, tag_service, 
                    depth + 1, color_applier, item_callback
                )


def setup_tag_tree_for_theme(tree_widget, object_name):
    """为标签树设置主题兼容的对象名称
    
    Args:
        tree_widget: QTreeWidget 树组件
        object_name: str 对象名称
    """
    tree_widget.setObjectName(object_name)
    tree_widget.setHeaderHidden(True)
    tree_widget.setRootIsDecorated(True)
