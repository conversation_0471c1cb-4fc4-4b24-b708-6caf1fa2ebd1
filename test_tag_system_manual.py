#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
标签系统手动测试 - 验证标签功能在实际环境中的工作情况
"""

import os
import sys
import tempfile

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from smartvault.services.tag_service import TagService
from smartvault.services.file import FileService


def test_tag_system_integration():
    """测试标签系统集成功能"""
    print("🏷️  测试标签系统集成功能...")
    
    # 创建服务实例
    tag_service = TagService()
    file_service = FileService()
    
    # 创建临时测试文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write("这是一个测试文件，用于验证标签功能")
        test_file = f.name
    
    try:
        print(f"📁 创建测试文件: {test_file}")
        
        # 1. 添加文件到库
        print("\n1️⃣ 添加文件到智能文件库...")
        file_id = file_service.add_file(test_file, "link")
        print(f"   ✅ 文件已添加，ID: {file_id}")
        
        # 2. 创建标签
        print("\n2️⃣ 创建标签...")
        work_tag_id = tag_service.create_tag("工作文档", "#FF9800")
        important_tag_id = tag_service.create_tag("重要", "#F44336")
        project_tag_id = tag_service.create_tag("项目A", "#2196F3")
        
        print(f"   ✅ 工作文档标签: {work_tag_id}")
        print(f"   ✅ 重要标签: {important_tag_id}")
        print(f"   ✅ 项目A标签: {project_tag_id}")
        
        # 3. 为文件添加标签
        print("\n3️⃣ 为文件添加标签...")
        tag_service.add_tag_to_file(file_id, work_tag_id)
        tag_service.add_tag_to_file(file_id, important_tag_id)
        print("   ✅ 已为文件添加标签")
        
        # 4. 查看文件的标签
        print("\n4️⃣ 查看文件的标签...")
        file_tags = tag_service.get_file_tags(file_id)
        for tag in file_tags:
            print(f"   🏷️  {tag['name']} ({tag['color']})")
        
        # 5. 按标签搜索文件
        print("\n5️⃣ 按标签搜索文件...")
        work_files = tag_service.get_files_by_tag(work_tag_id)
        print(f"   📄 '工作文档'标签的文件数量: {len(work_files)}")
        
        # 6. 获取标签统计
        print("\n6️⃣ 获取标签使用统计...")
        stats = tag_service.get_tag_statistics()
        for stat in stats:
            print(f"   📊 {stat['name']}: {stat['file_count']} 个文件")
        
        # 7. 获取所有标签
        print("\n7️⃣ 获取所有标签...")
        all_tags = tag_service.get_all_tags()
        print(f"   📋 总共有 {len(all_tags)} 个标签")
        
        print("\n🎉 标签系统集成测试完成！所有功能正常工作。")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        if os.path.exists(test_file):
            os.unlink(test_file)
            print(f"\n🧹 已清理测试文件: {test_file}")


if __name__ == "__main__":
    test_tag_system_integration()
