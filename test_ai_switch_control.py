#!/usr/bin/env python3
"""
AI开关控制测试脚本

测试AI功能开关是否能正确控制AI功能，确保不影响现有功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from smartvault.services.ai.ai_manager import AIManager
from smartvault.services.auto_tag_service import AutoTagService
from smartvault.utils.config import get_ai_status, save_ai_status, get_ai_config


def test_ai_switch_off():
    """测试AI功能关闭状态"""
    print("=== 测试AI功能关闭状态 ===")

    # 1. 确保AI功能关闭
    save_ai_status(False)

    # 2. 创建AI管理器
    ai_manager = AIManager()

    # 3. 测试配置（AI关闭）
    test_config = {
        "advanced": {
            "enable_ai_features": False
        },
        "ai": {
            "enabled": False,
            "stage": "rule_based"
        }
    }

    # 4. 初始化AI管理器
    success = ai_manager.initialize(test_config)
    print(f"AI管理器初始化: {'成功' if success else '失败'}")

    # 5. 检查AI状态
    status = ai_manager.get_status()
    print(f"AI启用状态: {status['enabled']}")
    print(f"AI可用性: {ai_manager.is_available()}")

    # 6. 测试AI功能调用（应该返回降级结果）
    test_file = {
        'name': 'test_document.pdf',
        'path': '/test/test_document.pdf'
    }

    suggestions = ai_manager.suggest_tags(test_file)
    print(f"AI关闭时的标签建议: {suggestions}")

    # 7. 测试自动标签服务集成
    auto_tag_service = AutoTagService()
    auto_tag_service.set_ai_manager(ai_manager)

    # 添加一个测试规则
    from smartvault.services.auto_tag_service import AutoTagRule, ConditionType
    test_rule = AutoTagRule(
        id="test_pdf_rule",
        name="PDF文档规则",
        condition_type=ConditionType.FILE_EXTENSION,
        condition_value=".pdf",
        tag_names=["文档", "PDF"]
    )
    auto_tag_service.add_rule(test_rule)

    # 获取自动标签（应该只有规则标签，没有AI标签）
    auto_tags = auto_tag_service.get_auto_tags_for_file(test_file)
    print(f"AI关闭时的自动标签: {auto_tags}")

    # 8. 验证只有规则标签
    rule_only_tags = auto_tag_service.get_rule_based_tags(test_file)
    ai_only_tags = auto_tag_service.get_ai_suggested_tags(test_file)

    print(f"仅规则标签: {rule_only_tags}")
    print(f"仅AI标签: {ai_only_tags}")

    assert not status['enabled'], "AI应该处于关闭状态"
    assert not ai_manager.is_available(), "AI应该不可用"
    assert len(ai_only_tags) == 0, "AI关闭时不应该有AI标签建议"
    assert len(rule_only_tags) > 0, "规则标签应该正常工作"

    print("✅ AI关闭状态测试通过")


def test_ai_switch_on():
    """测试AI功能开启状态"""
    print("\n=== 测试AI功能开启状态 ===")

    # 1. 启用AI功能
    save_ai_status(True)

    # 2. 创建AI管理器
    ai_manager = AIManager()

    # 3. 测试配置（AI开启）
    test_config = {
        "advanced": {
            "enable_ai_features": True
        },
        "ai": {
            "enabled": True,
            "stage": "rule_based"
        }
    }

    # 4. 初始化AI管理器
    success = ai_manager.initialize(test_config)
    print(f"AI管理器初始化: {'成功' if success else '失败'}")

    # 5. 检查AI状态
    status = ai_manager.get_status()
    print(f"AI启用状态: {status['enabled']}")
    print(f"AI可用性: {ai_manager.is_available()}")

    # 6. 测试AI功能调用（应该返回AI建议）
    test_file = {
        'name': 'test_document_v1.pdf',
        'path': '/test/project/test_document_v1.pdf'
    }

    suggestions = ai_manager.suggest_tags(test_file)
    print(f"AI开启时的标签建议: {suggestions}")

    # 7. 测试自动标签服务集成
    auto_tag_service = AutoTagService()
    auto_tag_service.set_ai_manager(ai_manager)

    # 添加一个测试规则
    from smartvault.services.auto_tag_service import AutoTagRule, ConditionType
    test_rule = AutoTagRule(
        id="test_pdf_rule_2",
        name="PDF文档规则",
        condition_type=ConditionType.FILE_EXTENSION,
        condition_value=".pdf",
        tag_names=["文档", "PDF"]
    )
    auto_tag_service.add_rule(test_rule)

    # 获取自动标签（应该包含规则标签和AI标签）
    auto_tags = auto_tag_service.get_auto_tags_for_file(test_file)
    print(f"AI开启时的自动标签: {auto_tags}")

    # 8. 验证包含规则标签和AI标签
    rule_only_tags = auto_tag_service.get_rule_based_tags(test_file)
    ai_only_tags = auto_tag_service.get_ai_suggested_tags(test_file)

    print(f"仅规则标签: {rule_only_tags}")
    print(f"仅AI标签: {ai_only_tags}")

    assert status['enabled'], "AI应该处于开启状态"
    assert ai_manager.is_available(), "AI应该可用"
    assert len(rule_only_tags) > 0, "规则标签应该正常工作"
    assert len(auto_tags) >= len(rule_only_tags), "自动标签应该包含规则标签"

    print("✅ AI开启状态测试通过")


def test_config_consistency():
    """测试配置一致性"""
    print("\n=== 测试配置一致性 ===")

    # 1. 测试关闭状态的配置一致性
    save_ai_status(False)
    ai_status = get_ai_status()
    ai_config = get_ai_config()

    print(f"AI状态（关闭）: {ai_status}")
    print(f"AI配置启用状态: {ai_config.get('enabled', False)}")

    assert not ai_status, "AI状态应该为False"
    assert not ai_config.get('enabled', False), "AI配置中的enabled应该为False"

    # 2. 测试开启状态的配置一致性
    save_ai_status(True)
    ai_status = get_ai_status()
    ai_config = get_ai_config()

    print(f"AI状态（开启）: {ai_status}")
    print(f"AI配置启用状态: {ai_config.get('enabled', False)}")

    assert ai_status, "AI状态应该为True"
    assert ai_config.get('enabled', False), "AI配置中的enabled应该为True"

    print("✅ 配置一致性测试通过")


def test_fallback_behavior():
    """测试降级行为"""
    print("\n=== 测试降级行为 ===")

    # 1. 创建AI管理器但不初始化
    ai_manager = AIManager()

    # 2. 测试未初始化状态
    print(f"未初始化时AI可用性: {ai_manager.is_available()}")

    test_file = {
        'name': 'fallback_test.txt',
        'path': '/test/fallback_test.txt'
    }

    # 3. 测试降级建议
    fallback_suggestions = ai_manager.suggest_tags(test_file)
    print(f"降级建议: {fallback_suggestions}")

    # 4. 验证降级服务正常工作
    assert not ai_manager.is_available(), "未初始化的AI应该不可用"
    assert len(fallback_suggestions) > 0, "降级服务应该提供建议"

    print("✅ 降级行为测试通过")


def test_existing_functionality():
    """测试现有功能不受影响"""
    print("\n=== 测试现有功能不受影响 ===")

    # 1. 测试自动标签服务的基础功能
    auto_tag_service = AutoTagService()

    # 2. 添加规则
    from smartvault.services.auto_tag_service import AutoTagRule, ConditionType

    rules = [
        AutoTagRule(id="img_rule", name="图片规则", condition_type=ConditionType.FILE_EXTENSION, condition_value=".jpg", tag_names=["图片", "照片"]),
        AutoTagRule(id="doc_rule", name="文档规则", condition_type=ConditionType.FILE_EXTENSION, condition_value=".pdf", tag_names=["文档", "PDF"]),
        AutoTagRule(id="code_rule", name="代码规则", condition_type=ConditionType.FILE_EXTENSION, condition_value=".py", tag_names=["代码", "Python"])
    ]

    for rule in rules:
        auto_tag_service.add_rule(rule)

    # 3. 测试不同文件类型
    test_files = [
        {'name': 'photo.jpg', 'path': '/test/photo.jpg'},
        {'name': 'document.pdf', 'path': '/test/document.pdf'},
        {'name': 'script.py', 'path': '/test/script.py'},
        {'name': 'unknown.xyz', 'path': '/test/unknown.xyz'}
    ]

    print("测试现有自动标签功能:")
    for test_file in test_files:
        tags = auto_tag_service.get_rule_based_tags(test_file)
        print(f"  {test_file['name']}: {tags}")

    # 4. 验证规则功能正常
    jpg_tags = auto_tag_service.get_rule_based_tags(test_files[0])
    pdf_tags = auto_tag_service.get_rule_based_tags(test_files[1])
    py_tags = auto_tag_service.get_rule_based_tags(test_files[2])
    unknown_tags = auto_tag_service.get_rule_based_tags(test_files[3])

    assert "图片" in jpg_tags, f"JPG文件应该有图片标签，实际标签：{jpg_tags}"
    assert "文档" in pdf_tags, f"PDF文件应该有文档标签，实际标签：{pdf_tags}"
    assert "代码" in py_tags, f"Python文件应该有代码标签，实际标签：{py_tags}"
    assert len(unknown_tags) == 0, f"未知扩展名文件不应该有标签，实际标签：{unknown_tags}"

    print("✅ 现有功能测试通过")


def main():
    """主测试函数"""
    print("SmartVault AI开关控制测试")
    print("=" * 50)

    try:
        # 测试现有功能不受影响
        test_existing_functionality()

        # 测试AI开关控制
        test_ai_switch_off()
        test_ai_switch_on()

        # 测试配置一致性
        test_config_consistency()

        # 测试降级行为
        test_fallback_behavior()

        print("\n" + "=" * 50)
        print("✅ 所有AI开关控制测试通过")
        print("✅ 现有功能完全不受影响")
        print("✅ AI功能可以安全启用/禁用")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
