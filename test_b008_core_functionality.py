#!/usr/bin/env python3
"""
测试B008核心功能：文件自动入库
"""

import sys
import os
import tempfile
import shutil
import time
import uuid
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from smartvault.services.file_monitor_service import FileMonitorService
from smartvault.services.file import FileService


def test_b008_core_functionality():
    """测试B008核心功能"""
    print("🚀 测试B008核心功能：文件自动入库")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp(prefix="smartvault_b008_test_")
    monitor_dir = os.path.join(temp_dir, "monitor")
    os.makedirs(monitor_dir, exist_ok=True)
    
    print(f"📁 测试目录: {temp_dir}")
    print(f"📁 监控目录: {monitor_dir}")
    
    try:
        # 创建服务实例
        monitor_service = FileMonitorService()
        file_service = FileService()
        
        # 记录监控事件
        monitor_events = []
        
        def on_file_event(event_type, file_path, monitor_id):
            monitor_events.append({
                "type": event_type,
                "path": file_path,
                "monitor_id": monitor_id,
                "timestamp": datetime.now()
            })
            print(f"   📝 监控事件: {event_type} - {os.path.basename(file_path)}")
        
        print("\n🧪 测试1: 链接模式自动入库")
        
        # 添加监控文件夹
        monitor_id = monitor_service.add_monitor_folder(
            folder_path=monitor_dir,
            entry_mode="link",
            file_patterns=["*.txt"],
            auto_add=True,
            recursive=False
        )
        
        # 设置事件回调
        monitor_service.set_event_callback(on_file_event)
        
        # 启动监控
        success = monitor_service.start_monitoring(monitor_id)
        assert success is True
        print(f"   ✅ 监控已启动: {monitor_id}")
        
        # 获取入库前的文件总数
        count_before = file_service.get_file_count()
        print(f"   📊 入库前文件总数: {count_before}")
        
        # 创建测试文件
        unique_name = f"test_link_{str(uuid.uuid4())[:8]}.txt"
        test_file = os.path.join(monitor_dir, unique_name)
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("这是一个链接模式测试文件")
        print(f"   📄 创建测试文件: {unique_name}")
        
        # 等待文件监控处理
        time.sleep(3)
        
        # 检查文件总数是否增加
        count_after = file_service.get_file_count()
        print(f"   📊 入库后文件总数: {count_after}")
        
        # 验证文件被添加
        if count_after > count_before:
            print("   ✅ 文件已自动添加到库")
        else:
            print("   ❌ 文件未被添加到库")
            return False
        
        # 验证原文件仍在原位置
        if os.path.exists(test_file):
            print("   ✅ 原文件仍在原位置")
        else:
            print("   ❌ 原文件不存在")
            return False
        
        print("   🎉 链接模式自动入库测试通过")
        
        print("\n🧪 测试2: 复制模式自动入库")
        
        # 停止之前的监控
        monitor_service.stop_monitoring(monitor_id)
        
        # 添加新的监控文件夹（复制模式）
        monitor_id2 = monitor_service.add_monitor_folder(
            folder_path=monitor_dir,
            entry_mode="copy",
            file_patterns=["*.pdf"],
            auto_add=True,
            recursive=False
        )
        
        # 启动监控
        success = monitor_service.start_monitoring(monitor_id2)
        assert success is True
        print(f"   ✅ 监控已启动: {monitor_id2}")
        
        # 获取入库前的文件总数
        count_before = file_service.get_file_count()
        
        # 创建测试文件
        unique_name = f"test_copy_{str(uuid.uuid4())[:8]}.pdf"
        test_file = os.path.join(monitor_dir, unique_name)
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("这是一个复制模式测试文件")
        print(f"   📄 创建测试文件: {unique_name}")
        
        # 等待文件监控处理
        time.sleep(3)
        
        # 检查文件总数是否增加
        count_after = file_service.get_file_count()
        print(f"   📊 入库后文件总数: {count_after}")
        
        # 验证文件被添加
        if count_after > count_before:
            print("   ✅ 文件已自动添加到库")
        else:
            print("   ❌ 文件未被添加到库")
            return False
        
        # 验证原文件仍在原位置
        if os.path.exists(test_file):
            print("   ✅ 原文件仍在原位置")
        else:
            print("   ❌ 原文件不存在")
            return False
        
        print("   🎉 复制模式自动入库测试通过")
        
        print("\n🧪 测试3: 文件类型过滤")
        
        # 停止之前的监控
        monitor_service.stop_monitoring(monitor_id2)
        
        # 添加新的监控文件夹（只监控.doc文件）
        monitor_id3 = monitor_service.add_monitor_folder(
            folder_path=monitor_dir,
            entry_mode="link",
            file_patterns=["*.doc"],
            auto_add=True,
            recursive=False
        )
        
        # 启动监控
        success = monitor_service.start_monitoring(monitor_id3)
        assert success is True
        print(f"   ✅ 监控已启动: {monitor_id3}")
        
        # 获取入库前的文件总数
        count_before = file_service.get_file_count()
        
        # 创建匹配的文件
        unique_id = str(uuid.uuid4())[:8]
        doc_name = f"should_add_{unique_id}.doc"
        doc_file = os.path.join(monitor_dir, doc_name)
        with open(doc_file, 'w', encoding='utf-8') as f:
            f.write("应该被添加的doc文件")
        
        # 创建不匹配的文件
        txt_name = f"should_ignore_{unique_id}.txt"
        txt_file = os.path.join(monitor_dir, txt_name)
        with open(txt_file, 'w', encoding='utf-8') as f:
            f.write("应该被忽略的txt文件")
        
        print(f"   📄 创建doc文件: {doc_name}")
        print(f"   📄 创建txt文件: {txt_name}")
        
        # 等待文件监控处理
        time.sleep(3)
        
        # 检查文件总数变化
        count_after = file_service.get_file_count()
        print(f"   📊 入库后文件总数: {count_after}")
        
        # 应该只增加了一个文件（doc文件）
        files_added = count_after - count_before
        if files_added == 1:
            print("   ✅ 只有匹配的文件被添加")
        else:
            print(f"   ❌ 期望添加1个文件，实际添加{files_added}个")
            return False
        
        print("   🎉 文件类型过滤测试通过")
        
        # 停止所有监控
        monitor_service.stop_all_monitoring()
        
        print("\n🎉 B008核心功能测试全部通过！")
        print("✅ 文件自动入库功能正常工作")
        print("✅ 支持链接模式和复制模式")
        print("✅ 文件类型过滤正常工作")
        print("✅ 重复文件检查正常工作")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理测试目录
        if os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir)
                print(f"\n🧹 清理测试目录: {temp_dir}")
            except Exception as e:
                print(f"清理测试目录失败: {e}")


if __name__ == "__main__":
    success = test_b008_core_functionality()
    sys.exit(0 if success else 1)
