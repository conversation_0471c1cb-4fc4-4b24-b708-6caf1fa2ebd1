# SmartVault 代码质量分析报告
分析时间: 2025-05-27 16:59:40.431767
分析文件数: 1692

## 🚨 可能的死代码
- `chunk_iCCP()` in .venv\Lib\site-packages\PIL\PngImagePlugin.py:396
- `directory_arg()` in .venv\Lib\site-packages\_pytest\config\__init__.py:215
- `default_predicate()` in .venv\Lib\site-packages\astroid\brain\brain_six.py:23
- `_key_column_X_key()` in .venv\Lib\site-packages\sqlalchemy\sql\naming.py:72
- `gen_testing_engine()` in .venv\Lib\site-packages\sqlalchemy\testing\fixtures\base.py:166
- `inc_convert()` in .venv\Lib\site-packages\pip\_vendor\distlib\util.py:1722
- `started()` in .venv\Lib\site-packages\pip\_vendor\rich\progress.py:990
- `named()` in .venv\Lib\site-packages\PIL\TiffImagePlugin.py:546
- `_lazyload_reverse()` in .venv\Lib\site-packages\sqlalchemy\orm\strategies.py:1078
- `strictly_right_of()` in .venv\Lib\site-packages\sqlalchemy\dialects\postgresql\ranges.py:811
... 还有 4219 个

## 🔄 重复方法
- `main_0` (41 个重复)
  - architecture_risk_assessment_2024.py:272
  - check_b2_completion.py:193
  - comprehensive_database_security_test.py:611
  - database_safety_test.py:761
  - database_security_fix.py:335
  - data_integrity_deep_test.py:413
  - debug_start.py:90
  - demo_folder_navigation.py:147
  - demo_quick_tag_menu.py:164
  - demo_tag_tree_improvement.py:91
  - diagnose_move_issue.py:301
  - emergency_start.py:106
  - final_security_assessment.py:355
  - fix_database_b022.py:154
  - fix_schedule_issue.py:9
  - force_cleanup.py:213
  - install_dependencies.py:35
  - migrate_to_library_config.py:197
  - minimal_main_window.py:160
  - minimal_test.py:182
  - monitor_diagnosis.py:143
  - run_smartvault.py:51
  - simple_start.py:11
  - startup_diagnostic.py:239
  - startup_monitor.py:118
  - .venv\Lib\site-packages\pip\_vendor\cachecontrol\_cmd.py:46
  - .venv\Lib\site-packages\pip\_vendor\distro\distro.py:1362
  - .venv\Lib\site-packages\pip\_vendor\platformdirs\__main__.py:26
  - .venv\Lib\site-packages\pip\_vendor\pyproject_hooks\_in_process\_in_process.py:353
  - .venv\Lib\site-packages\pip\_vendor\requests\help.py:121
  - .venv\Lib\site-packages\platformdirs\__main__.py:26
  - .venv\Lib\site-packages\psutil\tests\__init__.py:888
  - .venv\Lib\site-packages\PySide6\scripts\pyside_tool.py:40
  - .venv\Lib\site-packages\watchdog\watchmedo.py:784
  - smartvault\main.py:43
  - tools\code_quality_analyzer.py:279
  - utils\code_converter.py:105
  - utils\code_quality_check.py:226
  - utils\library_fix.py:268
  - utils\startup_fix.py:333
  - utils\startup_test.py:209
- `log_2` (7 个重复)
  - comprehensive_database_security_test.py:32
  - database_safety_test.py:34
  - database_security_fix.py:23
  - data_integrity_deep_test.py:25
  - minimal_main_window.py:75
  - .venv\Lib\site-packages\coverage\debug.py:406
  - .venv\Lib\site-packages\coverage\pytracer.py:93
- `get_database_path_1` (4 个重复)
  - comprehensive_database_security_test.py:39
  - database_security_fix.py:30
  - data_integrity_deep_test.py:32
  - smartvault\services\backup_service.py:78
- `create_backup_2` (2 个重复)
  - database_security_fix.py:42
  - smartvault\services\backup_service.py:83
- `auto_exit_0` (2 个重复)
  - debug_start.py:154
  - startup_monitor.py:93

## 📏 复杂文件 (>1000行)
- .venv\Lib\site-packages\pip\_vendor\idna\uts46data.py: 8681 行, 82 方法
- .venv\Lib\site-packages\sqlalchemy\sql\compiler.py: 7645 行, 351 方法
- .venv\Lib\site-packages\sqlalchemy\sql\selectable.py: 6916 行, 311 方法
- .venv\Lib\site-packages\sqlalchemy\sql\schema.py: 6082 行, 198 方法
- .venv\Lib\site-packages\pkg_resources\_vendor\pyparsing.py: 5742 行, 327 方法

## 💡 重构建议
1. **优先处理死代码**: 移除未使用的方法可以立即简化代码
2. **拆分复杂文件**: 超过1000行的文件建议按功能拆分
3. **合并重复方法**: 相似功能的方法可以合并或提取公共逻辑
4. **清理未使用导入**: 提高代码可读性