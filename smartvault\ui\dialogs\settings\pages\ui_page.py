"""
界面设置页面
从原 settings_dialog.py 中的 create_ui_tab 方法迁移而来
"""

from PySide6.QtWidgets import (
    QVBoxLayout, QHBoxLayout, QGroupBox, QLabel, QComboBox
)
from typing import Tuple
from ..base.base_page import BaseSettingsPage


class UISettingsPage(BaseSettingsPage):
    """界面设置页面"""

    def setup_ui(self):
        """设置UI界面"""
        layout = QVBoxLayout(self)

        # 视图设置组
        view_group = QGroupBox("视图设置")
        view_layout = QVBoxLayout(view_group)

        # 默认视图模式
        view_mode_layout = QHBoxLayout()
        view_mode_layout.addWidget(QLabel("默认视图模式:"))

        self.view_mode_combo = QComboBox()
        self.view_mode_combo.addItems(["表格视图", "网格视图", "详情视图"])
        view_mode_layout.addWidget(self.view_mode_combo)
        view_mode_layout.addStretch()

        view_layout.addLayout(view_mode_layout)

        # 默认页面大小
        page_size_layout = QHBoxLayout()
        page_size_layout.addWidget(QLabel("默认页面大小:"))

        self.page_size_combo = QComboBox()
        self.page_size_combo.addItems(["50", "100", "200", "500", "全部"])
        page_size_layout.addWidget(self.page_size_combo)
        page_size_layout.addStretch()

        view_layout.addLayout(page_size_layout)

        layout.addWidget(view_group)

        layout.addStretch()

    def load_settings(self, config: dict):
        """从配置加载设置到UI控件

        Args:
            config: 配置字典
        """
        self.config = config
        ui_config = config.get("ui", {})

        # 默认视图模式
        default_view = ui_config.get("default_view_mode", "table")
        view_mode_map = {"table": 0, "grid": 1, "details": 2}
        self.view_mode_combo.setCurrentIndex(view_mode_map.get(default_view, 0))

        # 默认页面大小
        default_page_size = str(ui_config.get("default_page_size", 100))
        if default_page_size in ["50", "100", "200", "500"]:
            self.page_size_combo.setCurrentText(default_page_size)
        else:
            self.page_size_combo.setCurrentText("100")

    def save_settings(self) -> dict:
        """从UI控件保存设置到配置

        Returns:
            dict: UI设置字典
        """
        # 视图模式映射
        view_mode_map = {0: "table", 1: "grid", 2: "details"}
        view_mode = view_mode_map.get(self.view_mode_combo.currentIndex(), "table")

        # 页面大小
        page_size_text = self.page_size_combo.currentText()
        if page_size_text == "全部":
            page_size = 999999
        else:
            try:
                page_size = int(page_size_text)
            except ValueError:
                page_size = 100

        return {
            "default_view_mode": view_mode,
            "default_page_size": page_size
        }

    def validate_settings(self) -> Tuple[bool, str]:
        """验证设置有效性

        Returns:
            tuple: (是否有效, 错误信息)
        """
        # UI设置通常不需要特殊验证
        return True, ""

    def reset_to_defaults(self):
        """重置为默认设置"""
        self.view_mode_combo.setCurrentIndex(0)  # 表格视图
        self.page_size_combo.setCurrentText("100")

    def get_page_title(self) -> str:
        """获取页面标题

        Returns:
            str: 页面标题
        """
        return "界面设置"
