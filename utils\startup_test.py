"""
启动稳定性测试工具
"""

import os
import sys
import time
import subprocess
import signal

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class StartupTester:
    """启动稳定性测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.test_results = []
        
    def test_startup_stability(self, test_count=5):
        """测试启动稳定性
        
        Args:
            test_count: 测试次数
        """
        print(f"🧪 开始启动稳定性测试 (共{test_count}次)...")
        
        success_count = 0
        
        for i in range(test_count):
            print(f"\n📋 第 {i+1}/{test_count} 次测试")
            
            result = self._single_startup_test(i+1)
            self.test_results.append(result)
            
            if result['success']:
                success_count += 1
                print(f"✅ 第 {i+1} 次测试成功")
            else:
                print(f"❌ 第 {i+1} 次测试失败: {result['error']}")
            
            # 测试间隔
            if i < test_count - 1:
                print("⏳ 等待5秒后进行下一次测试...")
                time.sleep(5)
        
        # 生成报告
        self._generate_stability_report(success_count, test_count)
    
    def _single_startup_test(self, test_num):
        """单次启动测试
        
        Args:
            test_num: 测试编号
            
        Returns:
            dict: 测试结果
        """
        start_time = time.time()
        
        try:
            # 启动程序
            print("  🚀 启动程序...")
            process = subprocess.Popen(
                [sys.executable, "run.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            )
            
            # 等待程序启动
            startup_timeout = 15  # 15秒启动超时
            startup_success = False
            
            for _ in range(startup_timeout * 10):  # 每0.1秒检查一次
                if process.poll() is not None:
                    # 程序已退出
                    stdout, stderr = process.communicate()
                    return {
                        'test_num': test_num,
                        'success': False,
                        'error': f"程序启动后立即退出\nstdout: {stdout}\nstderr: {stderr}",
                        'startup_time': time.time() - start_time
                    }
                
                # 检查是否有启动成功的标志
                try:
                    stdout_data = process.stdout.read(1024)
                    if stdout_data and "进入应用程序事件循环" in stdout_data:
                        startup_success = True
                        break
                except:
                    pass
                
                time.sleep(0.1)
            
            if not startup_success:
                # 启动超时
                process.terminate()
                process.wait(timeout=5)
                return {
                    'test_num': test_num,
                    'success': False,
                    'error': "启动超时",
                    'startup_time': time.time() - start_time
                }
            
            startup_time = time.time() - start_time
            print(f"  ✅ 程序启动成功 (耗时: {startup_time:.2f}秒)")
            
            # 让程序运行3秒
            print("  ⏳ 程序运行中...")
            time.sleep(3)
            
            # 优雅地关闭程序
            print("  🛑 关闭程序...")
            if os.name == 'nt':  # Windows
                process.send_signal(signal.CTRL_C_EVENT)
            else:  # Unix/Linux
                process.send_signal(signal.SIGTERM)
            
            # 等待程序关闭
            try:
                process.wait(timeout=10)
                print("  ✅ 程序正常关闭")
            except subprocess.TimeoutExpired:
                print("  ⚠️  程序未能正常关闭，强制终止")
                process.kill()
                process.wait()
            
            return {
                'test_num': test_num,
                'success': True,
                'error': None,
                'startup_time': startup_time
            }
            
        except Exception as e:
            return {
                'test_num': test_num,
                'success': False,
                'error': str(e),
                'startup_time': time.time() - start_time
            }
    
    def _generate_stability_report(self, success_count, total_count):
        """生成稳定性报告
        
        Args:
            success_count: 成功次数
            total_count: 总测试次数
        """
        print("\n" + "="*60)
        print("📊 启动稳定性测试报告")
        print("="*60)
        
        success_rate = (success_count / total_count) * 100
        print(f"总测试次数: {total_count}")
        print(f"成功次数: {success_count}")
        print(f"失败次数: {total_count - success_count}")
        print(f"成功率: {success_rate:.1f}%")
        
        # 计算平均启动时间
        successful_tests = [r for r in self.test_results if r['success']]
        if successful_tests:
            avg_startup_time = sum(r['startup_time'] for r in successful_tests) / len(successful_tests)
            print(f"平均启动时间: {avg_startup_time:.2f}秒")
        
        print("\n📋 详细结果:")
        for result in self.test_results:
            status = "✅ 成功" if result['success'] else "❌ 失败"
            print(f"  第{result['test_num']}次: {status} (启动时间: {result['startup_time']:.2f}秒)")
            if not result['success']:
                print(f"    错误: {result['error']}")
        
        # 评估稳定性
        print("\n💡 稳定性评估:")
        if success_rate >= 90:
            print("  🎉 启动稳定性优秀！")
        elif success_rate >= 70:
            print("  ✅ 启动稳定性良好")
        elif success_rate >= 50:
            print("  ⚠️  启动稳定性一般，建议进一步优化")
        else:
            print("  ❌ 启动稳定性较差，需要修复")
        
        # 建议
        if success_count < total_count:
            print("\n🔧 改进建议:")
            failed_tests = [r for r in self.test_results if not r['success']]
            
            # 分析失败原因
            timeout_failures = len([r for r in failed_tests if "超时" in r['error']])
            crash_failures = len([r for r in failed_tests if "退出" in r['error']])
            
            if timeout_failures > 0:
                print(f"  - {timeout_failures} 次启动超时，考虑优化启动流程")
            if crash_failures > 0:
                print(f"  - {crash_failures} 次启动崩溃，检查异常处理")
            
            print("  - 运行 utils/startup_fix.py 进行问题诊断")
            print("  - 检查数据库连接和资源文件")
            print("  - 确保没有进程残留")


def main():
    """主函数"""
    print("🧪 SmartVault 启动稳定性测试工具")
    print("="*40)
    
    # 询问测试次数
    try:
        test_count = int(input("请输入测试次数 (默认5次): ") or "5")
        if test_count <= 0:
            test_count = 5
    except ValueError:
        test_count = 5
    
    tester = StartupTester()
    tester.test_startup_stability(test_count)


if __name__ == "__main__":
    main()
