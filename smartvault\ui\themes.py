"""
主题管理模块
"""

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QSettings


class ThemeManager:
    """主题管理器"""

    # 可用主题
    THEMES = {
        "light": "浅色主题",
        "dark": "深色主题",
        "blue": "蓝色主题",
        "green": "绿色主题"
    }

    def __init__(self):
        self.settings = QSettings("SmartVault", "Themes")
        self.current_theme = self.settings.value("current_theme", "light")

    def get_current_theme(self):
        """获取当前主题"""
        return self.current_theme

    def set_theme(self, theme_name):
        """设置主题

        Args:
            theme_name: 主题名称
        """
        if theme_name in self.THEMES:
            self.current_theme = theme_name
            self.settings.setValue("current_theme", theme_name)
            self.apply_theme(theme_name)

    def apply_theme(self, theme_name):
        """应用主题

        Args:
            theme_name: 主题名称
        """
        app = QApplication.instance()
        if not app:
            return

        if theme_name == "light":
            app.setStyleSheet(self.get_light_theme())
        elif theme_name == "dark":
            app.setStyleSheet(self.get_dark_theme())
        elif theme_name == "blue":
            app.setStyleSheet(self.get_blue_theme())
        elif theme_name == "green":
            app.setStyleSheet(self.get_green_theme())

    def get_light_theme(self):
        """浅色主题样式"""
        return """
        QWidget {
            background-color: #ffffff;
            color: #333333;
            font-family: "Microsoft YaHei", "SimHei", "Arial Unicode MS", sans-serif;
        }

        QMainWindow {
            background-color: #f5f5f5;
        }

        QMenuBar {
            background-color: #ffffff;
            border-bottom: 1px solid #e0e0e0;
            padding: 2px;
        }

        QMenuBar::item {
            background-color: transparent;
            padding: 4px 8px;
            border-radius: 3px;
        }

        QMenuBar::item:selected {
            background-color: #e3f2fd;
        }

        QMenu {
            background-color: #ffffff;
            border: 1px solid #cccccc;
            border-radius: 4px;
        }

        QMenu::item {
            padding: 6px 20px;
        }

        QMenu::item:selected {
            background-color: #e3f2fd;
        }

        QToolBar {
            background-color: #ffffff;
            border: none;
            spacing: 2px;
        }

        QStatusBar {
            background-color: #f5f5f5;
            border-top: 1px solid #e0e0e0;
        }

        QPushButton {
            background-color: #ffffff;
            border: 1px solid #cccccc;
            border-radius: 4px;
            padding: 6px 12px;
            min-width: 60px;
        }

        QPushButton:hover {
            background-color: #f0f0f0;
            border-color: #999999;
        }

        QPushButton:pressed {
            background-color: #e0e0e0;
        }

        QLineEdit, QTextEdit, QPlainTextEdit {
            background-color: #ffffff;
            border: 1px solid #cccccc;
            border-radius: 3px;
            padding: 4px;
        }

        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
            border-color: #2196f3;
        }

        QTableView, QTreeView, QListView {
            background-color: #ffffff;
            alternate-background-color: #f9f9f9;
            border: 1px solid #e0e0e0;
            gridline-color: #e0e0e0;
        }

        QTableView::item:selected, QTreeView::item:selected, QListView::item:selected {
            background-color: #d1d1d1;
            color: #333333;
            margin: 1px 0px;
            border-radius: 2px;
        }

        QTableView::item:hover, QTreeView::item:hover, QListView::item:hover {
            background-color: #e8e8e8;
            margin: 1px 0px;
            border-radius: 2px;
        }

        QHeaderView::section {
            background-color: #f5f5f5;
            border: none;
            border-right: 1px solid #e0e0e0;
            border-bottom: 1px solid #e0e0e0;
            padding: 6px;
        }

        QScrollBar:vertical {
            background-color: #f5f5f5;
            width: 12px;
            border-radius: 6px;
        }

        QScrollBar::handle:vertical {
            background-color: #cccccc;
            border-radius: 6px;
            min-height: 20px;
        }

        QScrollBar::handle:vertical:hover {
            background-color: #999999;
        }

        /* 文件视图顶部工具栏 */
        QWidget#fileViewTopToolbar {
            background-color: #f5f5f5;
            border-bottom: 1px solid #ddd;
        }

        /* 分页按钮 */
        QToolButton#paginationButton {
            background-color: #f8f8f8;
            border: 1px solid #ccc;
            border-radius: 3px;
            min-width: 30px;
            min-height: 30px;
            color: #333333;
        }

        QToolButton#paginationButton:hover {
            background-color: #e0e0e0;
            border-color: #999999;
        }

        QToolButton#paginationButton:pressed {
            background-color: #d0d0d0;
        }

        QToolButton#paginationButton:disabled {
            background-color: #f0f0f0;
            color: #999999;
            border-color: #e0e0e0;
        }

        /* 搜索控件 */
        QLineEdit#searchEdit {
            background-color: #ffffff;
            border: 2px solid #e0e0e0;
            border-radius: 4px;
            padding: 4px;
            color: #333333;
        }

        QLineEdit#searchEdit:focus {
            border-color: #4CAF50;
            background-color: #f9fff9;
        }

        QComboBox#searchCombo {
            background-color: #ffffff;
            border: 2px solid #e0e0e0;
            border-radius: 4px;
            padding: 4px;
            color: #333333;
            min-width: 100px;
        }

        QComboBox#searchCombo:hover {
            border-color: #4CAF50;
        }

        QComboBox#searchCombo:focus {
            border-color: #4CAF50;
            background-color: #f9fff9;
        }

        /* 高级搜索按钮 */
        QPushButton#advancedSearchButton {
            background-color: #4CAF50;
            border: 1px solid #45a049;
            border-radius: 4px;
            padding: 6px 12px;
            color: white;
            font-weight: bold;
            min-height: 25px;
        }

        QPushButton#advancedSearchButton:hover {
            background-color: #45a049;
            border-color: #3d8b40;
        }

        QPushButton#advancedSearchButton:pressed {
            background-color: #3d8b40;
        }
        """

    def get_dark_theme(self):
        """深色主题样式"""
        return """
        QWidget {
            background-color: #2b2b2b;
            color: #ffffff;
            font-family: "Microsoft YaHei", "SimHei", "Arial Unicode MS", sans-serif;
        }

        QMainWindow {
            background-color: #1e1e1e;
        }

        /* 中央部件和分割器 */
        QSplitter {
            background-color: #2b2b2b;
        }

        QSplitter::handle {
            background-color: #404040;
        }

        /* 菜单栏 */
        QMenuBar {
            background-color: #2b2b2b;
            border-bottom: 1px solid #404040;
            padding: 2px;
            color: #ffffff;
        }

        QMenuBar::item {
            background-color: transparent;
            padding: 4px 8px;
            border-radius: 3px;
            color: #ffffff;
        }

        QMenuBar::item:selected {
            background-color: #404040;
        }

        QMenu {
            background-color: #2b2b2b;
            border: 1px solid #404040;
            border-radius: 4px;
            color: #ffffff;
        }

        QMenu::item {
            padding: 6px 20px;
            color: #ffffff;
        }

        QMenu::item:selected {
            background-color: #404040;
        }

        /* 工具栏 */
        QToolBar {
            background-color: #2b2b2b;
            border: none;
            spacing: 2px;
            color: #ffffff;
        }

        QToolBar QToolButton {
            background-color: #404040;
            border: 1px solid #555555;
            border-radius: 3px;
            padding: 4px;
            color: #ffffff;
        }

        QToolBar QToolButton:hover {
            background-color: #505050;
        }

        QToolBar QToolButton:pressed {
            background-color: #353535;
        }

        /* 状态栏 */
        QStatusBar {
            background-color: #1e1e1e;
            border-top: 1px solid #404040;
            color: #ffffff;
        }

        /* 按钮 */
        QPushButton {
            background-color: #404040;
            border: 1px solid #555555;
            border-radius: 4px;
            padding: 6px 12px;
            min-width: 60px;
            color: #ffffff;
        }

        QPushButton:hover {
            background-color: #505050;
            border-color: #666666;
        }

        QPushButton:pressed {
            background-color: #353535;
        }

        QPushButton:disabled {
            background-color: #2b2b2b;
            color: #666666;
            border-color: #404040;
        }

        /* 输入框 */
        QLineEdit, QTextEdit, QPlainTextEdit {
            background-color: #404040;
            border: 1px solid #555555;
            border-radius: 3px;
            padding: 4px;
            color: #ffffff;
            selection-background-color: #2196f3;
        }

        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
            border-color: #2196f3;
        }

        /* 表格和列表视图 */
        QTableView, QTreeView, QListView {
            background-color: #2b2b2b;
            alternate-background-color: #353535;
            border: 1px solid #404040;
            gridline-color: #404040;
            color: #ffffff;
            selection-background-color: #2196f3;
            selection-color: #ffffff;
        }

        /* 表格视图默认文字颜色，但允许模型覆盖（用于标签颜色） */
        QTableView {
            color: #ffffff;
        }

        /* 表格、树视图、列表视图的默认样式 - 不强制设置颜色，让代码控制 */
        QTableView::item, QTreeView::item, QListView::item {
            background-color: transparent;
            border: none;
            padding: 4px;
        }

        /* 只为没有自定义颜色的普通文本设置默认白色 */
        QTableView, QTreeView, QListView {
            color: #ffffff;
        }

        /* 标签导航和标签管理树特殊处理：不强制设置颜色，让代码控制 */
        QTreeWidget#tagNavigationTree, QTreeWidget#tagManagementTree, QTreeWidget#fileTagManagementTree {
            color: inherit;
        }

        /* 标签管理对话框右侧信息区域深色主题适配 */
        QLabel#tagDetailsLabel {
            background-color: #404040 !important;
            border-color: #555555 !important;
            color: #ffffff !important;
        }

        QLabel#tagStatsLabel {
            background-color: #353535 !important;
            border-color: #555555 !important;
            color: #ffffff !important;
        }

        QTableView::item:selected, QTreeView::item:selected, QListView::item:selected {
            background-color: #505050;
            color: #ffffff;
            margin: 1px 0px;
            border-radius: 2px;
        }

        QTableView::item:hover, QTreeView::item:hover, QListView::item:hover {
            background-color: #404040;
            margin: 1px 0px;
            border-radius: 2px;
        }

        /* 表头 */
        QHeaderView::section {
            background-color: #1e1e1e;
            border: none;
            border-right: 1px solid #404040;
            border-bottom: 1px solid #404040;
            padding: 6px;
            color: #ffffff;
        }

        QHeaderView::section:hover {
            background-color: #404040;
        }

        /* 滚动条 */
        QScrollBar:vertical {
            background-color: #1e1e1e;
            width: 12px;
            border-radius: 6px;
        }

        QScrollBar::handle:vertical {
            background-color: #555555;
            border-radius: 6px;
            min-height: 20px;
        }

        QScrollBar::handle:vertical:hover {
            background-color: #666666;
        }

        QScrollBar:horizontal {
            background-color: #1e1e1e;
            height: 12px;
            border-radius: 6px;
        }

        QScrollBar::handle:horizontal {
            background-color: #555555;
            border-radius: 6px;
            min-width: 20px;
        }

        QScrollBar::handle:horizontal:hover {
            background-color: #666666;
        }

        QScrollBar::add-line, QScrollBar::sub-line {
            border: none;
            background: none;
        }

        /* 下拉框 */
        QComboBox {
            background-color: #404040;
            border: 1px solid #555555;
            border-radius: 3px;
            padding: 4px;
            color: #ffffff;
        }

        QComboBox:hover {
            border-color: #666666;
        }

        QComboBox::drop-down {
            border: none;
            width: 20px;
        }

        QComboBox::down-arrow {
            image: none;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 4px solid #ffffff;
            margin-right: 4px;
        }

        QComboBox QAbstractItemView {
            background-color: #404040;
            border: 1px solid #555555;
            selection-background-color: #505050;
            color: #ffffff;
        }

        /* 标签页 */
        QTabWidget::pane {
            border: 1px solid #404040;
            background-color: #2b2b2b;
        }

        QTabBar::tab {
            background-color: #404040;
            border: 1px solid #555555;
            padding: 6px 12px;
            color: #ffffff;
        }

        QTabBar::tab:selected {
            background-color: #2b2b2b;
            border-bottom: 1px solid #2b2b2b;
        }

        QTabBar::tab:hover {
            background-color: #505050;
        }

        /* 分组框 */
        QGroupBox {
            border: 1px solid #404040;
            border-radius: 4px;
            margin-top: 8px;
            padding-top: 8px;
            color: #ffffff;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 8px;
            padding: 0 4px 0 4px;
            color: #ffffff;
        }

        /* 复选框和单选框 */
        QCheckBox, QRadioButton {
            color: #ffffff;
        }

        QCheckBox::indicator, QRadioButton::indicator {
            width: 16px;
            height: 16px;
        }

        QCheckBox::indicator:unchecked {
            background-color: #404040;
            border: 1px solid #555555;
        }

        QCheckBox::indicator:checked {
            background-color: #2196f3;
            border: 1px solid #2196f3;
        }

        /* 进度条 */
        QProgressBar {
            background-color: #404040;
            border: 1px solid #555555;
            border-radius: 3px;
            text-align: center;
            color: #ffffff;
        }

        QProgressBar::chunk {
            background-color: #2196f3;
            border-radius: 2px;
        }

        /* 文件视图顶部工具栏 */
        QWidget#fileViewTopToolbar {
            background-color: #3c3c3c;
            border-bottom: 1px solid #555555;
        }

        /* 分页按钮 */
        QToolButton#paginationButton {
            background-color: #4a4a4a;
            border: 1px solid #666666;
            border-radius: 3px;
            min-width: 30px;
            min-height: 30px;
            color: #ffffff;
        }

        QToolButton#paginationButton:hover {
            background-color: #5a5a5a;
            border-color: #777777;
        }

        QToolButton#paginationButton:pressed {
            background-color: #333333;
        }

        QToolButton#paginationButton:disabled {
            background-color: #2a2a2a;
            color: #666666;
            border-color: #444444;
        }

        /* 搜索控件 */
        QLineEdit#searchEdit {
            background-color: #4a4a4a;
            border: 2px solid #666666;
            border-radius: 4px;
            padding: 4px;
            color: #ffffff;
        }

        QLineEdit#searchEdit:focus {
            border-color: #0078d4;
            background-color: #525252;
        }

        QComboBox#searchCombo {
            background-color: #4a4a4a;
            border: 2px solid #666666;
            border-radius: 4px;
            padding: 4px;
            color: #ffffff;
            min-width: 100px;
        }

        QComboBox#searchCombo:hover {
            border-color: #777777;
        }

        QComboBox#searchCombo:focus {
            border-color: #0078d4;
            background-color: #525252;
        }

        /* 高级搜索按钮 */
        QPushButton#advancedSearchButton {
            background-color: #0078d4;
            border: 1px solid #005a9e;
            border-radius: 4px;
            padding: 6px 12px;
            color: white;
            font-weight: bold;
            min-height: 25px;
        }

        QPushButton#advancedSearchButton:hover {
            background-color: #106ebe;
            border-color: #004578;
        }

        QPushButton#advancedSearchButton:pressed {
            background-color: #005a9e;
        }
        """

    def get_blue_theme(self):
        """蓝色主题样式"""
        return """
        QWidget {
            background-color: #f5f5f5;
            color: #333333;
            font-family: "Microsoft YaHei", "SimHei", "Arial Unicode MS", sans-serif;
        }

        QMainWindow {
            background-color: #ffffff;
        }

        /* 蓝色主题特色 */
        QMenuBar {
            background-color: #e3f2fd;
            border-bottom: 1px solid #bbdefb;
            padding: 2px;
            color: #1976d2;
        }

        QMenuBar::item {
            background-color: transparent;
            padding: 4px 8px;
            border-radius: 3px;
            color: #1976d2;
        }

        QMenuBar::item:selected {
            background-color: #bbdefb;
            color: #0d47a1;
        }

        QMenu {
            background-color: #ffffff;
            border: 1px solid #bbdefb;
            border-radius: 4px;
        }

        QMenu::item {
            padding: 6px 20px;
            color: #333333;
        }

        QMenu::item:selected {
            background-color: #bbdefb;
            color: #0d47a1;
        }

        QToolBar {
            background-color: #e3f2fd;
            border: none;
            spacing: 2px;
        }

        QStatusBar {
            background-color: #e3f2fd;
            border-top: 1px solid #bbdefb;
            color: #1976d2;
        }

        QPushButton {
            background-color: #2196f3;
            border: 1px solid #1976d2;
            border-radius: 4px;
            padding: 6px 12px;
            min-width: 60px;
            color: white;
            font-weight: bold;
        }

        QPushButton:hover {
            background-color: #1976d2;
            border-color: #1565c0;
        }

        QPushButton:pressed {
            background-color: #1565c0;
        }

        QPushButton:disabled {
            background-color: #e0e0e0;
            color: #999999;
            border-color: #cccccc;
        }

        QLineEdit, QTextEdit, QPlainTextEdit {
            background-color: #ffffff;
            border: 2px solid #e0e0e0;
            border-radius: 4px;
            padding: 4px;
            color: #333333;
        }

        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
            border-color: #2196f3;
            background-color: #f3f9ff;
        }

        QTableView, QTreeView, QListView {
            background-color: #ffffff;
            alternate-background-color: #f8f9fa;
            border: 1px solid #e0e0e0;
            gridline-color: #e0e0e0;
            color: #333333;
        }

        QTableView::item:selected, QTreeView::item:selected, QListView::item:selected {
            background-color: #d1d1d1;
            color: #333333;
            margin: 1px 0px;
            border-radius: 2px;
        }

        QTableView::item:hover, QTreeView::item:hover, QListView::item:hover {
            background-color: #e8e8e8;
            margin: 1px 0px;
            border-radius: 2px;
        }

        QHeaderView::section {
            background-color: #e3f2fd;
            border: none;
            border-right: 1px solid #bbdefb;
            border-bottom: 1px solid #bbdefb;
            padding: 6px;
            color: #1976d2;
            font-weight: bold;
        }

        QHeaderView::section:hover {
            background-color: #bbdefb;
        }

        QScrollBar:vertical {
            background-color: #f5f5f5;
            width: 12px;
            border-radius: 6px;
        }

        QScrollBar::handle:vertical {
            background-color: #2196f3;
            border-radius: 6px;
            min-height: 20px;
        }

        QScrollBar::handle:vertical:hover {
            background-color: #1976d2;
        }

        QScrollBar:horizontal {
            background-color: #f5f5f5;
            height: 12px;
            border-radius: 6px;
        }

        QScrollBar::handle:horizontal {
            background-color: #2196f3;
            border-radius: 6px;
            min-width: 20px;
        }

        QScrollBar::handle:horizontal:hover {
            background-color: #1976d2;
        }

        QComboBox {
            background-color: #ffffff;
            border: 2px solid #e0e0e0;
            border-radius: 4px;
            padding: 4px;
            color: #333333;
        }

        QComboBox:hover {
            border-color: #2196f3;
        }

        QComboBox:focus {
            border-color: #2196f3;
            background-color: #f3f9ff;
        }

        QComboBox::drop-down {
            border: none;
            width: 20px;
        }

        QComboBox::down-arrow {
            image: none;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 4px solid #2196f3;
            margin-right: 4px;
        }

        QComboBox QAbstractItemView {
            background-color: #ffffff;
            border: 1px solid #2196f3;
            selection-background-color: #e3f2fd;
            color: #333333;
        }

        /* 文件视图顶部工具栏 */
        QWidget#fileViewTopToolbar {
            background-color: #e3f2fd;
            border-bottom: 1px solid #bbdefb;
        }

        /* 分页按钮 */
        QToolButton#paginationButton {
            background-color: #ffffff;
            border: 1px solid #bbdefb;
            border-radius: 3px;
            min-width: 30px;
            min-height: 30px;
            color: #1976d2;
        }

        QToolButton#paginationButton:hover {
            background-color: #bbdefb;
            border-color: #90caf9;
        }

        QToolButton#paginationButton:pressed {
            background-color: #90caf9;
        }

        QToolButton#paginationButton:disabled {
            background-color: #f5f5f5;
            color: #999999;
            border-color: #e0e0e0;
        }

        /* 搜索控件 */
        QLineEdit#searchEdit {
            background-color: #ffffff;
            border: 2px solid #bbdefb;
            border-radius: 4px;
            padding: 4px;
            color: #333333;
        }

        QLineEdit#searchEdit:focus {
            border-color: #2196f3;
            background-color: #f3f9ff;
        }

        QComboBox#searchCombo {
            background-color: #ffffff;
            border: 2px solid #bbdefb;
            border-radius: 4px;
            padding: 4px;
            color: #333333;
            min-width: 100px;
        }

        QComboBox#searchCombo:hover {
            border-color: #2196f3;
        }

        QComboBox#searchCombo:focus {
            border-color: #2196f3;
            background-color: #f3f9ff;
        }

        /* 高级搜索按钮 */
        QPushButton#advancedSearchButton {
            background-color: #2196f3;
            border: 1px solid #1976d2;
            border-radius: 4px;
            padding: 6px 12px;
            color: white;
            font-weight: bold;
            min-height: 25px;
        }

        QPushButton#advancedSearchButton:hover {
            background-color: #1976d2;
            border-color: #1565c0;
        }

        QPushButton#advancedSearchButton:pressed {
            background-color: #1565c0;
        }
        """

    def get_green_theme(self):
        """绿色主题样式"""
        return """
        QWidget {
            background-color: #f5f5f5;
            color: #333333;
            font-family: "Microsoft YaHei", "SimHei", "Arial Unicode MS", sans-serif;
        }

        QMainWindow {
            background-color: #ffffff;
        }

        /* 绿色主题特色 */
        QMenuBar {
            background-color: #e8f5e8;
            border-bottom: 1px solid #c8e6c9;
            padding: 2px;
            color: #2e7d32;
        }

        QMenuBar::item {
            background-color: transparent;
            padding: 4px 8px;
            border-radius: 3px;
            color: #2e7d32;
        }

        QMenuBar::item:selected {
            background-color: #c8e6c9;
            color: #1b5e20;
        }

        QMenu {
            background-color: #ffffff;
            border: 1px solid #c8e6c9;
            border-radius: 4px;
        }

        QMenu::item {
            padding: 6px 20px;
            color: #333333;
        }

        QMenu::item:selected {
            background-color: #c8e6c9;
            color: #1b5e20;
        }

        QToolBar {
            background-color: #e8f5e8;
            border: none;
            spacing: 2px;
        }

        QStatusBar {
            background-color: #e8f5e8;
            border-top: 1px solid #c8e6c9;
            color: #2e7d32;
        }

        QPushButton {
            background-color: #4caf50;
            border: 1px solid #388e3c;
            border-radius: 4px;
            padding: 6px 12px;
            min-width: 60px;
            color: white;
            font-weight: bold;
        }

        QPushButton:hover {
            background-color: #388e3c;
            border-color: #2e7d32;
        }

        QPushButton:pressed {
            background-color: #2e7d32;
        }

        QPushButton:disabled {
            background-color: #e0e0e0;
            color: #999999;
            border-color: #cccccc;
        }

        QLineEdit, QTextEdit, QPlainTextEdit {
            background-color: #ffffff;
            border: 2px solid #e0e0e0;
            border-radius: 4px;
            padding: 4px;
            color: #333333;
        }

        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
            border-color: #4caf50;
            background-color: #f1f8e9;
        }

        QTableView, QTreeView, QListView {
            background-color: #ffffff;
            alternate-background-color: #f8f9fa;
            border: 1px solid #e0e0e0;
            gridline-color: #e0e0e0;
            color: #333333;
        }

        QTableView::item:selected, QTreeView::item:selected, QListView::item:selected {
            background-color: #d1d1d1;
            color: #333333;
            margin: 1px 0px;
            border-radius: 2px;
        }

        QTableView::item:hover, QTreeView::item:hover, QListView::item:hover {
            background-color: #e8e8e8;
            margin: 1px 0px;
            border-radius: 2px;
        }

        QHeaderView::section {
            background-color: #e8f5e8;
            border: none;
            border-right: 1px solid #c8e6c9;
            border-bottom: 1px solid #c8e6c9;
            padding: 6px;
            color: #2e7d32;
            font-weight: bold;
        }

        QHeaderView::section:hover {
            background-color: #c8e6c9;
        }

        QScrollBar:vertical {
            background-color: #f5f5f5;
            width: 12px;
            border-radius: 6px;
        }

        QScrollBar::handle:vertical {
            background-color: #4caf50;
            border-radius: 6px;
            min-height: 20px;
        }

        QScrollBar::handle:vertical:hover {
            background-color: #388e3c;
        }

        QScrollBar:horizontal {
            background-color: #f5f5f5;
            height: 12px;
            border-radius: 6px;
        }

        QScrollBar::handle:horizontal {
            background-color: #4caf50;
            border-radius: 6px;
            min-width: 20px;
        }

        QScrollBar::handle:horizontal:hover {
            background-color: #388e3c;
        }

        QComboBox {
            background-color: #ffffff;
            border: 2px solid #e0e0e0;
            border-radius: 4px;
            padding: 4px;
            color: #333333;
        }

        QComboBox:hover {
            border-color: #4caf50;
        }

        QComboBox:focus {
            border-color: #4caf50;
            background-color: #f1f8e9;
        }

        QComboBox::drop-down {
            border: none;
            width: 20px;
        }

        QComboBox::down-arrow {
            image: none;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 4px solid #4caf50;
            margin-right: 4px;
        }

        QComboBox QAbstractItemView {
            background-color: #ffffff;
            border: 1px solid #4caf50;
            selection-background-color: #e8f5e8;
            color: #333333;
        }

        /* 文件视图顶部工具栏 */
        QWidget#fileViewTopToolbar {
            background-color: #e8f5e8;
            border-bottom: 1px solid #c8e6c9;
        }

        /* 分页按钮 */
        QToolButton#paginationButton {
            background-color: #ffffff;
            border: 1px solid #c8e6c9;
            border-radius: 3px;
            min-width: 30px;
            min-height: 30px;
            color: #2e7d32;
        }

        QToolButton#paginationButton:hover {
            background-color: #c8e6c9;
            border-color: #a5d6a7;
        }

        QToolButton#paginationButton:pressed {
            background-color: #a5d6a7;
        }

        QToolButton#paginationButton:disabled {
            background-color: #f5f5f5;
            color: #999999;
            border-color: #e0e0e0;
        }

        /* 搜索控件 */
        QLineEdit#searchEdit {
            background-color: #ffffff;
            border: 2px solid #c8e6c9;
            border-radius: 4px;
            padding: 4px;
            color: #333333;
        }

        QLineEdit#searchEdit:focus {
            border-color: #4caf50;
            background-color: #f1f8e9;
        }

        QComboBox#searchCombo {
            background-color: #ffffff;
            border: 2px solid #c8e6c9;
            border-radius: 4px;
            padding: 4px;
            color: #333333;
            min-width: 100px;
        }

        QComboBox#searchCombo:hover {
            border-color: #4caf50;
        }

        QComboBox#searchCombo:focus {
            border-color: #4caf50;
            background-color: #f1f8e9;
        }

        /* 高级搜索按钮 */
        QPushButton#advancedSearchButton {
            background-color: #4caf50;
            border: 1px solid #388e3c;
            border-radius: 4px;
            padding: 6px 12px;
            color: white;
            font-weight: bold;
            min-height: 25px;
        }

        QPushButton#advancedSearchButton:hover {
            background-color: #388e3c;
            border-color: #2e7d32;
        }

        QPushButton#advancedSearchButton:pressed {
            background-color: #2e7d32;
        }
        """


# 全局主题管理器实例
theme_manager = ThemeManager()
