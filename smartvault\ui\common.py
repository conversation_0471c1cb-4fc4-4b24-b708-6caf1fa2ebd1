"""
UI通用模块 - 统一的导入和常量定义
"""

# PySide6 核心导入
from PySide6.QtCore import Qt, Signal, QTimer, QThread, QObject, QSettings
from PySide6.QtGui import QIcon, QAction, QStandardItemModel, QStandardItem
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
    QMessageBox, QApplication, QToolBar, QSizePolicy, QProgressBar,
    QLabel, QPushButton, QFileDialog, QDialog, QDialogButtonBox,
    QCheckBox, QLineEdit, QProgressDialog, QTreeView, QTabWidget,
    QTableView, QHeaderView, QAbstractItemView, QMenu
)

# 常用样式定义
PROGRESS_BAR_STYLE = """
    QProgressBar {
        border: 1px solid #ccc;
        border-radius: 3px;
        text-align: center;
        background-color: #f8f8f8;
        font-size: 11px;
    }
    QProgressBar::chunk {
        background-color: #0078d4;
        border-radius: 2px;
        margin: 1px;
    }
"""

BUTTON_STYLE = """
    QPushButton {
        border: none;
        background: transparent;
        color: #666;
        font-size: 12px;
    }
    QPushButton:hover {
        color: #f00;
        background-color: #f0f0f0;
        border-radius: 3px;
    }
"""

# 常用尺寸定义
class UISizes:
    """UI尺寸常量"""
    
    # 进度条尺寸
    COMPACT_PROGRESS_WIDTH = (150, 200)  # (min, max)
    COMPACT_PROGRESS_HEIGHT = 25
    LARGE_PROGRESS_HEIGHT = 50
    
    # 按钮尺寸
    SMALL_BUTTON_SIZE = (18, 18)
    MEDIUM_BUTTON_SIZE = (60, 30)
    
    # 间距
    SMALL_MARGIN = 2
    MEDIUM_MARGIN = 5
    LARGE_MARGIN = 10


# 常用颜色定义
class UIColors:
    """UI颜色常量"""
    
    PRIMARY = "#0078d4"
    SUCCESS = "#4CAF50"
    WARNING = "#FF9800"
    ERROR = "#f44336"
    
    BACKGROUND = "#f8f8f8"
    BORDER = "#ccc"
    TEXT = "#333"
    TEXT_SECONDARY = "#666"


# 通用工具函数
def create_progress_bar(compact=True):
    """创建标准化的进度条
    
    Args:
        compact: 是否创建紧凑型进度条
        
    Returns:
        QProgressBar: 配置好的进度条
    """
    progress_bar = QProgressBar()
    progress_bar.setTextVisible(True)
    progress_bar.setAlignment(Qt.AlignCenter)
    
    if compact:
        progress_bar.setMinimumHeight(UISizes.COMPACT_PROGRESS_HEIGHT)
        progress_bar.setMaximumHeight(UISizes.COMPACT_PROGRESS_HEIGHT)
    else:
        progress_bar.setMinimumHeight(UISizes.LARGE_PROGRESS_HEIGHT)
        progress_bar.setMaximumHeight(UISizes.LARGE_PROGRESS_HEIGHT)
    
    progress_bar.setStyleSheet(PROGRESS_BAR_STYLE)
    return progress_bar


def create_button(text, style_type="default"):
    """创建标准化的按钮
    
    Args:
        text: 按钮文本
        style_type: 样式类型 ("default", "small", "cancel")
        
    Returns:
        QPushButton: 配置好的按钮
    """
    button = QPushButton(text)
    
    if style_type == "small":
        button.setMaximumSize(*UISizes.SMALL_BUTTON_SIZE)
        button.setMinimumSize(*UISizes.SMALL_BUTTON_SIZE)
    elif style_type == "cancel":
        button.setMaximumWidth(UISizes.MEDIUM_BUTTON_SIZE[0])
    
    button.setStyleSheet(BUTTON_STYLE)
    return button


def show_message(parent, title, message, msg_type="info"):
    """显示标准化的消息框
    
    Args:
        parent: 父窗口
        title: 标题
        message: 消息内容
        msg_type: 消息类型 ("info", "warning", "error", "question")
        
    Returns:
        int: 用户选择的结果
    """
    if msg_type == "info":
        return QMessageBox.information(parent, title, message)
    elif msg_type == "warning":
        return QMessageBox.warning(parent, title, message)
    elif msg_type == "error":
        return QMessageBox.critical(parent, title, message)
    elif msg_type == "question":
        return QMessageBox.question(parent, title, message)
    else:
        return QMessageBox.information(parent, title, message)
