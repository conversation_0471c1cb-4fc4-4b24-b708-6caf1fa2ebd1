#!/usr/bin/env python3
"""
SmartVault 文件库切换功能测试
测试文件库创建、切换、数据库连接等核心功能
"""

import sys
import os
import tempfile
import shutil
import uuid
import datetime
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from smartvault.services.file import FileService
from smartvault.data.file_system import FileSystem
from smartvault.utils.config import load_config, save_config


class LibrarySwitchingTester:
    """文件库切换功能测试器"""

    def __init__(self):
        self.test_results = []
        self.temp_dirs = []  # 记录临时目录，用于清理
        self.original_config = load_config()  # 保存原始配置

    def log_test(self, test_name, success, details=""):
        """记录测试结果"""
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{status} {test_name}")
        if details:
            print(f"   详情: {details}")
        self.test_results.append({
            "name": test_name,
            "success": success,
            "details": details
        })

    def create_temp_library(self, name="test_library"):
        """创建临时文件库"""
        temp_dir = tempfile.mkdtemp(prefix=f"smartvault_{name}_")
        self.temp_dirs.append(temp_dir)

        # 创建文件库结构
        file_system = FileSystem()
        success, library_path, details = file_system.create_library(temp_dir)

        if success:
            return library_path
        else:
            raise Exception(f"创建临时文件库失败: {details}")

    def add_test_files_to_library(self, library_path, count=5):
        """向文件库添加测试文件"""
        # 临时切换到测试文件库
        file_service = FileService()
        file_service.switch_library(library_path)

        cursor = file_service.db.conn.cursor()
        for i in range(count):
            file_id = str(uuid.uuid4())
            now = datetime.datetime.now().isoformat()

            cursor.execute(
                """
                INSERT INTO files (
                    id, name, original_path, library_path, size,
                    created_at, modified_at, added_at, entry_type, is_available
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """,
                (
                    file_id,
                    f"库文件_{i+1}.txt",
                    f"{library_path}\\files\\库文件_{i+1}.txt",
                    f"{library_path}\\files\\库文件_{i+1}.txt",
                    1024 * (i + 1),
                    now,
                    now,
                    now,
                    "copy",
                    1
                )
            )

        file_service.db.conn.commit()
        file_service.db.close()
        return count

    def test_create_new_library(self):
        """测试创建新文件库"""
        print("\n🔍 测试创建新文件库...")

        try:
            # 创建新文件库
            new_library = self.create_temp_library("new_library")

            # 检查文件库结构
            data_dir = os.path.join(new_library, "data")
            db_file = os.path.join(data_dir, "smartvault.db")

            # 创建一个文件服务实例来初始化数据库
            file_service = FileService()
            file_service.switch_library(new_library)
            file_service.db.close()  # 确保数据库文件被创建

            structure_ok = os.path.exists(data_dir) and os.path.exists(db_file)
            self.log_test(
                "新文件库结构创建正确",
                structure_ok,
                f"数据目录: {os.path.exists(data_dir)}, 数据库文件: {os.path.exists(db_file)}"
            )

            # 测试新文件库为空
            file_service = FileService()
            file_service.switch_library(new_library)
            file_count = file_service.get_file_count()

            empty_ok = file_count == 0
            self.log_test(
                "新文件库初始为空",
                empty_ok,
                f"文件数量: {file_count}"
            )

            file_service.db.close()
            return new_library

        except Exception as e:
            self.log_test("创建新文件库", False, str(e))
            return None

    def test_switch_between_libraries(self):
        """测试在文件库之间切换"""
        print("\n🔍 测试文件库切换...")

        try:
            # 创建两个测试文件库
            library1 = self.create_temp_library("library1")
            library2 = self.create_temp_library("library2")

            # 向两个文件库添加不同数量的文件
            count1 = self.add_test_files_to_library(library1, 3)
            count2 = self.add_test_files_to_library(library2, 7)

            # 测试切换到第一个文件库
            file_service = FileService()
            file_service.switch_library(library1)
            actual_count1 = file_service.get_file_count()

            switch1_ok = actual_count1 == count1
            self.log_test(
                "切换到文件库1正确",
                switch1_ok,
                f"期望 {count1} 个文件，实际 {actual_count1} 个"
            )

            # 测试切换到第二个文件库
            file_service.switch_library(library2)
            actual_count2 = file_service.get_file_count()

            switch2_ok = actual_count2 == count2
            self.log_test(
                "切换到文件库2正确",
                switch2_ok,
                f"期望 {count2} 个文件，实际 {actual_count2} 个"
            )

            # 测试再次切换回第一个文件库
            file_service.switch_library(library1)
            actual_count1_again = file_service.get_file_count()

            switch_back_ok = actual_count1_again == count1
            self.log_test(
                "切换回文件库1正确",
                switch_back_ok,
                f"期望 {count1} 个文件，实际 {actual_count1_again} 个"
            )

            file_service.db.close()

        except Exception as e:
            self.log_test("文件库切换", False, str(e))

    def test_database_connection_switching(self):
        """测试数据库连接切换"""
        print("\n🔍 测试数据库连接切换...")

        try:
            # 创建测试文件库
            test_library = self.create_temp_library("db_test")
            self.add_test_files_to_library(test_library, 5)

            # 创建文件服务实例
            file_service = FileService()

            # 记录原始数据库路径
            original_db_path = file_service.db.db_path

            # 切换到测试文件库
            file_service.switch_library(test_library)
            new_db_path = file_service.db.db_path

            # 检查数据库路径是否正确切换
            expected_db_path = os.path.join(test_library, "data", "smartvault.db")
            path_ok = new_db_path == expected_db_path
            self.log_test(
                "数据库路径切换正确",
                path_ok,
                f"期望: {expected_db_path}, 实际: {new_db_path}"
            )

            # 检查能否正确查询新数据库
            try:
                file_count = file_service.get_file_count()
                query_ok = file_count == 5
                self.log_test(
                    "新数据库查询正常",
                    query_ok,
                    f"查询到 {file_count} 个文件"
                )
            except Exception as e:
                self.log_test("新数据库查询正常", False, str(e))

            file_service.db.close()

        except Exception as e:
            self.log_test("数据库连接切换", False, str(e))

    def test_config_persistence(self):
        """测试配置持久化"""
        print("\n🔍 测试配置持久化...")

        try:
            # 创建测试文件库
            test_library = self.create_temp_library("config_test")

            # 保存配置
            config = load_config()
            original_path = config["library_path"]
            config["library_path"] = test_library
            save_config(config)

            # 重新加载配置
            reloaded_config = load_config()

            # 检查配置是否正确保存和加载（标准化路径比较）
            saved_path = os.path.normpath(test_library)
            loaded_path = os.path.normpath(reloaded_config["library_path"])
            persistence_ok = saved_path == loaded_path
            self.log_test(
                "配置持久化正常",
                persistence_ok,
                f"保存: {saved_path}, 加载: {loaded_path}"
            )

            # 恢复原始配置
            config["library_path"] = original_path
            save_config(config)

        except Exception as e:
            self.log_test("配置持久化", False, str(e))

    def cleanup(self):
        """清理测试环境"""
        print("\n🧹 清理测试环境...")

        # 恢复原始配置
        try:
            save_config(self.original_config)
            print("   ✅ 原始配置已恢复")
        except Exception as e:
            print(f"   ⚠️  恢复配置失败: {e}")

        # 删除临时目录
        for temp_dir in self.temp_dirs:
            try:
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
                    print(f"   ✅ 已删除临时目录: {temp_dir}")
            except Exception as e:
                print(f"   ⚠️  删除临时目录失败: {temp_dir} - {e}")

    def run_all_tests(self):
        """运行所有测试"""
        print("开始文件库切换功能测试")
        print("=" * 60)

        try:
            # 运行各项测试
            self.test_create_new_library()
            self.test_switch_between_libraries()
            self.test_database_connection_switching()
            self.test_config_persistence()

        finally:
            # 清理测试环境
            self.cleanup()

        # 输出测试结果
        print("\n" + "=" * 60)
        print("测试结果汇总")
        print("=" * 60)

        passed = sum(1 for result in self.test_results if result["success"])
        total = len(self.test_results)

        print(f"总测试数: {total}")
        print(f"通过数: {passed}")
        print(f"失败数: {total - passed}")
        print(f"通过率: {passed/total*100:.1f}%")

        if passed == total:
            print("\n所有测试通过！文件库切换功能工作正常。")
            return True
        else:
            print("\n部分测试失败，需要进一步检查。")
            print("\n失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  失败 {result['name']}: {result['details']}")
            return False


def main():
    """主函数"""
    app = QApplication(sys.argv)

    try:
        tester = LibrarySwitchingTester()
        success = tester.run_all_tests()

        # 自动退出
        QTimer.singleShot(1000, app.quit)
        app.exec()

        return 0 if success else 1

    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
