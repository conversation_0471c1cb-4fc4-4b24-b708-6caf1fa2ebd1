# SmartVault 代码质量控制指南

## 🎯 核心原则

> **"功能优先，适度优化；能工作的代码比完美的架构更有价值。"**

## 📏 代码质量标准

### 文件大小控制

| 文件 | 当前行数 | 警告阈值 | 临界阈值 | 状态 |
|------|----------|----------|----------|------|
| `core.py` | 2,533 | 2,500 | 3,000 | 🟡 WARNING |

### 方法复杂度控制

- **单个方法**: 最大100行
- **单个类**: 最大1,500行
- **复杂逻辑**: 必须抽取为独立方法

## 🚫 core.py 禁止添加的功能

### 非核心功能类别

1. **备份相关功能** → 移至 `backup_manager.py`
2. **剪贴板功能** → 移至 `clipboard_handler.py`
3. **拖拽处理** → 移至 `drag_drop_handler.py`
4. **监控管理** → 移至 `monitor_manager.py`
5. **搜索增强** → 移至 `search_handler.py`
6. **标签操作** → 移至 `tag_handler.py`
7. **设备管理** → 移至 `device_manager.py`

### 识别关键词

如果新方法名包含以下关键词，**禁止**添加到 `core.py`：
- `backup_`, `clipboard_`, `drag_`, `drop_`
- `monitor_`, `search_`, `tag_`, `device_`
- `usb_`, `floating_`, `duplicate_`

## ✅ core.py 允许的核心功能

### 必须保留的功能

1. **初始化**: `__init__`, `init_ui`
2. **窗口管理**: `closeEvent`, `restore_window_geometry`
3. **配置管理**: `apply_user_config`, `on_library_changed`
4. **基础交互**: `keyPressEvent`, `show_status_message`
5. **数据加载**: `load_initial_data`, `_load_files_with_pagination`

## 🔧 开发流程控制

### 1. 开发前检查

```bash
# 运行代码质量检查
python tools/code_quality_monitor.py
```

### 2. 新功能开发规则

#### Rule 1: 功能归属判断
```
新功能是否为窗口核心功能？
├─ 是 → 可以添加到 core.py
└─ 否 → 必须创建独立模块
```

#### Rule 2: 方法长度控制
```
新方法超过50行？
├─ 是 → 拆分为多个私有方法
└─ 否 → 可以直接添加
```

#### Rule 3: 依赖关系检查
```
新功能依赖外部服务？
├─ 是 → 通过依赖注入，避免直接耦合
└─ 否 → 可以直接实现
```

### 3. 代码审查清单

- [ ] 新增代码行数 < 50行
- [ ] 不包含非核心功能关键词
- [ ] 方法职责单一明确
- [ ] 没有复杂的业务逻辑
- [ ] 运行质量检查工具通过

## 📊 监控机制

### 自动化检查

1. **每次提交前**: 运行 `code_quality_monitor.py`
2. **每周定期**: 生成质量报告
3. **达到阈值**: 自动触发重构提醒

### 手动检查

1. **新功能开发**: 评估是否属于核心功能
2. **代码审查**: 检查是否符合质量标准
3. **重构时机**: 评估收益与风险

## 🚨 强制拆分触发条件

### 立即拆分 (CRITICAL)

- `core.py` 超过 3,000 行
- 单个方法超过 150 行
- 添加了明显的非核心功能

### 考虑拆分 (WARNING)

- `core.py` 超过 2,500 行
- 单个方法超过 100 行
- 非核心方法数量 > 核心方法数量

## 💡 重构策略

### 渐进式拆分

1. **第一步**: 移动明显的非核心功能
2. **第二步**: 拆分过长的方法
3. **第三步**: 优化类结构和依赖关系

### 拆分优先级

1. **高优先级**: 备份、剪贴板、拖拽功能
2. **中优先级**: 监控、搜索、标签功能
3. **低优先级**: 工具方法和辅助功能

## 🎯 目标管理

### 短期目标 (1个月)

- 保持 `core.py` 在 3,000 行以下
- 新功能 100% 在其他模块实现
- 建立代码审查习惯

### 中期目标 (3个月)

- 将 `core.py` 控制在 2,500 行以下
- 完成主要非核心功能的模块化
- 建立自动化质量检查流程

### 长期目标 (6个月)

- 将 `core.py` 控制在 2,000 行以下
- 实现完全的模块化架构
- 建立可持续的代码质量管理体系

## 📝 开发提醒模板

### 新对话开始时的提醒

```
🔍 SmartVault 代码质量提醒:
- core.py 当前 2,533 行 (警告状态)
- 新功能必须在其他模块实现
- 复杂逻辑必须抽取为独立方法
- 超过 3,000 行强制拆分
```

### 功能开发时的检查

```
❓ 开发检查清单:
1. 这是核心窗口功能吗？
2. 方法长度是否合理？
3. 是否可以在其他模块实现？
4. 是否需要拆分复杂逻辑？
```

---

**记住**: 这不是为了追求完美的代码，而是为了确保项目的长期可维护性和开发效率。遵循"功能优先，适度优化"的原则，在保证功能交付的前提下，维护合理的代码质量标准。
