#!/usr/bin/env python3
"""
条件编辑对话框
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QFormLayout, QComboBox, QLineEdit,
    QDialogButtonBox, QLabel, QMessageBox
)
from PySide6.QtCore import Qt

from smartvault.services.auto_tag_service import ConditionType


class ConditionEditDialog(QDialog):
    """条件编辑对话框"""

    def __init__(self, parent=None, condition_type=None, condition_value=""):
        """初始化对话框

        Args:
            parent: 父窗口
            condition_type: 条件类型
            condition_value: 条件值
        """
        super().__init__(parent)
        self.setWindowTitle("编辑条件")
        self.resize(400, 200)

        self.condition_type = condition_type or ConditionType.FILE_EXTENSION
        self.condition_value = condition_value

        self.init_ui()
        self.load_data()

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)

        # 表单布局
        form_layout = QFormLayout()

        # 条件类型
        self.condition_type_combo = QComboBox()
        self.condition_type_combo.addItems([
            "文件扩展名",
            "文件名模式",
            "文件路径模式",
            "文件大小范围",
            "文件类型",
            "文件大小",
            "文件名正则表达式"
        ])
        self.condition_type_combo.currentTextChanged.connect(self.on_condition_type_changed)
        form_layout.addRow("条件类型:", self.condition_type_combo)

        # 条件值
        self.condition_value_edit = QLineEdit()
        self.condition_value_edit.setPlaceholderText("请输入条件值")
        form_layout.addRow("条件值:", self.condition_value_edit)

        # 条件说明
        self.condition_help_label = QLabel()
        self.condition_help_label.setWordWrap(True)
        self.condition_help_label.setStyleSheet("color: #666666; font-size: 12px;")
        form_layout.addRow("", self.condition_help_label)

        layout.addLayout(form_layout)

        # 按钮
        buttons = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        buttons.accepted.connect(self.accept_condition)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)

        # 初始化条件说明
        self.on_condition_type_changed()

    def on_condition_type_changed(self):
        """条件类型改变时更新说明"""
        condition_type = self.condition_type_combo.currentText()

        help_texts = {
            "文件扩展名": "输入文件扩展名，多个扩展名用逗号分隔。例如：pdf,doc,docx",
            "文件名模式": "输入文件名匹配模式，支持正则表达式。例如：report 或 .*报告.*",
            "文件路径模式": "输入文件路径匹配模式，支持正则表达式。例如：/Documents/ 或 .*文档.*",
            "文件大小范围": "输入文件大小范围。例如：1MB-10MB 或 >5MB",
            "文件类型": "选择文件类型。可选：图片、文档、表格、演示、视频、音频、压缩、代码、可执行",
            "文件大小": "输入文件大小条件，支持比较操作符。例如：>1MB、<500KB、=2MB",
            "文件名正则表达式": "输入严格的正则表达式。例如：^[A-Z].*\\.pdf$ （以大写字母开头的PDF文件）"
        }

        help_text = help_texts.get(condition_type, "")
        self.condition_help_label.setText(help_text)

        # 更新占位符文本
        placeholders = {
            "文件扩展名": "pdf,doc,docx",
            "文件名模式": "report",
            "文件路径模式": "/Documents/",
            "文件大小范围": "1MB-10MB",
            "文件类型": "图片",
            "文件大小": ">1MB",
            "文件名正则表达式": "^[A-Z].*\\.pdf$"
        }

        placeholder = placeholders.get(condition_type, "")
        self.condition_value_edit.setPlaceholderText(placeholder)

    def load_data(self):
        """加载数据"""
        # 设置条件类型
        type_map = {
            ConditionType.FILE_EXTENSION: "文件扩展名",
            ConditionType.FILE_NAME_PATTERN: "文件名模式",
            ConditionType.FILE_PATH_PATTERN: "文件路径模式",
            ConditionType.FILE_SIZE_RANGE: "文件大小范围",
            ConditionType.FILE_TYPE: "文件类型",
            ConditionType.FILE_SIZE: "文件大小",
            ConditionType.FILE_NAME_REGEX: "文件名正则表达式"
        }

        type_text = type_map.get(self.condition_type, "文件扩展名")
        index = self.condition_type_combo.findText(type_text)
        if index >= 0:
            self.condition_type_combo.setCurrentIndex(index)

        # 设置条件值
        self.condition_value_edit.setText(self.condition_value)

    def accept_condition(self):
        """接受条件"""
        condition_value = self.condition_value_edit.text().strip()
        if not condition_value:
            QMessageBox.warning(self, "错误", "请输入条件值")
            return

        # 验证特定条件类型的值
        condition_type_text = self.condition_type_combo.currentText()

        if condition_type_text == "文件类型":
            valid_types = ["图片", "文档", "表格", "演示", "视频", "音频", "压缩", "代码", "可执行"]
            if condition_value not in valid_types:
                QMessageBox.warning(
                    self, "错误",
                    f"文件类型必须是以下之一：{', '.join(valid_types)}"
                )
                return

        elif condition_type_text == "文件大小" or condition_type_text == "文件大小范围":
            # 简单验证大小格式
            if not any(unit in condition_value.upper() for unit in ['B', 'KB', 'MB', 'GB', 'TB']) and not condition_value.isdigit():
                if '-' not in condition_value and not any(op in condition_value for op in ['>', '<', '=', '>=']):
                    QMessageBox.warning(
                        self, "错误",
                        "文件大小格式不正确。例如：1MB、>500KB、1MB-10MB"
                    )
                    return

        self.accept()

    def get_condition(self):
        """获取条件"""
        condition_type_text = self.condition_type_combo.currentText()
        condition_value = self.condition_value_edit.text().strip()

        # 转换条件类型
        type_map = {
            "文件扩展名": ConditionType.FILE_EXTENSION,
            "文件名模式": ConditionType.FILE_NAME_PATTERN,
            "文件路径模式": ConditionType.FILE_PATH_PATTERN,
            "文件大小范围": ConditionType.FILE_SIZE_RANGE,
            "文件类型": ConditionType.FILE_TYPE,
            "文件大小": ConditionType.FILE_SIZE,
            "文件名正则表达式": ConditionType.FILE_NAME_REGEX
        }

        condition_type = type_map.get(condition_type_text, ConditionType.FILE_EXTENSION)

        return condition_type, condition_value
