#!/usr/bin/env python3
"""
SmartVault 分页功能综合测试
测试分页功能的各个方面，确保重构后的功能正常工作
"""

import sys
import os
import time
import uuid
import datetime
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from smartvault.services.file import FileService
from smartvault.data.database import Database
from smartvault.ui.models.file_table_model import FileTableModel


class PaginationTester:
    """分页功能测试器"""

    def __init__(self):
        self.file_service = FileService()
        self.model = FileTableModel()
        self.test_results = []

        # 设置数据加载回调
        self.model.set_data_loader_callback(self._load_files_callback)
        self.model.set_search_total_count_callback(self._get_search_total_count_callback)

    def _load_files_callback(self, limit, offset, search_keyword=None, search_column=None):
        """数据加载回调"""
        return self.file_service.get_files(
            limit=limit,
            offset=offset,
            search_keyword=search_keyword,
            search_column=search_column
        )

    def _get_search_total_count_callback(self, search_keyword, search_column):
        """搜索总数回调"""
        return self.file_service.get_file_count(
            search_keyword=search_keyword,
            search_column=search_column
        )

    def log_test(self, test_name, success, details=""):
        """记录测试结果"""
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{status} {test_name}")
        if details:
            print(f"   详情: {details}")
        self.test_results.append({
            "name": test_name,
            "success": success,
            "details": details
        })

    def prepare_test_data(self):
        """准备测试数据"""
        print("📝 准备测试数据...")

        # 获取当前文件数量
        current_count = self.file_service.get_file_count()
        print(f"   当前数据库中有 {current_count} 个文件")

        # 如果文件数量少于150个，添加一些测试文件
        if current_count < 150:
            needed = 150 - current_count
            print(f"   需要添加 {needed} 个测试文件以进行分页测试")

            cursor = self.file_service.db.conn.cursor()
            for i in range(needed):
                file_id = str(uuid.uuid4())
                now = datetime.datetime.now().isoformat()

                cursor.execute(
                    """
                    INSERT INTO files (
                        id, name, original_path, library_path, size,
                        created_at, modified_at, added_at, entry_type, is_available
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """,
                    (
                        file_id,
                        f"测试文件_{i+1:03d}.txt",
                        f"C:\\test\\测试文件_{i+1:03d}.txt",
                        None,
                        1024 * (i + 1),
                        now,
                        now,
                        now,
                        "link",
                        1
                    )
                )

            self.file_service.db.conn.commit()
            print(f"   已添加 {needed} 个测试文件")

        final_count = self.file_service.get_file_count()
        print(f"   测试数据准备完成，共 {final_count} 个文件")
        return final_count

    def test_basic_pagination(self):
        """测试基本分页功能"""
        print("\n🔍 测试基本分页功能...")

        # 获取总文件数
        total_files = self.file_service.get_file_count()
        self.model.set_total_files(total_files)

        # 测试1: 启动时显示第一页
        self.model.setPageSize(50)  # 设置每页50条
        first_page_files = self._load_files_callback(50, 0)

        success = len(first_page_files) > 0
        self.log_test(
            "启动时正确显示第一页数据",
            success,
            f"第一页加载了 {len(first_page_files)} 个文件"
        )

        # 测试2: 分页信息正确
        expected_total_pages = (total_files + 49) // 50  # 向上取整
        actual_total_pages = self.model.getTotalPages()

        success = actual_total_pages == expected_total_pages
        self.log_test(
            "分页信息计算正确",
            success,
            f"期望 {expected_total_pages} 页，实际 {actual_total_pages} 页"
        )

        # 测试3: 翻页功能
        if actual_total_pages > 1:
            # 翻到下一页
            next_success = self.model.nextPage()
            if next_success:
                second_page_files = self._load_files_callback(50, 50)
                success = len(second_page_files) > 0 and second_page_files != first_page_files
                self.log_test(
                    "翻页功能正常",
                    success,
                    f"第二页加载了 {len(second_page_files)} 个文件，与第一页不同"
                )

                # 翻回第一页
                prev_success = self.model.previousPage()
                self.log_test("返回上一页功能正常", prev_success)
            else:
                self.log_test("翻页功能正常", False, "无法翻到下一页")
        else:
            self.log_test("翻页功能正常", True, "只有一页，跳过翻页测试")

    def test_page_size_change(self):
        """测试页面大小变更功能"""
        print("\n🔍 测试页面大小变更功能...")

        total_files = self.file_service.get_file_count()

        # 测试不同的页面大小
        page_sizes = [25, 50, 100, 200]

        for page_size in page_sizes:
            self.model.setPageSize(page_size)
            expected_pages = (total_files + page_size - 1) // page_size
            actual_pages = self.model.getTotalPages()

            success = actual_pages == expected_pages
            self.log_test(
                f"页面大小 {page_size} 设置正确",
                success,
                f"期望 {expected_pages} 页，实际 {actual_pages} 页"
            )

        # 测试"全部"选项
        self.model.setPageSize(999999)
        all_files = self._load_files_callback(999999, 0)

        success = len(all_files) == total_files
        self.log_test(
            "全部显示功能正常",
            success,
            f"加载了 {len(all_files)} 个文件，期望 {total_files} 个"
        )

    def test_search_functionality(self):
        """测试搜索功能"""
        print("\n🔍 测试搜索功能...")

        # 测试1: 基本搜索
        search_keyword = "测试"
        search_results = self.file_service.get_files(
            limit=100, offset=0,
            search_keyword=search_keyword,
            search_column=None
        )
        search_count = self.file_service.get_file_count(
            search_keyword=search_keyword,
            search_column=None
        )

        success = len(search_results) <= search_count
        self.log_test(
            "基本搜索功能正常",
            success,
            f"搜索 '{search_keyword}' 找到 {search_count} 个结果，当前页显示 {len(search_results)} 个"
        )

        # 测试2: 搜索分页
        if search_count > 50:
            # 设置较小的页面大小来测试搜索分页
            page1_results = self.file_service.get_files(
                limit=25, offset=0,
                search_keyword=search_keyword,
                search_column=None
            )
            page2_results = self.file_service.get_files(
                limit=25, offset=25,
                search_keyword=search_keyword,
                search_column=None
            )

            success = len(page1_results) > 0 and len(page2_results) > 0 and page1_results != page2_results
            self.log_test(
                "搜索结果分页正常",
                success,
                f"第一页 {len(page1_results)} 个，第二页 {len(page2_results)} 个"
            )
        else:
            self.log_test("搜索结果分页正常", True, "搜索结果不足，跳过分页测试")

        # 测试3: 清除搜索
        all_files_count = self.file_service.get_file_count()
        success = all_files_count > search_count
        self.log_test(
            "清除搜索功能正常",
            success,
            f"全部文件 {all_files_count} 个 > 搜索结果 {search_count} 个"
        )

    def run_all_tests(self):
        """运行所有测试"""
        print("开始分页功能综合测试")
        print("=" * 60)

        # 准备测试数据
        total_files = self.prepare_test_data()

        # 运行各项测试
        self.test_basic_pagination()
        self.test_page_size_change()
        self.test_search_functionality()

        # 输出测试结果
        print("\n" + "=" * 60)
        print("测试结果汇总")
        print("=" * 60)

        passed = sum(1 for result in self.test_results if result["success"])
        total = len(self.test_results)

        print(f"总测试数: {total}")
        print(f"通过数: {passed}")
        print(f"失败数: {total - passed}")
        print(f"通过率: {passed/total*100:.1f}%")

        if passed == total:
            print("\n所有测试通过！分页功能工作正常。")
            return True
        else:
            print("\n部分测试失败，需要进一步检查。")
            print("\n失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  失败 {result['name']}: {result['details']}")
            return False


def main():
    """主函数"""
    app = QApplication(sys.argv)

    try:
        tester = PaginationTester()
        success = tester.run_all_tests()

        # 自动退出
        QTimer.singleShot(1000, app.quit)
        app.exec()

        return 0 if success else 1

    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
