"""
标签管理对话框
"""

import sys
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QLineEdit, QColorDialog, QMessageBox,
    QMenu, QWidget, QSplitter, QTreeWidget, QTreeWidgetItem
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QColor, QAction

from smartvault.services.tag_service import TagService
from smartvault.utils.tree_state_manager import tree_state_manager


class TagManagementDialog(QDialog):
    """标签管理对话框"""

    # 信号：标签发生变化时发出
    tags_changed = Signal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.tag_service = TagService()
        self.init_ui()
        self.load_tags()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("标签管理")
        self.setModal(True)
        self.resize(600, 400)

        # 主布局
        layout = QVBoxLayout(self)

        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(splitter)

        # 左侧：标签列表
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)

        # 标签列表标题
        left_layout.addWidget(QLabel("标签列表"))

        # 标签树
        self.tag_tree = QTreeWidget()
        self.tag_tree.setObjectName("tagManagementTree")  # 设置对象名称用于特殊样式处理
        self.tag_tree.setHeaderHidden(True)
        self.tag_tree.setRootIsDecorated(True)
        self.tag_tree.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.tag_tree.customContextMenuRequested.connect(self.show_context_menu)
        self.tag_tree.itemSelectionChanged.connect(self.on_tag_selection_changed)
        self.tag_tree.itemClicked.connect(self.on_tag_clicked)
        self.tag_tree.itemDoubleClicked.connect(self.on_tag_double_clicked)

        # 设置标签树样式，确保在所有主题下都能正确显示彩色标签
        # 关键：不设置color属性，让代码控制标签颜色
        self.tag_tree.setStyleSheet("""
            QTreeWidget {
                border: 1px solid #ccc;
                border-radius: 4px;
                font-size: 12px;
            }
            QTreeWidget::item {
                padding: 4px;
                border: none;
            }
            QTreeWidget::item:selected {
                background-color: #2196f3;
            }
            QTreeWidget::item:hover {
                background-color: rgba(128, 128, 128, 0.2);
            }
        """)

        left_layout.addWidget(self.tag_tree)

        # 标签操作按钮
        tag_buttons_layout = QHBoxLayout()

        self.add_tag_btn = QPushButton("新建标签")
        self.add_tag_btn.clicked.connect(self.add_tag)
        tag_buttons_layout.addWidget(self.add_tag_btn)

        self.edit_tag_btn = QPushButton("编辑标签")
        self.edit_tag_btn.clicked.connect(self.edit_tag)
        self.edit_tag_btn.setEnabled(False)
        tag_buttons_layout.addWidget(self.edit_tag_btn)

        self.delete_tag_btn = QPushButton("删除标签")
        self.delete_tag_btn.clicked.connect(self.delete_tag)
        self.delete_tag_btn.setEnabled(False)
        tag_buttons_layout.addWidget(self.delete_tag_btn)

        left_layout.addLayout(tag_buttons_layout)
        splitter.addWidget(left_widget)

        # 右侧：标签详情和统计
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)

        # 标签详情标题
        right_layout.addWidget(QLabel("标签详情"))

        # 标签详情显示区域
        self.tag_details = QLabel("请选择一个标签查看详情")
        self.tag_details.setObjectName("tagDetailsLabel")  # 设置对象名称用于主题处理
        self.tag_details.setWordWrap(True)
        self.tag_details.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.tag_details.setStyleSheet("""
            QLabel#tagDetailsLabel {
                border: 1px solid #ccc;
                padding: 10px;
                background-color: #f9f9f9;
                border-radius: 4px;
                color: #333;
            }
        """)
        right_layout.addWidget(self.tag_details)

        # 标签统计
        stats_title = QLabel("标签统计")
        stats_title.setStyleSheet("font-weight: bold; margin-top: 10px;")
        right_layout.addWidget(stats_title)

        self.tag_stats = QLabel("加载中...")
        self.tag_stats.setObjectName("tagStatsLabel")  # 设置对象名称用于主题处理
        self.tag_stats.setWordWrap(True)
        self.tag_stats.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.tag_stats.setStyleSheet("""
            QLabel#tagStatsLabel {
                border: 1px solid #ccc;
                padding: 10px;
                background-color: #f0f8ff;
                border-radius: 4px;
                min-height: 100px;
                color: #333;
            }
        """)
        right_layout.addWidget(self.tag_stats)

        splitter.addWidget(right_widget)

        # 设置分割器比例
        splitter.setSizes([300, 300])

        # 对话框按钮
        buttons_layout = QHBoxLayout()

        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.load_tags)
        buttons_layout.addWidget(self.refresh_btn)

        buttons_layout.addStretch()

        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.close_dialog)
        buttons_layout.addWidget(self.close_btn)

        layout.addLayout(buttons_layout)

    def close_dialog(self):
        """关闭对话框前保存树状态"""
        try:
            # 🔧 保存树展开状态
            tree_state_manager.save_tree_state(self.tag_tree, "tag_management_dialog_tree")
        except Exception as e:
            print(f"保存标签管理对话框树状态失败: {e}")
        finally:
            self.accept()

    def load_tags(self):
        """加载标签树"""
        try:
            # 清空树
            self.tag_tree.clear()

            # 获取标签树结构
            tag_tree = self.tag_service.get_tag_tree()

            # 构建树控件
            self.build_tree_items(tag_tree, self.tag_tree)

            # 🔧 使用状态管理器恢复展开状态，而不是强制展开所有
            tree_state_manager.restore_tree_state(self.tag_tree, "tag_management_dialog_tree")

            # 更新统计信息
            self.update_tag_statistics()

        except Exception as e:
            QMessageBox.warning(self, "错误", f"加载标签失败: {e}")

    def build_tree_items(self, tag_nodes, parent_widget, depth=0):
        """构建树项

        Args:
            tag_nodes: 标签节点列表
            parent_widget: 父组件
            depth: 当前深度（用于颜色继承）
        """
        for tag_node in tag_nodes:
            # 创建树项
            item = QTreeWidgetItem(parent_widget)

            # 设置显示文本
            display_text = f"{tag_node['name']}"
            if tag_node['total_file_count'] > 0:
                display_text += f" ({tag_node['total_file_count']})"
            item.setText(0, display_text)

            # 存储标签数据
            item.setData(0, Qt.ItemDataRole.UserRole, tag_node)

            # 设置颜色（支持继承和深度递减）
            self._apply_tag_color(item, tag_node, depth)

            # 递归添加子项
            if tag_node['children']:
                self.build_tree_items(tag_node['children'], item, depth + 1)

    def _apply_tag_color(self, item, tag_node, depth):
        """应用标签颜色（支持继承和深度递减）

        Args:
            item: 树项
            tag_node: 标签数据
            depth: 深度级别
        """
        # 获取有效颜色（考虑继承）
        effective_color = None
        if tag_node.get('color'):
            effective_color = tag_node['color']
        else:
            # 如果没有颜色，尝试从TagService获取继承颜色
            try:
                inherited_attrs = self.tag_service.get_inherited_attributes(tag_node['id'])
                effective_color = inherited_attrs.get('color')
            except:
                pass

        if effective_color:
            from smartvault.ui.utils.color_utils import get_inherited_color

            # 根据深度调整颜色（参考导航栏的成功经验）
            display_color = get_inherited_color(effective_color, depth)

            # 设置文字颜色（使用QBrush，参考导航栏）
            from PySide6.QtGui import QBrush
            item.setForeground(0, QBrush(display_color))

            # 可选：为根级标签设置轻微的背景色
            if depth == 0 and effective_color:
                from smartvault.ui.utils.color_utils import lighten_color
                bg_color = lighten_color(effective_color, 0.8)  # 非常浅的背景
                item.setBackground(0, bg_color)

    def update_tag_statistics(self):
        """更新标签统计信息"""
        try:
            stats = self.tag_service.get_tag_statistics()

            if not stats:
                self.tag_stats.setText("暂无标签\n\n点击\"新建标签\"按钮创建第一个标签。")
                return

            # 构建统计文本
            stats_text = f"📊 总标签数: {len(stats)}\n\n"

            # 按使用频率排序显示
            used_tags = [s for s in stats if s["file_count"] > 0]
            unused_tags = [s for s in stats if s["file_count"] == 0]

            if used_tags:
                # 按文件数量排序
                used_tags.sort(key=lambda x: x["file_count"], reverse=True)
                stats_text += "🏷️ 已使用的标签:\n"
                for stat in used_tags[:10]:  # 最多显示前10个
                    stats_text += f"  • {stat['name']}: {stat['file_count']} 个文件\n"
                if len(used_tags) > 10:
                    stats_text += f"  • ... 还有 {len(used_tags) - 10} 个\n"

            if unused_tags:
                stats_text += f"\n📝 未使用的标签 ({len(unused_tags)} 个):\n"
                for stat in unused_tags[:8]:  # 最多显示前8个
                    stats_text += f"  • {stat['name']}\n"
                if len(unused_tags) > 8:
                    stats_text += f"  • ... 还有 {len(unused_tags) - 8} 个\n"

            # 添加提示信息
            if not used_tags and unused_tags:
                stats_text += "\n💡 提示: 右键点击文件可以为其添加标签"

            self.tag_stats.setText(stats_text)

        except Exception as e:
            self.tag_stats.setText(f"❌ 统计信息加载失败:\n{e}\n\n请尝试刷新标签列表。")

    def on_tag_selection_changed(self):
        """标签选择变化时的处理"""
        current_item = self.tag_tree.currentItem()
        has_selection = current_item is not None

        # 更新按钮状态
        self.edit_tag_btn.setEnabled(has_selection)
        self.delete_tag_btn.setEnabled(has_selection)

        # 更新详情显示
        if has_selection:
            tag = current_item.data(0, Qt.ItemDataRole.UserRole)
            self.show_tag_details(tag)
        else:
            self.tag_details.setText("请选择一个标签查看详情")

    def on_tag_clicked(self, item, column):
        """标签点击事件"""
        if item:
            tag_data = item.data(0, Qt.ItemDataRole.UserRole)
            if tag_data:
                self.show_tag_details(tag_data)

    def on_tag_double_clicked(self, item, column):
        """标签双击事件"""
        # 双击时展开/折叠
        if item:
            item.setExpanded(not item.isExpanded())

    def show_tag_details(self, tag):
        """显示标签详情"""
        try:
            # 获取使用该标签的文件数量
            files = self.tag_service.get_files_by_tag(tag["id"])
            file_count = len(files)

            # 构建详情文本
            details = f"标签名称: {tag['name']}\n"

            # 显示颜色信息（参考导航栏显示方式）
            if tag.get('color'):
                details += f"标签颜色: {tag['color']}\n"
            else:
                # 检查是否有继承的颜色
                try:
                    inherited_attrs = self.tag_service.get_inherited_attributes(tag["id"])
                    if inherited_attrs.get('color'):
                        details += f"标签颜色: {inherited_attrs['color']} (继承)\n"
                    else:
                        details += f"标签颜色: 未设置\n"
                except:
                    details += f"标签颜色: 未设置\n"

            details += f"创建时间: {tag.get('created_at', '未知')}\n"
            details += f"使用文件数: {file_count} 个\n"

            # 显示备注信息
            if tag.get("description"):
                details += f"\n备注信息:\n{tag['description']}\n"

            if tag.get("parent_id"):
                parent_tag = self.tag_service.get_tag_by_id(tag["parent_id"])
                if parent_tag:
                    details += f"\n父标签: {parent_tag['name']}\n"

            # 获取子标签
            child_tags = self.tag_service.get_child_tags(tag["id"])
            if child_tags:
                child_names = [t['name'] for t in child_tags]
                details += f"\n子标签: {', '.join(child_names)}\n"

            self.tag_details.setText(details)

        except Exception as e:
            self.tag_details.setText(f"详情加载失败: {e}")

    def show_context_menu(self, position):
        """显示增强的右键菜单"""
        item = self.tag_tree.itemAt(position)
        if not item:
            return

        tag_data = item.data(0, Qt.ItemDataRole.UserRole)
        if not tag_data:
            return

        menu = QMenu(self)

        # 添加子标签
        add_child_action = QAction("添加子标签", self)
        add_child_action.triggered.connect(lambda: self.add_child_tag(tag_data['id']))
        menu.addAction(add_child_action)

        menu.addSeparator()

        # 编辑标签
        edit_action = QAction("编辑标签", self)
        edit_action.triggered.connect(self.edit_tag)
        menu.addAction(edit_action)

        # 删除标签
        delete_action = QAction("删除标签", self)
        delete_action.triggered.connect(self.delete_tag_with_confirmation)
        menu.addAction(delete_action)

        menu.exec(self.tag_tree.mapToGlobal(position))

    def add_tag(self):
        """添加新标签"""
        dialog = TagEditDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            try:
                tag_id = self.tag_service.create_tag(
                    dialog.tag_name,
                    dialog.tag_color,
                    dialog.parent_tag_id
                )
                self.load_tags()
                self.tags_changed.emit()
                # 移除不必要的成功提示，符合"用户干预极简化"原则
            except Exception as e:
                QMessageBox.warning(self, "错误", f"创建标签失败: {e}")

    def add_child_tag(self, parent_tag_id):
        """添加子标签"""
        dialog = TagEditDialog(self, parent_tag_id=parent_tag_id)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            try:
                tag_id = self.tag_service.create_tag(
                    dialog.tag_name,
                    dialog.tag_color,
                    parent_tag_id
                )
                self.load_tags()
                self.tags_changed.emit()
                # 移除不必要的成功提示，符合"用户干预极简化"原则
            except Exception as e:
                QMessageBox.warning(self, "错误", f"创建子标签失败: {e}")

    def edit_tag(self):
        """编辑标签"""
        current_item = self.tag_tree.currentItem()
        if not current_item:
            return

        tag = current_item.data(0, Qt.ItemDataRole.UserRole)
        dialog = TagEditDialog(self, tag)

        if dialog.exec() == QDialog.DialogCode.Accepted:
            try:
                # 使用新的update_tag方法
                success = self.tag_service.update_tag(
                    tag['id'],
                    name=dialog.tag_name,
                    color=dialog.tag_color,
                    parent_id=dialog.parent_tag_id
                )

                if success:
                    self.load_tags()
                    self.tags_changed.emit()
                    # 移除不必要的成功提示，符合"用户干预极简化"原则
                else:
                    QMessageBox.warning(self, "错误", "标签编辑失败，可能会造成循环依赖")
            except Exception as e:
                QMessageBox.warning(self, "错误", f"编辑标签失败: {e}")

    def delete_tag(self):
        """删除标签（保持向后兼容）"""
        self.delete_tag_with_confirmation()

    def delete_tag_with_confirmation(self):
        """带确认的标签删除"""
        current_item = self.tag_tree.currentItem()
        if not current_item:
            return

        tag = current_item.data(0, Qt.ItemDataRole.UserRole)

        # 检查子标签和关联文件
        child_tags = self.tag_service.get_child_tags(tag['id'])
        files = self.tag_service.get_files_by_tag(tag['id'])

        message = f"确定要删除标签 '{tag['name']}' 吗？\n"
        if child_tags:
            message += f"该标签有 {len(child_tags)} 个子标签。\n"
        if files:
            message += f"该标签被 {len(files)} 个文件使用。\n"

        reply = QMessageBox.question(
            self, "确认删除", message,
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 如果有子标签，询问是否级联删除
            delete_children = False
            if child_tags:
                cascade_reply = QMessageBox.question(
                    self, "级联删除", "是否同时删除所有子标签？\n\n选择'是'：删除所有子标签\n选择'否'：子标签将变为顶级标签",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
                )
                delete_children = cascade_reply == QMessageBox.StandardButton.Yes

            try:
                success = self.tag_service.delete_tag_cascade(tag['id'], delete_children)
                if success:
                    self.load_tags()
                    self.tags_changed.emit()
                    # 移除删除成功提示，符合"用户干预极简化"原则
                else:
                    QMessageBox.warning(self, "错误", "标签删除失败")
            except Exception as e:
                QMessageBox.warning(self, "错误", f"删除标签失败: {e}")


class TagEditDialog(QDialog):
    """标签编辑对话框"""

    def __init__(self, parent=None, tag=None, parent_tag_id=None):
        super().__init__(parent)
        self.tag = tag
        self.tag_name = ""
        self.tag_color = "#2196F3"  # 默认蓝色
        self.parent_tag_id = parent_tag_id

        # 如果是创建子标签，自动继承父标签颜色
        if parent_tag_id and not tag:
            self._set_inherited_color(parent_tag_id)

        self.init_ui()

        if tag:
            self.load_tag_data()

    def init_ui(self):
        """初始化用户界面"""
        title = "编辑标签" if self.tag else "新建标签"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(300, 200)

        layout = QVBoxLayout(self)

        # 标签名称
        layout.addWidget(QLabel("标签名称:"))
        self.name_edit = QLineEdit()
        layout.addWidget(self.name_edit)

        # 标签颜色
        color_layout = QHBoxLayout()
        color_layout.addWidget(QLabel("标签颜色:"))

        # 使用新的颜色选择按钮
        from smartvault.ui.widgets.color_display_widget import ColorPickerButton
        self.color_btn = ColorPickerButton(self.tag_color)
        self.color_btn.color_changed.connect(self.on_color_changed)
        color_layout.addWidget(self.color_btn)

        # 如果是子标签且有继承颜色，显示提示
        if self.parent_tag_id and not self.tag:
            hint_label = QLabel("(已自动继承父标签颜色)")
            hint_label.setStyleSheet("color: #666666; font-size: 11px;")
            color_layout.addWidget(hint_label)

        color_layout.addStretch()
        layout.addLayout(color_layout)

        # 对话框按钮
        buttons_layout = QHBoxLayout()

        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(ok_btn)

        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)

    def _set_inherited_color(self, parent_tag_id):
        """设置继承的颜色（创建子标签时自动调用）

        Args:
            parent_tag_id: 父标签ID
        """
        try:
            from smartvault.services.tag_service import TagService
            from smartvault.ui.utils.color_utils import lighten_color

            tag_service = TagService()
            parent_tag = tag_service.get_tag_by_id(parent_tag_id)

            if parent_tag and parent_tag.get('color'):
                # 获取父标签颜色并使其变浅作为子标签的默认颜色
                parent_color = parent_tag['color']
                inherited_color = lighten_color(parent_color, 0.15)  # 变浅15%，保证可读性
                # 确保颜色具有足够的可读性
                from smartvault.ui.utils.color_utils import ensure_readable_color
                readable_color = ensure_readable_color(inherited_color, min_lightness=50, max_lightness=180)
                self.tag_color = readable_color.name()
            else:
                # 如果父标签没有颜色，尝试获取继承的颜色
                inherited_attrs = tag_service.get_inherited_attributes(parent_tag_id)
                if inherited_attrs.get('color'):
                    parent_color = inherited_attrs['color']
                    inherited_color = lighten_color(parent_color, 0.15)  # 变浅15%，保证可读性
                    from smartvault.ui.utils.color_utils import ensure_readable_color
                    readable_color = ensure_readable_color(inherited_color, min_lightness=50, max_lightness=180)
                    self.tag_color = readable_color.name()

        except Exception as e:
            print(f"设置继承颜色失败: {e}")
            # 如果失败，保持默认颜色

    def load_tag_data(self):
        """加载标签数据（编辑模式）"""
        if self.tag:
            self.name_edit.setText(self.tag["name"])
            if self.tag.get("color"):
                self.tag_color = self.tag["color"]
                self.color_btn.set_color(self.tag_color)
            self.parent_tag_id = self.tag.get("parent_id")

    def on_color_changed(self, color_name):
        """颜色改变事件处理"""
        self.tag_color = color_name

    def choose_color(self):
        """选择颜色（保留兼容性）"""
        color = QColorDialog.getColor(QColor(self.tag_color), self)
        if color.isValid():
            self.tag_color = color.name()
            self.color_btn.set_color(self.tag_color)

    def update_color_button(self):
        """更新颜色按钮显示（保留兼容性）"""
        if hasattr(self, 'color_btn'):
            self.color_btn.set_color(self.tag_color)

    def accept(self):
        """确认对话框"""
        self.tag_name = self.name_edit.text().strip()

        if not self.tag_name:
            QMessageBox.warning(self, "错误", "请输入标签名称")
            return

        super().accept()


if __name__ == "__main__":
    from PySide6.QtWidgets import QApplication

    app = QApplication(sys.argv)
    dialog = TagManagementDialog()
    dialog.show()
    sys.exit(app.exec())
