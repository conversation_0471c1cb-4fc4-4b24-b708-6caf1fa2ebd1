"""
导航面板
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QTabWidget, QTreeView, QMenu,
    QInputDialog, QMessageBox
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QStandardItemModel, QStandardItem, QAction

from smartvault.ui.components.tag_navigation_panel import TagNavigationPanel
from smartvault.services.tag_service import TagService
from smartvault.utils.tree_state_manager import tree_state_manager


class DragDropTreeView(QTreeView):
    """支持拖拽的树视图"""

    # 自定义信号
    files_dropped = Signal(list, str)  # 文件列表, 目标文件夹ID

    def __init__(self, parent=None):
        super().__init__(parent)
        # 启用拖拽接收
        self.setAcceptDrops(True)
        self.setDropIndicatorShown(True)

    def dragEnterEvent(self, event):
        """拖拽进入事件"""
        # 检查是否包含文本数据（文件ID列表）
        if event.mimeData().hasText():
            print("🎯 导航面板：拖拽进入，包含文本数据")
            event.acceptProposedAction()
        else:
            print("❌ 导航面板：拖拽进入，无有效数据")
            event.ignore()

    def dragMoveEvent(self, event):
        """拖拽移动事件"""
        if event.mimeData().hasText():
            # 获取拖拽位置的项目
            index = self.indexAt(event.pos())
            if index.isValid():
                item_data = index.data(Qt.UserRole)
                # 只允许拖拽到自定义文件夹
                if item_data and item_data.startswith("tag:"):
                    print(f"✅ 导航面板：可拖拽到 {item_data}")
                    event.acceptProposedAction()
                    return

            print("❌ 导航面板：无效拖拽目标")
            event.ignore()
        else:
            event.ignore()

    def dropEvent(self, event):
        """拖拽放下事件"""
        try:
            if not event.mimeData().hasText():
                event.ignore()
                return

            # 获取拖拽位置的项目
            index = self.indexAt(event.pos())
            if not index.isValid():
                event.ignore()
                return

            item_data = index.data(Qt.UserRole)
            if not item_data or not item_data.startswith("tag:"):
                event.ignore()
                return

            # 获取文件ID列表
            file_ids_text = event.mimeData().text()
            file_ids = [fid.strip() for fid in file_ids_text.split(",") if fid.strip()]

            if not file_ids:
                event.ignore()
                return

            # 提取标签ID
            tag_id = item_data.replace("tag:", "")

            print(f"🎯 导航面板：拖拽放下 {len(file_ids)} 个文件到 {tag_id}")

            # 发出信号
            self.files_dropped.emit(file_ids, tag_id)
            event.acceptProposedAction()

        except Exception as e:
            print(f"❌ 导航面板拖拽处理失败: {e}")
            import traceback
            traceback.print_exc()
            event.ignore()


class NavigationPanel(QWidget):
    """导航面板"""

    # 自定义信号
    folder_selected = Signal(str)  # 文件夹被选中
    tag_selected = Signal(str)     # 标签被选中
    files_dropped_to_folder = Signal(list, str)  # 文件拖拽到文件夹

    def __init__(self, parent=None):
        """初始化面板

        Args:
            parent: 父窗口
        """
        super().__init__(parent)

        # 初始化标签服务
        self.tag_service = TagService()

        # 初始化UI
        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # 创建选项卡
        self.tab_widget = QTabWidget()

        # 文件夹选项卡 - 使用支持拖拽的树视图
        self.folder_tree = DragDropTreeView()
        self.folder_tree.setHeaderHidden(True)
        self.folder_tree.setContextMenuPolicy(Qt.CustomContextMenu)
        self.folder_tree.customContextMenuRequested.connect(self.show_context_menu)
        self.folder_tree.files_dropped.connect(self.on_files_dropped_to_folder)
        self.init_folder_tree()
        self.tab_widget.addTab(self.folder_tree, "文件夹")

        # 标签选项卡 - 使用新的标签导航面板
        self.tag_navigation_panel = TagNavigationPanel()
        self.tab_widget.addTab(self.tag_navigation_panel, "标签")

        layout.addWidget(self.tab_widget)

        # 连接信号
        self.folder_tree.clicked.connect(self.on_folder_clicked)
        self.tag_navigation_panel.tag_selected.connect(self.on_tag_navigation_selected)
        self.tag_navigation_panel.tag_filter_cleared.connect(self.on_tag_filter_cleared)

    def init_folder_tree(self):
        """初始化文件夹树"""
        model = QStandardItemModel()
        root = model.invisibleRootItem()

        # 添加基础文件夹
        all_files = QStandardItem("📁 所有文件")
        all_files.setData("all", Qt.UserRole)
        root.appendRow(all_files)

        # 添加中转文件夹（独立导航项）
        staging = QStandardItem("📥 中转文件夹")
        staging.setData("staging", Qt.UserRole)
        root.appendRow(staging)

        # 添加最近添加（独立导航项，只显示正式文件）
        recent = QStandardItem("📅 最近添加")
        recent.setData("recent", Qt.UserRole)
        root.appendRow(recent)

        # 按类型分类
        type_folder = QStandardItem("📂 按类型分类")
        type_folder.setData("type_category", Qt.UserRole)

        # 文档类型
        doc_item = QStandardItem("📄 文档")
        doc_item.setData("type:document", Qt.UserRole)
        type_folder.appendRow(doc_item)

        # 图片类型
        image_item = QStandardItem("🖼️ 图片")
        image_item.setData("type:image", Qt.UserRole)
        type_folder.appendRow(image_item)

        # 音频类型
        audio_item = QStandardItem("🎵 音频")
        audio_item.setData("type:audio", Qt.UserRole)
        type_folder.appendRow(audio_item)

        # 视频类型
        video_item = QStandardItem("🎬 视频")
        video_item.setData("type:video", Qt.UserRole)
        type_folder.appendRow(video_item)

        # 其他类型
        other_item = QStandardItem("📦 其他")
        other_item.setData("type:other", Qt.UserRole)
        type_folder.appendRow(other_item)

        root.appendRow(type_folder)

        # 按入库方式分类
        entry_folder = QStandardItem("📂 按入库方式")
        entry_folder.setData("entry_category", Qt.UserRole)

        link_item = QStandardItem("🔗 链接文件")
        link_item.setData("entry:link", Qt.UserRole)
        entry_folder.appendRow(link_item)

        copy_item = QStandardItem("📋 复制文件")
        copy_item.setData("entry:copy", Qt.UserRole)
        entry_folder.appendRow(copy_item)

        move_item = QStandardItem("➡️ 移动文件")
        move_item.setData("entry:move", Qt.UserRole)
        entry_folder.appendRow(move_item)

        root.appendRow(entry_folder)

        # 按时间分类
        time_folder = QStandardItem("📂 按时间分类")
        time_folder.setData("time_category", Qt.UserRole)

        today_item = QStandardItem("📅 今天")
        today_item.setData("time:today", Qt.UserRole)
        time_folder.appendRow(today_item)

        week_item = QStandardItem("📅 本周")
        week_item.setData("time:week", Qt.UserRole)
        time_folder.appendRow(week_item)

        month_item = QStandardItem("📅 本月")
        month_item.setData("time:month", Qt.UserRole)
        time_folder.appendRow(month_item)

        older_item = QStandardItem("📅 更早")
        older_item.setData("time:older", Qt.UserRole)
        time_folder.appendRow(older_item)

        root.appendRow(time_folder)

        # 添加自定义文件夹功能
        self.init_custom_folders(root)

        # 添加移动设备文件夹功能
        self.init_device_folders(root)

        self.folder_tree.setModel(model)

        # 🔧 使用状态管理器恢复展开状态，而不是强制展开所有
        tree_state_manager.restore_tree_state(self.folder_tree, "navigation_folder_tree")

    def on_tag_navigation_selected(self, tag_id):
        """处理标签导航面板的标签选择事件

        Args:
            tag_id: 标签ID
        """
        # 转发标签选择信号
        self.tag_selected.emit(tag_id)

    def on_tag_filter_cleared(self):
        """处理标签筛选清除事件"""
        # 发出特殊的清除信号（使用空字符串表示清除）
        self.tag_selected.emit("")

    def on_files_dropped_to_folder(self, file_ids, tag_id):
        """处理文件拖拽到文件夹事件

        Args:
            file_ids: 文件ID列表
            tag_id: 目标文件夹标签ID
        """
        print(f"📁 导航面板：收到拖拽事件 - {len(file_ids)} 个文件到标签 {tag_id}")
        # 转发信号到主窗口
        self.files_dropped_to_folder.emit(file_ids, tag_id)

    def on_folder_clicked(self, index):
        """处理文件夹点击事件

        Args:
            index: 项目索引
        """
        # 获取文件夹ID
        folder_id = index.data(Qt.UserRole)
        if folder_id:
            self.folder_selected.emit(folder_id)

    def on_folder_selected(self, index):
        """处理文件夹选择事件（用于程序化选择）

        Args:
            index: 项目索引
        """
        # 获取文件夹ID
        folder_id = index.data(Qt.UserRole)
        if folder_id:
            self.folder_selected.emit(folder_id)

    def refresh_tags(self):
        """刷新标签（外部调用）"""
        if hasattr(self, 'tag_navigation_panel'):
            self.tag_navigation_panel.refresh_tags()

    def init_custom_folders(self, root):
        """初始化自定义文件夹 - 基于标签系统"""
        try:
            # 创建自定义文件夹分类项
            custom_folder_item = QStandardItem("📁 自定义文件夹")
            custom_folder_item.setData("custom_folders", Qt.UserRole)

            # 加载文件夹标签
            folder_tags = self.tag_service.get_folder_tags()
            for tag in folder_tags:
                folder_item = QStandardItem(tag['name'])
                folder_item.setData(f"tag:{tag['id']}", Qt.UserRole)
                custom_folder_item.appendRow(folder_item)

            root.appendRow(custom_folder_item)

        except Exception as e:
            print(f"初始化自定义文件夹失败: {e}")

    def init_device_folders(self, root):
        """初始化移动设备文件夹 - 基于标签系统"""
        try:
            # 创建移动设备分类项
            device_folder_item = QStandardItem("💾 移动设备")
            device_folder_item.setData("device_folders", Qt.UserRole)

            # 加载设备标签
            device_tags = self.tag_service.get_device_tags()
            for tag in device_tags:
                device_item = QStandardItem(tag['name'])
                device_item.setData(f"tag:{tag['id']}", Qt.UserRole)
                device_folder_item.appendRow(device_item)

            root.appendRow(device_folder_item)

        except Exception as e:
            print(f"初始化移动设备文件夹失败: {e}")

    def show_context_menu(self, position):
        """显示右键菜单"""
        try:
            index = self.folder_tree.indexAt(position)
            if not index.isValid():
                return

            item_data = index.data(Qt.UserRole)
            menu = QMenu(self)

            if item_data == "custom_folders":
                # 自定义文件夹右键菜单
                add_folder_action = QAction("添加文件夹", self)
                add_folder_action.triggered.connect(self.add_custom_folder)
                menu.addAction(add_folder_action)

            elif item_data == "device_folders":
                # 移动设备右键菜单
                add_device_action = QAction("添加设备文件夹", self)
                add_device_action.triggered.connect(self.add_device_folder)
                menu.addAction(add_device_action)

            elif item_data and item_data.startswith("tag:"):
                # 标签项右键菜单
                delete_action = QAction("删除", self)
                # 使用functools.partial避免lambda闭包问题
                from functools import partial
                delete_action.triggered.connect(partial(self.delete_folder_tag, item_data))
                menu.addAction(delete_action)

            if menu.actions():
                menu.exec(self.folder_tree.mapToGlobal(position))

        except Exception as e:
            print(f"显示右键菜单失败: {e}")

    def add_custom_folder(self):
        """添加自定义文件夹 - 增强版"""
        try:
            folder_name, ok = QInputDialog.getText(
                self, "添加自定义文件夹", "请输入文件夹名称:"
            )

            if ok and folder_name.strip():
                folder_name = folder_name.strip()

                # 创建文件夹标签
                tag_id = self.tag_service.create_folder_tag(folder_name)

                # 立即刷新导航面板
                self.refresh_folder_tree()

                # 自动选中新创建的文件夹
                self.select_folder_by_tag_id(tag_id)

                QMessageBox.information(
                    self, "成功", f"自定义文件夹 '{folder_name}' 已创建"
                )

        except Exception as e:
            QMessageBox.warning(self, "错误", f"创建文件夹失败: {e}")

    def add_device_folder(self):
        """添加设备文件夹 - 通过选择设备路径自动读取卷标"""
        try:
            from PySide6.QtWidgets import QFileDialog
            from smartvault.utils.device_volume_reader import get_volume_info_from_path

            # 弹出文件夹选择对话框
            device_path = QFileDialog.getExistingDirectory(
                self,
                "选择移动设备根目录",
                "",
                QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
            )

            if not device_path:
                return

            # 读取设备卷标信息
            device_info = get_volume_info_from_path(device_path)

            if not device_info:
                QMessageBox.warning(
                    self, "错误",
                    f"无法读取设备信息: {device_path}\n请确保选择的是有效的设备根目录。"
                )
                return

            # 使用读取到的卷标作为设备文件夹名称
            device_name = device_info.volume_name

            # 显示确认对话框
            reply = QMessageBox.question(
                self, "确认创建设备文件夹",
                f"检测到设备信息:\n\n"
                f"路径: {device_path}\n"
                f"卷标: {device_name}\n"
                f"文件系统: {device_info.file_system or '未知'}\n\n"
                f"是否创建名为 '{device_name}' 的设备文件夹？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply != QMessageBox.Yes:
                return

            # 创建设备标签
            tag_id = self.tag_service.create_device_folder_tag(device_name)

            # 刷新文件夹树
            self.refresh_folder_tree()

            # 自动选中新创建的设备文件夹
            self.select_folder_by_tag_id(tag_id)

            # 询问用户是否要立即添加设备中的文件
            reply = QMessageBox.question(
                self, "添加设备文件",
                f"设备文件夹 '{device_name}' 已创建成功！\n\n"
                f"是否要立即将设备中的文件添加到智能文件库？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                # 弹出文件添加对话框
                self._open_add_files_dialog_for_device(device_path, tag_id, device_name)
            else:
                QMessageBox.information(
                    self, "完成",
                    f"设备文件夹 '{device_name}' 已创建\n\n"
                    f"您可以稍后在文件添加时选择此设备作为来源。"
                )

        except Exception as e:
            QMessageBox.warning(self, "错误", f"创建设备文件夹失败: {e}")

    def _open_add_files_dialog_for_device(self, device_path, device_tag_id, device_name):
        """为设备打开文件添加对话框"""
        try:
            from smartvault.ui.dialogs.add_file_dialog import AddFileDialog

            # 获取主窗口
            main_window = self
            while main_window and not hasattr(main_window, 'file_service'):
                main_window = main_window.parent()

            if not main_window:
                QMessageBox.warning(self, "错误", "无法找到主窗口")
                return

            # 创建文件添加对话框
            dialog = AddFileDialog(main_window)

            # 预填设备路径作为文件夹
            dialog.file_list.append(device_path)
            dialog.file_list_widget.addItem(f"文件夹: {device_path}")

            # 设置设备来源为刚创建的设备文件夹
            # 需要刷新设备列表以包含新创建的设备
            dialog._load_device_list()

            # 查找并选择对应的设备
            for i in range(dialog.device_combo.count()):
                if dialog.device_combo.itemData(i) == device_tag_id:
                    dialog.device_combo.setCurrentIndex(i)
                    break

            # 设置对话框标题
            dialog.setWindowTitle(f"添加设备文件 - {device_name}")

            # 显示对话框
            result = dialog.exec()

            if result == AddFileDialog.Accepted and dialog.file_list:
                # 调用主窗口的文件添加处理逻辑
                main_window._process_device_files_addition(dialog, device_name)

        except Exception as e:
            QMessageBox.warning(self, "错误", f"打开文件添加对话框失败: {e}")
            import traceback
            traceback.print_exc()

    def delete_folder_tag(self, tag_data):
        """删除文件夹标签"""
        try:
            tag_id = tag_data.replace("tag:", "")
            tag = self.tag_service.get_tag_by_id(tag_id)

            if not tag:
                QMessageBox.warning(self, "错误", "标签不存在")
                return

            reply = QMessageBox.question(
                self, "确认删除",
                f"确定要删除 '{tag['name']}' 吗？\n\n"
                "注意：这将移除该标签与所有文件的关联关系。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 删除标签
                success = self.tag_service.delete_tag(tag_id)

                if success:
                    # 刷新文件夹树
                    self.refresh_folder_tree()
                    QMessageBox.information(self, "成功", "标签已删除")
                else:
                    QMessageBox.warning(self, "错误", "删除标签失败")

        except Exception as e:
            QMessageBox.warning(self, "错误", f"删除标签失败: {e}")

    def refresh_folder_tree(self):
        """刷新文件夹树"""
        try:
            # 🔧 在刷新前保存当前展开状态
            tree_state_manager.save_tree_state(self.folder_tree, "navigation_folder_tree")

            # 重新初始化文件夹树
            self.init_folder_tree()
        except Exception as e:
            print(f"刷新文件夹树失败: {e}")

    def save_tree_states(self):
        """保存树控件的展开状态"""
        try:
            # 保存文件夹树状态
            tree_state_manager.save_tree_state(self.folder_tree, "navigation_folder_tree")

            # 保存标签导航面板的状态
            if hasattr(self.tag_navigation_panel, 'save_tree_state'):
                self.tag_navigation_panel.save_tree_state()

        except Exception as e:
            print(f"保存导航面板树状态失败: {e}")

    def select_folder_by_tag_id(self, tag_id):
        """根据标签ID选中文件夹

        Args:
            tag_id: 标签ID
        """
        try:
            # 遍历模型查找对应的标签项
            def find_tag_item(parent_item, target_tag_id):
                for row in range(parent_item.rowCount()):
                    item = parent_item.child(row)
                    if item:
                        item_data = item.data(Qt.UserRole)
                        if item_data == f"tag:{target_tag_id}":
                            return item

                        # 递归查找子项
                        child_result = find_tag_item(item, target_tag_id)
                        if child_result:
                            return child_result
                return None

            # 从根项开始查找
            root_item = self.folder_tree.model().invisibleRootItem()
            target_item = find_tag_item(root_item, tag_id)

            if target_item:
                # 获取项目索引
                index = self.folder_tree.model().indexFromItem(target_item)

                # 展开父项
                parent_index = index.parent()
                if parent_index.isValid():
                    self.folder_tree.expand(parent_index)

                # 选中项目
                self.folder_tree.setCurrentIndex(index)
                self.folder_tree.scrollTo(index)

                # 触发选择事件
                self.on_folder_selected(index)

        except Exception as e:
            print(f"选中文件夹失败: {e}")
