"""
文件服务模块 - 使用功能切片组织代码
"""

from PySide6.QtCore import QObject, Signal
from .core import FileServiceCore
from .import_ops import FileImportMixin
from .operations import FileOperationsMixin


class FileService(QObject, FileImportMixin, FileOperationsMixin, FileServiceCore):
    """文件服务类，组合各功能模块"""

    # 重复文件建议信号
    duplicate_suggestion = Signal(dict)  # 发送重复文件建议信息
    batch_duplicate_processed = Signal(dict)  # 批量重复文件处理完成信号

    def __init__(self):
        QObject.__init__(self)
        FileServiceCore.__init__(self)

        # 批量操作上下文
        self.batch_context = None

    def start_batch_operation(self, operation_name):
        """开始批量操作

        Args:
            operation_name: 操作名称，如 "拖拽添加", "监控批量处理" 等
        """
        import time
        self.batch_context = {
            'name': operation_name,
            'duplicate_suggestions': [],
            'start_time': time.time(),
            'total_files': 0,
            'processed_files': 0
        }
        print(f"🚀 开始批量操作: {operation_name}")

    def end_batch_operation(self):
        """结束批量操作并处理收集的建议"""
        if not self.batch_context:
            return

        context = self.batch_context
        self.batch_context = None

        print(f"✅ 批量操作完成: {context['name']}")
        print(f"   处理文件: {context['processed_files']} 个")
        print(f"   重复建议: {len(context['duplicate_suggestions'])} 个")

        # 如果有重复文件建议，发送批量处理信号
        if context['duplicate_suggestions']:
            self.batch_duplicate_processed.emit(context)

    def is_in_batch_operation(self):
        """检查是否在批量操作中"""
        return self.batch_context is not None

    def add_to_batch_stats(self, processed=True):
        """更新批量操作统计"""
        if self.batch_context:
            if processed:
                self.batch_context['processed_files'] += 1
            self.batch_context['total_files'] += 1
