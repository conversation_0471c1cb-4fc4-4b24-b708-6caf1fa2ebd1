#!/usr/bin/env python3
"""
简单导入测试
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("开始测试导入...")

try:
    print("1. 测试枚举导入...")
    from smartvault.services.auto_tag_service import ConditionType, LogicOperator
    print("✅ 枚举导入成功")
    
    print("2. 测试基础类导入...")
    from smartvault.services.auto_tag_service import Condition
    print("✅ Condition 导入成功")
    
    print("3. 测试条件组导入...")
    from smartvault.services.auto_tag_service import ConditionGroup
    print("✅ ConditionGroup 导入成功")
    
    print("4. 测试规则导入...")
    from smartvault.services.auto_tag_service import AutoTagRule
    print("✅ AutoTagRule 导入成功")
    
    print("5. 测试服务导入...")
    from smartvault.services.auto_tag_service import AutoTagService
    print("✅ AutoTagService 导入成功")
    
    print("6. 测试创建简单条件...")
    condition = Condition(type=ConditionType.FILE_EXTENSION, value="pdf")
    print("✅ 简单条件创建成功")
    
    print("7. 测试条件匹配...")
    file_info = {"name": "test.pdf", "size": 1024}
    result = condition.matches(file_info)
    print(f"✅ 条件匹配结果: {result}")
    
    print("🎉 核心功能测试完成！")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
