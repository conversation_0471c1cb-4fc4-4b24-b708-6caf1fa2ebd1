"""
调试启动脚本 - 逐步诊断启动问题
"""

import os
import sys
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_step(step_name, func):
    """调试步骤包装器"""
    print(f"🔍 {step_name}...")
    start_time = time.time()

    try:
        result = func()
        elapsed = time.time() - start_time
        print(f"✅ {step_name} 完成 ({elapsed:.2f}s)")
        return result
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ {step_name} 失败 ({elapsed:.2f}s): {e}")
        import traceback
        traceback.print_exc()
        return None

def step1_imports():
    """步骤1: 导入模块"""
    from PySide6.QtWidgets import QApplication
    from smartvault.ui.main_window import MainWindow
    return True

def step2_create_app():
    """步骤2: 创建QApplication"""
    from PySide6.QtWidgets import QApplication
    app = QApplication(sys.argv)
    app.setApplicationName("SmartVault")
    return app

def step3_test_database():
    """步骤3: 测试数据库连接"""
    from smartvault.data.database import Database
    db = Database.create_from_config()
    cursor = db.conn.cursor()
    cursor.execute("SELECT COUNT(*) FROM files")
    count = cursor.fetchone()[0]
    db.close()
    print(f"  数据库文件数量: {count}")
    return True

def step4_test_file_service():
    """步骤4: 测试文件服务"""
    from smartvault.services.file import FileService
    file_service = FileService()
    files = file_service.get_files(limit=1)
    print(f"  文件服务测试: 获取到 {len(files)} 个文件")
    return True

def step5_create_main_window_core():
    """步骤5: 创建主窗口核心（不加载数据）"""
    from smartvault.ui.main_window import MainWindow

    # 临时禁用数据加载
    original_load_initial_data = MainWindow.load_initial_data
    MainWindow.load_initial_data = lambda self, show_message=True: None

    try:
        window = MainWindow()
        print("  主窗口核心创建成功")
        return window
    finally:
        # 恢复原始方法
        MainWindow.load_initial_data = original_load_initial_data

def step6_create_full_main_window():
    """步骤6: 创建完整主窗口"""
    from smartvault.ui.main_window import MainWindow
    window = MainWindow()
    return window

def step7_show_window(window):
    """步骤7: 显示窗口"""
    if window:
        window.show()
        return True
    return False

def main():
    """主函数"""
    print("=" * 60)
    print("🐛 SmartVault 调试启动工具")
    print("=" * 60)

    # 步骤1: 导入测试
    if not debug_step("导入模块", step1_imports):
        return 1

    # 步骤2: 创建应用程序
    app = debug_step("创建QApplication", step2_create_app)
    if not app:
        return 1

    # 步骤3: 数据库测试
    if not debug_step("测试数据库连接", step3_test_database):
        return 1

    # 步骤4: 文件服务测试
    if not debug_step("测试文件服务", step4_test_file_service):
        return 1

    # 步骤5: 创建主窗口核心（无数据加载）
    window_core = debug_step("创建主窗口核心", step5_create_main_window_core)
    if window_core:
        print("🎯 主窗口核心创建成功，问题可能在数据加载部分")
        window_core.close()

    # 步骤6: 创建完整主窗口
    print("\n⚠️  即将创建完整主窗口，如果在此处卡住，说明问题在数据加载线程")
    print("等待10秒...")

    import threading
    import signal

    # 设置超时机制
    window = None
    creation_complete = threading.Event()

    def create_window():
        nonlocal window
        try:
            window = debug_step("创建完整主窗口", step6_create_full_main_window)
            creation_complete.set()
        except Exception as e:
            print(f"窗口创建异常: {e}")
            creation_complete.set()

    # 在单独线程中创建窗口
    creation_thread = threading.Thread(target=create_window, daemon=True)
    creation_thread.start()

    # 等待创建完成或超时
    if creation_complete.wait(timeout=10):
        if window:
            print("✅ 完整主窗口创建成功")

            # 步骤7: 显示窗口
            if debug_step("显示窗口", lambda: step7_show_window(window)):
                print("🚀 启动成功！进入事件循环...")

                # 运行一小段时间然后退出
                from PySide6.QtCore import QTimer
                def auto_exit():
                    print("🔄 自动退出测试模式")
                    app.quit()

                timer = QTimer()
                timer.timeout.connect(auto_exit)
                timer.start(3000)  # 3秒后自动退出

                return app.exec()
        else:
            print("❌ 完整主窗口创建失败")
    else:
        print("❌ 主窗口创建超时 - 确认问题在数据加载线程")
        print("\n🔧 建议解决方案:")
        print("1. 禁用启动时的数据加载")
        print("2. 将数据加载改为延迟加载")
        print("3. 检查数据库连接池设置")

    return 1

if __name__ == "__main__":
    sys.exit(main())
