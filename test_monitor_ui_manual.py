#!/usr/bin/env python3
"""
手动测试监控设置UI界面
"""

import sys
import os
import tempfile
import shutil
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from smartvault.ui.dialogs.settings_dialog import SettingsDialog
from smartvault.services.file_monitor_service import FileMonitorService


class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("监控设置UI测试")
        self.resize(400, 200)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 添加测试按钮
        test_button = QPushButton("打开监控设置对话框")
        test_button.clicked.connect(self.open_settings_dialog)
        layout.addWidget(test_button)
        
        # 创建测试数据
        self.setup_test_data()
    
    def setup_test_data(self):
        """设置测试数据"""
        print("🔧 设置测试数据...")
        
        # 创建测试目录
        self.test_dirs = []
        for i in range(3):
            test_dir = tempfile.mkdtemp(prefix=f"smartvault_test_{i+1}_")
            self.test_dirs.append(test_dir)
            print(f"   📁 创建测试目录 {i+1}: {test_dir}")
        
        # 创建监控服务并添加一些测试监控配置
        monitor_service = FileMonitorService()
        
        # 添加测试监控配置
        configs = [
            {
                "folder_path": self.test_dirs[0],
                "entry_mode": "link",
                "file_patterns": ["*.txt", "*.pdf"],
                "auto_add": True,
                "recursive": True
            },
            {
                "folder_path": self.test_dirs[1],
                "entry_mode": "copy",
                "file_patterns": ["*.doc", "*.docx"],
                "auto_add": False,
                "recursive": False
            },
            {
                "folder_path": self.test_dirs[2],
                "entry_mode": "move",
                "file_patterns": [],  # 所有文件
                "auto_add": True,
                "recursive": True
            }
        ]
        
        for i, config in enumerate(configs):
            try:
                monitor_id = monitor_service.add_monitor_folder(**config)
                print(f"   ✅ 添加测试监控配置 {i+1}: {monitor_id}")
                
                # 启动第一个监控作为示例
                if i == 0:
                    monitor_service.start_monitoring(monitor_id)
                    print(f"   🚀 启动监控 {i+1}")
                    
            except Exception as e:
                print(f"   ❌ 添加监控配置 {i+1} 失败: {e}")
    
    def open_settings_dialog(self):
        """打开设置对话框"""
        print("\n🔧 打开设置对话框...")
        
        try:
            dialog = SettingsDialog(self)
            
            # 切换到监控设置选项卡
            dialog.tab_widget.setCurrentIndex(1)  # 监控设置是第二个选项卡
            
            print("   ✅ 设置对话框已打开，已切换到监控设置选项卡")
            print("   📋 请在对话框中测试以下功能：")
            print("      1. 查看监控文件夹列表")
            print("      2. 添加新的监控文件夹")
            print("      3. 编辑现有监控配置")
            print("      4. 启动/停止监控")
            print("      5. 删除监控配置")
            
            result = dialog.exec()
            
            if result == SettingsDialog.Accepted:
                print("   ✅ 设置已保存")
            else:
                print("   ❌ 设置已取消")
                
        except Exception as e:
            print(f"   ❌ 打开设置对话框失败: {e}")
            import traceback
            traceback.print_exc()
    
    def closeEvent(self, event):
        """关闭事件"""
        print("\n🧹 清理测试数据...")
        
        # 清理测试目录
        for test_dir in self.test_dirs:
            if os.path.exists(test_dir):
                try:
                    shutil.rmtree(test_dir)
                    print(f"   🗑️ 删除测试目录: {test_dir}")
                except Exception as e:
                    print(f"   ❌ 删除测试目录失败: {e}")
        
        event.accept()


def main():
    """主函数"""
    print("🚀 启动监控设置UI手动测试...")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    print("📋 测试说明：")
    print("   1. 程序将创建一个测试窗口")
    print("   2. 点击按钮打开监控设置对话框")
    print("   3. 在对话框中测试各种监控功能")
    print("   4. 关闭程序时会自动清理测试数据")
    print()
    
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestMainWindow()
    window.show()
    
    print("✅ 测试窗口已显示，请点击按钮开始测试")
    
    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
