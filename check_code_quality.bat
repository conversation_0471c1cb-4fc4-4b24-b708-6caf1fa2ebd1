@echo off
echo ============================================
echo SmartVault 代码质量检查
echo ============================================
echo.

REM 检查Python是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: Python 未安装或不在PATH中
    pause
    exit /b 1
)

REM 运行代码质量监控工具
echo 🔍 正在检查代码质量...
python tools/code_quality_monitor.py

REM 检查返回码
if errorlevel 2 (
    echo.
    echo ⚠️  警告: 代码质量需要关注
    echo 📋 请查看生成的 code_quality_status.json 报告
    echo 📖 参考 docs/代码质量控制指南.md 进行改进
) else if errorlevel 1 (
    echo.
    echo 🚨 严重: 代码质量达到临界状态
    echo 🔧 必须立即进行重构！
    echo 📋 请查看生成的 code_quality_status.json 报告
    echo 📖 参考 docs/代码质量控制指南.md 进行重构
) else (
    echo.
    echo ✅ 代码质量检查通过
)

echo.
echo 📊 详细报告已保存到: code_quality_status.json
echo 📖 质量控制指南: docs/代码质量控制指南.md
echo.
pause
