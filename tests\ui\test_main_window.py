"""
主窗口UI测试
"""

import os
import sys
import unittest
import tempfile
from unittest.mock import patch, MagicMock

from PySide6.QtWidgets import QApplication
from PySide6.QtTest import QTest
from PySide6.QtCore import Qt

# 确保能够导入 smartvault 模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from smartvault.ui.main_window import MainWindow


class TestMainWindow(unittest.TestCase):
    """主窗口UI测试类"""

    @classmethod
    def setUpClass(cls):
        """测试类前准备"""
        # 创建应用程序实例
        cls.app = QApplication.instance()
        if cls.app is None:
            cls.app = QApplication([])

    def setUp(self):
        """测试前准备"""
        # 创建临时目录
        self.temp_dir = tempfile.TemporaryDirectory()

        # 创建测试文件
        self.test_file_path = os.path.join(self.temp_dir.name, "test_file.txt")
        with open(self.test_file_path, "w", encoding="utf-8") as f:
            f.write("This is a test file.")

        # 模拟文件服务
        self.patcher = patch('smartvault.services.file.FileService')
        self.MockFileService = self.patcher.start()
        self.mock_file_service = MagicMock()
        self.MockFileService.return_value = self.mock_file_service

        # 配置模拟对象
        self.mock_file_service.get_files.return_value = [
            {
                "id": "test_id1",
                "name": "test_file1.txt",
                "original_path": "/path/to/test_file1.txt",
                "library_path": None,
                "size": 100,
                "created_at": "2023-01-01T00:00:00",
                "modified_at": "2023-01-01T00:00:00",
                "added_at": "2023-01-01T00:00:00",
                "entry_type": "link",
                "is_available": 1
            },
            {
                "id": "test_id2",
                "name": "test_file2.txt",
                "original_path": "/path/to/test_file2.txt",
                "library_path": "/path/to/library/test_file2.txt",
                "size": 200,
                "created_at": "2023-01-02T00:00:00",
                "modified_at": "2023-01-02T00:00:00",
                "added_at": "2023-01-02T00:00:00",
                "entry_type": "copy",
                "is_available": 1
            }
        ]

        # 创建主窗口
        self.window = MainWindow()

    def tearDown(self):
        """测试后清理"""
        # 关闭主窗口
        self.window.close()

        # 停止模拟
        self.patcher.stop()

        # 删除临时目录
        self.temp_dir.cleanup()

    def test_window_title(self):
        """测试窗口标题"""
        self.assertEqual(self.window.windowTitle(), "SmartVault")

    def test_window_size(self):
        """测试窗口大小"""
        self.assertGreaterEqual(self.window.width(), 800)
        self.assertGreaterEqual(self.window.height(), 600)

    def test_file_list_view(self):
        """测试文件列表视图"""
        # 验证文件列表视图是否存在
        self.assertIsNotNone(self.window.file_view)

        # 设置文件列表
        test_files = [
            {
                "id": "test_id1",
                "name": "test_file1.txt",
                "original_path": "/path/to/test_file1.txt",
                "library_path": None,
                "size": 100,
                "created_at": "2023-01-01T00:00:00",
                "modified_at": "2023-01-01T00:00:00",
                "added_at": "2023-01-01T00:00:00",
                "entry_type": "link",
                "is_available": 1
            },
            {
                "id": "test_id2",
                "name": "test_file2.txt",
                "original_path": "/path/to/test_file2.txt",
                "library_path": "/path/to/library/test_file2.txt",
                "size": 200,
                "created_at": "2023-01-02T00:00:00",
                "modified_at": "2023-01-02T00:00:00",
                "added_at": "2023-01-02T00:00:00",
                "entry_type": "copy",
                "is_available": 1
            }
        ]
        self.window.file_view.set_files(test_files)

        # 验证文件列表是否包含测试文件
        model = self.window.file_view.model
        self.assertEqual(len(model.files), 2)
        self.assertEqual(model.files[0]["name"], "test_file1.txt")
        self.assertEqual(model.files[1]["name"], "test_file2.txt")

    def test_search_box(self):
        """测试搜索框"""
        # 验证搜索框是否存在
        self.assertIsNotNone(self.window.search_box)

        # 配置搜索服务
        from smartvault.services.search_service import SearchService
        mock_search_service = MagicMock()
        mock_search_service.search_files.return_value = []

        # 保存原始方法
        original_search_service = SearchService

        try:
            # 替换搜索服务
            SearchService = MagicMock(return_value=mock_search_service)

            # 设置搜索关键词
            self.window.search_box.setText("test")

            # 模拟按下回车键
            QTest.keyClick(self.window.search_box, Qt.Key_Return)

            # 验证搜索功能是否被调用
            # 由于我们无法直接访问搜索服务实例，所以只验证搜索框文本
            self.assertEqual(self.window.search_box.text(), "test")
        finally:
            # 恢复原始方法
            SearchService = original_search_service

    def test_navigation_panel(self):
        """测试导航面板"""
        # 验证导航面板是否存在
        self.assertIsNotNone(self.window.navigation_panel)

        # 验证导航面板是否包含文件夹和标签选项卡
        self.assertEqual(self.window.navigation_panel.tab_widget.count(), 2)
        self.assertEqual(self.window.navigation_panel.tab_widget.tabText(0), "文件夹")
        self.assertEqual(self.window.navigation_panel.tab_widget.tabText(1), "标签")


if __name__ == "__main__":
    unittest.main()
