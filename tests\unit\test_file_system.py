"""
文件系统模块单元测试
"""

import os
import unittest
import tempfile
import shutil
from smartvault.data.file_system import FileSystem


class TestFileSystem(unittest.TestCase):
    """文件系统模块测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时目录
        self.temp_dir = tempfile.TemporaryDirectory()
        self.library_path = os.path.join(self.temp_dir.name, "library")
        self.fs = FileSystem(self.library_path)
        
        # 创建测试文件
        self.test_file_path = os.path.join(self.temp_dir.name, "test_file.txt")
        with open(self.test_file_path, "w", encoding="utf-8") as f:
            f.write("This is a test file.")
    
    def tearDown(self):
        """测试后清理"""
        # 删除临时目录
        self.temp_dir.cleanup()
    
    def test_init(self):
        """测试初始化"""
        # 验证文件库目录是否存在
        self.assertTrue(os.path.exists(self.library_path))
        self.assertTrue(os.path.isdir(self.library_path))
    
    def test_file_exists(self):
        """测试文件是否存在"""
        # 验证存在的文件
        self.assertTrue(self.fs.file_exists(self.test_file_path))
        
        # 验证不存在的文件
        non_existent_file = os.path.join(self.temp_dir.name, "non_existent.txt")
        self.assertFalse(self.fs.file_exists(non_existent_file))
    
    def test_get_file_info(self):
        """测试获取文件信息"""
        # 获取文件信息
        file_info = self.fs.get_file_info(self.test_file_path)
        
        # 验证文件信息
        self.assertIsNotNone(file_info)
        self.assertTrue("size" in file_info)
        self.assertTrue("created_at" in file_info)
        self.assertTrue("modified_at" in file_info)
        self.assertTrue("is_file" in file_info)
        self.assertTrue(file_info["is_file"])
        
        # 验证不存在的文件
        non_existent_file = os.path.join(self.temp_dir.name, "non_existent.txt")
        with self.assertRaises(FileNotFoundError):
            self.fs.get_file_info(non_existent_file)
    
    def test_copy_file(self):
        """测试复制文件"""
        # 复制文件
        target_path = os.path.join(self.library_path, "copied_file.txt")
        result = self.fs.copy_file(self.test_file_path, target_path)
        
        # 验证复制结果
        self.assertEqual(result, target_path)
        self.assertTrue(os.path.exists(target_path))
        
        # 验证文件内容
        with open(target_path, "r", encoding="utf-8") as f:
            content = f.read()
        self.assertEqual(content, "This is a test file.")
        
        # 验证覆盖已存在的文件
        with open(target_path, "w", encoding="utf-8") as f:
            f.write("Modified content.")
        
        # 默认不覆盖
        with self.assertRaises(FileExistsError):
            self.fs.copy_file(self.test_file_path, target_path)
        
        # 允许覆盖
        result = self.fs.copy_file(self.test_file_path, target_path, overwrite=True)
        self.assertEqual(result, target_path)
        
        # 验证文件内容已被覆盖
        with open(target_path, "r", encoding="utf-8") as f:
            content = f.read()
        self.assertEqual(content, "This is a test file.")


if __name__ == "__main__":
    unittest.main()
