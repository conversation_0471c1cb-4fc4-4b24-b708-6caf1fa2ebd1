#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
文件监控功能手动测试 - 验证文件监控在实际环境中的工作情况
"""

import os
import sys
import tempfile
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from smartvault.services.file_monitor_service import FileMonitorService
from smartvault.services.file import FileService


def test_file_monitor_integration():
    """测试文件监控集成功能"""
    print("📁 测试文件监控集成功能...")
    
    # 创建服务实例
    monitor_service = FileMonitorService()
    file_service = FileService()
    
    # 创建临时监控目录
    temp_dir = tempfile.mkdtemp()
    monitor_dir = os.path.join(temp_dir, "monitor_test")
    os.makedirs(monitor_dir, exist_ok=True)
    
    print(f"📂 创建监控目录: {monitor_dir}")
    
    # 设置事件回调
    events = []
    def on_file_event(event_type, file_path, monitor_id):
        events.append({
            "type": event_type,
            "path": file_path,
            "monitor_id": monitor_id,
            "filename": os.path.basename(file_path)
        })
        print(f"   🔔 文件事件: {event_type} - {os.path.basename(file_path)}")
    
    monitor_service.set_event_callback(on_file_event)
    
    try:
        # 1. 添加监控文件夹
        print("\n1️⃣ 添加监控文件夹...")
        monitor_id = monitor_service.add_monitor_folder(
            folder_path=monitor_dir,
            entry_mode="link",
            file_patterns=["*.txt", "*.pdf"],  # 只监控txt和pdf文件
            auto_add=True,
            recursive=True
        )
        print(f"   ✅ 监控ID: {monitor_id}")
        
        # 2. 启动监控
        print("\n2️⃣ 启动监控...")
        success = monitor_service.start_monitoring(monitor_id)
        print(f"   ✅ 监控启动: {success}")
        
        # 3. 获取监控状态
        status = monitor_service.get_monitor_status(monitor_id)
        print(f"   📊 监控状态: 活动={status['is_active']}, 路径={status['folder_path']}")
        
        # 4. 创建测试文件
        print("\n3️⃣ 创建测试文件...")
        
        # 创建txt文件（应该被监控）
        txt_file = os.path.join(monitor_dir, "test_document.txt")
        with open(txt_file, 'w', encoding='utf-8') as f:
            f.write("这是一个测试文档")
        print(f"   📄 创建txt文件: {os.path.basename(txt_file)}")
        
        # 等待文件系统事件
        time.sleep(1)
        
        # 创建pdf文件（应该被监控）
        pdf_file = os.path.join(monitor_dir, "test_report.pdf")
        with open(pdf_file, 'wb') as f:
            f.write(b"PDF content")
        print(f"   📄 创建pdf文件: {os.path.basename(pdf_file)}")
        
        # 等待文件系统事件
        time.sleep(1)
        
        # 创建jpg文件（应该被忽略）
        jpg_file = os.path.join(monitor_dir, "test_image.jpg")
        with open(jpg_file, 'wb') as f:
            f.write(b"Image content")
        print(f"   🖼️ 创建jpg文件: {os.path.basename(jpg_file)} (应该被忽略)")
        
        # 等待文件系统事件
        time.sleep(1)
        
        # 5. 检查监控事件
        print("\n4️⃣ 检查监控事件...")
        print(f"   📋 总共检测到 {len(events)} 个事件:")
        for event in events:
            print(f"      • {event['type']}: {event['filename']}")
        
        # 6. 检查自动添加的文件
        print("\n5️⃣ 检查自动添加的文件...")
        files = file_service.get_files()
        added_files = [f for f in files if f["name"] in ["test_document.txt", "test_report.pdf"]]
        print(f"   📚 自动添加到库的文件: {len(added_files)} 个")
        for file_info in added_files:
            print(f"      • {file_info['name']} ({file_info['entry_type']})")
        
        # 7. 测试子文件夹监控
        print("\n6️⃣ 测试子文件夹监控...")
        sub_dir = os.path.join(monitor_dir, "subfolder")
        os.makedirs(sub_dir, exist_ok=True)
        
        sub_file = os.path.join(sub_dir, "sub_document.txt")
        with open(sub_file, 'w', encoding='utf-8') as f:
            f.write("子文件夹中的文档")
        print(f"   📁 在子文件夹创建文件: {os.path.basename(sub_file)}")
        
        # 等待文件系统事件
        time.sleep(1)
        
        # 8. 获取监控统计
        print("\n7️⃣ 获取监控统计...")
        stats = monitor_service.get_monitor_statistics()
        print(f"   📊 监控统计:")
        print(f"      • 总监控数: {stats['total_monitors']}")
        print(f"      • 活动监控数: {stats['active_monitors']}")
        print(f"      • 运行监控数: {stats['running_monitors']}")
        print(f"      • 处理文件数: {stats['total_files_added']}")
        print(f"      • 错误数: {stats['error_count']}")
        
        # 9. 停止监控
        print("\n8️⃣ 停止监控...")
        success = monitor_service.stop_monitoring(monitor_id)
        print(f"   ✅ 监控停止: {success}")
        
        # 10. 最终事件统计
        print("\n9️⃣ 最终事件统计...")
        print(f"   📋 总共检测到 {len(events)} 个文件事件")
        
        # 按文件类型分组
        txt_events = [e for e in events if e['filename'].endswith('.txt')]
        pdf_events = [e for e in events if e['filename'].endswith('.pdf')]
        jpg_events = [e for e in events if e['filename'].endswith('.jpg')]
        
        print(f"   📄 txt文件事件: {len(txt_events)} 个")
        print(f"   📄 pdf文件事件: {len(pdf_events)} 个")
        print(f"   🖼️ jpg文件事件: {len(jpg_events)} 个 (应该为0)")
        
        # 验证结果
        if len(txt_events) > 0 and len(pdf_events) > 0 and len(jpg_events) == 0:
            print("\n🎉 文件监控功能测试成功！")
            print("   ✅ 正确监控了txt和pdf文件")
            print("   ✅ 正确忽略了jpg文件")
            print("   ✅ 自动添加功能正常工作")
            print("   ✅ 递归监控功能正常工作")
        else:
            print("\n❌ 文件监控功能测试失败")
            print(f"   txt事件: {len(txt_events)}, pdf事件: {len(pdf_events)}, jpg事件: {len(jpg_events)}")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理
        monitor_service.stop_all_monitoring()
        
        # 清理临时文件
        import shutil
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
            print(f"\n🧹 已清理临时目录: {temp_dir}")


if __name__ == "__main__":
    test_file_monitor_integration()
