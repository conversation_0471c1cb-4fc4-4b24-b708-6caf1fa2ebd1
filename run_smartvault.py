#!/usr/bin/env python3
"""
SmartVault 启动脚本
自动检查依赖并启动SmartVault应用程序
"""

import subprocess
import sys
import os

def check_dependencies():
    """检查关键依赖是否已安装"""
    required_packages = ["PySide6", "schedule", "Pillow", "sqlalchemy"]
    missing_packages = []

    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)

    return missing_packages

def install_dependencies():
    """运行依赖安装脚本"""
    try:
        print("检测到缺失依赖，正在自动安装...")
        result = subprocess.run([sys.executable, "install_dependencies.py"],
                              capture_output=True, text=True, check=True)
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"[ERROR] 依赖安装失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def start_smartvault():
    """启动SmartVault应用程序"""
    try:
        print("启动 SmartVault...")
        # 直接运行，不捕获输出，让用户看到实时日志
        subprocess.run([sys.executable, "-m", "smartvault.main"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"[ERROR] SmartVault 启动失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n[INTERRUPTED] SmartVault 被用户中断")
        return True
    return True

def main():
    """主函数"""
    print("SmartVault 智能文件管理系统")
    print("=" * 40)

    # 检查依赖
    missing = check_dependencies()
    if missing:
        print(f"[WARNING] 检测到缺失依赖: {', '.join(missing)}")

        # 询问是否自动安装
        response = input("是否自动安装缺失的依赖？(y/n): ").lower().strip()
        if response in ['y', 'yes', '是', '']:
            if not install_dependencies():
                print("[ERROR] 依赖安装失败，无法启动SmartVault")
                return False
        else:
            print("[ERROR] 缺少必要依赖，无法启动SmartVault")
            print("请运行: python install_dependencies.py")
            return False

    # 启动SmartVault
    return start_smartvault()

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            input("\n按回车键退出...")
            sys.exit(1)
    except Exception as e:
        print(f"\n[ERROR] 启动过程中发生错误: {e}")
        input("\n按回车键退出...")
        sys.exit(1)
