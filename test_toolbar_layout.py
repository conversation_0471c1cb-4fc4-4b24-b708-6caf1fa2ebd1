#!/usr/bin/env python3
"""
测试工具栏布局改进效果
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QHBoxLayout, QPushButton, QLabel, QProgressBar
from PySide6.QtCore import Qt, QTimer

from smartvault.ui.themes import theme_manager


class TestToolbarLayoutWindow(QMainWindow):
    """测试工具栏布局窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("工具栏布局改进测试")
        self.setGeometry(100, 100, 1200, 500)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 添加说明标签
        info_label = QLabel("工具栏布局改进效果测试")
        info_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(info_label)
        
        # 创建主题切换按钮
        theme_layout = QHBoxLayout()
        
        light_btn = QPushButton("浅色主题")
        light_btn.clicked.connect(lambda: self.switch_theme("light"))
        theme_layout.addWidget(light_btn)
        
        dark_btn = QPushButton("深色主题")
        dark_btn.clicked.connect(lambda: self.switch_theme("dark"))
        theme_layout.addWidget(dark_btn)
        
        blue_btn = QPushButton("蓝色主题")
        blue_btn.clicked.connect(lambda: self.switch_theme("blue"))
        theme_layout.addWidget(blue_btn)
        
        green_btn = QPushButton("绿色主题")
        green_btn.clicked.connect(lambda: self.switch_theme("green"))
        theme_layout.addWidget(green_btn)
        
        theme_layout.addStretch()
        layout.addLayout(theme_layout)
        
        # 创建模拟工具栏
        toolbar_layout = QHBoxLayout()
        
        # 左侧按钮组
        left_group = QHBoxLayout()
        
        # 文件夹监控按钮
        monitor_btn = QPushButton("文件夹监控")
        monitor_btn.setMaximumWidth(100)
        monitor_btn.setMinimumWidth(80)
        monitor_btn.setStyleSheet(self.get_button_style("#4CAF50", "#45a049", "#388e3c"))
        left_group.addWidget(monitor_btn)
        
        # 剪贴板查重按钮
        clipboard_btn = QPushButton("剪贴板查重")
        clipboard_btn.setMaximumWidth(100)
        clipboard_btn.setMinimumWidth(80)
        clipboard_btn.setStyleSheet(self.get_button_style("#9e9e9e", "#757575", "#616161"))
        left_group.addWidget(clipboard_btn)
        
        # 分隔符
        separator1 = QLabel("|")
        separator1.setStyleSheet("color: #ccc; margin: 0 5px;")
        left_group.addWidget(separator1)
        
        # 添加文件按钮
        add_file_btn = QPushButton("添加文件")
        add_file_btn.setMaximumWidth(100)
        add_file_btn.setMinimumWidth(80)
        add_file_btn.setStyleSheet(self.get_button_style("#2196f3", "#1976d2", "#1565c0"))
        left_group.addWidget(add_file_btn)
        
        # 添加文件夹按钮
        add_folder_btn = QPushButton("添加文件夹")
        add_folder_btn.setMaximumWidth(100)
        add_folder_btn.setMinimumWidth(80)
        add_folder_btn.setStyleSheet(self.get_button_style("#ff9800", "#f57c00", "#ef6c00"))
        left_group.addWidget(add_folder_btn)
        
        toolbar_layout.addLayout(left_group)
        
        # 弹性空间
        toolbar_layout.addStretch()
        
        # 右侧组件组
        right_group = QHBoxLayout()
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimumWidth(150)
        self.progress_bar.setMaximumWidth(200)
        self.progress_bar.setMinimumHeight(25)
        self.progress_bar.setMaximumHeight(25)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #ccc;
                border-radius: 3px;
                text-align: center;
                background-color: #f8f8f8;
                font-size: 11px;
            }
            QProgressBar::chunk {
                background-color: #0078d4;
                border-radius: 2px;
                margin: 1px;
            }
        """)
        self.progress_bar.setValue(0)
        self.progress_bar.setFormat("就绪")
        right_group.addWidget(self.progress_bar)
        
        # 分隔符
        separator2 = QLabel("|")
        separator2.setStyleSheet("color: #ccc; margin: 0 5px;")
        right_group.addWidget(separator2)
        
        # 帮助按钮
        help_btn = QPushButton("帮助")
        help_btn.setMaximumWidth(100)
        help_btn.setMinimumWidth(80)
        help_btn.setStyleSheet(self.get_button_style("#607d8b", "#455a64", "#37474f"))
        help_btn.clicked.connect(self.test_progress)
        right_group.addWidget(help_btn)
        
        # 退出按钮
        exit_btn = QPushButton("退出")
        exit_btn.setMaximumWidth(100)
        exit_btn.setMinimumWidth(80)
        exit_btn.setStyleSheet(self.get_button_style("#f44336", "#d32f2f", "#c62828"))
        exit_btn.clicked.connect(self.close)
        right_group.addWidget(exit_btn)
        
        toolbar_layout.addLayout(right_group)
        layout.addLayout(toolbar_layout)
        
        # 添加说明文本
        desc_label = QLabel("""
工具栏布局改进说明：

左侧区域（功能按钮）：
• 文件夹监控：绿色 - 监控功能
• 剪贴板查重：灰色 - 查重功能
• 添加文件：蓝色 - 文件操作
• 添加文件夹：橙色 - 文件夹操作

右侧区域（系统功能）：
• 进度条：显示当前操作进度，位置更合理
• 帮助：蓝灰色 - 系统帮助
• 退出：红色 - 退出程序

改进效果：
• 进度条移到右侧，不再突兀
• 所有按钮高度一致，视觉统一
• 功能分组更清晰，左侧操作，右侧系统
        """)
        desc_label.setStyleSheet("margin: 20px; padding: 10px; background-color: #f5f5f5; border-radius: 5px;")
        layout.addWidget(desc_label)
        
        layout.addStretch()
        
        # 应用默认主题
        theme_manager.apply_theme("light")
    
    def get_button_style(self, bg_color, hover_color, pressed_color):
        """获取按钮样式"""
        return f"""
            QPushButton {{
                background-color: {bg_color} !important;
                color: white !important;
                border: 1px solid {hover_color} !important;
                border-radius: 3px !important;
                padding: 4px 8px !important;
                font-size: 12px !important;
                font-weight: normal !important;
            }}
            QPushButton:hover {{
                background-color: {hover_color} !important;
                border-color: {pressed_color} !important;
            }}
            QPushButton:pressed {{
                background-color: {pressed_color} !important;
            }}
        """
    
    def switch_theme(self, theme_name):
        """切换主题"""
        theme_manager.set_theme(theme_name)
        print(f"切换到{theme_name}主题")
    
    def test_progress(self):
        """测试进度条效果"""
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)
        self.progress_bar.setFormat("处理中...")
        
        # 模拟进度更新
        self.timer = QTimer()
        self.progress_value = 0
        self.timer.timeout.connect(self.update_progress)
        self.timer.start(50)
    
    def update_progress(self):
        """更新进度"""
        self.progress_value += 2
        self.progress_bar.setValue(self.progress_value)
        
        if self.progress_value >= 100:
            self.timer.stop()
            self.progress_bar.setFormat("完成")
            # 3秒后重置
            QTimer.singleShot(3000, self.reset_progress)
    
    def reset_progress(self):
        """重置进度条"""
        self.progress_bar.setValue(0)
        self.progress_bar.setFormat("就绪")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("SmartVault Toolbar Layout Test")
    app.setApplicationVersion("1.0")
    
    # 创建测试窗口
    window = TestToolbarLayoutWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
