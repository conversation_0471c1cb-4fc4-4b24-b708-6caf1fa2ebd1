# SmartVault 用户术语表

__version__ = "1.0.0"
__author__ = "Mojianghu"
__release_date__ = "20230601"

> 本文档定义了SmartVault系统中使用的用户术语，旨在统一界面、文档和代码中的术语使用，提高用户体验的一致性，强化"用户干预极简化"的核心价值。本版本更新了"智能文件库"概念相关术语，替代原"仓库"概念。

## 1. 核心术语

| 原术语 | 新术语 | 说明 |
|--------|--------|------|
| 管理文件 | 添加到智能文件库 | "智能文件库"概念更清晰地表达系统职责边界 |
| 整理区 | 智能文件库 | "智能文件库"概念更符合用户心智模型，易于理解 |
| 库内文件 | 智能文件库内文件 | 与"智能文件库"概念保持一致 |
| 外部文件 | 智能文件库外文件 | 与"智能文件库"概念保持一致 |
| 文件链接 | 链接到智能文件库 | 更清晰地表达文件与智能文件库的关系 |
| 记住设备文件 | 链接外部设备文件到智能文件库 | 更清晰地表达操作目的 |
| 复制到电脑 | 复制到智能文件库 | 与"智能文件库"概念保持一致 |
| 移动文件 | 移动到智能文件库 | 与"智能文件库"概念保持一致 |
| 自动索引 | 自动添加到智能文件库 | 更清晰地表达功能目的 |
| 索引目录 | 监控文件夹 | 更清晰地表达功能目的 |
| 记录 | 智能文件库记录 | 与"智能文件库"概念保持一致 |
| 文件信息 | 文件信息 | 保持不变，已足够清晰 |
| 重复检查 | 重复检查 | 保持不变，已足够清晰 |
| 自动整理 | 自动整理 | 保持不变，已足够清晰 |
| 设备连接 | 设备连接 | 保持不变，已足够清晰 |
| 文件状态 | 文件状态 | 保持不变，已足够清晰 |
| 文件变更 | 文件变更 | 保持不变，已足够清晰 |
| 快速更新 | 快速更新 | 保持不变，已足够清晰 |
| 文件处理 | 文件处理 | 保持不变，已足够清晰 |
| 智能分类 | 智能分类 | 保持不变，已足够清晰 |
| 标签管理 | 标签管理 | 保持不变，已足够清晰 |
| 文件搜索 | 智能文件库搜索 | 与"智能文件库"概念保持一致 |
| 内容识别 | 内容识别 | 保持不变，已足够清晰 |
| 批量操作 | 批量操作 | 保持不变，已足够清晰 |
| 智能文件夹 | 智能文件夹 | 保持不变，已足够清晰 |

## 2. 界面术语

| 原术语 | 新术语 | 说明 |
|--------|--------|------|
| 添加到整理区 | 添加到智能文件库 | 与"智能文件库"概念保持一致 |
| 整理文件夹 | 批量添加到智能文件库 | 更清晰地表达操作目的 |
| 文件记录 | 智能文件库记录 | 与"智能文件库"概念保持一致 |
| 获取文件信息 | 获取文件信息 | 保持不变，已足够清晰 |
| 查找重复文件 | 查找重复文件 | 保持不变，已足够清晰 |
| 自动整理设置 | 监控文件夹设置 | 更清晰地表达功能目的 |
| 添加标签 | 添加标签 | 保持不变，已足够清晰 |
| 搜索 | 搜索智能文件库 | 与"智能文件库"概念保持一致 |
| 详细搜索 | 详细搜索 | 保持不变，已足够清晰 |
| 文件操作 | 文件操作 | 保持不变，已足够清晰 |
| 设置 | 设置 | 保持不变，已足够清晰 |
| 帮助 | 帮助 | 保持不变，已足够清晰 |
| 退出 | 退出 | 保持不变，已足够清晰 |
| 关于 | 关于 | 保持不变，已足够清晰 |
| 最近添加 | 最近添加到智能文件库 | 与"智能文件库"概念保持一致 |
| 文件详情 | 文件详情 | 保持不变，已足够清晰 |
| 标签编辑 | 标签编辑 | 保持不变，已足够清晰 |
| 批量操作 | 批量操作 | 保持不变，已足够清晰 |
| 文件统计 | 智能文件库统计 | 与"智能文件库"概念保持一致 |
| 备份与恢复 | 智能文件库备份与恢复 | 与"智能文件库"概念保持一致 |
| - | 智能文件库状态 | 新增：显示智能文件库文件数量和存储使用情况 |
| - | 搜索电脑 | 新增：扩展搜索范围到智能文件库外文件 |
| - | 入库方式 | 新增：选择文件添加到智能文件库的方式 |

## 3. 操作术语

| 原术语 | 新术语 | 说明 |
|--------|--------|------|
| 添加文件 | 添加到智能文件库 | 与"智能文件库"概念保持一致 |
| 添加文件夹 | 批量添加到智能文件库 | 与界面术语保持一致 |
| 整理 | 整理 | 保持不变，已足够清晰 |
| 记录 | 智能文件库记录 | 与"智能文件库"概念保持一致 |
| 查找重复 | 查找重复 | 保持不变，已足够清晰 |
| 添加标签 | 添加标签 | 保持不变，已足够清晰 |
| 移除标签 | 移除标签 | 保持不变，已足够清晰 |
| 搜索 | 搜索智能文件库 | 与"智能文件库"概念保持一致 |
| 详细搜索 | 详细搜索 | 保持不变，已足够清晰 |
| 创建备份 | 创建智能文件库备份 | 与"智能文件库"概念保持一致 |
| 恢复备份 | 恢复智能文件库备份 | 与"智能文件库"概念保持一致 |
| 优化整理区 | 优化智能文件库 | 与"智能文件库"概念保持一致 |
| 清理临时文件 | 清理临时文件 | 保持不变，已足够清晰 |
| 更新文件记录 | 更新智能文件库记录 | 与"智能文件库"概念保持一致 |
| 导出 | 导出 | 保持不变，已足够清晰 |
| 导入设置 | 导入设置 | 保持不变，已足够清晰 |
| 导出设置 | 导出设置 | 保持不变，已足够清晰 |
| 重置设置 | 重置设置 | 保持不变，已足够清晰 |
| - | 链接到智能文件库 | 新增：保持文件在原位置，在智能文件库中创建引用 |
| - | 复制到智能文件库 | 新增：在智能文件库存储位置创建文件副本 |
| - | 移动到智能文件库 | 新增：将文件移动到智能文件库存储位置 |
| - | 从智能文件库移除 | 新增：将文件从智能文件库中移除但不删除物理文件 |
| - | 监控文件夹 | 新增：设置自动将新文件添加到智能文件库的文件夹 |

## 4. 消息术语

| 原术语 | 新术语 | 说明 |
|--------|--------|------|
| 已添加到整理区 | 已添加到智能文件库 | 与"智能文件库"概念保持一致 |
| 添加失败 | 添加到智能文件库失败 | 与"智能文件库"概念保持一致 |
| 整理完成 | 整理完成 | 保持不变，已足够清晰 |
| 记录已更新 | 智能文件库记录已更新 | 与"智能文件库"概念保持一致 |
| 重复检查完成 | 重复检查完成 | 保持不变，已足够清晰 |
| 标签已添加 | 标签已添加 | 保持不变，已足够清晰 |
| 标签已移除 | 标签已移除 | 保持不变，已足够清晰 |
| 搜索完成 | 搜索完成 | 保持不变，已足够清晰 |
| 备份已创建 | 智能文件库备份已创建 | 与"智能文件库"概念保持一致 |
| 备份已恢复 | 智能文件库备份已恢复 | 与"智能文件库"概念保持一致 |
| 整理区已优化 | 智能文件库已优化 | 与"智能文件库"概念保持一致 |
| 临时文件已清理 | 临时文件已清理 | 保持不变，已足够清晰 |
| 文件记录已更新 | 智能文件库记录已更新 | 与"智能文件库"概念保持一致 |
| 设置已保存 | 设置已保存 | 保持不变，已足够清晰 |
| 设置已重置 | 设置已重置 | 保持不变，已足够清晰 |
| - | 已链接到智能文件库 | 新增：文件已通过链接方式添加到智能文件库 |
| - | 已复制到智能文件库 | 新增：文件已通过复制方式添加到智能文件库 |
| - | 已移动到智能文件库 | 新增：文件已通过移动方式添加到智能文件库 |
| - | 已从智能文件库移除 | 新增：文件已从智能文件库中移除 |
| - | 智能文件库中已存在此文件 | 新增：查重提示消息 |
| - | 监控文件夹已设置 | 新增：监控文件夹设置成功消息 |

## 5. 实施指南

1. **代码更新**：
   - 更新UI文本和提示信息，统一使用"智能文件库"相关术语
   - 更新函数和方法名称（保持向后兼容）
   - 更新注释和文档字符串
   - 修改文件管理模块，实现三种入库方式（链接/复制/移动到智能文件库）
   - 调整搜索服务，默认只搜索智能文件库内文件

2. **文档更新**：
   - 更新所有用户文档，统一使用"智能文件库"相关术语
   - 更新开发文档，明确"智能文件库"概念的技术实现
   - 更新帮助信息，解释"智能文件库"概念及相关操作
   - 创建新用户引导，介绍"智能文件库"概念的优势

3. **界面更新**：
   - 更新应用标题和描述，突出"智能文件库"概念
   - 更新菜单项和按钮文本，使用新术语
   - 更新对话框和表单，提供清晰的入库选项
   - 添加智能文件库状态指示器，显示文件数量和存储使用情况
   - 实现文件状态图标，区分不同入库方式和文件位置

4. **测试验证**：
   - 确保术语在整个应用中一致使用
   - 验证所有功能在术语更新后仍正常工作
   - 测试三种入库方式的功能正确性
   - 验证搜索功能在默认和扩展模式下的正确性
   - 收集用户反馈，评估"智能文件库"概念的接受度

5. **数据迁移**：
   - 为现有数据添加适当的入库状态标记
   - 更新索引，确保只包含智能文件库内文件
   - 提供数据迁移向导，帮助用户理解变化

## 6. 应用指南

1. **界面一致性**：所有用户界面元素（菜单、按钮、标签等）必须使用新术语。

2. **文档一致性**：所有面向用户的文档必须使用新术语，技术文档可以在括号中注明旧术语。

3. **代码注释**：代码中的注释应尽量使用新术语，必要时可以注明旧术语。

4. **变量命名**：代码中的变量名可以继续使用旧术语，以保持代码的一致性和可维护性。

5. **术语更新**：当需要添加新术语或修改现有术语时，必须更新本文档并通知所有开发人员。

## 7. 术语使用示例

### 正确示例

- "请点击'添加到智能文件库'按钮将文件添加到智能文件库。"
- "您可以在设置中配置监控文件夹。"
- "使用标签分类功能可以更好地组织您的文件。"
- "查找重复文件功能可以帮助您节省存储空间。"
- "您可以选择'链接到智能文件库'、'复制到智能文件库'或'移动到智能文件库'三种方式添加文件。"

### 错误示例

- ~~"请点击'导入文件'按钮将文件添加到数据库。"~~
- ~~"您可以在配置中设置监控目录。"~~
- ~~"使用标签层次结构功能可以更好地组织您的文件。"~~
- ~~"查重功能可以帮助您节省存储空间。"~~
- ~~"请点击'添加到整理区'按钮将文件添加到整理区。"~~
- ~~"请点击'添加到仓库'按钮将文件添加到仓库。"~~

## 8. 术语更新历史

| 日期 | 版本 | 更新内容 |
|------|------|---------|
| 20250601 | 1.0.0 | 初始版本，定义基本术语 |
| 20250610 | 2.0.0 | 更新"仓库"概念相关术语 |
| 20250610 | 2.1.0 | 整合旧版术语表，完善应用指南和使用示例 |
| 20250601 | 3.0.0 | 更新"智能文件库"概念相关术语，替代原"仓库"概念 |
