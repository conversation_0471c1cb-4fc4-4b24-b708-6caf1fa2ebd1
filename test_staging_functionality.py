#!/usr/bin/env python3
"""
测试中转文件夹功能
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

from smartvault.services.file import FileService
from smartvault.utils.config import load_config

def test_staging_functionality():
    """测试中转文件夹功能"""
    print("🧪 开始测试中转文件夹功能...")
    
    try:
        # 初始化文件服务
        file_service = FileService()
        
        # 获取一些文件进行测试
        files = file_service.get_files(limit=5)
        if not files:
            print("❌ 没有找到文件进行测试")
            return False
        
        test_file = files[0]
        file_id = test_file['id']
        file_name = test_file['name']
        
        print(f"📁 测试文件: {file_name} (ID: {file_id})")
        
        # 检查初始状态
        initial_status = test_file.get('staging_status', 'normal')
        print(f"🔍 初始状态: {initial_status}")
        
        # 测试移动到中转状态
        print("📥 测试移动到中转状态...")
        success = file_service.move_to_staging(file_id)
        if success:
            print("✅ 成功移动到中转状态")
            
            # 验证状态
            updated_file = file_service.get_file_by_id(file_id)
            if updated_file and updated_file.get('staging_status') == 'staging':
                print("✅ 状态验证成功: staging")
            else:
                print("❌ 状态验证失败")
                return False
        else:
            print("❌ 移动到中转状态失败")
            return False
        
        # 测试从中转状态移出
        print("📤 测试从中转状态移出...")
        success = file_service.move_from_staging(file_id)
        if success:
            print("✅ 成功从中转状态移出")
            
            # 验证状态
            updated_file = file_service.get_file_by_id(file_id)
            if updated_file and updated_file.get('staging_status') == 'normal':
                print("✅ 状态验证成功: normal")
            else:
                print("❌ 状态验证失败")
                return False
        else:
            print("❌ 从中转状态移出失败")
            return False
        
        # 测试切换状态功能
        print("🔄 测试切换状态功能...")
        success = file_service.toggle_staging_status(file_id)
        if success:
            updated_file = file_service.get_file_by_id(file_id)
            new_status = updated_file.get('staging_status', 'normal')
            print(f"✅ 切换成功，新状态: {new_status}")
            
            # 再次切换回来
            file_service.toggle_staging_status(file_id)
            final_file = file_service.get_file_by_id(file_id)
            final_status = final_file.get('staging_status', 'normal')
            print(f"✅ 再次切换成功，最终状态: {final_status}")
        else:
            print("❌ 切换状态失败")
            return False
        
        # 测试批量操作
        print("📦 测试批量操作...")
        if len(files) >= 3:
            test_file_ids = [f['id'] for f in files[:3]]
            
            # 批量移入中转
            result = file_service.batch_move_to_staging(test_file_ids)
            success_count = len(result['success'])
            failed_count = len(result['failed'])
            print(f"📥 批量移入结果: 成功 {success_count}, 失败 {failed_count}")
            
            # 验证中转文件夹筛选
            staging_files = file_service.get_files(folder_filter_type="staging")
            print(f"🔍 中转文件夹中的文件数量: {len(staging_files)}")
            
            # 批量移出中转
            result = file_service.batch_move_from_staging(test_file_ids)
            success_count = len(result['success'])
            failed_count = len(result['failed'])
            print(f"📤 批量移出结果: 成功 {success_count}, 失败 {failed_count}")
            
            # 再次验证中转文件夹筛选
            staging_files = file_service.get_files(folder_filter_type="staging")
            print(f"🔍 清理后中转文件夹中的文件数量: {len(staging_files)}")
        
        print("🎉 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_staging_functionality()
    sys.exit(0 if success else 1)
