# 🚨 SmartVault 开发提醒清单

## 📊 当前状态 (2025年1月27日更新)

- **core.py 行数**: 2,092 行 🟢 GOOD ✅ 已优化
- **风险等级**: 良好状态 (已从WARNING降级)
- **已完成拆分**: 2个模块 (备份管理 + 剪贴板功能)
- **剩余可拆分**: 560行 (9个非核心方法) - 可选
- **强制拆分阈值**: 3,000 行 (当前距离阈值还有908行)

## 🔥 每次开发必读

### ⚠️ 核心约束

1. **禁止在 core.py 添加非核心功能**
2. **新方法超过50行必须拆分**
3. **复杂逻辑必须抽取为独立方法**
4. **每次开发前运行质量检查**

### 🚫 禁止添加到 core.py 的功能

- ❌ 备份相关 (`backup_`, `create_backup`, `restore_backup`)
- ❌ 剪贴板功能 (`clipboard_`, `on_clipboard_`)
- ❌ 拖拽处理 (`drag_`, `drop_`, `dragEnterEvent`)
- ❌ 监控管理 (`monitor_`, `start_monitoring`)
- ❌ 搜索增强 (`search_`, `advanced_search`)
- ❌ 标签操作 (`tag_`, `on_tag_`)
- ❌ 设备管理 (`device_`, `usb_`, `volume_`)
- ❌ 浮动窗口 (`floating_`, `popup_`)

### ✅ 允许在 core.py 的核心功能

- ✅ 窗口初始化 (`__init__`, `init_ui`)
- ✅ 窗口事件 (`closeEvent`, `keyPressEvent`)
- ✅ 配置管理 (`apply_user_config`, `restore_window_geometry`)
- ✅ 库切换 (`on_library_changed`)
- ✅ 状态显示 (`show_status_message`)
- ✅ 数据加载 (`load_initial_data`)

## 🔧 开发流程

### 1. 开发前检查
```bash
# 运行质量检查
python tools/code_quality_monitor.py
# 或者
check_code_quality.bat
```

### 2. 功能归属判断
```
新功能是窗口核心功能吗？
├─ 是 → 评估方法长度
│   ├─ <50行 → 可以添加到 core.py
│   └─ ≥50行 → 拆分为多个方法
└─ 否 → 创建独立模块
    ├─ backup_manager.py
    ├─ clipboard_handler.py
    ├─ drag_drop_handler.py
    ├─ monitor_manager.py
    └─ search_handler.py
```

### 3. 代码审查
- [ ] 新增代码 < 50行
- [ ] 不包含禁止关键词
- [ ] 方法职责单一
- [ ] 质量检查通过

## 📈 可移动的非核心方法 (754行)

### 备份相关 (预计200行)
- `start_backup_service`
- `stop_backup_service`
- `setup_backup_status_display`
- `update_backup_status_display`
- `create_manual_backup`

### 剪贴板相关 (预计150行)
- `start_clipboard_monitor_if_enabled`
- `setup_clipboard_float_mode`
- `on_clipboard_duplicate_found`
- `on_clipboard_open_file`

### 拖拽相关 (预计100行)
- `dragEnterEvent`
- `dropEvent`
- `_handle_dropped_files`

### 监控相关 (预计200行)
- `start_configured_monitors`
- `toggle_all_monitors`
- `on_monitor_event`
- `_update_monitor_stats`

### 其他功能 (预计104行)
- 搜索增强功能
- 标签操作功能
- 设备管理功能

## 🎯 重构优先级

### 高优先级 (立即执行)
1. **备份功能模块化** → `backup_manager.py`
2. **剪贴板功能模块化** → `clipboard_handler.py`
3. **拖拽功能模块化** → `drag_drop_handler.py`

### 中优先级 (后续执行)
4. **监控功能模块化** → `monitor_manager.py`
5. **搜索功能模块化** → `search_handler.py`

### 低优先级 (可选)
6. **工具方法整理** → `utils/window_utils.py`

## 🚨 紧急情况处理

### 如果 core.py 超过 3,000 行
1. **立即停止开发**
2. **强制执行重构**
3. **移动至少500行代码到其他模块**
4. **重新运行质量检查**

### 如果新功能必须添加到 core.py
1. **先移动等量的非核心代码**
2. **确保总行数不增加**
3. **记录技术债务**

## 📞 寻求帮助

### 新对话开始时的提醒词
```
🔍 SmartVault 代码质量提醒 (已优化):
- core.py 当前 2,092 行 (🟢 GOOD状态) ✅ 已优化
- 已完成拆分: 备份管理 + 剪贴板功能模块
- 新功能仍需在其他模块实现
- 复杂逻辑必须抽取为独立方法
- 剩余可选拆分: 560行 (9个方法)
- 请先运行: python tools/code_quality_monitor.py
- 参考: core_py_非核心方法拆分分析报告.md
```

### 功能开发检查清单
```
❓ 开发前检查:
1. 这是核心窗口功能吗？
2. 方法长度是否合理？
3. 是否可以在其他模块实现？
4. 是否需要拆分复杂逻辑？
5. 质量检查是否通过？
```

---

**记住**: 这些约束是为了项目的长期健康，不是为了限制创新。遵循"功能优先，适度优化"的原则，在保证功能交付的前提下，维护合理的代码质量。
