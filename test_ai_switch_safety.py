#!/usr/bin/env python3
"""
AI开关安全性测试
测试AI功能开关对系统稳定性和现有功能的影响
"""

import sys
import os
import tempfile
import shutil
import uuid

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ai_disabled_safety():
    """测试AI功能关闭时的系统安全性"""
    print("🔍 测试AI功能关闭时的系统安全性...")

    try:
        from smartvault.utils.config import save_ai_status, get_ai_status
        from smartvault.services.file import FileService
        from smartvault.services.tag_service import TagService

        # 确保AI功能关闭
        save_ai_status(False)
        ai_status = get_ai_status()
        print(f"AI状态: {ai_status} (应该为False)")

        if ai_status:
            print("❌ AI状态设置失败")
            return False

        # 创建测试文件
        test_dir = tempfile.mkdtemp(prefix="smartvault_ai_disabled_")
        unique_id = str(uuid.uuid4())[:8]

        test_files = [
            (f"config_{unique_id}.json", '{"name": "test", "version": "1.0"}'),
            (f"script_{unique_id}.py", "print('Hello World')"),
            (f"document_{unique_id}.txt", "这是一个测试文档"),
        ]

        created_files = []
        for filename, content in test_files:
            file_path = os.path.join(test_dir, filename)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            created_files.append(file_path)

        try:
            # 创建服务
            file_service = FileService()
            tag_service = TagService()

            print(f"创建了 {len(created_files)} 个测试文件")

            # 测试基本文件操作（不应该受AI影响）
            results = []
            for test_file in created_files:
                try:
                    filename = os.path.basename(test_file)
                    print(f"\n添加文件: {filename}")

                    # 添加文件
                    file_id = file_service.add_file(test_file, mode="link")
                    print(f"✅ 文件添加成功: {file_id}")

                    # 检查文件是否正确添加到数据库
                    file_info = file_service.get_file_by_id(file_id)
                    if file_info:
                        print(f"✅ 文件信息正确: {file_info['name']}")
                    else:
                        print("❌ 文件信息获取失败")
                        results.append(False)
                        continue

                    # 检查是否有AI标签被错误应用
                    tags = tag_service.get_file_tags(file_id)
                    ai_tags = [tag for tag in tags if tag['name'] in ['配置文件', 'JSON配置', '代码', 'Python', '文档']]

                    if ai_tags:
                        print(f"⚠️ 发现AI标签被错误应用: {[tag['name'] for tag in ai_tags]}")
                        results.append(False)
                    else:
                        print("✅ 未应用AI标签（符合预期）")
                        results.append(True)

                    # 测试手动添加标签（基本功能）
                    manual_tag_id = tag_service.create_tag(f"手动标签_{unique_id}")
                    tag_service.add_tag_to_file(file_id, manual_tag_id)

                    # 验证手动标签
                    updated_tags = tag_service.get_file_tags(file_id)
                    manual_tags = [tag for tag in updated_tags if f"手动标签_{unique_id}" in tag['name']]

                    if manual_tags:
                        print("✅ 手动标签功能正常")
                    else:
                        print("❌ 手动标签功能异常")
                        results.append(False)

                except Exception as e:
                    print(f"❌ 文件操作失败: {e}")
                    results.append(False)

            return all(results) and len(results) > 0

        finally:
            # 清理测试文件
            shutil.rmtree(test_dir, ignore_errors=True)

    except Exception as e:
        print(f"❌ AI关闭安全性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_enabled_functionality():
    """测试AI功能开启时的正常工作"""
    print("\n🔍 测试AI功能开启时的正常工作...")

    try:
        from smartvault.utils.config import save_ai_status, get_ai_status
        from smartvault.services.file import FileService
        from smartvault.services.tag_service import TagService
        from smartvault.services.ai.ai_manager import AIManager

        # 启用AI功能
        save_ai_status(True)
        ai_status = get_ai_status()
        print(f"AI状态: {ai_status} (应该为True)")

        if not ai_status:
            print("❌ AI状态设置失败")
            return False

        # 测试AI管理器
        ai_manager = AIManager()
        from smartvault.utils.config import load_config
        config = load_config()

        success = ai_manager.initialize(config)
        print(f"AI管理器初始化: {success}")

        if not success or not ai_manager.is_available():
            print("❌ AI管理器不可用")
            return False

        # 创建测试文件
        test_dir = tempfile.mkdtemp(prefix="smartvault_ai_enabled_")
        unique_id = str(uuid.uuid4())[:8]

        test_files = [
            (f"app_config_{unique_id}.json", '{"app": "test", "version": "2.0"}'),
            (f"main_script_{unique_id}.py", "def main():\n    print('AI Test')"),
        ]

        created_files = []
        for filename, content in test_files:
            file_path = os.path.join(test_dir, filename)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            created_files.append(file_path)

        try:
            # 创建服务
            file_service = FileService()
            tag_service = TagService()

            print(f"创建了 {len(created_files)} 个测试文件")

            # 测试AI功能
            results = []
            for test_file in created_files:
                try:
                    filename = os.path.basename(test_file)
                    print(f"\n添加文件: {filename}")

                    # 添加文件（应该触发AI标签）
                    file_id = file_service.add_file(test_file, mode="link")
                    print(f"✅ 文件添加成功: {file_id}")

                    # 检查AI标签是否被应用
                    tags = tag_service.get_file_tags(file_id)
                    tag_names = [tag['name'] for tag in tags]

                    # 验证预期的AI标签
                    expected_tags = []
                    if 'config' in filename and '.json' in filename:
                        expected_tags = ['配置文件', 'JSON配置']
                    elif '.py' in filename:
                        expected_tags = ['代码', 'Python']

                    found_expected = any(tag in tag_names for tag in expected_tags)

                    if found_expected:
                        print(f"✅ AI标签正确应用: {tag_names}")
                        results.append(True)
                    else:
                        print(f"⚠️ AI标签未按预期应用: {tag_names}")
                        print(f"预期标签: {expected_tags}")
                        results.append(False)

                except Exception as e:
                    print(f"❌ AI功能测试失败: {e}")
                    results.append(False)

            return all(results) and len(results) > 0

        finally:
            # 清理测试文件
            shutil.rmtree(test_dir, ignore_errors=True)
            # 恢复AI状态
            save_ai_status(False)

    except Exception as e:
        print(f"❌ AI启用功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_switch_toggle():
    """测试AI开关切换的稳定性"""
    print("\n🔍 测试AI开关切换的稳定性...")

    try:
        from smartvault.utils.config import save_ai_status, get_ai_status

        # 记录原始状态
        original_status = get_ai_status()
        print(f"原始AI状态: {original_status}")

        # 测试多次切换
        switch_results = []
        for i in range(3):
            # 切换到True
            save_ai_status(True)
            status1 = get_ai_status()

            # 切换到False
            save_ai_status(False)
            status2 = get_ai_status()

            print(f"切换测试 {i+1}: True={status1}, False={status2}")

            if status1 == True and status2 == False:
                switch_results.append(True)
            else:
                switch_results.append(False)

        # 恢复原始状态
        save_ai_status(original_status)
        final_status = get_ai_status()

        print(f"恢复后状态: {final_status} (应该为 {original_status})")

        return all(switch_results) and final_status == original_status

    except Exception as e:
        print(f"❌ AI开关切换测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_manager_safety():
    """测试AI管理器的安全性"""
    print("\n🔍 测试AI管理器的安全性...")

    try:
        from smartvault.services.ai.ai_manager import AIManager
        from smartvault.utils.config import save_ai_status, load_config

        # 测试AI关闭时的管理器行为
        save_ai_status(False)

        ai_manager = AIManager()
        config = load_config()

        # 初始化应该成功，但不可用
        success = ai_manager.initialize(config)
        available = ai_manager.is_available()

        print(f"AI关闭时 - 初始化: {success}, 可用性: {available}")

        if available:
            print("⚠️ AI关闭时管理器仍然可用，可能存在安全问题")
            return False

        # 测试标签建议（应该返回空列表）
        test_file = {'name': 'test.json', 'extension': '.json', 'path': '/test.json'}
        suggestions = ai_manager.suggest_tags(test_file)

        print(f"AI关闭时标签建议: {suggestions}")

        if suggestions:
            print("⚠️ AI关闭时仍然返回标签建议，存在安全问题")
            return False

        # 测试AI开启时的管理器行为
        save_ai_status(True)

        # 重新加载配置以确保AI状态更新
        config = load_config()

        ai_manager2 = AIManager()
        success2 = ai_manager2.initialize(config)
        available2 = ai_manager2.is_available()

        print(f"AI开启时 - 初始化: {success2}, 可用性: {available2}")

        if not available2:
            print("❌ AI开启时管理器不可用")
            print(f"AI管理器状态: enabled={ai_manager2.enabled}, status={ai_manager2.initialization_status}")
            save_ai_status(False)
            return False

        # 测试标签建议（应该返回建议）
        suggestions2 = ai_manager2.suggest_tags(test_file)
        print(f"AI开启时标签建议: {suggestions2}")

        # 恢复状态
        save_ai_status(False)

        return len(suggestions2) > 0

    except Exception as e:
        print(f"❌ AI管理器安全性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 AI开关安全性测试")
    print("=" * 70)

    # 测试1: AI关闭时的系统安全性
    test1 = test_ai_disabled_safety()

    # 测试2: AI开启时的正常功能
    test2 = test_ai_enabled_functionality()

    # 测试3: AI开关切换稳定性
    test3 = test_ai_switch_toggle()

    # 测试4: AI管理器安全性
    test4 = test_ai_manager_safety()

    # 总结
    print("\n" + "=" * 70)
    print("📊 AI开关安全性测试结果:")
    print(f"AI关闭时系统安全性: {'✅ 通过' if test1 else '❌ 失败'}")
    print(f"AI开启时正常功能: {'✅ 通过' if test2 else '❌ 失败'}")
    print(f"AI开关切换稳定性: {'✅ 通过' if test3 else '❌ 失败'}")
    print(f"AI管理器安全性: {'✅ 通过' if test4 else '❌ 失败'}")

    if all([test1, test2, test3, test4]):
        print("\n🎉 所有安全性测试通过！")
        print("✅ AI功能开关安全可靠")
        print("✅ 现有功能不受AI开关影响")
        print("✅ AI功能在启用时正常工作")
        return 0
    else:
        print("\n⚠️ 发现安全性问题，需要修复")
        return 1

if __name__ == "__main__":
    sys.exit(main())
