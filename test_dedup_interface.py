#!/usr/bin/env python3
"""
测试查重预留接口
"""

import sys
import os
import tempfile
import shutil
import time
import uuid
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from smartvault.services.file_monitor_service import FileMonitorService
from smartvault.ui.dialogs.settings_dialog import MonitorConfigDialog
from PySide6.QtWidgets import QApplication


def test_dedup_interface():
    """测试查重预留接口"""
    print("🚀 测试查重预留接口")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp(prefix="smartvault_dedup_test_")
    monitor_dir = os.path.join(temp_dir, "monitor")
    os.makedirs(monitor_dir, exist_ok=True)
    
    print(f"📁 测试目录: {temp_dir}")
    print(f"📁 监控目录: {monitor_dir}")
    
    try:
        # 创建服务实例
        monitor_service = FileMonitorService()
        
        print("\n🧪 测试1: 添加带查重功能的监控配置")
        
        # 添加监控文件夹（启用查重）
        monitor_id = monitor_service.add_monitor_folder(
            folder_path=monitor_dir,
            entry_mode="link",
            file_patterns=["*.txt"],
            auto_add=True,
            recursive=False,
            auto_dedup=True  # 启用查重
        )
        
        print(f"   ✅ 监控已添加: {monitor_id}")
        
        # 获取配置验证
        config = monitor_service.get_monitor_config(monitor_id)
        assert config is not None
        assert config["auto_dedup"] is True
        print(f"   ✅ 查重配置已保存: {config['auto_dedup']}")
        
        # 启动监控
        success = monitor_service.start_monitoring(monitor_id)
        assert success is True
        print(f"   ✅ 监控已启动")
        
        print("\n🧪 测试2: 创建文件触发查重逻辑")
        
        # 创建测试文件
        unique_name = f"dedup_test_{str(uuid.uuid4())[:8]}.txt"
        test_file = os.path.join(monitor_dir, unique_name)
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("这是一个查重测试文件")
        print(f"   📄 创建测试文件: {unique_name}")
        
        # 等待文件监控处理
        time.sleep(3)
        
        print("   ✅ 查重预留接口已触发（查看日志中的'查重功能已启用但尚未实现'消息）")
        
        print("\n🧪 测试3: UI界面查重选项")
        
        # 创建QApplication实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 测试MonitorConfigDialog的查重选项
        dialog = MonitorConfigDialog()
        
        # 验证查重复选框存在且被禁用
        assert hasattr(dialog, 'auto_dedup_check')
        assert dialog.auto_dedup_check.isEnabled() is False
        assert "开发中" in dialog.auto_dedup_check.text()
        print("   ✅ UI界面查重选项已添加且正确禁用")
        
        # 测试配置获取
        dialog.auto_dedup_check.setChecked(True)  # 即使禁用也可以设置用于测试
        config = dialog.get_config()
        assert "auto_dedup" in config
        print("   ✅ 配置获取包含查重选项")
        
        # 停止监控
        monitor_service.stop_monitoring(monitor_id)
        
        print("\n🎉 查重预留接口测试全部通过！")
        print("✅ 数据库字段已添加")
        print("✅ 服务接口已预留")
        print("✅ UI界面已准备")
        print("✅ 配置传递正常")
        print("📝 后续开发时只需实现具体的查重逻辑")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理测试目录
        if os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir)
                print(f"\n🧹 清理测试目录: {temp_dir}")
            except Exception as e:
                print(f"清理测试目录失败: {e}")


if __name__ == "__main__":
    success = test_dedup_interface()
    sys.exit(0 if success else 1)
